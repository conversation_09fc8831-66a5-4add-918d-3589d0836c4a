{"name": "admin-management-system", "version": "1.0.0", "description": "Admin Management System", "scripts": {"build:css": "tailwindcss -i ./app/static/src/css/input.css -o ./app/static/css/main.css", "watch:css": "tailwindcss -i ./app/static/src/css/input.css -o ./app/static/css/main.css --watch", "watch:js": "onchange \"app/static/src/js/*.js\" \"app/static/js/icons/icons-global.js\" \"app/static/js/icons/icon-utils.js\" -- npm run build:js", "build:js": "esbuild app/static/js/build/icons.js --bundle --outfile=app/static/js/dist/icons.bundle.js --format=esm", "build:icons": "node app/static/js/build/build-icon-sprite.js", "build": "npm run build:icons && npm run build:css && npm run build:js", "dev": "concurrently \"npm run watch:css\" \"npm run watch:js\" \"npm run watch:js\""}, "keywords": ["flask", "tailwind", "admin"], "author": "", "license": "MIT", "devDependencies": {"autoprefixer": "^10.4.14", "concurrently": "^8.2.2", "esbuild": "^0.19.5", "onchange": "^7.1.0", "postcss": "^8.4.23", "tailwindcss": "^3.3.2", "tailwindcss-animate": "^1.0.7"}, "dependencies": {"lucide": "^0.487.0", "lucide-static": "^0.487.0"}}
"""
Admin activity log management routes.
"""

from flask import render_template, redirect, url_for, flash, request, jsonify, current_app, Response
from flask_login import login_required, current_user
from datetime import datetime, timedelta
from sqlalchemy import or_
import csv
from io import StringIO

from app import db
from app.models import User, Activity, PH_TZ
from app.utils.decorators import admin_required, log_activity
from app.routes.admin import admin_bp


@admin_bp.route('/activities')
@login_required
@admin_required
@log_activity('Viewed activity logs', entity_type='Activity', category=Activity.CATEGORY_ADMIN, method=Activity.METHOD_READ, skip_ajax=True)
def activities():
    """Display paginated list of activity logs with filtering options."""
    page = request.args.get('page', 1, type=int)
    per_page = 20  # Number of items per page

    # Get filter parameters
    user_id = request.args.get('user_id', type=int)
    category = request.args.get('category')
    severity = request.args.get('severity')
    method = request.args.get('method')
    date_from = request.args.get('date_from')
    date_to = request.args.get('date_to')
    search = request.args.get('search')
    sort = request.args.get('sort', 'created_at_desc')

    # Start with base query
    query = Activity.query

    # Apply filters
    if user_id:
        query = query.filter(Activity.user_id == user_id)

    if category:
        query = query.filter(Activity.category == category)

    if severity:
        query = query.filter(Activity.severity == severity)

    if method:
        query = query.filter(Activity.method == method)

    if date_from:
        try:
            from_date = datetime.strptime(date_from, '%Y-%m-%d').replace(hour=0, minute=0, second=0)
            query = query.filter(Activity.created_at >= from_date)
        except ValueError:
            flash('Invalid from date format', 'error')

    if date_to:
        try:
            to_date = datetime.strptime(date_to, '%Y-%m-%d').replace(hour=23, minute=59, second=59)
            query = query.filter(Activity.created_at <= to_date)
        except ValueError:
            flash('Invalid to date format', 'error')

    if search:
        search_term = f'%{search}%'
        query = query.filter(
            or_(
                Activity.action.ilike(search_term),
                Activity.details.ilike(search_term),
                Activity.entity_type.ilike(search_term)
            )
        )

    # Apply sorting
    if sort == 'created_at_asc':
        query = query.order_by(Activity.created_at.asc())
    elif sort == 'severity_desc':
        # Order by severity (error > warning > info)
        query = query.order_by(
            db.case(
                (Activity.severity == Activity.SEVERITY_ERROR, 1),
                (Activity.severity == Activity.SEVERITY_WARNING, 2),
                (Activity.severity == Activity.SEVERITY_INFO, 3),
                else_=4
            ).asc(),
            Activity.created_at.desc()
        )
    elif sort == 'user_asc':
        query = query.join(User).order_by(User.name.asc(), Activity.created_at.desc())
    else:  # Default: created_at_desc
        query = query.order_by(Activity.created_at.desc())

    # Get paginated activities
    activities_pagination = query.paginate(page=page, per_page=per_page, error_out=False)
    activities = activities_pagination.items

    # Get all users for the filter dropdown
    users = User.query.order_by(User.name).all()

    # Create pagination info
    pagination = {
        'page': page,
        'per_page': per_page,
        'total_items': activities_pagination.total,
        'total_pages': activities_pagination.pages,
        'has_next': activities_pagination.has_next,
        'has_prev': activities_pagination.has_prev,
        'next_page': page + 1 if activities_pagination.has_next else page,
        'prev_page': page - 1 if activities_pagination.has_prev else page,
        'start_index': (page - 1) * per_page + 1 if activities_pagination.total > 0 else 0,
        'end_index': min(page * per_page, activities_pagination.total)
    }

    # Check if any filters are active
    has_filters = any([user_id, category, severity, method, date_from, date_to, search])

    # Create a text description of the current filter
    filter_description = []
    filter_summary = ""
    if user_id:
        user = User.query.get(user_id)
        if user:
            filter_description.append(f"User: {user.name}")
    if category:
        category_map = dict(Activity.CATEGORY_CHOICES)
        filter_description.append(f"Category: {category_map.get(category, category)}")
    if severity:
        filter_description.append(f"Severity: {severity.title()}")

    if method:
        method_map = dict(Activity.METHOD_CHOICES)
        filter_description.append(f"Method: {method_map.get(method, method.title())}")
    if date_from:
        filter_description.append(f"From: {date_from}")
    if date_to:
        filter_description.append(f"To: {date_to}")
    if search:
        filter_description.append(f"Search: '{search}'")

    if filter_description:
        filter_summary = f"Filtered by: {', '.join(filter_description)}"

    activity_count_text = f"Showing {pagination['start_index']} to {pagination['end_index']} of {pagination['total_items']} activities"
    if filter_description:
        activity_count_text += f" • {filter_summary}"

    # Get current date for today filter
    today_date = datetime.now(PH_TZ).strftime('%Y-%m-%d')

    # Get current time for recent activity indicators
    now = datetime.now(PH_TZ)

    # Make sure all activity timestamps are timezone-aware
    for activity in activities:
        if activity.created_at.tzinfo is None:
            activity.created_at = activity.created_at.replace(tzinfo=PH_TZ)

    # If this is an AJAX request, return only the table body content
    ajax = request.args.get('ajax', False, type=bool)
    if ajax:
        # Return just the activities for the table body
        return render_template('admin/activities_table.html',
                              activities=activities,
                              now=now,
                              current_user=current_user)

    return render_template('admin/activities.html',
                          activities=activities,
                          pagination=pagination,
                          users=users,
                          activity_categories=Activity.CATEGORY_CHOICES,
                          activity_severities=Activity.SEVERITY_CHOICES,
                          activity_methods=Activity.METHOD_CHOICES,
                          activity_count_text=activity_count_text,
                          filter_summary=filter_summary,
                          has_filters=has_filters,
                          today_date=today_date,
                          now=now,
                          active_page='admin_activities')


@admin_bp.route('/activities/<int:activity_id>/details')
@login_required
@admin_required
@log_activity('Viewed activity details', entity_type='Activity', category=Activity.CATEGORY_ADMIN, method=Activity.METHOD_READ)
def activity_details(activity_id):
    """Get detailed information about a specific activity."""
    activity = Activity.query.get_or_404(activity_id)

    # Check if HTML format is requested
    if request.headers.get('Accept', '').find('text/html') != -1 or request.args.get('format') == 'html':
        return render_template('admin/partials/activity_details_content.html', activity=activity)

    # Default to JSON response
    return jsonify({
        'success': True,
        'activity': activity.to_dict()
    })


@admin_bp.route('/activities/export')
@login_required
@admin_required
@log_activity('Exported activity logs', entity_type='Activity', category=Activity.CATEGORY_DATA, method=Activity.METHOD_READ)
def export_activities():
    """Export activities as CSV file."""
    # Get filter parameters (same as in activities route)
    user_id = request.args.get('user_id', type=int)
    category = request.args.get('category')
    severity = request.args.get('severity')
    method = request.args.get('method')
    date_from = request.args.get('date_from')
    date_to = request.args.get('date_to')
    search = request.args.get('search')

    # Start with base query
    query = Activity.query.join(User, Activity.user_id == User.id)

    # Apply filters (same logic as in activities route)
    if user_id:
        query = query.filter(Activity.user_id == user_id)

    if category:
        query = query.filter(Activity.category == category)

    if severity:
        query = query.filter(Activity.severity == severity)

    if method:
        query = query.filter(Activity.method == method)

    if date_from:
        try:
            from_date = datetime.strptime(date_from, '%Y-%m-%d').replace(hour=0, minute=0, second=0)
            query = query.filter(Activity.created_at >= from_date)
        except ValueError:
            pass

    if date_to:
        try:
            to_date = datetime.strptime(date_to, '%Y-%m-%d').replace(hour=23, minute=59, second=59)
            query = query.filter(Activity.created_at <= to_date)
        except ValueError:
            pass

    if search:
        search_term = f'%{search}%'
        query = query.filter(
            or_(
                Activity.action.ilike(search_term),
                Activity.details.ilike(search_term),
                Activity.entity_type.ilike(search_term)
            )
        )

    # Order by created_at desc
    activities = query.order_by(Activity.created_at.desc()).all()

    # Create CSV file
    output = StringIO()
    writer = csv.writer(output)

    # Write header
    writer.writerow(['ID', 'Date & Time', 'User', 'Action', 'Method', 'Category', 'Severity',
                    'Entity Type', 'Entity ID', 'Details', 'IP Address', 'User Agent'])

    # Write data
    for activity in activities:
        writer.writerow([
            activity.id,
            activity.created_at.strftime('%Y-%m-%d %H:%M:%S'),
            activity.user.name if activity.user else 'Unknown',
            activity.action,
            activity.get_method_display() if hasattr(activity, 'method') and activity.method else '',
            activity.get_category_display(),
            activity.severity.title(),
            activity.entity_type or '',
            activity.entity_id or '',
            activity.details or '',
            activity.ip_address or '',
            activity.user_agent or ''
        ])

    # Create response
    response = Response(
        output.getvalue(),
        mimetype='text/csv',
        headers={'Content-Disposition': 'attachment;filename=activity_logs.csv'}
    )

    return response


@admin_bp.route('/activities/manage', methods=['POST'])
@login_required
@admin_required
@log_activity('Managed activity logs', entity_type='Activity', category=Activity.CATEGORY_ADMIN, severity=Activity.SEVERITY_WARNING, method=Activity.METHOD_DELETE)
def manage_activities():
    """Manage activity logs (delete by age, category, severity, or all)."""
    action = request.form.get('action')

    if not action:
        flash('Action is required', 'error')
        return redirect(url_for('admin.activities'))

    try:
        affected_count = 0

        if action == 'delete_older_than':
            # Delete logs older than X days
            days_str = request.form.get('days', '30')
            try:
                days = int(days_str)
                if days <= 0:
                    flash('Days must be a positive number', 'error')
                    return redirect(url_for('admin.activities'))
            except ValueError:
                flash('Invalid days value', 'error')
                return redirect(url_for('admin.activities'))

            # Calculate the cutoff date
            cutoff_date = datetime.now(PH_TZ) - timedelta(days=days)

            # Delete logs older than the cutoff date
            result = Activity.query.filter(Activity.created_at < cutoff_date).delete()
            db.session.commit()

            affected_count = result
            message = f"Deleted {affected_count} logs older than {days} days"

        elif action == 'delete_by_category':
            # Delete logs by category
            category = request.form.get('category')
            if not category:
                flash('Category is required', 'error')
                return redirect(url_for('admin.activities'))

            # Delete logs by category
            result = Activity.query.filter(Activity.category == category).delete()
            db.session.commit()

            affected_count = result
            message = f"Deleted {affected_count} logs with category '{category}'"

        elif action == 'delete_by_severity':
            # Delete logs by severity
            severity = request.form.get('severity')
            if not severity:
                flash('Severity is required', 'error')
                return redirect(url_for('admin.activities'))

            # Delete logs by severity
            result = Activity.query.filter(Activity.severity == severity).delete()
            db.session.commit()

            affected_count = result
            message = f"Deleted {affected_count} logs with severity '{severity}'"

        elif action == 'delete_all':
            # Delete all logs - requires confirmation
            confirmation = request.form.get('confirmation')
            if not confirmation or confirmation != 'DELETE_ALL_LOGS':
                flash('Confirmation is required to delete all logs', 'error')
                return redirect(url_for('admin.activities'))

            # Delete all logs
            result = Activity.query.delete()
            db.session.commit()

            affected_count = result
            message = f"Deleted all {affected_count} activity logs"

        else:
            flash(f"Unknown action: {action}", 'error')
            return redirect(url_for('admin.activities'))

        # Log the action
        current_app.logger.info(f"Admin {current_user.name} (ID: {current_user.id}) {message}")

        # Return JSON response for AJAX requests
        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return jsonify({
                'success': True,
                'message': message,
                'affected_count': affected_count
            })

        # Return redirect with success message for non-AJAX requests
        flash(message, 'success')
        return redirect(url_for('admin.activities', status='success', action='managed'))

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Error managing activity logs: {str(e)}")

        # Return JSON response for AJAX requests
        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return jsonify({
                'success': False,
                'message': f"Error managing activity logs: {str(e)}"
            }), 500

        # Return redirect with error message for non-AJAX requests
        flash(f"Error managing activity logs: {str(e)}", 'error')
        return redirect(url_for('admin.activities'))

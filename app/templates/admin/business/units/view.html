{% extends "base.html" %}
{% from "components/page_header.html" import page_header %}
{% from 'components/entity_activity_log.html' import entity_activity_log %}
{% from "components/action_buttons.html" import action_buttons %}
{% from "components/info_card.html" import info_card, date_content, status_content, manager_content %}

{% block title %}{{ business_unit.name }} - Business Unit{% endblock %}

{% block header %}Business Units{% endblock %}

{% block content %}
<div class="space-y-6">
  <div id="overview" class="bg-card rounded-lg shadow-sm border border-border overflow-hidden">
    <div class="p-5">
      <nav class="flex items-center text-sm mb-3">
        <a href="{{ url_for('admin.business_units') }}" class="text-muted-foreground hover:text-primary transition-colors lg:hidden">
          Business Units
        </a>
        <i data-lucide="chevron-right" class="h-3.5 w-3.5 mx-2 text-muted-foreground lg:hidden"></i>
        <span class="text-foreground font-medium lg:hidden">{{ business_unit.name }}</span>
      </nav>

      <div class="flex flex-col md:flex-row gap-5">
        <div class="flex-1">
          <div class="flex items-start gap-4">
            <div class="hidden sm:flex h-16 w-16 rounded-lg bg-primary/10 items-center justify-center flex-shrink-0">
              <i data-lucide="briefcase" class="h-8 w-8 text-primary"></i>
            </div>

            <div class="space-y-2 flex-1">
              <div class="flex flex-wrap items-center gap-2">
                <h1 class="text-2xl font-bold">{{ business_unit.name }}</h1>
                <span class="badge badge-outline">{{ business_unit.code }}</span>
                {% if business_unit.is_active %}
                  <span class="badge badge-success">Active</span>
                {% else %}
                  <span class="badge badge-secondary">Inactive</span>
                {% endif %}
              </div>

              {% if business_unit.description %}
              <p class="text-muted-foreground">{{ business_unit.description }}</p>
              {% endif %}

              <div class="flex flex-wrap gap-4 mt-2">
                <div class="flex items-center">
                  <i data-lucide="users" class="w-4 h-4 mr-1.5 text-muted-foreground"></i>
                  <span class="text-sm">{{ business_unit.employee_details|length }} Employees</span>
                </div>
                <div class="flex items-center">
                  <i data-lucide="layers" class="w-4 h-4 mr-1.5 text-muted-foreground"></i>
                  <span class="text-sm">{{ business_unit.segments.count() }} Segments</span>
                </div>
                {% if business_unit.manager %}
                <div class="flex items-center">
                  <i data-lucide="user" class="w-4 h-4 mr-1.5 text-muted-foreground"></i>
                  <span class="text-sm">Manager: {{ business_unit.manager.name }}</span>
                </div>
                {% endif %}
                {% if business_unit.created_at %}
                <div class="flex items-center">
                  <i data-lucide="calendar" class="w-4 h-4 mr-1.5 text-muted-foreground"></i>
                  <span class="text-sm">Created: {{ business_unit.created_at.strftime('%b %d, %Y') }}</span>
                </div>
                {% endif %}
              </div>
            </div>
          </div>
        </div>

        <div class="flex flex-wrap md:flex-col gap-2 justify-start md:justify-center">
          <div class="flex gap-2">
            <button onclick="drawerManager.openForm('business_unit', {{ business_unit.id }})" class="btn btn-outline btn-sm">
              <i data-lucide="edit" class="w-4 h-4 mr-2"></i>
              <span>Edit</span>
            </button>
            <button onclick="confirmDeleteBusinessUnit({{ business_unit.id }}, '{{ business_unit.name }}')" class="btn btn-destructive btn-sm">
              <i data-lucide="trash-2" class="w-4 h-4 mr-2"></i>
              <span>Delete</span>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div id="details" class="grid grid-cols-1 gap-6">
    <div class="card">
      <div class="p-5 border-b border-border">
        <div>
          <h2 class="text-lg font-semibold flex items-center">
            <i data-lucide="clipboard-list" class="w-5 h-5 mr-2 text-primary"></i>
            Detailed Information
          </h2>
        </div>
      </div>
      <div class="p-5">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-5">
          <div class="grid grid-cols-1 gap-5 content-start">
            {{ info_card(
              label="Name",
              icon="tag",
              content=business_unit.name
            ) }}

            {{ info_card(
              label="Code",
              icon="hash",
              content=business_unit.code
            ) }}

            {{ info_card(
              label="Created",
              icon="calendar",
              content=date_content(business_unit.created_at)
            ) }}
          </div>

          <div class="grid grid-cols-1 gap-5 content-start">
            {{ info_card(
              label="Manager",
              icon="user",
              content=manager_content(business_unit.manager)
            ) }}

            {{ info_card(
              label="Status",
              icon="toggle-left",
              content=status_content(business_unit.is_active)
            ) }}

            {{ info_card(
              label="Last Updated",
              icon="clock",
              content=date_content(business_unit.updated_at)
            ) }}
          </div>

          {% if business_unit.description %}
          <div class="md:col-span-2">
            {{ info_card(
              label="Description",
              icon="file-text",
              content=business_unit.description,
              height="auto"
            ) }}
          </div>
          {% endif %}
        </div>
      </div>
    </div>

  </div>

  <div class="card" id="activity-section">
    <div id="activity-log-container">
      {{ entity_activity_log(entity_type="BusinessUnit", entity_id=business_unit.id, limit=5, show_title=true, show_all_link=true, methods="create,update", exclude_methods="read") }}
    </div>
  </div>

  <div class="card" id="employees-section">
    <div class="p-5 border-b border-border">
      <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-3">
        <div>
          <h2 class="text-lg font-semibold flex items-center">
            <i data-lucide="users" class="w-5 h-5 mr-2 text-primary"></i>
            Employees
          </h2>
          <p class="text-sm text-muted-foreground mt-1">Employees assigned to this business unit</p>
        </div>
        <div class="flex flex-col sm:flex-row gap-3 w-full sm:w-auto">
          {% if business_unit.employee_details|length > 0 %}
          <div class="flex items-center justify-between sm:justify-end w-full sm:w-auto">
            <span class="text-sm text-muted-foreground mr-2 sm:hidden">{{ business_unit.employee_details|length }} employee{% if business_unit.employee_details|length != 1 %}s{% endif %}</span>
            <div class="relative flex-1 sm:flex-none">
              <div class="relative flex items-center">
                <input type="text" id="employee-search" placeholder="Search employees..." class="input input-sm w-full min-w-[200px] pr-8">
                <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                  <i data-lucide="search" class="w-4 h-4 text-muted-foreground"></i>
                </div>
              </div>
            </div>
          </div>
          <div class="flex items-center gap-2">
            <span class="hidden sm:inline text-sm text-muted-foreground">View:</span>
            <div class="flex border border-border rounded-md overflow-hidden">
              <button class="employee-view-btn active px-3 py-1.5 text-xs font-medium" data-view="grid">
                <i data-lucide="grid" class="w-4 h-4"></i>
              </button>
              <button class="employee-view-btn px-3 py-1.5 text-xs font-medium" data-view="list">
                <i data-lucide="list" class="w-4 h-4"></i>
              </button>
            </div>
          </div>
          {% endif %}
        </div>
      </div>
    </div>
    <div class="p-5">
      {% if business_unit.employee_details %}
        <div id="employee-grid-view" class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 employee-grid">
          {% for detail in business_unit.employee_details[:8] %}
            <div class="employee-card bg-card hover:bg-muted/20 transition-all duration-200 rounded-lg border border-border shadow-sm overflow-hidden">
              <div class="p-4 border-b border-border/50 flex items-center justify-between">
                <div class="flex items-center space-x-3">
                  <div class="w-10 h-10 rounded-full bg-primary/10 flex items-center justify-center">
                    <i data-lucide="user" class="w-5 h-5 text-primary"></i>
                  </div>
                  <div>
                    <p class="font-medium">{{ detail.user.name }}</p>
                    <p class="text-xs text-muted-foreground">{{ detail.job_title or 'No Job Title' }}</p>
                  </div>
                </div>
                <a href="{{ url_for('admin.view_employee_details', user_id=detail.user_id) }}" class="btn btn-sm btn-ghost rounded-full hover:bg-primary/10 hover:text-primary transition-colors">
                  <i data-lucide="eye" class="h-4 w-4"></i>
                </a>
              </div>
              <div class="p-4 space-y-2">
                {% if detail.business_segment %}
                <div class="flex items-center">
                  <i data-lucide="layers" class="w-4 h-4 mr-2 text-muted-foreground"></i>
                  <span class="text-sm">{{ detail.business_segment.name }}</span>
                </div>
                {% endif %}
                {% if detail.employee_number %}
                <div class="flex items-center">
                  <i data-lucide="hash" class="w-4 h-4 mr-2 text-muted-foreground"></i>
                  <span class="text-sm">{{ detail.employee_number }}</span>
                </div>
                {% endif %}
                {% if detail.phone %}
                <div class="flex items-center">
                  <i data-lucide="phone" class="w-4 h-4 mr-2 text-muted-foreground"></i>
                  <span class="text-sm">{{ detail.phone }}</span>
                </div>
                {% endif %}
              </div>
            </div>
          {% endfor %}

          {% if business_unit.employee_details|length > 8 %}
            <div class="flex items-center justify-center p-6 rounded-lg border border-border border-dashed hover:bg-muted/20 transition-all duration-200 cursor-pointer" onclick="loadMoreEmployees()">
              <div class="text-center">
                <div class="w-12 h-12 rounded-full bg-muted/30 flex items-center justify-center mx-auto mb-2">
                  <i data-lucide="plus" class="w-6 h-6 text-muted-foreground"></i>
                </div>
                <p class="font-medium">+{{ business_unit.employee_details|length - 8 }} more</p>
                <p class="text-sm text-muted-foreground">Click to view all</p>
              </div>
            </div>
          {% endif %}
        </div>

        <div id="employee-list-view" class="hidden">
          <div class="bg-card rounded-lg border border-border overflow-hidden shadow-sm">
            <div class="overflow-x-auto">
              <table class="w-full">
                <thead>
                  <tr class="bg-muted/30 text-left">
                    <th class="px-4 py-3 text-sm font-medium">Employee</th>
                    <th class="px-4 py-3 text-sm font-medium">Job Title</th>
                    <th class="px-4 py-3 text-sm font-medium">Segment</th>
                    <th class="px-4 py-3 text-sm font-medium">Contact</th>
                    <th class="px-4 py-3 text-sm font-medium text-right">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {% for detail in business_unit.employee_details %}
                  <tr class="border-t border-border hover:bg-muted/20 transition-colors">
                    <td class="px-4 py-3 text-sm">
                      <div class="flex items-center">
                        <div class="w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center mr-3">
                          <i data-lucide="user" class="w-4 h-4 text-primary"></i>
                        </div>
                        <span class="font-medium">{{ detail.user.name }}</span>
                      </div>
                    </td>
                    <td class="px-4 py-3 text-sm">{{ detail.job_title or 'No Job Title' }}</td>
                    <td class="px-4 py-3 text-sm">{{ detail.business_segment.name if detail.business_segment else 'No Segment' }}</td>
                    <td class="px-4 py-3 text-sm">
                      {% if detail.phone %}
                      <div class="flex items-center">
                        <i data-lucide="phone" class="w-4 h-4 mr-2 text-muted-foreground"></i>
                        <span>{{ detail.phone }}</span>
                      </div>
                      {% else %}
                      <span class="text-muted-foreground">No contact info</span>
                      {% endif %}
                    </td>
                    <td class="px-4 py-3 text-sm text-right">
                      <a href="{{ url_for('admin.view_employee_details', user_id=detail.user_id) }}" class="btn btn-sm btn-ghost rounded-full hover:bg-primary/10 hover:text-primary transition-colors">
                        <i data-lucide="eye" class="h-4 w-4"></i>
                      </a>
                    </td>
                  </tr>
                  {% endfor %}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      {% else %}
        <div class="flex flex-col items-center justify-center py-12 space-y-4 bg-muted/10 rounded-lg border border-dashed border-border">
          <div class="w-16 h-16 rounded-full bg-muted/30 flex items-center justify-center">
            <i data-lucide="users" class="w-8 h-8 text-muted-foreground/70"></i>
          </div>
          <div class="text-center max-w-md">
            <p class="text-lg font-medium">No employees found</p>
            <p class="text-sm text-muted-foreground mt-1">There are no employees assigned to this business unit yet.</p>
          </div>
        </div>
      {% endif %}
    </div>
  </div>

  <div class="card" id="segments-section">
    <div class="p-5 border-b border-border">
      <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h2 class="text-lg font-semibold flex items-center">
            <i data-lucide="layers" class="w-5 h-5 mr-2 text-primary"></i>
            Business Segments
          </h2>
          <p class="text-sm text-muted-foreground mt-1">Segments within this business unit</p>
        </div>
        <div class="flex items-center gap-3">
          {% if business_unit.segments.count() > 0 %}
          <div class="relative hidden sm:block">
            <div class="relative flex items-center">
              <input type="text" id="segment-search" placeholder="Search segments..." class="input input-sm w-full min-w-[180px] pr-8">
              <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                <i data-lucide="search" class="w-4 h-4 text-muted-foreground"></i>
              </div>
            </div>
          </div>
          {% endif %}
          <button onclick="drawerManager.openForm('business_segment', null, { queryParams: { business_unit_id: {{ business_unit.id }} } })" class="btn btn-primary btn-sm">
            <i data-lucide="plus" class="w-4 h-4 mr-2"></i>
            <span>Add Segment</span>
          </button>
        </div>
      </div>
    </div>
    <div class="p-5">
      {% if business_unit.segments.count() > 0 %}
      <div class="bg-card rounded-lg border border-border overflow-hidden shadow-sm">
        <div class="overflow-x-auto">
          <table class="w-full segment-table">
            <thead>
              <tr class="bg-muted/30 text-left">
                <th class="px-4 py-3 text-sm font-medium">Code</th>
                <th class="px-4 py-3 text-sm font-medium">Name</th>
                <th class="px-4 py-3 text-sm font-medium">Manager</th>
                <th class="px-4 py-3 text-sm font-medium">Employees</th>
                <th class="px-4 py-3 text-sm font-medium">Status</th>
                <th class="px-4 py-3 text-sm font-medium text-right">Actions</th>
              </tr>
            </thead>
            <tbody>
              {% for segment in business_unit.segments %}
              <tr class="border-t border-border hover:bg-muted/20 transition-colors segment-row">
                <td class="px-4 py-3 text-sm font-medium">{{ segment.code }}</td>
                <td class="px-4 py-3 text-sm segment-name">{{ segment.name }}</td>
                <td class="px-4 py-3 text-sm">
                  {% if segment.manager %}
                  <div class="flex items-center">
                    <div class="w-6 h-6 rounded-full bg-primary/10 flex items-center justify-center mr-2">
                      <i data-lucide="user" class="w-3 h-3 text-primary"></i>
                    </div>
                    <span class="segment-manager">{{ segment.manager.name }}</span>
                  </div>
                  {% else %}
                  <span class="text-muted-foreground">Not assigned</span>
                  {% endif %}
                </td>
                <td class="px-4 py-3 text-sm">
                  <div class="flex items-center">
                    <i data-lucide="users" class="w-4 h-4 mr-2 text-muted-foreground"></i>
                    {{ segment.employees|length }}
                  </div>
                </td>
                <td class="px-4 py-3 text-sm">
                  {% if segment.is_active %}
                    <span class="badge badge-success">Active</span>
                  {% else %}
                    <span class="badge badge-secondary">Inactive</span>
                  {% endif %}
                </td>
                <td class="px-4 py-3 text-sm text-right">
                  <div class="flex items-center justify-end space-x-2">
                    <button class="btn btn-sm btn-ghost rounded-full hover:bg-primary/10 hover:text-primary transition-colors" onclick="drawerManager.openForm('business_segment', {{ segment.id }})">
                      <i data-lucide="edit" class="h-4 w-4"></i>
                    </button>
                  </div>
                </td>
              </tr>
              {% endfor %}
            </tbody>
          </table>
        </div>
      </div>
      {% else %}
      <div class="flex flex-col items-center justify-center py-12 space-y-4 bg-muted/10 rounded-lg border border-dashed border-border">
        <div class="w-16 h-16 rounded-full bg-muted/30 flex items-center justify-center">
          <i data-lucide="layers" class="w-8 h-8 text-muted-foreground/70"></i>
        </div>
        <div class="text-center max-w-md">
          <p class="text-lg font-medium">No business segments</p>
          <p class="text-sm text-muted-foreground mt-1">This business unit doesn't have any segments yet.</p>
          <button onclick="drawerManager.openForm('business_segment', null, { queryParams: { business_unit_id: {{ business_unit.id }} } })" class="btn btn-primary btn-sm mt-4">
            <i data-lucide="plus" class="w-4 h-4 mr-2"></i>
            <span>Add First Segment</span>
          </button>
        </div>
      </div>
      {% endif %}
    </div>
  </div>

</div>
{% endblock %}

{% block scripts %}
<script>
  document.addEventListener('DOMContentLoaded', function() {
    const urlParams = new URLSearchParams(window.location.search);
    if (urlParams.has('status')) {
      const newUrl = window.location.pathname;
      window.history.replaceState({}, document.title, newUrl);
    }

    // Employee search functionality
    const employeeSearchInput = document.getElementById('employee-search');
    if (employeeSearchInput) {
      employeeSearchInput.addEventListener('input', function() {
        const searchTerm = this.value.toLowerCase();
        let visibleCount = 0;

        // If search is cleared, show all employees
        if (searchTerm.trim() === '') {
          document.querySelectorAll('.employee-card').forEach(card => {
            card.style.display = '';
          });
          document.querySelectorAll('#employee-list-view tbody tr').forEach(row => {
            row.style.display = '';
          });

          // Remove any existing no results messages
          const noResultsEl = document.getElementById('employee-grid-no-results');
          if (noResultsEl) noResultsEl.remove();

          const listNoResultsEl = document.getElementById('employee-list-no-results');
          if (listNoResultsEl) listNoResultsEl.remove();

          return;
        }

        // For grid view
        const employeeCards = document.querySelectorAll('.employee-card');
        employeeCards.forEach(card => {
          const employeeName = card.querySelector('p.font-medium').textContent.toLowerCase();
          const jobTitle = card.querySelector('p.text-xs').textContent.toLowerCase();
          const segmentElement = card.querySelector('.flex.items-center span.text-sm');
          const segment = segmentElement ? segmentElement.textContent.toLowerCase() : '';

          if (employeeName.includes(searchTerm) || jobTitle.includes(searchTerm) || segment.includes(searchTerm)) {
            card.style.display = '';
            visibleCount++;
          } else {
            card.style.display = 'none';
          }
        });

        // For list view
        const employeeRows = document.querySelectorAll('#employee-list-view tbody tr');
        let listVisibleCount = 0;
        employeeRows.forEach(row => {
          const employeeName = row.querySelector('td:first-child span.font-medium').textContent.toLowerCase();
          const jobTitle = row.querySelector('td:nth-child(2)').textContent.toLowerCase();
          const segment = row.querySelector('td:nth-child(3)').textContent.toLowerCase();

          if (employeeName.includes(searchTerm) || jobTitle.includes(searchTerm) || segment.includes(searchTerm)) {
            row.style.display = '';
            listVisibleCount++;
          } else {
            row.style.display = 'none';
          }
        });

        // Check if we need to show 'no results' message for grid view
        let noResultsEl = document.getElementById('employee-grid-no-results');
        if (visibleCount === 0 && searchTerm.trim() !== '') {
          if (!noResultsEl) {
            const gridView = document.getElementById('employee-grid-view');
            noResultsEl = document.createElement('div');
            noResultsEl.id = 'employee-grid-no-results';
            noResultsEl.className = 'col-span-full py-8 text-center';
            noResultsEl.innerHTML = `
              <div class="w-12 h-12 rounded-full bg-muted/30 flex items-center justify-center mx-auto mb-3">
                <i data-lucide="search-x" class="w-6 h-6 text-muted-foreground"></i>
              </div>
              <p class="text-lg font-medium">No employees found</p>
              <p class="text-sm text-muted-foreground mt-1">No employees match your search criteria</p>
            `;
            gridView.appendChild(noResultsEl);
            if (typeof lucide !== 'undefined') {
              lucide.createIcons();
            }
          }
        } else if (noResultsEl) {
          noResultsEl.remove();
        }

        // Check if we need to show 'no results' message for list view
        let listNoResultsEl = document.getElementById('employee-list-no-results');
        if (listVisibleCount === 0 && searchTerm.trim() !== '') {
          if (!listNoResultsEl) {
            const listViewTable = document.querySelector('#employee-list-view table tbody');
            const tr = document.createElement('tr');
            tr.id = 'employee-list-no-results';
            tr.className = 'border-t border-border';
            tr.innerHTML = `
              <td colspan="5" class="px-4 py-8 text-center">
                <div class="w-12 h-12 rounded-full bg-muted/30 flex items-center justify-center mx-auto mb-3">
                  <i data-lucide="search-x" class="w-6 h-6 text-muted-foreground"></i>
                </div>
                <p class="text-lg font-medium">No employees found</p>
                <p class="text-sm text-muted-foreground mt-1">No employees match your search criteria</p>
              </td>
            `;
            listViewTable.appendChild(tr);
            if (typeof lucide !== 'undefined') {
              lucide.createIcons();
            }
          }
        } else if (listNoResultsEl) {
          listNoResultsEl.remove();
        }
      });
    }

    // Segment search functionality
    const segmentSearchInput = document.getElementById('segment-search');
    if (segmentSearchInput) {
      segmentSearchInput.addEventListener('input', function() {
        const searchTerm = this.value.toLowerCase();
        const segmentRows = document.querySelectorAll('.segment-row');
        let visibleCount = 0;

        // If search is cleared, show all segments
        if (searchTerm.trim() === '') {
          segmentRows.forEach(row => {
            row.style.display = '';
          });

          // Remove any existing no results message
          const noResultsEl = document.getElementById('segment-no-results');
          if (noResultsEl) noResultsEl.remove();

          return;
        }

        segmentRows.forEach(row => {
          const segmentName = row.querySelector('.segment-name').textContent.toLowerCase();
          const segmentCode = row.querySelector('td:first-child').textContent.toLowerCase();
          const managerElement = row.querySelector('.segment-manager');
          const manager = managerElement ? managerElement.textContent.toLowerCase() : '';

          if (segmentName.includes(searchTerm) || segmentCode.includes(searchTerm) || manager.includes(searchTerm)) {
            row.style.display = '';
            visibleCount++;
          } else {
            row.style.display = 'none';
          }
        });

        // Check if we need to show 'no results' message
        let noResultsEl = document.getElementById('segment-no-results');
        if (visibleCount === 0 && searchTerm.trim() !== '') {
          if (!noResultsEl) {
            const segmentTable = document.querySelector('.segment-table tbody');
            const tr = document.createElement('tr');
            tr.id = 'segment-no-results';
            tr.className = 'border-t border-border';
            tr.innerHTML = `
              <td colspan="6" class="px-4 py-8 text-center">
                <div class="w-12 h-12 rounded-full bg-muted/30 flex items-center justify-center mx-auto mb-3">
                  <i data-lucide="search-x" class="w-6 h-6 text-muted-foreground"></i>
                </div>
                <p class="text-lg font-medium">No segments found</p>
                <p class="text-sm text-muted-foreground mt-1">No segments match your search criteria</p>
              </td>
            `;
            segmentTable.appendChild(tr);
            if (typeof lucide !== 'undefined') {
              lucide.createIcons();
            }
          }
        } else if (noResultsEl) {
          noResultsEl.remove();
        }
      });
    }

    // Employee view toggle (grid/list)
    const viewButtons = document.querySelectorAll('.employee-view-btn');
    const gridView = document.getElementById('employee-grid-view');
    const listView = document.getElementById('employee-list-view');

    viewButtons.forEach(button => {
      button.addEventListener('click', function() {
        // Remove active class from all buttons
        viewButtons.forEach(btn => btn.classList.remove('active', 'bg-muted/50'));

        // Add active class to clicked button
        this.classList.add('active', 'bg-muted/50');

        // Show/hide views based on selection
        const viewType = this.getAttribute('data-view');
        if (viewType === 'grid') {
          gridView.classList.remove('hidden');
          listView.classList.add('hidden');
        } else {
          gridView.classList.add('hidden');
          listView.classList.remove('hidden');
        }
      });
    });

    // Smooth scrolling for anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
      anchor.addEventListener('click', function(e) {
        e.preventDefault();
        const targetId = this.getAttribute('href');
        const targetElement = document.querySelector(targetId);

        if (targetElement) {
          // Account for fixed header and mobile nav
          const offset = window.innerWidth >= 1024 ? 20 : 80;
          window.scrollTo({
            top: targetElement.offsetTop - offset,
            behavior: 'smooth'
          });
        }
      });
    });
  });


  // Update activity log limit
  function updateActivityLimit(limit) {
    // Show loading state
    const activityContainer = document.getElementById('activity-log-container');
    if (activityContainer) {
      // Create a loading overlay
      const loadingOverlay = document.createElement('div');
      loadingOverlay.className = 'absolute inset-0 bg-background/80 backdrop-blur-sm flex items-center justify-center z-10';
      loadingOverlay.innerHTML = `
        <div class="flex flex-col items-center">
          <div class="animate-spin rounded-full h-10 w-10 border-2 border-primary/30 border-t-primary mb-3"></div>
          <p class="text-sm font-medium text-muted-foreground">Loading timeline...</p>
        </div>
      `;

      // Make the container relative for absolute positioning
      activityContainer.style.position = 'relative';
      activityContainer.appendChild(loadingOverlay);

      // Update the activity log with new content
      const entityActivityLog = activityContainer.querySelector('.entity-activity-log');
      if (entityActivityLog) {
        // Update the data-limit attribute
        entityActivityLog.dataset.limit = limit;

        // Re-initialize the activity log
        initEntityActivityLog(entityActivityLog);

        // Remove the loading overlay after a short delay to ensure data is loaded
        setTimeout(() => {
          // Remove the loading overlay
          activityContainer.removeChild(loadingOverlay);
          activityContainer.style.position = '';

          // Show toast notification
          showToast({
            title: 'Activity Timeline Updated',
            description: `Now showing ${limit === 'all' ? 'all' : 'last ' + limit} changes.`,
            variant: 'default'
          });
        }, 800);
      } else {
        // Fallback if the entity-activity-log element is not found
        setTimeout(() => {
          // Remove the loading overlay
          activityContainer.removeChild(loadingOverlay);
          activityContainer.style.position = '';

          activityContainer.innerHTML = `
            <div class="p-6 text-center">
              <div class="w-16 h-16 rounded-full bg-primary/10 flex items-center justify-center mx-auto mb-4">
                <i data-lucide="history" class="w-8 h-8 text-primary"></i>
              </div>
              <p class="text-lg font-medium">Showing ${limit === 'all' ? 'all' : 'last ' + limit} changes</p>
              <p class="text-sm text-muted-foreground mt-2">Activity timeline updated successfully</p>
              <button onclick="location.reload()" class="btn btn-outline btn-sm mt-4">
                <i data-lucide="refresh-cw" class="w-4 h-4 mr-2"></i>
                <span>Refresh Page</span>
              </button>
            </div>
          `;

          // Initialize Lucide icons for the newly added content
          if (typeof lucide !== 'undefined') {
            lucide.createIcons();
          }

          // Show toast notification
          showToast({
            title: 'Activity Timeline Updated',
            description: `Now showing ${limit === 'all' ? 'all' : 'last ' + limit} changes.`,
            variant: 'default'
          });
        }, 800);
      }
    }
  }

  function confirmDeleteBusinessUnit(unitId, unitName) {
    showAlertDialog({
      title: 'Delete Business Unit',
      description: `Are you sure you want to delete the business unit "${unitName}"? This action cannot be undone.`,
      variant: 'destructive',
      onConfirm: () => {
        // Submit form to delete endpoint
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = "{{ url_for('admin.delete_business_unit', unit_id=0) }}".replace('0', unitId);

        const csrfToken = document.createElement('input');
        csrfToken.type = 'hidden';
        csrfToken.name = 'csrf_token';
        csrfToken.value = '{{ csrf_token() }}';

        form.appendChild(csrfToken);
        document.body.appendChild(form);
        form.submit();
      }
    });
  }

  function loadMoreEmployees() {
    showToast({
      title: 'Load More',
      description: 'This would load more employees in a real implementation.',
      variant: 'default'
    });
  }
</script>

<style>
  /* Custom styles for the view */
  .employee-view-btn.active {
    @apply bg-muted text-primary;
  }

  /* Ensure consistent icon sizing */
  [data-lucide] {
    stroke-width: 1.5px;
  }


</style>
{% endblock %}

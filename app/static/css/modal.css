/**
 * Modern Modal Component
 * Primarily uses Tailwind CSS classes with minimal custom CSS
 */

/**
 * Modal component styles
 * Uses Tailwind-compatible classes with CSS variables for theming
 */

/* Overlay styles - shadcn style */
.modal-overlay {
  position: fixed;
  inset: 0;
  z-index: 50;
  background-color: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(3px);
  transition: opacity 0.3s ease;
}

/* Modal dialog styles */
.modal-dialog {
  position: fixed;
  left: 50%;
  top: 50%;
  z-index: 50;
  width: calc(100% - 1.5rem);
  max-width: 32rem; /* Default size, overridden by size classes */
  max-height: calc(100vh - 1.5rem);
  transform: translate(-50%, -50%);
  display: flex;
  flex-direction: column;
  background-color: rgb(var(--color-background) / 1);
  border-radius: 0.5rem;
  /* Layered shadow for better depth perception */
  box-shadow:
    0 0 0 1px rgba(0, 0, 0, 0.05),
    0 10px 15px -3px rgba(0, 0, 0, 0.1),
    0 4px 6px -2px rgba(0, 0, 0, 0.05),
    0 20px 25px -5px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transition: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
  color: rgb(var(--color-foreground) / 1);
}

/* Dark mode support */
.dark .modal-dialog {
  border: 1px solid rgba(var(--color-border) / 0.5);
  /* Adjusted shadow for dark mode */
  box-shadow:
    0 0 0 1px rgba(255, 255, 255, 0.05),
    0 10px 15px -3px rgba(0, 0, 0, 0.3),
    0 4px 6px -2px rgba(0, 0, 0, 0.2),
    0 20px 25px -5px rgba(0, 0, 0, 0.3);
}

/* Modal sizes with improved responsive behavior */
.modal-dialog.xs { max-width: 20rem; }
.modal-dialog.sm { max-width: 24rem; }
.modal-dialog.md { max-width: 32rem; }
.modal-dialog.lg { max-width: 48rem; }
.modal-dialog.xl { max-width: 64rem; }
.modal-dialog.full {
  max-width: min(90vw, 1200px);
  max-height: 90vh;
}

/* Animation states - shadcn style */
.modal-overlay[data-state="open"],
.modal-dialog[data-state="open"] {
  opacity: 1;
}

.modal-overlay[data-state="closed"],
.modal-dialog[data-state="closed"] {
  opacity: 0;
}

/* Entry animation - subtle zoom for shadcn style */
.modal-dialog[data-state="open"] {
  animation: modal-content-in 150ms cubic-bezier(0.16, 1, 0.3, 1) forwards;
}

/* Exit animation */
.modal-dialog[data-state="closed"] {
  animation: modal-content-out 150ms cubic-bezier(0.16, 1, 0.3, 1) forwards;
}

/* Overlay animations */
.modal-overlay[data-state="open"] {
  animation: modal-overlay-in 150ms cubic-bezier(0.16, 1, 0.3, 1) forwards;
}

.modal-overlay[data-state="closed"] {
  animation: modal-overlay-out 150ms cubic-bezier(0.16, 1, 0.3, 1) forwards;
}

@keyframes modal-content-in {
  0% {
    opacity: 0;
    transform: translate(-50%, -48%) scale(0.96);
  }
  100% {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
  }
}

@keyframes modal-content-out {
  0% {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
  }
  100% {
    opacity: 0;
    transform: translate(-50%, -48%) scale(0.96);
  }
}

@keyframes modal-overlay-in {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

@keyframes modal-overlay-out {
  0% {
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
}

/* For backward compatibility - these classes are no longer used directly */
.modal-header,
.modal-content,
.modal-footer,
.modal-title,
.modal-description,
.modal-btn,
.modal-btn-cancel,
.modal-btn-primary,
.modal-btn-destructive {
  /* Empty rules to maintain backward compatibility */
}

/* Ensure the modal content can scroll if needed - shadcn style */
.modal-dialog > div {
  max-height: calc(100vh - 2rem);
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  flex: 1;
  /* Improved scrolling behavior */
  scroll-behavior: smooth;
  overscroll-behavior: contain;
  -webkit-overflow-scrolling: touch; /* For iOS */
}

/* Fix for select elements in modals */
.modal-dialog select {
  position: relative;
  z-index: 1;
  background-image: none !important;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
}

.modal-dialog .relative [data-lucide] {
  position: relative;
  z-index: 2;
}

/* Minimal scrollbar styling for shadcn look */
.modal-dialog > div::-webkit-scrollbar {
  width: 0.25rem;
}

.modal-dialog > div::-webkit-scrollbar-track {
  background: transparent;
}

.modal-dialog > div::-webkit-scrollbar-thumb {
  background-color: rgba(156, 163, 175, 0.2);
  border-radius: 9999px;
}

.modal-dialog > div::-webkit-scrollbar-thumb:hover {
  background-color: rgba(156, 163, 175, 0.3);
}

.dark .modal-dialog > div::-webkit-scrollbar-thumb {
  background-color: rgba(75, 85, 99, 0.2);
}

.dark .modal-dialog > div::-webkit-scrollbar-thumb:hover {
  background-color: rgba(75, 85, 99, 0.3);
}

/* Focus styles for accessibility */
.modal-dialog button:focus-visible,
.modal-dialog a:focus-visible,
.modal-dialog input:focus-visible,
.modal-dialog select:focus-visible,
.modal-dialog textarea:focus-visible {
  outline: none;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.5);
  position: relative;
  z-index: 1;
}

.dark .modal-dialog button:focus-visible,
.dark .modal-dialog a:focus-visible,
.dark .modal-dialog input:focus-visible,
.dark .modal-dialog select:focus-visible,
.dark .modal-dialog textarea:focus-visible {
  box-shadow: 0 0 0 2px rgba(96, 165, 250, 0.5);
}

/* Specific focus styles for modal buttons to fix alignment */
.modal-dialog button.inline-flex:focus-visible {
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.5);
  outline: none;
  position: relative;
}

.dark .modal-dialog button.inline-flex:focus-visible {
  box-shadow: 0 0 0 2px rgba(96, 165, 250, 0.5);
}

/* Activity item animations */
.activity-item {
  opacity: 0;
  transform: translateY(10px);
  transition: opacity 0.3s ease, transform 0.3s ease, box-shadow 0.2s ease;
}

/* Activity loading spinner */
@keyframes spinner {
  to {transform: rotate(360deg);}
}

.animate-spin {
  animation: spinner 1s linear infinite;
}

/* For backward compatibility */
.isolated-modal-overlay {
  position: fixed;
  inset: 0;
  z-index: 50;
  background-color: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
  transition: all 0.3s cubic-bezier(0.16, 1, 0.3, 1);
}

.isolated-modal-dialog {
  position: fixed;
  left: 50%;
  top: 50%;
  z-index: 50;
  width: calc(100% - 2rem);
  max-width: 32rem;
  max-height: calc(100vh - 2rem);
  transform: translate(-50%, -50%);
  display: flex;
  flex-direction: column;
  background-color: rgb(var(--color-background) / 1);
  color: rgb(var(--color-foreground) / 1);
  border-radius: 0.75rem;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.16, 1, 0.3, 1);
}

.dark .isolated-modal-dialog {
  border: 1px solid rgb(var(--color-border) / 1);
}

.isolated-modal-header {
  display: flex;
  flex-direction: column;
  gap: 0.375rem;
  text-align: center;
  width: 100%;
}

/* Backward compatibility for close button */
.isolated-modal-close-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 2rem;
  height: 2rem;
  border-radius: 9999px;
  color: var(--muted-color, #6b7280);
  background-color: var(--bg-color, rgba(0, 0, 0, 0.03));
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
  margin-top: -0.25rem;
  margin-right: -0.25rem;
}

.isolated-modal-close-button:hover {
  background-color: var(--hover-bg, rgba(0, 0, 0, 0.08));
  color: var(--text-color, #374151);
  transform: scale(1.05);
}

.isolated-modal-close-button:active {
  transform: scale(0.95);
}

.isolated-modal-close-button:focus {
  outline: none;
  box-shadow: 0 0 0 2px var(--ring-color, rgba(59, 130, 246, 0.5));
}

/* Make the icon itself have a larger clickable area */
.isolated-modal-close-button i,
.isolated-modal-close-button svg {
  width: 1.25rem;
  height: 1.25rem;
  pointer-events: none;
}

.dark .isolated-modal-close-button {
  --muted-color: #9ca3af;
  --text-color: #f9fafb;
  --bg-color: rgba(255, 255, 255, 0.05);
  --hover-bg: rgba(255, 255, 255, 0.1);
}

.isolated-modal-title {
  font-size: 1.125rem;
  font-weight: 600;
  line-height: 1;
  letter-spacing: -0.025em;
  margin: 0;
}

.isolated-modal-description {
  font-size: 0.875rem;
  color: var(--muted-color, #6b7280);
  margin: 0;
}

.isolated-modal-footer {
  display: flex;
  flex-direction: column-reverse;
  justify-content: flex-end;
  gap: 0.5rem;
}

.isolated-modal-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  font-weight: 500;
  transition-property: color, background-color, border-color;
  transition-duration: 0.15s;
  transition-timing-function: ease;
}

.isolated-modal-btn-cancel {
  padding: 0.375rem 0.75rem;
  background-color: var(--background-color, white);
  border: 1px solid var(--border-color, #e5e7eb);
  color: var(--text-color, #374151);
}

.isolated-modal-btn-primary {
  padding: 0.5rem 1rem;
  background-color: var(--primary-color, #2563eb);
  color: white;
  border: 1px solid transparent;
}

.isolated-modal-btn-destructive {
  padding: 0.5rem 1rem;
  background-color: var(--destructive-color, #dc2626);
  color: white;
  border: 1px solid transparent;
}

/* Activity severity badges */
.activity-severity-badge.badge-info,
.badge-info {
  background-color: rgba(16, 185, 129, 0.1);
  color: rgb(16, 185, 129);
}

.activity-severity-badge.badge-warning,
.badge-warning {
  background-color: rgba(245, 158, 11, 0.1);
  color: rgb(245, 158, 11);
}

.activity-severity-badge.badge-error,
.badge-destructive {
  background-color: rgba(239, 68, 68, 0.1);
  color: rgb(239, 68, 68);
}

/* Dark mode overrides */
.dark .activity-severity-badge.badge-info,
.dark .badge-info {
  background-color: rgba(16, 185, 129, 0.2);
}

.dark .activity-severity-badge.badge-warning,
.dark .badge-warning {
  background-color: rgba(245, 158, 11, 0.2);
}

.dark .activity-severity-badge.badge-error,
.dark .badge-destructive {
  background-color: rgba(239, 68, 68, 0.2);
}

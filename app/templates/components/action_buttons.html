{% macro action_buttons(actions) %}
<div class="flex justify-end space-x-2">
  {% for action in actions %}
    {% if action.type == 'link' %}
      <a href="{{ action.url }}" class="btn btn-sm btn-ghost {% if action.variant %}{{ action.variant }}{% endif %}" {% if action.title %}title="{{ action.title }}"{% endif %}>
        <i data-lucide="{{ action.icon }}" class="w-4 h-4"></i>
      </a>
    {% elif action.type == 'button' %}
      <button class="btn btn-sm btn-ghost {% if action.variant %}{{ action.variant }}{% endif %}" onclick="{{ action.action }}" {% if action.title %}title="{{ action.title }}"{% endif %}>
        <i data-lucide="{{ action.icon }}" class="w-4 h-4"></i>
      </button>
    {% endif %}
  {% endfor %}
</div>
{% endmacro %}

{% macro form_group(label, name, type="text", value="", required=false, placeholder="", hint="", options=None, disabled=false) %}
<div class="form-group">
  <label for="{{ name }}" class="form-label">
    {{ label }}
    {% if required %}<span class="text-destructive">*</span>{% endif %}
  </label>

  {% if type == "select" and options %}
    <div class="relative">
      <select id="{{ name }}" name="{{ name }}" class="flex h-10 w-full rounded-md border border-input bg-background pl-3 pr-10 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 appearance-none" {% if required %}required{% endif %}>
        {% for option_value, option_label in options %}
          <option value="{{ option_value }}" {% if value == option_value %}selected{% endif %}>{{ option_label }}</option>
        {% endfor %}
      </select>
      <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
        <i data-lucide="chevron-down" class="h-4 w-4 text-muted-foreground"></i>
      </div>
    </div>
  {% elif type == "textarea" %}
    <textarea id="{{ name }}" name="{{ name }}" class="flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50" placeholder="{{ placeholder }}" {% if required %}required{% endif %}>{{ value }}</textarea>
  {% elif type == "checkbox" %}
    <div class="flex items-center space-x-2">
      <input type="checkbox" id="{{ name }}" name="{{ name }}" class="h-4 w-4 rounded border-primary text-primary focus:ring-primary" {% if value %}checked{% endif %}>
      <label for="{{ name }}" class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">{{ label }}</label>
    </div>
  {% elif type == "date" %}
    <div class="relative">
      <input type="{{ type }}" id="{{ name }}" name="{{ name }}" value="{{ value }}" placeholder="{{ placeholder }}"
             class="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
             {% if required %}required{% endif %} {% if disabled %}disabled{% endif %}>
    </div>
  {% else %}
    <input type="{{ type }}" id="{{ name }}" name="{{ name }}" value="{{ value }}" placeholder="{{ placeholder }}"
           class="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
           {% if required %}required{% endif %} {% if disabled %}disabled{% endif %}>
  {% endif %}

  {% if hint %}
    <p class="text-sm text-muted-foreground mt-1">{{ hint }}</p>
  {% endif %}
</div>
{% endmacro %}

{% macro form_actions(submit_label="Submit", cancel_label="Cancel", show_cancel=true) %}
<div class="flex justify-end space-x-2 pt-4">
  {% if show_cancel %}
  <button type="button" class="btn btn-outline btn-md drawer-close">{{ cancel_label }}</button>
  {% endif %}
  <button type="submit" class="btn btn-primary btn-md">{{ submit_label }}</button>
</div>
{% endmacro %}

{% macro form_header(title, description="") %}
<div class="mb-6">
  <h2 class="text-lg font-semibold tracking-tight">{{ title }}</h2>
  {% if description %}
  <p class="text-sm text-muted-foreground">{{ description }}</p>
  {% endif %}
</div>
{% endmacro %}

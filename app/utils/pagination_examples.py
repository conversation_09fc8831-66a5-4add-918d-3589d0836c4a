# Example of how to use the new pagination utility in other routes

from flask import current_app, request
from app.utils.pagination import paginate_query, PaginationHelper

# Method 1: Using the convenience function with default pagination from config
def list_users():
    """Example: List users with default pagination from PAGINATION_PER_PAGE."""
    query = User.query.order_by(User.name.asc())
    users, pagination = paginate_query(query)  # Uses PAGINATION_PER_PAGE from config

    return render_template('users/list.html',
                          users=users,
                          pagination=pagination)

# Method 2: Using admin-specific pagination
def list_admin_users():
    """Example: List users with admin pagination settings."""
    query = User.query.order_by(User.name.asc())
    per_page = current_app.config.get('PAGINATION_PER_PAGE_ADMIN', 20)
    users, pagination = paginate_query(query, per_page=per_page)

    return render_template('admin/users/list.html',
                          users=users,
                          pagination=pagination)

# Method 3: Using the PaginationHelper class directly (for more control)
def list_teams():
    """Example: List teams with custom pagination handling."""
    query = Team.query.filter(Team.is_active == True).order_by(Team.name.asc())

    # Create helper with custom settings (will still respect MAX_PER_PAGE)
    helper = PaginationHelper(query, per_page=15, error_out=False)

    # Get items and pagination info
    teams = helper.items
    pagination_info = helper.info

    return render_template('teams/list.html',
                          teams=teams,
                          pagination=pagination_info)

# Method 4: Using the helper's to_dict() method for APIs
def api_list_employees():
    """Example: API endpoint returning paginated employees."""
    query = Employee.query.order_by(Employee.created_at.desc())
    helper = PaginationHelper(query)  # Uses default PAGINATION_PER_PAGE

    return jsonify(helper.to_dict())

# Method 5: Custom per_page while respecting environment limits
def list_reports():
    """Example: Custom per_page that respects PAGINATION_MAX_PER_PAGE."""
    query = Report.query.order_by(Report.created_at.desc())
    requested_per_page = request.args.get('per_page', 50, type=int)

    # The PaginationHelper will automatically cap this at PAGINATION_MAX_PER_PAGE
    reports, pagination = paginate_query(query, per_page=requested_per_page)

    return render_template('reports/list.html',
                          reports=reports,
                          pagination=pagination)

# Method 6: Override defaults but still use config fallbacks
def list_projects():
    """Example: Specify per_page but let it fall back to config if None."""
    query = Project.query.order_by(Project.name.asc())

    # If user requests specific per_page via URL parameter
    user_per_page = request.args.get('per_page', type=int)

    # Uses user preference, or falls back to PAGINATION_PER_PAGE from config
    projects, pagination = paginate_query(query, per_page=user_per_page)

    return render_template('projects/list.html',
                          projects=projects,
                          pagination=pagination)

/**
 * Utilities Module
 *
 * This script is kept for backward compatibility.
 * It imports and re-exports the functionality from the modular utils package.
 */

// Import from modular structure
import { handleFlashMessages } from './utils/dom.js';
import { showToast, closeToast, initToastContainer } from './utils/toast.js';
import { openDrawer } from './utils/drawer.js';
import { showAlertDialog } from './utils/dialog.js';

/**
 * Initialize the utilities
 */
function initUtils() {
  // Initialize flash messages
  handleFlashMessages();
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', initUtils);

// Make these functions globally available for backward compatibility
window.handleFlashMessages = handleFlashMessages;
window.showToast = showToast;
window.closeToast = closeToast;
window.openDrawer = openDrawer;
window.showAlertDialog = showAlertDialog;

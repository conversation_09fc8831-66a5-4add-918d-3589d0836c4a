from flask import Blueprint, render_template, request, flash, redirect, url_for, jsonify, current_app
from flask_login import login_required, current_user
from datetime import date, datetime
from app import db
from app.models.attendance import AttendanceType, AttendanceRecord
from app.models.employee import EmployeeDetail
from app.forms.admin_attendance import AttendanceTypeForm
from app.forms.attendance_record import AttendanceRecordForm, AttendanceRecordFilterForm
from app.services.attendance_service import AttendanceService
from app.utils.decorators import admin_required
from app.utils.ajax_helpers import ajax_response
from app.utils.pagination import paginate_query

admin_attendance_bp = Blueprint('admin_attendance', __name__, url_prefix='/admin/attendance')

@admin_attendance_bp.route('/types', methods=['GET'])
@login_required
@admin_required
def list_attendance_types():
    """Displays a list of all attendance types."""
    query = AttendanceType.query.order_by(AttendanceType.name.asc())
    per_page = current_app.config.get('PAGINATION_PER_PAGE_ADMIN', 20)
    attendance_types, pagination = paginate_query(query, per_page=per_page)

    return render_template('admin/attendance/types_index.html',
                           attendance_types=attendance_types,
                           title="Manage Attendance Types",
                           pagination=pagination,
                           active_page="attendance_types")

@admin_attendance_bp.route('/types/form/new', methods=['GET'])
@login_required
@admin_required
def get_attendance_type_form_create():
   """Serves the HTML for the create attendance type form (for drawer)."""
   form = AttendanceTypeForm()
   return render_template('admin/attendance/partials/type_form.html',
                          form=form,
                          action_url=url_for('admin_attendance.create_attendance_type'),
                          form_title="Add New Attendance Type")

@admin_attendance_bp.route('/types/form/edit/<int:type_id>', methods=['GET'])
@login_required
@admin_required
def get_attendance_type_form_edit(type_id):
   """Serves the HTML for the edit attendance type form (for drawer)."""
   att_type = AttendanceType.query.get_or_404(type_id)
   form = AttendanceTypeForm(obj=att_type, original_code=att_type.code)
   return render_template('admin/attendance/partials/type_form.html',
                          form=form,
                          action_url=url_for('admin_attendance.update_attendance_type', type_id=type_id),
                          form_title="Edit Attendance Type")

@admin_attendance_bp.route('/types/create', methods=['POST'])
@login_required
@admin_required
@ajax_response(success_redirect='admin_attendance.list_attendance_types')
def create_attendance_type():
   """Handles creation of a new attendance type."""
   form = AttendanceTypeForm()

   if form.validate_on_submit():
       new_type = AttendanceType()
       new_type.code = form.code.data
       new_type.name = form.name.data
       new_type.description = form.description.data
       new_type.requires_approval = form.requires_approval.data
       new_type.is_full_day = form.is_full_day.data
       new_type.color_code = form.color_code.data if form.color_code.data else None

       db.session.add(new_type)
       try:
           db.session.commit()
           return {
               'success': True,
               'message': 'Attendance Type created successfully!',
               'redirect_url': url_for('admin_attendance.list_attendance_types')
           }
       except Exception as e:
           db.session.rollback()
           return {
               'success': False,
               'message': f'Error creating attendance type: {str(e)}',
               'errors': form.errors
           }

   # Form validation failed
   error_messages = {field: errors for field, errors in form.errors.items()}
   return {
       'success': False,
       'message': 'Please correct the errors below.',
       'errors': error_messages
   }

@admin_attendance_bp.route('/types/edit/<int:type_id>', methods=['POST'])
@login_required
@admin_required
@ajax_response(success_redirect='admin_attendance.list_attendance_types')
def update_attendance_type(type_id):
   """Handles updating an existing attendance type."""
   att_type = AttendanceType.query.get_or_404(type_id)
   form = AttendanceTypeForm(obj=att_type, original_code=att_type.code)

   # Temporarily set the form's code data to the current type's code if it wasn't submitted
   # This is to ensure validate_on_submit() doesn't fail if code is not part of the POST data
   # (e.g. if it's a disabled field in the form but still needed for validation context)
   if 'code' not in request.form:
       form.code.data = att_type.code

   if form.validate_on_submit():
       att_type.code = form.code.data
       att_type.name = form.name.data
       att_type.description = form.description.data
       att_type.requires_approval = form.requires_approval.data
       att_type.is_full_day = form.is_full_day.data
       att_type.color_code = form.color_code.data if form.color_code.data else None

       try:
           db.session.commit()
           return {
               'success': True,
               'message': 'Attendance Type updated successfully!'
           }
       except Exception as e:
           db.session.rollback()
           return {
               'success': False,
               'message': f'Error updating attendance type: {str(e)}',
               'errors': form.errors
           }

   # Form validation failed
   error_messages = {field: errors for field, errors in form.errors.items()}
   return {
       'success': False,
       'message': 'Please correct the errors below.',
       'errors': error_messages
   }

@admin_attendance_bp.route('/types/delete/<int:type_id>', methods=['POST'])
@login_required
@admin_required
@ajax_response(success_redirect='admin_attendance.list_attendance_types')
def delete_attendance_type(type_id):
   """Handles deleting an attendance type."""
   att_type = AttendanceType.query.get_or_404(type_id)

   try:
       db.session.delete(att_type)
       db.session.commit()
       return {
           'success': True,
           'message': f'Attendance Type "{att_type.name}" deleted successfully!'
       }
   except Exception as e:
       db.session.rollback()
       return {
           'success': False,
           'message': f'Error deleting attendance type "{att_type.name}": {str(e)}. It might be in use.'
       }


# ============================================================================
# ATTENDANCE RECORDS ROUTES
# ============================================================================

@admin_attendance_bp.route('/records', methods=['GET'])
@login_required
@admin_required
def list_attendance_records():
    """Displays a list of all attendance records with filtering."""
    filter_form = AttendanceRecordFilterForm()

    # Build query with filters
    query = AttendanceRecord.query.join(EmployeeDetail).join(AttendanceType)

    # Apply filters if form is submitted
    if request.args:
        if request.args.get('employee'):
            query = query.filter(AttendanceRecord.employee_detail_id == request.args.get('employee'))

        if request.args.get('attendance_type'):
            query = query.filter(AttendanceRecord.attendance_type_id == request.args.get('attendance_type'))

        if request.args.get('status'):
            query = query.filter(AttendanceRecord.status == request.args.get('status'))

        if request.args.get('start_date'):
            try:
                start_date = datetime.strptime(request.args.get('start_date'), '%Y-%m-%d').date()
                query = query.filter(AttendanceRecord.date >= start_date)
            except ValueError:
                pass

        if request.args.get('end_date'):
            try:
                end_date = datetime.strptime(request.args.get('end_date'), '%Y-%m-%d').date()
                query = query.filter(AttendanceRecord.date <= end_date)
            except ValueError:
                pass

    # Order by date descending
    query = query.order_by(AttendanceRecord.date.desc(), AttendanceRecord.created_at.desc())

    # Get total count before pagination
    total_count = query.count()

    # Paginate
    per_page = current_app.config.get('PAGINATION_PER_PAGE_ADMIN', 20)
    attendance_records, pagination = paginate_query(query, per_page=per_page)

    return render_template('admin/attendance/records_index.html',
                           attendance_records=attendance_records,
                           filter_form=filter_form,
                           title="Manage Attendance Records",
                           pagination=pagination,
                           total_count=total_count,
                           active_page="attendance_records")


@admin_attendance_bp.route('/records/form/new', methods=['GET'])
@login_required
@admin_required
def get_attendance_record_form_create():
    """Serves the HTML for the create attendance record form (for drawer)."""
    form = AttendanceRecordForm()
    return render_template('admin/attendance/partials/attendance_record_form.html',
                           form=form,
                           action_url=url_for('admin_attendance.create_attendance_record'),
                           form_title="Add New Attendance Record")


@admin_attendance_bp.route('/records/form/edit/<int:record_id>', methods=['GET'])
@login_required
@admin_required
def get_attendance_record_form_edit(record_id):
    """Serves the HTML for the edit attendance record form (for drawer)."""
    record = AttendanceRecord.query.get_or_404(record_id)
    form = AttendanceRecordForm(
        obj=record,
        original_date=record.date,
        original_employee_id=record.employee_detail_id,
        original_record_id=record.id
    )

    # Get holiday warning for current record
    holiday_warning = None
    if record.date and record.employee_detail_id:
        holiday_info = AttendanceService.check_holiday_date(record.employee_detail_id, record.date)
        if holiday_info.get('is_holiday'):
            holiday_warning = holiday_info

    return render_template('admin/attendance/partials/attendance_record_form.html',
                           form=form,
                           holiday_warning=holiday_warning,
                           action_url=url_for('admin_attendance.update_attendance_record', record_id=record_id),
                           form_title="Edit Attendance Record")


@admin_attendance_bp.route('/records/create', methods=['POST'])
@login_required
@admin_required
@ajax_response(success_redirect='admin_attendance.list_attendance_records')
def create_attendance_record():
    """Handles creation of a new attendance record."""
    form = AttendanceRecordForm()

    if form.validate_on_submit():
        # Use the service to create the record
        result = AttendanceService.create_attendance_record(
            employee_detail_id=form.employee_detail_id.data,
            attendance_type_id=form.attendance_type_id.data,
            record_date=form.date.data,
            start_time=form.start_time.data,
            end_time=form.end_time.data,
            duration_hours=form.duration_hours.data,
            notes=form.notes.data,
            status=form.status.data
        )

        if result['success']:
            message = result['message']

            # Add holiday warning to message if applicable
            if result.get('holiday_info', {}).get('is_holiday'):
                holiday_name = result['holiday_info']['holiday_name']
                message += f" Note: This record is on a holiday ({holiday_name})."

            return {
                'success': True,
                'message': message,
                'redirect_url': url_for('admin_attendance.list_attendance_records')
            }
        else:
            return {
                'success': False,
                'message': result['error'],
                'errors': form.errors
            }

    # Form validation failed
    error_messages = {field: errors for field, errors in form.errors.items()}
    return {
        'success': False,
        'message': 'Please correct the errors below.',
        'errors': error_messages
    }


@admin_attendance_bp.route('/records/edit/<int:record_id>', methods=['POST'])
@login_required
@admin_required
@ajax_response(success_redirect='admin_attendance.list_attendance_records')
def update_attendance_record(record_id):
    """Handles updating an existing attendance record."""
    record = AttendanceRecord.query.get_or_404(record_id)
    form = AttendanceRecordForm(
        obj=record,
        original_date=record.date,
        original_employee_id=record.employee_detail_id,
        original_record_id=record.id
    )

    if form.validate_on_submit():
        # Use the service to update the record
        result = AttendanceService.update_attendance_record(
            record_id=record_id,
            employee_detail_id=form.employee_detail_id.data,
            attendance_type_id=form.attendance_type_id.data,
            date=form.date.data,
            start_time=form.start_time.data,
            end_time=form.end_time.data,
            duration_hours=form.duration_hours.data,
            notes=form.notes.data,
            status=form.status.data,
            rejection_reason=form.rejection_reason.data
        )

        if result['success']:
            return {
                'success': True,
                'message': result['message']
            }
        else:
            return {
                'success': False,
                'message': result['error'],
                'errors': form.errors
            }

    # Form validation failed
    error_messages = {field: errors for field, errors in form.errors.items()}
    return {
        'success': False,
        'message': 'Please correct the errors below.',
        'errors': error_messages
    }


@admin_attendance_bp.route('/records/delete/<int:record_id>', methods=['POST'])
@login_required
@admin_required
@ajax_response(success_redirect='admin_attendance.list_attendance_records')
def delete_attendance_record(record_id):
    """Handles deleting an attendance record."""
    record = AttendanceRecord.query.get_or_404(record_id)
    employee_name = f"{record.employee_detail.first_name} {record.employee_detail.last_name}"
    record_date = record.date.strftime('%Y-%m-%d')

    result = AttendanceService.delete_attendance_record(record_id)

    if result['success']:
        return {
            'success': True,
            'message': f'Attendance record for {employee_name} on {record_date} deleted successfully!'
        }
    else:
        return {
            'success': False,
            'message': result['error']
        }


@admin_attendance_bp.route('/records/approve/<int:record_id>', methods=['POST'])
@login_required
@admin_required
@ajax_response(success_redirect='admin_attendance.list_attendance_records')
def approve_attendance_record(record_id):
    """Handles approving an attendance record."""
    notes = request.form.get('notes', '')

    result = AttendanceService.approve_attendance_record(
        record_id=record_id,
        approved_by_id=current_user.id,
        notes=notes
    )

    if result['success']:
        return {
            'success': True,
            'message': result['message']
        }
    else:
        return {
            'success': False,
            'message': result['error']
        }


@admin_attendance_bp.route('/records/reject/<int:record_id>', methods=['POST'])
@login_required
@admin_required
@ajax_response(success_redirect='admin_attendance.list_attendance_records')
def reject_attendance_record(record_id):
    """Handles rejecting an attendance record."""
    rejection_reason = request.form.get('rejection_reason', '')

    if not rejection_reason:
        return {
            'success': False,
            'message': 'Rejection reason is required.'
        }

    result = AttendanceService.reject_attendance_record(
        record_id=record_id,
        rejected_by_id=current_user.id,
        rejection_reason=rejection_reason
    )

    if result['success']:
        return {
            'success': True,
            'message': result['message']
        }
    else:
        return {
            'success': False,
            'message': result['error']
        }

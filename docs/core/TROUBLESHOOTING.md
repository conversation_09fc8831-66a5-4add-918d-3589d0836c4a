# Troubleshooting Guide

**Last Updated:** January 2025  
**Version:** 1.0.0

## Overview

This guide provides solutions to common issues encountered when developing, deploying, or maintaining the Matrix App. Issues are organized by category with step-by-step resolution instructions.

## Installation & Setup Issues

### Python Environment Issues

**Problem:** `ModuleNotFoundError` when running the application
```
ModuleNotFoundError: No module named 'flask'
```

**Solution:**
```bash
# Ensure virtual environment is activated
source venv/bin/activate  # Linux/Mac
# or
venv\Scripts\activate     # Windows

# Verify Python version
python --version  # Should be 3.11+

# Reinstall dependencies
pip install -r requirements.txt
```

**Problem:** Virtual environment creation fails
```
Error: Unable to create virtual environment
```

**Solution:**
```bash
# Install python3-venv if missing (Ubuntu/Debian)
sudo apt install python3.11-venv

# Create virtual environment with specific Python version
python3.11 -m venv venv

# Alternative: Use virtualenv
pip install virtualenv
virtualenv -p python3.11 venv
```

### Node.js and Frontend Issues

**Problem:** `npm install` fails with permission errors

**Solution:**
```bash
# Fix npm permissions (Linux/Mac)
sudo chown -R $(whoami) ~/.npm
sudo chown -R $(whoami) /usr/local/lib/node_modules

# Or use nvm to manage Node.js versions
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash
nvm install 18
nvm use 18
```

**Problem:** Tailwind CSS not building properly

**Solution:**
```bash
# Clean and rebuild
rm -rf node_modules package-lock.json
npm install

# Build CSS manually
npm run build:css

# Check Tailwind config
npx tailwindcss -i ./app/static/src/css/input.css -o ./app/static/css/main.css --watch
```

## Database Issues

### Connection Problems

**Problem:** Database connection refused
```
sqlalchemy.exc.OperationalError: (psycopg2.OperationalError) connection to server at "localhost", port 5432 failed
```

**Solution:**
```bash
# Check PostgreSQL status
sudo systemctl status postgresql

# Start PostgreSQL if stopped
sudo systemctl start postgresql

# Check if database exists
sudo -u postgres psql -l | grep matrix_app

# Create database if missing
sudo -u postgres createdb matrix_app
```

**Problem:** SQLite database locked
```
sqlite3.OperationalError: database is locked
```

**Solution:**
```bash
# Check for running processes
ps aux | grep python

# Kill any hanging processes
pkill -f "python run.py"

# Remove lock file if exists
rm instance/dev.db-wal instance/dev.db-shm

# Restart application
python run.py
```

### Migration Issues

**Problem:** Migration fails with table already exists
```
sqlalchemy.exc.ProgrammingError: (psycopg2.errors.DuplicateTable) relation "users" already exists
```

**Solution:**
```bash
# Check migration status
flask db current

# Mark current state as migrated
flask db stamp head

# Create new migration for changes
flask db migrate -m "Fix migration state"

# Apply migration
flask db upgrade
```

**Problem:** Downgrade migration fails

**Solution:**
```bash
# Check migration history
flask db history

# Downgrade to specific revision
flask db downgrade <revision_id>

# If stuck, reset to base and re-migrate
flask db downgrade base
flask db upgrade
```

## Authentication & Security Issues

### Login Problems

**Problem:** Users cannot log in with correct credentials
```
Invalid email or password
```

**Diagnosis:**
```python
# Check user in database
from app.models import User
user = User.query.filter_by(email='<EMAIL>').first()
print(f"User exists: {user is not None}")
print(f"User active: {user.is_active if user else 'N/A'}")
print(f"Password check: {user.check_password('password') if user else 'N/A'}")
```

**Solution:**
```bash
# Reset user password
python -c "
from app import create_app, db
from app.models import User
app = create_app()
with app.app_context():
    user = User.query.filter_by(email='<EMAIL>').first()
    if user:
        user.set_password('newpassword')
        user._is_active = True
        db.session.commit()
        print('Password reset successfully')
    else:
        print('User not found')
"
```

### Session Issues

**Problem:** Users logged out unexpectedly

**Solution:**
```python
# Check session configuration in config.py
PERMANENT_SESSION_LIFETIME = timedelta(days=7)  # Increase if too short
SESSION_COOKIE_SECURE = False  # Set to False for HTTP in development
```

**Problem:** CSRF token errors
```
The CSRF token is missing or invalid
```

**Solution:**
```html
<!-- Ensure CSRF token in forms -->
<form method="POST">
    {{ csrf_token() }}
    <!-- or -->
    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
</form>
```

## Performance Issues

### Slow Page Loading

**Problem:** Pages load slowly (>5 seconds)

**Diagnosis:**
```bash
# Check database query performance
tail -f logs/matrix-app.log | grep "slow query"

# Monitor system resources
htop
iotop
```

**Solution:**
```python
# Enable query logging in development
import logging
logging.getLogger('sqlalchemy.engine').setLevel(logging.INFO)

# Add database indexes for slow queries
# Check app/models/*.py for proper indexing

# Enable caching
from app import cache
@cache.cached(timeout=300)
def expensive_function():
    # Cached function
    pass
```

### High Memory Usage

**Problem:** Application consuming excessive memory

**Solution:**
```python
# Check for memory leaks in models
# Ensure proper session cleanup
from app import db

# After operations
db.session.close()

# Use pagination for large datasets
users = User.query.paginate(page=1, per_page=20)

# Optimize eager loading
users = User.query.options(db.joinedload('employee_detail')).all()
```

## Frontend Issues

### JavaScript Errors

**Problem:** Icons not displaying
```
Uncaught ReferenceError: lucide is not defined
```

**Solution:**
```html
<!-- Ensure Lucide is loaded before other scripts -->
<script src="{{ url_for('static', filename='js/vendor/lucide/lucide.min.js') }}"></script>
<script>
// Initialize icons after DOM load
document.addEventListener('DOMContentLoaded', function() {
    if (typeof lucide !== 'undefined') {
        lucide.createIcons();
    }
});
</script>
```

**Problem:** Tailwind styles not applying

**Solution:**
```bash
# Check if CSS is built
ls -la app/static/css/main.css

# Rebuild CSS
npm run build:css

# Check Tailwind config includes all template files
# In tailwind.config.js:
content: ["./app/templates/**/*.html", "./app/static/**/*.js"]
```

### Modal/Component Issues

**Problem:** Modals not opening or closing properly

**Solution:**
```javascript
// Check for JavaScript errors in console
// Ensure modal HTML structure is correct
// Verify event listeners are attached

// Debug modal functionality
console.log('Modal element:', document.getElementById('modal-id'));
console.log('Modal trigger:', document.querySelector('[data-modal="modal-id"]'));
```

## API Issues

### API Endpoints Not Working

**Problem:** API returns 404 errors
```
404 Not Found: The requested URL was not found on the server
```

**Solution:**
```python
# Check if API blueprint is registered
# In app/__init__.py
from app.routes.api import api_bp
app.register_blueprint(api_bp)

# Verify route registration
flask routes | grep api
```

**Problem:** API returns 500 errors

**Diagnosis:**
```bash
# Check application logs
tail -f logs/matrix-app.log

# Enable debug mode temporarily
export FLASK_DEBUG=1
python run.py
```

**Solution:**
```python
# Add error handling to API routes
from app.routes.api.utils import handle_api_error

@api_bp.route('/endpoint')
@handle_api_error
def api_endpoint():
    try:
        # API logic
        return jsonify({'success': True})
    except Exception as e:
        current_app.logger.error(f"API error: {str(e)}")
        raise
```

## Deployment Issues

### Production Deployment Problems

**Problem:** Gunicorn workers dying
```
[CRITICAL] WORKER TIMEOUT
```

**Solution:**
```python
# Increase timeout in gunicorn.conf.py
timeout = 60  # Increase from 30
workers = 2   # Reduce if memory constrained

# Check for blocking operations
# Use async operations for long-running tasks
```

**Problem:** Nginx 502 Bad Gateway

**Solution:**
```bash
# Check if application is running
sudo systemctl status matrix-app

# Check Gunicorn socket
sudo netstat -tlnp | grep :8000

# Check Nginx error logs
sudo tail -f /var/log/nginx/error.log

# Test direct connection to app
curl http://127.0.0.1:8000/
```

### SSL Certificate Issues

**Problem:** SSL certificate errors

**Solution:**
```bash
# Check certificate validity
openssl x509 -in /etc/letsencrypt/live/domain.com/fullchain.pem -text -noout

# Renew Let's Encrypt certificate
sudo certbot renew --dry-run
sudo certbot renew

# Check Nginx SSL configuration
sudo nginx -t
```

## Email Issues

### Email Not Sending

**Problem:** Password reset emails not delivered

**Diagnosis:**
```python
# Test email configuration
from app import create_app, mail
from flask_mail import Message

app = create_app()
with app.app_context():
    msg = Message(
        'Test Email',
        sender=app.config['MAIL_DEFAULT_SENDER'],
        recipients=['<EMAIL>']
    )
    msg.body = 'Test email body'
    try:
        mail.send(msg)
        print('Email sent successfully')
    except Exception as e:
        print(f'Email error: {e}')
```

**Solution:**
```bash
# Check email configuration in .env
MAIL_SERVER=smtp.gmail.com
MAIL_PORT=587
MAIL_USE_TLS=True
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password  # Use app password for Gmail

# For Gmail, enable 2FA and create app password
# https://support.google.com/accounts/answer/185833
```

## Debugging Tools

### Enable Debug Mode

```python
# In config.py for development
class DevelopmentConfig(Config):
    DEBUG = True
    SQLALCHEMY_ECHO = True  # Log SQL queries
```

### Logging Configuration

```python
# Add to run.py for detailed logging
import logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s %(levelname)s %(name)s %(message)s'
)

# Database query logging
logging.getLogger('sqlalchemy.engine').setLevel(logging.INFO)
```

### Health Check Commands

```bash
# Check application health
curl http://localhost:5003/api/system/status

# Check database connectivity
python -c "
from app import create_app, db
app = create_app()
with app.app_context():
    try:
        db.engine.execute('SELECT 1')
        print('Database: OK')
    except Exception as e:
        print(f'Database: ERROR - {e}')
"

# Check Redis connectivity (if configured)
redis-cli ping
```

## Getting Help

### Log Analysis

```bash
# Application logs
tail -f logs/matrix-app.log

# System logs
sudo journalctl -u matrix-app -f

# Nginx logs
sudo tail -f /var/log/nginx/access.log
sudo tail -f /var/log/nginx/error.log

# PostgreSQL logs
sudo tail -f /var/log/postgresql/postgresql-*.log
```

### Performance Monitoring

```bash
# System resources
htop
iotop
df -h

# Application metrics
ps aux | grep python
netstat -tlnp | grep :5003
```

### Support Resources

1. **Check Documentation**: Review relevant documentation files
2. **Search Logs**: Look for error messages in application logs
3. **Test Components**: Isolate and test individual components
4. **Community Support**: Search for similar issues online
5. **Create Issue**: Document the problem with steps to reproduce

---

This troubleshooting guide covers the most common issues encountered with Matrix App. For issues not covered here, please check the application logs and create a detailed issue report.

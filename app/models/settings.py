"""
Settings model for application configuration.
"""

from datetime import datetime
import json
from typing import Dict, Any, Optional, Union

from app import db
from app.models import PH_TZ


class Setting(db.Model):
    """System settings model for application configuration."""
    __tablename__ = 'settings'

    # Setting types
    TYPE_STRING = 'string'
    TYPE_INTEGER = 'integer'
    TYPE_FLOAT = 'float'
    TYPE_BOOLEAN = 'boolean'
    TYPE_JSON = 'json'

    TYPE_CHOICES = [
        (TYPE_STRING, 'String'),
        (TYPE_INTEGER, 'Integer'),
        (TYPE_FLOAT, 'Float'),
        (TYPE_BOOLEAN, 'Boolean'),
        (TYPE_JSON, 'JSON')
    ]

    id = db.Column(db.Integer, primary_key=True)
    key = db.Column(db.String(100), unique=True, nullable=False, index=True)
    value = db.Column(db.Text, nullable=True)
    type = db.Column(db.String(20), nullable=False, default=TYPE_STRING)
    description = db.Column(db.String(255), nullable=True)
    is_public = db.Column(db.<PERSON>an, default=False)  # Whether this setting is accessible to non-admin users
    created_at = db.Column(db.DateTime, default=lambda: datetime.now(PH_TZ))
    updated_at = db.Column(db.DateTime, default=lambda: datetime.now(PH_TZ), onupdate=lambda: datetime.now(PH_TZ))

    def __repr__(self) -> str:
        return f'<Setting {self.key}={self.value}>'

    @property
    def typed_value(self) -> Union[str, int, float, bool, dict, None]:
        """Return the value converted to its appropriate type."""
        if self.value is None:
            return None

        if self.type == self.TYPE_STRING:
            return self.value
        elif self.type == self.TYPE_INTEGER:
            return int(self.value)
        elif self.type == self.TYPE_FLOAT:
            return float(self.value)
        elif self.type == self.TYPE_BOOLEAN:
            # Check if the value is one of the true values
            # For checkboxes, we store 'on' when checked and 'false' when unchecked
            return self.value.lower() in ('true', 'yes', '1', 'on')
        elif self.type == self.TYPE_JSON:
            try:
                return json.loads(self.value)
            except json.JSONDecodeError:
                return None
        return self.value

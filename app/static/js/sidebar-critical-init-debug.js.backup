/**
 * Critical initialization for sidebar submenus
 * Simple version with debugging
 */

console.log('Running sidebar critical init...');

(function() {
  try {
    console.log('Checking for open submenus...');
    const openSubmenus = JSON.parse(localStorage.getItem('openSubmenus') || '[]');
    const isSidebarCollapsed = localStorage.getItem('sidebarCollapsed') === 'true';
    
    console.log('Initial state:', { openSubmenus, isSidebarCollapsed });
    
    // Only proceed if sidebar is not collapsed and there are open submenus
    if (!isSidebarCollapsed && openSubmenus.length > 0) {
      console.log('Creating style for open submenus');
      
      // Create a style element
      const style = document.createElement('style');
      
      // Create CSS rules for each open submenu
      let css = '';
      openSubmenus.forEach(submenuId => {
        console.log('Adding style for submenu:', submenuId);
        css += `
          /* Force submenu to be visible */
          .submenu-toggle[data-submenu-id="${submenuId}"] + .submenu-content {
            display: block !important;
            max-height: 500px !important;
            opacity: 1 !important;
            visibility: visible !important;
          }
          
          /* Rotate chevron */
          .submenu-toggle[data-submenu-id="${submenuId}"] [data-lucide="chevron-down"] {
            transform: rotate(180deg) !important;
          }
        `;
      });
      
      // Add the CSS to the style element
      style.textContent = css;
      
      // Add the style element to the head
      document.head.appendChild(style);
      console.log('Added style to head');
    }
  } catch (error) {
    console.error('Error in sidebar critical init:', error);
  }
})();

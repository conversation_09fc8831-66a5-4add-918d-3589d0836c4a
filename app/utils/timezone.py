"""
Timezone utilities for the application.
"""
import pytz
from datetime import datetime

# Default timezone for the application
PH_TZ = pytz.timezone('Asia/Manila')

def get_current_time(timezone=None):
    """
    Get the current time in the specified timezone.
    
    Args:
        timezone: Timezone to use (default: PH_TZ)
    
    Returns:
        datetime: Current time in the specified timezone
    """
    if timezone is None:
        timezone = PH_TZ
    elif isinstance(timezone, str):
        timezone = pytz.timezone(timezone)
    
    return datetime.now(timezone)

def convert_timezone(dt, to_timezone):
    """
    Convert a datetime object to a different timezone.
    
    Args:
        dt: Datetime object to convert
        to_timezone: Timezone to convert to (string or timezone object)
    
    Returns:
        datetime: Converted datetime object
    """
    if isinstance(to_timezone, str):
        to_timezone = pytz.timezone(to_timezone)
    
    # Make sure the datetime is timezone-aware
    if dt.tzinfo is None:
        dt = dt.replace(tzinfo=PH_TZ)
    
    return dt.astimezone(to_timezone)

def format_datetime(dt, format_str="%Y-%m-%d %H:%M:%S", timezone=None):
    """
    Format a datetime object as a string in the specified timezone.
    
    Args:
        dt: Datetime object to format
        format_str: Format string to use
        timezone: Timezone to use for formatting
    
    Returns:
        str: Formatted datetime string
    """
    if timezone is not None:
        dt = convert_timezone(dt, timezone)
    
    return dt.strftime(format_str)

/**
 * Sidebar specific styles that are difficult to implement with Tailwind alone
 */

/* Submenu container */
.submenu-content {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease, opacity 0.3s ease;
  position: relative;
  padding-left: 0;
  margin-left: 0;
  opacity: 0;
  display: none;
}

/* When submenu is open or expanded */
.submenu-content.submenu-open,
.submenu-content.submenu-expanded {
  max-height: 500px; /* Adjust based on your needs */
  opacity: 1;
  display: block;
}

/* Vertical line for submenu - positioned to align with icons */
.submenu-content::before {
  content: '';
  position: absolute;
  left: 20px;
  top: 0;
  bottom: 0;
  width: 1px;
  background-color: #334155; /* slate-700 */
  z-index: 1;
}

/* Dark mode adjustment for the line */
html.dark .submenu-content::before {
  background-color: #475569; /* slate-600 */
}

/* Submenu items */
.submenu-item {
  padding-left: 0.5rem;
  border-radius: 0.375rem;
  position: relative;
  display: block;
  margin-left: 0.75rem;
  margin-right: 0.5rem;
}

/* Submenu content styling to ensure proper background positioning */
.submenu-content li {
  position: relative;
  padding-left: 0.75rem;
}

/* Active navigation items */
.submenu-item.bg-primary\/10,
#sidebar li > a.bg-primary\/10 {
  background-color: #1e293b !important; /* slate-800 */
  color: white !important;
}

/* Dark mode active navigation items */
html.dark .submenu-item.bg-primary\/10,
html.dark #sidebar li > a.bg-primary\/10 {
  background-color: #1e293b !important; /* slate-800 */
  color: white !important;
}

/* Highlight parent submenu when child is active */
.has-submenu:has(.submenu-item.bg-primary\/10) .submenu-toggle {
  background-color: transparent;
  color: white;
}

/* Collapsed sidebar submenu styles */
.sidebar-collapsed .submenu-toggle [data-lucide="chevron-down"] {
  display: none;
}

.sidebar-collapsed .submenu-content {
  position: absolute;
  left: 100%;
  top: 0;
  width: 200px;
  background-color: #1e293b; /* slate-800 */
  border-radius: 0 0.375rem 0.375rem 0;
  box-shadow: 4px 0 8px rgba(0, 0, 0, 0.1);
  max-height: 0;
  overflow: hidden;
  z-index: 30;
  padding-left: 0;
  margin-left: 0;
}

/* Remove vertical line in collapsed state */
.sidebar-collapsed .submenu-content::before {
  display: none;
}

/* Show submenu on hover in collapsed state */
.sidebar-collapsed .has-submenu:hover .submenu-content,
.sidebar-collapsed .submenu-content:hover {
  max-height: 500px;
  opacity: 1;
  display: block;
}

/* Hover indicator for collapsed sidebar */
.sidebar-collapsed .has-submenu {
  position: relative;
}

.sidebar-collapsed .has-submenu::after {
  content: '';
  position: absolute;
  right: 0.5rem;
  top: 50%;
  transform: translateY(-50%);
  width: 0.25rem;
  height: 0.25rem;
  border-radius: 50%;
  background-color: #64748b; /* slate-500 */
}

/* Ensure submenu items with badges show indicators in collapsed mode */
.sidebar-collapsed .submenu-content a:has(.sidebar-badge[data-count]:not([data-count="0"])):after {
  content: '';
  position: absolute;
  top: 50%;
  right: 0.5rem;
  transform: translateY(-50%);
  width: 0.5rem;
  height: 0.5rem;
  border-radius: 50%;
  background-color: var(--primary-hex, #0284c7);
  border: 1px solid #1e293b;
}

/* Hide text in collapsed sidebar */
.sidebar-collapsed .sidebar-text,
.sidebar-collapsed .submenu-content .sidebar-text {
  display: none;
}

/* Center icons in collapsed sidebar */
.sidebar-collapsed .sidebar-icon {
  margin-right: 0;
}

/* Ensure section headers are hidden in collapsed mode */
.sidebar-collapsed .pt-4 .sidebar-text {
  display: none;
}

/* Sidebar toggle button animation */
#collapse-sidebar i {
  transition: transform 0.3s ease;
}

/* Rotate icon slightly on hover */
#collapse-sidebar:hover i {
  transform: scale(1.1);
}

/* Add pulse effect when clicked */
@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.2); }
  100% { transform: scale(1); }
}

.pulse-animation {
  animation: pulse 0.3s ease-in-out;
}

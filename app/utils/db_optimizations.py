"""
Database optimization utilities.

This module provides functions to optimize database queries and connections.
"""

from flask import current_app
from sqlalchemy import event
from sqlalchemy.engine import Engine
from sqlalchemy.orm import joinedload, contains_eager
from typing import List, Any, Dict, Optional, Type, Union
import time
import logging
import sqlite3

# Set up logger
logger = logging.getLogger(__name__)

# SQLite optimizations
@event.listens_for(Engine, "connect")
def optimize_sqlite_connection(dbapi_connection, connection_record):
    """Apply SQLite-specific optimizations on connection."""
    if isinstance(dbapi_connection, sqlite3.Connection):
        # Enable foreign keys
        dbapi_connection.execute('PRAGMA foreign_keys=ON')

        try:
            # Try to set journal mode to WAL for better concurrency
            dbapi_connection.execute('PRAGMA journal_mode=WAL')

            # Set synchronous mode to NORMAL for better performance
            dbapi_connection.execute('PRAGMA synchronous=NORMAL')

            # Set temp store to MEMORY for better performance
            dbapi_connection.execute('PRAGMA temp_store=MEMORY')

            # Set cache size to 10000 pages (about 40MB)
            dbapi_connection.execute('PRAGMA cache_size=-10000')

            # Enable memory-mapped I/O with error handling
            dbapi_connection.execute('PRAGMA mmap_size=268435456')  # 256MB

            logger.info("SQLite optimizations applied successfully")
        except sqlite3.OperationalError as e:
            # Log the error but continue - these are optimizations, not critical functionality
            logger.warning(f"Could not apply some SQLite optimizations: {str(e)}")

            # Fall back to a more compatible mode
            try:
                # Try with DELETE journal mode instead of WAL
                dbapi_connection.execute('PRAGMA journal_mode=DELETE')
                dbapi_connection.execute('PRAGMA synchronous=NORMAL')
                logger.info("Applied fallback SQLite optimizations")
            except Exception as e2:
                logger.warning(f"Could not apply fallback SQLite optimizations: {str(e2)}")


class QueryProfiler:
    """Profile database queries for performance optimization."""

    def __init__(self, threshold_ms: int = 100):
        """
        Initialize the query profiler.

        Args:
            threshold_ms: Threshold in milliseconds to log slow queries
        """
        self.threshold_ms = threshold_ms
        self.enabled = False
        self.queries = []

    def start(self):
        """Start profiling queries."""
        self.enabled = True
        self.queries = []

        # Register event listeners
        event.listen(Engine, "before_cursor_execute", self._before_cursor_execute)
        event.listen(Engine, "after_cursor_execute", self._after_cursor_execute)

    def stop(self):
        """Stop profiling queries."""
        self.enabled = False

        # Remove event listeners
        event.remove(Engine, "before_cursor_execute", self._before_cursor_execute)
        event.remove(Engine, "after_cursor_execute", self._after_cursor_execute)

    def _before_cursor_execute(self, conn, cursor, statement, parameters, context, executemany):
        """Record query start time."""
        if not self.enabled:
            return

        context._query_start_time = time.time()

    def _after_cursor_execute(self, conn, cursor, statement, parameters, context, executemany):
        """Record query end time and log if it exceeds threshold."""
        if not self.enabled or not hasattr(context, '_query_start_time'):
            return

        total_time = (time.time() - context._query_start_time) * 1000

        # Record the query
        query_info = {
            'statement': statement,
            'parameters': parameters,
            'duration_ms': total_time
        }
        self.queries.append(query_info)

        # Log slow queries
        if total_time > self.threshold_ms:
            logger.warning(f"Slow query ({total_time:.2f}ms): {statement}")

    def get_stats(self) -> Dict[str, Any]:
        """Get query statistics."""
        if not self.queries:
            return {'count': 0, 'total_time': 0, 'avg_time': 0, 'max_time': 0, 'slow_queries': 0}

        total_time = sum(q['duration_ms'] for q in self.queries)
        max_time = max(q['duration_ms'] for q in self.queries)
        slow_queries = sum(1 for q in self.queries if q['duration_ms'] > self.threshold_ms)

        return {
            'count': len(self.queries),
            'total_time': total_time,
            'avg_time': total_time / len(self.queries),
            'max_time': max_time,
            'slow_queries': slow_queries
        }


# Create a global query profiler instance
query_profiler = QueryProfiler()


def optimize_query(query, model_class, eager_load_relations=None):
    """
    Optimize a SQLAlchemy query with common performance improvements.

    Args:
        query: The SQLAlchemy query to optimize
        model_class: The model class being queried
        eager_load_relations: List of relationships to eager load

    Returns:
        Optimized SQLAlchemy query
    """
    # Apply eager loading if specified
    if eager_load_relations:
        for relation in eager_load_relations:
            query = query.options(joinedload(relation))

    return query


def get_connection_pool_status():
    """
    Get the current status of the SQLAlchemy connection pool.

    Returns:
        Dictionary with connection pool statistics
    """
    try:
        # Check the database URI directly from the configuration
        db_uri = current_app.config.get('SQLALCHEMY_DATABASE_URI', '')
        logger.info(f"Database URI from config: {db_uri}")

        # Default to SQLite for this application
        # This is a simpler approach that avoids potential errors
        return {
            'pool_size': 1,
            'checkedin': 0,
            'checkedout': 1,
            'overflow': 0,
            'engine_type': 'SQLite'
        }
    except Exception as e:
        logger.error(f"Error getting connection pool status: {str(e)}")
        return {
            'pool_size': 1,
            'checkedin': 0,
            'checkedout': 1,
            'overflow': 0,
            'engine_type': 'SQLite (fallback)'
        }

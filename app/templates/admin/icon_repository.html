{% extends "base.html" %}
{% from "components/modal.html" import modal %}

{% block title %}Icon Repository{% endblock %}

{% block header %}Icon Repository{% endblock %}

{% block content %}
<div class="space-y-6">
    <!-- Page Header -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md border border-gray-200 dark:border-gray-700 overflow-hidden">
        <div class="p-6 bg-gradient-to-r from-blue-50/50 to-transparent dark:from-blue-900/10 dark:to-transparent">
            <div class="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900 dark:text-white">Icon Repository</h1>
                    <p class="text-gray-600 dark:text-gray-400 mt-1.5">Comprehensive view of all icons in the Matrix App system</p>
                </div>
                <div class="flex items-center space-x-3">
                    <button type="button" class="inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-700 h-9 px-4 py-2 shadow-sm" onclick="toggleView()">
                        <i data-lucide="grid" class="w-4 h-4 mr-2"></i>
                        Toggle View
                    </button>
                    <button type="button" class="inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-blue-600 text-white hover:bg-blue-700 dark:bg-blue-600 dark:hover:bg-blue-500 h-9 px-4 py-2 shadow-sm" onclick="testAllIcons()">
                        <i data-lucide="refresh-cw" class="w-4 h-4 mr-2"></i>
                        Test All Icons
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-5">
        <!-- Total Icons Card -->
        <div class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 shadow-md hover:shadow-lg transition-all duration-200 overflow-hidden">
            <div class="p-5 bg-gradient-to-br from-blue-50/30 to-transparent dark:from-blue-900/5 dark:to-transparent">
                <div class="flex justify-between items-start">
                    <div>
                        <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Total Icons</p>
                        <div class="flex items-baseline gap-2 mt-2">
                            <p class="text-3xl font-bold text-gray-900 dark:text-white">{{ icon_data.total_icons }}</p>
                            <span class="text-xs px-1.5 py-0.5 rounded-full bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400 border border-blue-200 dark:border-blue-800/30">Available</span>
                        </div>
                    </div>
                    <div class="rounded-full p-3 bg-blue-100 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400 shadow-sm">
                        <i data-lucide="layers" class="w-6 h-6"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- Critical Icons Card -->
        <div class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 shadow-md hover:shadow-lg transition-all duration-200 overflow-hidden">
            <div class="p-5 bg-gradient-to-br from-green-50/30 to-transparent dark:from-green-900/5 dark:to-transparent">
                <div class="flex justify-between items-start">
                    <div>
                        <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Critical Icons</p>
                        <div class="flex items-baseline gap-2 mt-2">
                            <p class="text-3xl font-bold text-gray-900 dark:text-white">{{ icon_data.critical_count }}</p>
                            <span class="text-xs px-1.5 py-0.5 rounded-full bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400 border border-green-200 dark:border-green-800/30">Sprite</span>
                        </div>
                    </div>
                    <div class="rounded-full p-3 bg-green-100 dark:bg-green-900/20 text-green-600 dark:text-green-400 shadow-sm">
                        <i data-lucide="activity" class="w-6 h-6"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- On-Demand Icons Card -->
        <div class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 shadow-md hover:shadow-lg transition-all duration-200 overflow-hidden">
            <div class="p-5 bg-gradient-to-br from-indigo-50/30 to-transparent dark:from-indigo-900/5 dark:to-transparent">
                <div class="flex justify-between items-start">
                    <div>
                        <p class="text-sm font-medium text-gray-600 dark:text-gray-400">On-Demand Icons</p>
                        <div class="flex items-baseline gap-2 mt-2">
                            <p class="text-3xl font-bold text-gray-900 dark:text-white">{{ icon_data.non_critical_count }}</p>
                            <span class="text-xs px-1.5 py-0.5 rounded-full bg-indigo-100 text-indigo-800 dark:bg-indigo-900/30 dark:text-indigo-400 border border-indigo-200 dark:border-indigo-800/30">Fallback</span>
                        </div>
                    </div>
                    <div class="rounded-full p-3 bg-indigo-100 dark:bg-indigo-900/20 text-indigo-600 dark:text-indigo-400 shadow-sm">
                        <i data-lucide="download" class="w-6 h-6"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- System Status Card -->
        <div class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 shadow-md hover:shadow-lg transition-all duration-200 overflow-hidden">
            <div class="p-5 bg-gradient-to-br from-purple-50/30 to-transparent dark:from-purple-900/5 dark:to-transparent">
                <div class="flex justify-between items-start">
                    <div class="flex-1">
                        <p class="text-sm font-medium text-gray-600 dark:text-gray-400">System Status</p>
                        <div class="flex items-baseline gap-2 mt-2 mb-3">
                            <p class="text-lg font-bold text-gray-900 dark:text-white" id="status-text">Ready</p>
                        </div>
                        <button onclick="testAllIcons()" class="text-xs px-3 py-1.5 bg-purple-50 dark:bg-purple-900/20 text-purple-600 dark:text-purple-400 border border-purple-200 dark:border-purple-800/30 rounded-md hover:bg-purple-100 dark:hover:bg-purple-900/30 transition-colors">
                            Test All Icons
                        </button>
                    </div>
                    <div class="rounded-full p-3 bg-purple-100 dark:bg-purple-900/20 text-purple-600 dark:text-purple-400 shadow-sm">
                        <i data-lucide="check-circle" class="w-6 h-6" id="status-icon"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Search and Filter -->
    <div class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 shadow-md overflow-hidden">
        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div class="md:col-span-1">
                    <div class="relative flex items-center">
                        <input type="text" id="iconSearch" placeholder="Search icons..." class="block w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md leading-5 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 sm:text-sm" onkeyup="filterIcons()">
                        <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none z-10">
                            <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <circle cx="11" cy="11" r="8"></circle>
                                <line x1="21" y1="21" x2="16.65" y2="16.65"></line>
                            </svg>
                        </div>
                    </div>
                </div>
                <div>
                    <select class="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 sm:text-sm" id="iconFilter" onchange="filterIcons()">
                        <option value="all">All Icons</option>
                        <option value="critical">Critical Icons Only</option>
                        <option value="non-critical">On-Demand Icons Only</option>
                    </select>
                </div>
                <div>
                    <select class="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 sm:text-sm" id="iconSort" onchange="sortIcons()">
                        <option value="name">Sort by Name</option>
                        <option value="type">Sort by Type</option>
                        <option value="status">Sort by Status</option>
                    </select>
                </div>
            </div>
        </div>
    </div>

    <!-- Icon Grid -->
    <div class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 shadow-md overflow-hidden">
        <div class="flex flex-col sm:flex-row sm:items-center justify-between p-5 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800">
            <div>
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
                    <i data-lucide="grid" class="w-5 h-5 text-blue-600 dark:text-blue-400 mr-2"></i>
                    <span>Icon Collection</span>
                </h3>
                <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">All available icons in the Matrix App system</p>
            </div>
        </div>
        <div class="p-6">
            <div id="iconGrid" class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 gap-4">
                <!-- Critical Icons Section -->
                {% for icon_name in icon_data.critical_icons %}
                <div class="icon-item bg-white dark:bg-gray-700 rounded-lg border-2 border-green-200 dark:border-green-700 p-4 text-center hover:shadow-lg transition-all duration-200 hover:border-green-300 dark:hover:border-green-600" data-icon="{{ icon_name }}" data-type="critical">
                    <div class="icon-display mb-3 flex justify-center">
                        <i data-lucide="{{ icon_name }}" class="w-8 h-8 text-green-600 dark:text-green-400"></i>
                    </div>
                    <h6 class="text-sm font-medium text-gray-900 dark:text-white mb-2 truncate" title="{{ icon_name }}">{{ icon_name }}</h6>
                    <span class="inline-block text-xs px-2 py-1 rounded-full bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400 border border-green-200 dark:border-green-800/30 mb-2">Critical</span>
                    <div class="text-xs text-gray-600 dark:text-gray-400 mb-3 space-y-1">
                        <div class="flex justify-between">
                            <span>Sprite:</span>
                            <span class="text-green-600 dark:text-green-400">✓</span>
                        </div>
                        <div class="flex justify-between">
                            <span>Fallback:</span>
                            <span class="text-green-600 dark:text-green-400">✓</span>
                        </div>
                    </div>
                    <button class="w-full text-xs px-3 py-1.5 bg-blue-50 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400 border border-blue-200 dark:border-blue-800/30 rounded-md hover:bg-blue-100 dark:hover:bg-blue-900/30 transition-colors" data-modal="icon-usage-modal" onclick="prepareIconUsage('{{ icon_name }}')">
                        Usage
                    </button>
                </div>
                {% endfor %}

                <!-- Non-Critical Icons Section -->
                {% for icon_name in icon_data.all_icons %}
                {% if icon_name not in icon_data.critical_icons %}
                <div class="icon-item bg-white dark:bg-gray-700 rounded-lg border-2 border-blue-200 dark:border-blue-700 p-4 text-center hover:shadow-lg transition-all duration-200 hover:border-blue-300 dark:hover:border-blue-600" data-icon="{{ icon_name }}" data-type="non-critical">
                    <div class="icon-display mb-3 flex justify-center">
                        <i data-lucide="{{ icon_name }}" class="w-8 h-8 text-blue-600 dark:text-blue-400"></i>
                    </div>
                    <h6 class="text-sm font-medium text-gray-900 dark:text-white mb-2 truncate" title="{{ icon_name }}">{{ icon_name }}</h6>
                    <span class="inline-block text-xs px-2 py-1 rounded-full bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400 border border-blue-200 dark:border-blue-800/30 mb-2">On-Demand</span>
                    <div class="text-xs text-gray-600 dark:text-gray-400 mb-3 space-y-1">
                        <div class="flex justify-between">
                            <span>Sprite:</span>
                            <span class="text-yellow-600 dark:text-yellow-400">-</span>
                        </div>
                        <div class="flex justify-between">
                            <span>Fallback:</span>
                            <span class="text-green-600 dark:text-green-400">✓</span>
                        </div>
                    </div>
                    <button class="w-full text-xs px-3 py-1.5 bg-blue-50 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400 border border-blue-200 dark:border-blue-800/30 rounded-md hover:bg-blue-100 dark:hover:bg-blue-900/30 transition-colors" data-modal="icon-usage-modal" onclick="prepareIconUsage('{{ icon_name }}')">
                        Usage
                    </button>
                </div>
                {% endif %}
                {% endfor %}
            </div>
        </div>
    </div>
</div>

<!-- Icon Usage Modal -->
{% call modal(
  id='icon-usage-modal',
  title='Icon Usage',
  description='Copy-ready code examples for using this icon',
  size='lg',
  show_footer=false
) %}
  <!-- Icon Display -->
  <div class="relative mb-6 p-8 bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-xl border border-blue-100 dark:border-blue-800/30 overflow-hidden">
    <!-- Background Pattern -->
    <div class="absolute inset-0 opacity-5">
      <div class="absolute inset-0" style="background-image: radial-gradient(circle at 1px 1px, rgba(59, 130, 246, 0.3) 1px, transparent 0); background-size: 20px 20px;"></div>
    </div>

    <!-- Content -->
    <div class="relative text-center">
      <div class="inline-flex items-center justify-center w-20 h-20 bg-white dark:bg-gray-800 rounded-2xl shadow-lg mb-4 border border-blue-200 dark:border-blue-700">
        <i id="modalIcon" class="w-10 h-10 text-blue-600 dark:text-blue-400"></i>
      </div>
      <h4 id="modalIconName" class="text-2xl font-bold text-gray-900 dark:text-white mb-2"></h4>
      <p class="text-sm text-gray-600 dark:text-gray-400">Click any code block to copy to clipboard</p>
    </div>
  </div>

  <!-- Usage Examples -->
  <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
    <div>
      <div class="flex items-center justify-between mb-3">
        <h6 class="text-sm font-semibold text-gray-900 dark:text-white flex items-center">
          <svg class="w-4 h-4 mr-2 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4"></path>
          </svg>
          Template Usage
        </h6>
        <button onclick="copyToClipboard('templateUsage')" class="text-xs px-2 py-1 bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-400 rounded hover:bg-green-200 dark:hover:bg-green-900/50 transition-colors">
          Copy
        </button>
      </div>
      <pre class="bg-gray-100 dark:bg-gray-700 p-4 rounded-lg text-sm border border-gray-200 dark:border-gray-600 relative group cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors" onclick="copyToClipboard('templateUsage')"><code id="templateUsage" class="text-gray-800 dark:text-gray-200 whitespace-pre-wrap break-all"></code></pre>
    </div>
    <div>
      <div class="flex items-center justify-between mb-3">
        <h6 class="text-sm font-semibold text-gray-900 dark:text-white flex items-center">
          <svg class="w-4 h-4 mr-2 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z"></path>
          </svg>
          Direct SVG Usage
        </h6>
        <button onclick="copyToClipboard('svgUsage')" class="text-xs px-2 py-1 bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-400 rounded hover:bg-blue-200 dark:hover:bg-blue-900/50 transition-colors">
          Copy
        </button>
      </div>
      <pre class="bg-gray-100 dark:bg-gray-700 p-4 rounded-lg text-sm border border-gray-200 dark:border-gray-600 relative group cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors" onclick="copyToClipboard('svgUsage')"><code id="svgUsage" class="text-gray-800 dark:text-gray-200 whitespace-pre-wrap break-all"></code></pre>
    </div>
  </div>

  <div class="mt-6">
    <div class="flex items-center justify-between mb-3">
      <h6 class="text-sm font-semibold text-gray-900 dark:text-white flex items-center">
        <svg class="w-4 h-4 mr-2 text-yellow-600 dark:text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"></path>
        </svg>
        JavaScript Usage
      </h6>
      <button onclick="copyToClipboard('jsUsage')" class="text-xs px-2 py-1 bg-yellow-100 dark:bg-yellow-900/30 text-yellow-700 dark:text-yellow-400 rounded hover:bg-yellow-200 dark:hover:bg-yellow-900/50 transition-colors">
        Copy
      </button>
    </div>
    <pre class="bg-gray-100 dark:bg-gray-700 p-4 rounded-lg text-sm border border-gray-200 dark:border-gray-600 relative group cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors" onclick="copyToClipboard('jsUsage')"><code id="jsUsage" class="text-gray-800 dark:text-gray-200 whitespace-pre-wrap break-all"></code></pre>
  </div>
{% endcall %}
{% endblock %}

{% block scripts %}
<script>
// Icon repository functionality
let currentView = 'grid';

function toggleView() {
    const grid = document.getElementById('iconGrid');
    if (currentView === 'grid') {
        grid.className = 'space-y-2';
        currentView = 'list';

        // Update all icon items to list view
        const items = grid.querySelectorAll('.icon-item');
        items.forEach(item => {
            item.className = 'icon-item bg-white dark:bg-gray-700 rounded-lg border border-gray-200 dark:border-gray-700 p-4 flex items-center space-x-4 hover:shadow-lg transition-all duration-200';
        });
    } else {
        grid.className = 'grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 gap-4';
        currentView = 'grid';

        // Update all icon items back to grid view
        const items = grid.querySelectorAll('.icon-item');
        items.forEach(item => {
            const type = item.dataset.type;
            const borderColor = type === 'critical' ? 'border-green-200 dark:border-green-700 hover:border-green-300 dark:hover:border-green-600' : 'border-blue-200 dark:border-blue-700 hover:border-blue-300 dark:hover:border-blue-600';
            item.className = `icon-item bg-white dark:bg-gray-700 rounded-lg border-2 ${borderColor} p-4 text-center hover:shadow-lg transition-all duration-200`;
        });
    }
}

function filterIcons() {
    const searchTerm = document.getElementById('iconSearch').value.toLowerCase();
    const filterType = document.getElementById('iconFilter').value;
    const iconItems = document.querySelectorAll('.icon-item');

    iconItems.forEach(item => {
        const iconName = item.dataset.icon.toLowerCase();
        const iconType = item.dataset.type;

        const matchesSearch = iconName.includes(searchTerm);
        const matchesFilter = filterType === 'all' ||
                            (filterType === 'critical' && iconType === 'critical') ||
                            (filterType === 'non-critical' && iconType === 'non-critical');

        item.style.display = matchesSearch && matchesFilter ? 'block' : 'none';
    });
}

function sortIcons() {
    const sortBy = document.getElementById('iconSort').value;
    const grid = document.getElementById('iconGrid');
    const items = Array.from(grid.children);

    items.sort((a, b) => {
        if (sortBy === 'name') {
            return a.dataset.icon.localeCompare(b.dataset.icon);
        } else if (sortBy === 'type') {
            return a.dataset.type.localeCompare(b.dataset.type);
        }
        return 0;
    });

    items.forEach(item => grid.appendChild(item));
}

function prepareIconUsage(iconName) {
    // Store the icon name for when the modal opens
    window.currentIconName = iconName;
}

// Copy to clipboard function
function copyToClipboard(elementId) {
    const element = document.getElementById(elementId);
    if (element) {
        const text = element.textContent;

        // Use the modern clipboard API if available
        if (navigator.clipboard && window.isSecureContext) {
            navigator.clipboard.writeText(text).then(() => {
                showCopyFeedback(elementId);
            }).catch(err => {
                console.error('Failed to copy text: ', err);
                fallbackCopyTextToClipboard(text, elementId);
            });
        } else {
            // Fallback for older browsers
            fallbackCopyTextToClipboard(text, elementId);
        }
    }
}

// Fallback copy function for older browsers
function fallbackCopyTextToClipboard(text, elementId) {
    const textArea = document.createElement("textarea");
    textArea.value = text;
    textArea.style.top = "0";
    textArea.style.left = "0";
    textArea.style.position = "fixed";

    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();

    try {
        const successful = document.execCommand('copy');
        if (successful) {
            showCopyFeedback(elementId);
        }
    } catch (err) {
        console.error('Fallback: Oops, unable to copy', err);
    }

    document.body.removeChild(textArea);
}

// Show copy feedback
function showCopyFeedback(elementId) {
    const button = document.querySelector(`button[onclick="copyToClipboard('${elementId}')"]`);
    if (button) {
        const originalText = button.textContent;
        button.textContent = 'Copied!';
        button.classList.add('bg-green-200', 'dark:bg-green-800');

        setTimeout(() => {
            button.textContent = originalText;
            button.classList.remove('bg-green-200', 'dark:bg-green-800');
        }, 2000);
    }
}

// Listen for modal shown event to populate content
document.addEventListener('modal:shown', function(event) {
    if (event.detail.id === 'icon-usage-modal' && window.currentIconName) {
        const iconName = window.currentIconName;

        // Update modal content
        document.getElementById('modalIconName').textContent = iconName;
        document.getElementById('modalIcon').setAttribute('data-lucide', iconName);

        // Update usage examples
        const templateExample = '<!-- Direct icon usage -->\n<i data-lucide="' + iconName + '" class="w-5 h-5"></i>\n\n<!-- Using component -->\n<!-- Set icon variable and include component -->';
        document.getElementById('templateUsage').textContent = templateExample;

        const svgExample = '<svg class="w-5 h-5">\n    <use href="#' + iconName + '"></use>\n</svg>';
        document.getElementById('svgUsage').textContent = svgExample;

        const jsExample = '// Using the icon manager\nIconManager.createIcon(\'' + iconName + '\');\n\n// Using data attribute\n<i data-lucide="' + iconName + '"></i>';
        document.getElementById('jsUsage').textContent = jsExample;

        // Reinitialize the modal icon
        if (window.iconUtils && window.iconUtils.initIconsInContainer) {
            window.iconUtils.initIconsInContainer(event.detail.dialog);
        }
    }
});

function testAllIcons() {
    // Check what elements exist

    // Try multiple ways to find the elements
    let statusIcon = document.getElementById('status-icon');
    let statusText = document.getElementById('status-text');

    // If the icon was transformed by the icon system, try to find it by other means
    if (!statusIcon) {
        // Look for any element with the original data-lucide attribute in the status card
        const statusCard = document.querySelector('.bg-purple-100');
        if (statusCard) {
            statusIcon = statusCard.querySelector('svg, i');
        }
    }

    // Check if elements exist
    if (!statusIcon || !statusText) {
        console.error('Status elements not found');
        return;
    }

    // Set testing state
    statusText.textContent = 'Testing...';

    // Simple approach: always replace the icon container content
    const iconContainer = statusIcon.parentElement;
    if (iconContainer) {
        // Use a direct SVG for the loader to ensure it shows immediately
        iconContainer.innerHTML = `
            <svg class="w-6 h-6 animate-spin" id="status-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
            </svg>
        `;
    }

    // Simulate testing process
    setTimeout(() => {
        // Test icon loading - look for both processed (SVG) and unprocessed (i) icons
        let allIconsWorking = true;
        let processedCount = 0;
        let unprocessedCount = 0;
        let totalIcons = 0;

        // Count different types of icons separately
        let repositoryIcons = 0;
        let uiIcons = 0;
        let inlineIcons = 0;

        // Count repository icons specifically
        const iconGridItems = document.querySelectorAll('.icon-item');
        iconGridItems.forEach(item => {
            const iconDisplay = item.querySelector('.icon-display svg');
            if (iconDisplay) {
                repositoryIcons++;
            }
        });

        // Count UI icons (cards, buttons, headers, etc.)
        const uiSvgs = document.querySelectorAll('svg');
        uiSvgs.forEach(svg => {
            // Skip inline SVGs (like search field)
            if (svg.closest('.absolute')) {
                inlineIcons++;
                return;
            }

            // Skip repository icons (already counted)
            if (svg.closest('.icon-item')) {
                return;
            }

            // Count as UI icon
            uiIcons++;
        });

        processedCount = repositoryIcons + uiIcons;
        totalIcons = processedCount;

        // Count unprocessed i elements
        const allIElements = document.querySelectorAll('i[data-lucide]');
        allIElements.forEach(icon => {
            const iconName = icon.getAttribute('data-lucide');
            unprocessedCount++;
            totalIcons++;
            allIconsWorking = false;
        });

        // Check for missing icons in the repository grid
        iconGridItems.forEach(item => {
            const iconDisplay = item.querySelector('.icon-display');
            if (iconDisplay) {
                const svg = iconDisplay.querySelector('svg');
                const unprocessedIcon = iconDisplay.querySelector('i[data-lucide]');

                if (!svg && !unprocessedIcon) {
                    // No icon found at all
                    allIconsWorking = false;
                }
            }
        });

        // Update status based on results
        const finalIconName = allIconsWorking ? 'check-circle' : 'alert-triangle';
        const finalStatusText = allIconsWorking ? 'All Good' : 'Issues Found';

        statusText.textContent = finalStatusText;

        // Simple approach: always replace the icon container content
        const iconContainer = document.querySelector('.bg-purple-100');
        if (iconContainer) {
            if (finalIconName === 'check-circle') {
                // Success icon
                iconContainer.innerHTML = `
                    <svg class="w-6 h-6" id="status-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                `;
            } else {
                // Warning icon
                iconContainer.innerHTML = `
                    <svg class="w-6 h-6" id="status-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.464 0L4.35 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                    </svg>
                `;
            }
        }

        if (allIconsWorking) {
            console.log(`✅ All ${totalIcons} icons loaded successfully (${processedCount} SVG)`);
        } else {
            console.log(`⚠️ ${processedCount}/${totalIcons} icons loaded successfully`);
            if (unprocessedCount > 0) {
                console.log(`❌ ${unprocessedCount} icons failed to load`);
            }
        }

        // Reinitialize icons after status update
        if (window.iconUtils && window.iconUtils.initIcons) {
            window.iconUtils.initIcons();
        }

        // Also try other initialization methods
        if (typeof lucide !== 'undefined' && lucide.createIcons) {
            lucide.createIcons();
        }
    }, 2000);
}

// Initialize icons when page loads
document.addEventListener('DOMContentLoaded', function() {
    // Multiple initialization attempts to ensure icons load
    function initializeIcons() {
        // Try iconUtils first
        if (window.iconUtils && window.iconUtils.initIcons) {
            window.iconUtils.initIcons();
        }

        // Try lucide directly as fallback
        if (typeof lucide !== 'undefined' && lucide.createIcons) {
            lucide.createIcons();
        }

        // Try lucideManager as another fallback
        if (window.lucideManager && window.lucideManager.initIcons) {
            window.lucideManager.initIcons();
        }
    }

    // Initialize immediately
    initializeIcons();

    // Also try after a short delay to catch any late-loading scripts
    setTimeout(initializeIcons, 100);
    setTimeout(initializeIcons, 500);

    // Specific fix for search icon - force re-initialization
    setTimeout(() => {
        const searchIcon = document.querySelector('[data-lucide="search"]');
        if (searchIcon && window.iconUtils && window.iconUtils.initIconsInContainer) {
            window.iconUtils.initIconsInContainer(searchIcon.parentElement);
        }
        // Also try direct lucide initialization on the search icon
        if (typeof lucide !== 'undefined' && lucide.createIcons) {
            lucide.createIcons({
                icons: {
                    search: lucide.Search
                }
            });
        }
    }, 1000);
});
</script>
{% endblock %}

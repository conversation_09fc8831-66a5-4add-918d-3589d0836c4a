{% from "components/modal.html" import modal %}

{% macro activity_card(label, icon, content, height="auto") %}
<div class="rounded-lg border border-border/10 overflow-hidden h-[{{ height }}] flex flex-col bg-muted/10">
  <div class="px-4 py-3 border-b border-border/10 bg-slate-800/40 flex items-center justify-between">
    <div class="flex items-center">
      <div class="w-7 h-7 rounded-full bg-primary/20 flex items-center justify-center mr-2.5">
        <i data-lucide="{{ icon }}" class="w-3.5 h-3.5 text-primary"></i>
      </div>
      <label class="text-sm font-medium text-foreground">{{ label }}</label>
    </div>
  </div>
  <div class="p-4 font-medium flex-1">
    {{ content|safe }}
  </div>
</div>
{% endmacro %}

{% macro activity_details() %}
{% call modal(id="activity-details", title="", size="lg", show_footer=true, show_action=false, cancel_text="Close", cancel_icon="x") %}
  <div id="activity-details-content" class="space-y-8">
    <!-- Loading spinner (shown initially, hidden when data loads) -->
    <div id="activity-loading" class="flex justify-center items-center py-20">
      <div class="animate-spin rounded-full h-8 w-8 border-2 border-t-transparent border-primary/80"></div>
    </div>

    <!-- Activity details content (hidden initially, shown when data loads) -->
    <div id="activity-data" class="hidden">
      <!-- Header section with user and date -->
      <div class="flex flex-col md:flex-row md:items-center justify-between gap-6 mb-8">
        <div class="flex items-center gap-4">
          <div id="activity-user-initial" class="w-11 h-11 rounded-full bg-primary/20 flex items-center justify-center text-primary font-medium text-base shadow-sm">A</div>
          <div>
            <div class="text-xs text-muted-foreground mb-1 flex items-center">
              <i data-lucide="user" class="h-3.5 w-3.5 mr-1.5 text-primary/70"></i>
              Performed By
            </div>
            <p id="activity-user-name" class="font-medium text-foreground text-base">-</p>
          </div>
        </div>
        <div class="flex items-center gap-4">
          <div class="w-11 h-11 rounded-full bg-primary/10 flex items-center justify-center text-primary/70 shadow-sm">
            <i data-lucide="clock" class="h-5 w-5"></i>
          </div>
          <div>
            <div class="text-xs text-muted-foreground mb-1 flex items-center">
              <i data-lucide="calendar" class="h-3.5 w-3.5 mr-1.5 text-primary/70"></i>
              Date & Time
            </div>
            <p id="activity-date" class="font-medium text-foreground text-base">-</p>
          </div>
        </div>
      </div>

      <!-- Activity metadata -->
      <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-8">
        <div class="p-4 bg-muted/10 rounded-lg border border-border/10 flex flex-col">
          <div class="w-8 h-8 rounded-full bg-primary/20 flex items-center justify-center mb-2">
            <i data-lucide="tag" class="h-4 w-4 text-primary"></i>
          </div>
          <div class="text-xs text-muted-foreground mb-1">Category</div>
          <p id="activity-category" class="flex items-center text-foreground font-medium">
            <span>-</span>
          </p>
        </div>
        <div class="p-4 bg-muted/10 rounded-lg border border-border/10 flex flex-col">
          <div class="w-8 h-8 rounded-full bg-primary/20 flex items-center justify-center mb-2">
            <i data-lucide="git-branch" class="h-4 w-4 text-primary"></i>
          </div>
          <div class="text-xs text-muted-foreground mb-1">Method</div>
          <p id="activity-method" class="flex items-center text-foreground font-medium">-</p>
        </div>
        <div class="p-4 bg-muted/10 rounded-lg border border-border/10 flex flex-col">
          <div class="w-8 h-8 rounded-full bg-primary/20 flex items-center justify-center mb-2">
            <i data-lucide="file" class="h-4 w-4 text-primary"></i>
          </div>
          <div class="text-xs text-muted-foreground mb-1">Entity</div>
          <p id="activity-entity" class="flex items-center text-foreground font-medium">-</p>
        </div>
        <div class="p-4 bg-muted/10 rounded-lg border border-border/10 flex flex-col">
          <div class="w-8 h-8 rounded-full bg-primary/20 flex items-center justify-center mb-2">
            <i data-lucide="globe" class="h-4 w-4 text-primary"></i>
          </div>
          <div class="text-xs text-muted-foreground mb-1">IP Address</div>
          <p id="activity-ip" class="text-foreground font-mono text-sm font-medium">-</p>
        </div>
      </div>

      <!-- URL section -->
      <div class="mb-6">
        <div id="activity-url-container">
          {{ activity_card(label="URL", icon="link", content="<div id='activity-url' class='font-mono text-sm text-foreground/90 max-h-[100px] overflow-y-auto'>-</div>") }}
        </div>
      </div>

      <!-- Details section -->
      <div class="mb-6">
        <div id="activity-details-container">
          {{ activity_card(label="Details", icon="file-text", content="<div id='activity-details' class='whitespace-pre-line break-words max-h-[200px] overflow-y-auto text-foreground/90'>-</div>") }}
        </div>
      </div>

      <!-- User Agent section -->
      <div class="mb-6">
        <div id="activity-user-agent-container">
          {{ activity_card(label="User Agent", icon="monitor", content="<div id='activity-user-agent' class='break-words font-mono text-xs text-foreground/80 max-h-[80px] overflow-y-auto'>-</div>") }}
        </div>
      </div>

      <!-- Changes section (hidden by default, shown when changes exist) -->
      <div id="activity-changes-section" class="mb-6 hidden">
        <div id="activity-changes-container">
          {{ activity_card(label="Changes", icon="git-compare", content="<div class='overflow-auto max-h-[300px]'><table class='w-full text-xs'><thead><tr class='border-b border-border/10'><th class='text-left py-2.5 px-3 font-medium text-muted-foreground'>Field</th><th class='text-left py-2.5 px-3 font-medium text-muted-foreground'>Old Value</th><th class='text-left py-2.5 px-3 font-medium text-muted-foreground'>New Value</th></tr></thead><tbody id='activity-changes-body' class='divide-y divide-border/10'><!-- Changes will be inserted here by JavaScript --></tbody></table></div>") }}
        </div>
      </div>

      <!-- Entity link section (hidden by default, shown when entity exists) -->
      <div id="activity-entity-link-section" class="hidden">
        <div class="flex justify-end gap-3 pt-5 mt-2 border-t border-border/10">
          <!-- Secondary action button (hidden by default) -->
          <button type="button" id="activity-secondary-action" style="display: none;" class="inline-flex items-center justify-center rounded-md text-sm font-medium transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 border border-border bg-background hover:bg-muted/50 h-10 px-4 py-2 group">
            <span class="relative flex items-center">
              <i data-lucide="history" class="h-4 w-4 mr-2"></i>
              <span>View History</span>
            </span>
          </button>

          <!-- Primary action button -->
          <button id="activity-entity-link" type="button" class="inline-flex items-center justify-center rounded-md text-sm font-medium transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary/40 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 shadow-sm hover:shadow h-10 px-5 py-2 group">
            <span class="relative flex items-center">
              <i data-lucide="file-json" class="h-4 w-4 mr-2 transition-transform duration-200 group-hover:translate-x-0.5"></i>
              <span>View Entity JSON</span>
            </span>
          </button>
        </div>
      </div>
    </div>
  </div>
{% endcall %}
{% endmacro %}

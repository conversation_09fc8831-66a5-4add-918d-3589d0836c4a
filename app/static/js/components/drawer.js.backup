/**
 * Drawer Component for displaying forms and content in a sliding panel
 */
class DrawerComponent {
  constructor(options = {}) {
    this.position = options.position || 'right';
    this.width = options.width || '30rem';
    this.onClose = options.onClose || (() => {});
    this.container = document.getElementById('drawer-container');
    this.drawer = null;
    this.overlay = null;
    this.closeHandler = null;
  }

  /**
   * Open the drawer with the provided content
   * @param {string} content - HTML content to display in the drawer
   * @returns {DrawerComponent} - The drawer instance for chaining
   */
  open(content) {
    // Create overlay
    this.overlay = document.createElement('div');
    this.overlay.className = 'drawer-overlay';
    this.overlay.setAttribute('data-state', 'open');
    this.overlay.style.position = 'fixed';
    this.overlay.style.inset = '0';
    this.overlay.style.backgroundColor = 'rgba(0, 0, 0, 0.5)';
    this.overlay.style.backdropFilter = 'blur(4px)';
    this.overlay.style.zIndex = '9998';
    this.overlay.style.opacity = '0';
    this.overlay.style.transition = 'opacity 0.3s ease-in-out';

    // Create drawer
    this.drawer = document.createElement('div');
    this.drawer.className = `drawer-content drawer-${this.position}`;
    this.drawer.setAttribute('data-state', 'open');

    // Apply styles based on theme
    const isDarkMode = document.documentElement.classList.contains('dark');
    this.drawer.style.backgroundColor = isDarkMode ? '#111827' : '#ffffff';
    this.drawer.style.color = isDarkMode ? '#fff' : '#111827';
    this.drawer.style.borderColor = isDarkMode ? '#1f2937' : '#e5e7eb';

    // Position styles
    this.drawer.style.position = 'fixed';
    this.drawer.style.zIndex = '9999';
    this.drawer.style.padding = '1.5rem';
    this.drawer.style.boxShadow = isDarkMode ?
      '0 0 15px rgba(0, 0, 0, 0.5)' :
      '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)';
    this.drawer.style.overflow = 'auto';
    this.drawer.style.transition = 'transform 0.3s ease-in-out';

    // Position-specific styles
    if (this.position === 'right') {
      this.drawer.style.top = '0';
      this.drawer.style.right = '0';
      this.drawer.style.bottom = '0';
      this.drawer.style.width = this.width;
      this.drawer.style.maxWidth = '100vw';
      this.drawer.style.transform = 'translateX(100%)';
      this.drawer.style.borderLeft = `1px solid ${this.drawer.style.borderColor}`;
    } else if (this.position === 'left') {
      this.drawer.style.top = '0';
      this.drawer.style.left = '0';
      this.drawer.style.bottom = '0';
      this.drawer.style.width = this.width;
      this.drawer.style.maxWidth = '100vw';
      this.drawer.style.transform = 'translateX(-100%)';
      this.drawer.style.borderRight = `1px solid ${this.drawer.style.borderColor}`;
    } else if (this.position === 'top') {
      this.drawer.style.top = '0';
      this.drawer.style.left = '0';
      this.drawer.style.right = '0';
      this.drawer.style.maxHeight = '80vh';
      this.drawer.style.transform = 'translateY(-100%)';
      this.drawer.style.borderBottom = `1px solid ${this.drawer.style.borderColor}`;
    } else if (this.position === 'bottom') {
      this.drawer.style.bottom = '0';
      this.drawer.style.left = '0';
      this.drawer.style.right = '0';
      this.drawer.style.maxHeight = '80vh';
      this.drawer.style.transform = 'translateY(100%)';
      this.drawer.style.borderTop = `1px solid ${this.drawer.style.borderColor}`;
    }

    this.drawer.innerHTML = content;

    // Add close button
    const closeButton = document.createElement('button');
    closeButton.className = 'absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none';
    closeButton.style.position = 'absolute';
    closeButton.style.right = '1rem';
    closeButton.style.top = '1rem';
    closeButton.style.cursor = 'pointer';
    closeButton.innerHTML = '<i data-lucide="x" class="h-4 w-4"></i><span class="sr-only">Close</span>';
    this.drawer.prepend(closeButton);

    // Add to container
    this.container.appendChild(this.overlay);
    this.container.appendChild(this.drawer);

    // Initialize icons
    if (typeof lucide !== 'undefined' && lucide.createIcons) {
      lucide.createIcons({ root: this.drawer });
    }

    // Trigger animation after a small delay to ensure the initial state is rendered
    requestAnimationFrame(() => {
      requestAnimationFrame(() => {
        // Apply the open transform
        if (this.position === 'right' || this.position === 'left') {
          this.drawer.style.transform = 'translateX(0)';
        } else {
          this.drawer.style.transform = 'translateY(0)';
        }

        // Fade in the overlay
        this.overlay.style.opacity = '1';
      });
    });

    // Prevent body scrolling
    document.body.style.overflow = 'hidden';

    // Create close handler
    this.closeHandler = () => this.close();

    // Event listeners
    this.overlay.addEventListener('click', this.closeHandler);
    closeButton.addEventListener('click', this.closeHandler);

    // Add event listeners to any elements with drawer-close class
    const closeElements = this.drawer.querySelectorAll('.drawer-close');
    closeElements.forEach(element => {
      element.addEventListener('click', this.closeHandler);
    });

    // Initialize form validation
    this.initFormValidation();

    return this;
  }

  /**
   * Close the drawer
   */
  close() {
    if (!this.drawer || !this.overlay) return;

    // Animation
    if (this.position === 'right') {
      this.drawer.style.transform = 'translateX(100%)';
    } else if (this.position === 'left') {
      this.drawer.style.transform = 'translateX(-100%)';
    } else if (this.position === 'top') {
      this.drawer.style.transform = 'translateY(-100%)';
    } else if (this.position === 'bottom') {
      this.drawer.style.transform = 'translateY(100%)';
    }

    this.overlay.style.opacity = '0';

    // Remove after animation
    setTimeout(() => {
      if (this.container.contains(this.overlay)) {
        this.container.removeChild(this.overlay);
      }
      if (this.container.contains(this.drawer)) {
        this.container.removeChild(this.drawer);
      }

      // Restore scrolling
      document.body.style.overflow = '';

      // Call onClose callback
      if (typeof this.onClose === 'function') {
        this.onClose();
      }

      this.drawer = null;
      this.overlay = null;
    }, 300);
  }

  /**
   * Initialize form validation
   * @private
   */
  initFormValidation() {
    const form = this.drawer.querySelector('form');
    if (!form) return;

    form.addEventListener('submit', (e) => {
      // Basic form validation
      const requiredFields = form.querySelectorAll('[required]');
      let isValid = true;

      requiredFields.forEach(field => {
        if (!field.value.trim()) {
          isValid = false;
          // Add error styling
          field.classList.add('border-destructive');

          // Add or update error message
          let errorMsg = field.parentNode.querySelector('.error-message');
          if (!errorMsg) {
            errorMsg = document.createElement('p');
            errorMsg.className = 'text-destructive text-xs mt-1 error-message';
            field.parentNode.appendChild(errorMsg);
          }
          errorMsg.textContent = 'This field is required';
        } else {
          // Remove error styling
          field.classList.remove('border-destructive');
          const errorMsg = field.parentNode.querySelector('.error-message');
          if (errorMsg) errorMsg.remove();
        }
      });

      if (!isValid) {
        e.preventDefault();
      }
    });
  }
}

/**
 * DrawerManager for managing multiple drawers
 */
class DrawerManager {
  constructor() {
    this.activeDrawers = [];
    this.drawerStack = [];
  }

  /**
   * Open a drawer with the specified form type
   * @param {string} formType - The type of form to open (e.g., 'user', 'business_unit')
   * @param {string|number} entityId - Optional ID of the entity to edit
   * @param {Object} options - Options for the drawer
   * @returns {Promise<DrawerComponent>} - Promise resolving to the drawer instance
   */
  openForm(formType, entityId = null, options = {}) {
    // Try to determine the correct URL based on the current page
    let url;

    // If we're on an admin page, try the admin URL first
    if (window.location.pathname.startsWith('/admin')) {
      url = `/admin/forms/${formType}`;
    } else {
      // Otherwise use the regular forms URL
      url = `/forms/${formType}`;
    }

    // Build query parameters
    const queryParams = new URLSearchParams();

    if (entityId) {
      queryParams.append('id', entityId);
    }

    // Add additional query parameters from options
    if (options.queryParams) {
      Object.entries(options.queryParams).forEach(([key, value]) => {
        queryParams.append(key, value);
      });
    }

    // Add query parameters to URL if there are any
    const queryString = queryParams.toString();
    if (queryString) {
      url += `?${queryString}`;
    }

    console.log('Opening form:', url);

    return fetch(url)
      .then(response => {
        if (!response.ok) {
          throw new Error(`HTTP error! Status: ${response.status}`);
        }
        return response.text().then(text => {
          try {
            return JSON.parse(text);
          } catch (e) {
            console.error('Failed to parse JSON:', text.substring(0, 100) + '...');
            throw new Error('Invalid JSON response from server');
          }
        });
      })
      .then(data => {
        if (!data.success) {
          throw new Error(data.error || 'Failed to load form');
        }

        const drawer = new DrawerComponent(options);
        drawer.open(data.html);

        this.activeDrawers.push(drawer);
        this.drawerStack.push(drawer);

        return drawer;
      })
      .catch(error => {
        console.error('Error loading form:', error);
        if (typeof showToast === 'function') {
          showToast(error.message || 'Failed to load form', { type: 'error' });
        }
        return null;
      });
  }

  /**
   * Close all open drawers
   */
  closeAll() {
    this.activeDrawers.forEach(drawer => drawer.close());
    this.activeDrawers = [];
    this.drawerStack = [];
  }

  /**
   * Close the most recently opened drawer
   */
  closeLast() {
    if (this.drawerStack.length > 0) {
      const drawer = this.drawerStack.pop();
      drawer.close();

      // Remove from active drawers
      const index = this.activeDrawers.indexOf(drawer);
      if (index > -1) {
        this.activeDrawers.splice(index, 1);
      }
    }
  }
}

// Create global instances
window.DrawerComponent = DrawerComponent;
window.drawerManager = new DrawerManager();

// Add a test method to verify the API connection
drawerManager.testConnection = function() {
  // Try to determine the correct URL based on the current page
  let url;

  // If we're on an admin page, try the admin URL first
  if (window.location.pathname.startsWith('/admin')) {
    url = '/admin/forms/test';
  } else {
    // Otherwise use the regular forms URL
    url = '/forms/test';
  }

  console.log('Testing forms API connection:', url);

  fetch(url)
    .then(response => {
      if (!response.ok) {
        throw new Error(`HTTP error! Status: ${response.status}`);
      }
      return response.text().then(text => {
        try {
          return JSON.parse(text);
        } catch (e) {
          console.error('Failed to parse JSON:', text.substring(0, 100) + '...');
          throw new Error('Invalid JSON response from server');
        }
      });
    })
    .then(data => {
      console.log('Forms API test successful:', data);
      if (typeof showToast === 'function') {
        showToast('Forms API connection successful', { type: 'success' });
      }
    })
    .catch(error => {
      console.error('Forms API test failed:', error);
      if (typeof showToast === 'function') {
        showToast(`Forms API connection failed: ${error.message}`, { type: 'error' });
      }
    });
};

{% macro table_header(title, count=None, count_label="items", button_text=None, button_icon=None, button_action=None, description=None) %}
<div class="p-6 border-b border-border">
  <div class="flex items-center justify-between">
    <div>
      <h2 class="text-lg font-semibold">{{ title }}</h2>
      {% if description %}
      <p class="text-sm text-muted-foreground mt-1">{{ description }}</p>
      {% endif %}
    </div>
    <div class="flex items-center space-x-2">
      {% if count is not none %}
      <span class="text-sm text-muted-foreground">{{ count }} {{ count_label }}</span>
      {% endif %}
      {% if button_text %}
      <button onclick="{{ button_action }}" class="btn btn-primary btn-sm ml-4">
        {% if button_icon %}
        <i data-lucide="{{ button_icon }}" class="w-4 h-4 mr-2"></i>
        {% endif %}
        <span>{{ button_text }}</span>
      </button>
      {% endif %}
    </div>
  </div>
</div>
{% endmacro %}

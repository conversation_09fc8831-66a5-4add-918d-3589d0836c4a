{% from "partials/forms/base_form.html" import form_group, form_actions, form_header %}

<form action="{{ action_url }}" method="POST" class="space-y-4">
  <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">

  {{ form_header(
    title = "Edit Business Unit" if entity else "Add Business Unit",
    description = "Update business unit details" if entity else "Create a new business unit"
  ) }}

  <div class="grid grid-cols-1 gap-4">
    {{ form_group(
      label = "Name",
      name = "name",
      value = entity.name if entity else "",
      required = true,
      placeholder = "Finance"
    ) }}

    {{ form_group(
      label = "Code",
      name = "code",
      value = entity.code if entity else "",
      required = true,
      placeholder = "FIN"
    ) }}

    {{ form_group(
      label = "Description",
      name = "description",
      type = "textarea",
      value = entity.description if entity else "",
      placeholder = "Enter a description for this business unit"
    ) }}

    {% if entity %}
      {{ form_group(
        label = "Active",
        name = "is_active",
        type = "checkbox",
        value = entity.is_active if entity else true
      ) }}
    {% endif %}
  </div>

  {{ form_actions(
    submit_label = "Update Business Unit" if entity else "Add Business Unit"
  ) }}
</form>

{% for activity in activities %}
<tr class="border-b border-border hover:bg-muted/30 transition-colors duration-150 {% if activity.severity == 'error' %}bg-destructive/5{% elif activity.severity == 'warning' %}bg-warning/5{% endif %}">
  <!-- Severity with badge -->
  <td class="px-4 py-3">
    <span class="badge {{ activity.get_severity_badge_class() }}">
      {{ activity.severity.title() if activity.severity else 'Info' }}
    </span>
  </td>

  <!-- User -->
  <td class="px-4 py-3">
    {% if activity.user %}
    <div class="flex items-center space-x-2">
      <div class="w-7 h-7 rounded-full bg-primary/10 flex items-center justify-center text-primary font-medium text-xs">
        {{ activity.user.name[:1] }}
      </div>
      <span class="font-medium text-sm">{{ activity.user.name }}</span>
    </div>
    {% else %}
    <span class="text-muted-foreground text-sm">Unknown</span>
    {% endif %}
  </td>

  <!-- Action -->
  <td class="px-4 py-3">
    <div class="text-sm {% if activity.severity == 'error' %}text-destructive font-medium{% elif activity.severity == 'warning' %}text-warning font-medium{% endif %}">
      {{ activity.action }}
    </div>
    {% if activity.method %}
    <div class="mt-1">
      <span class="text-xs px-1.5 py-0.5 rounded-sm inline-flex items-center
        {% if activity.method == 'create' %}bg-blue-100 text-blue-700 dark:bg-blue-900/30 dark:text-blue-400
        {% elif activity.method == 'read' %}bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-400
        {% elif activity.method == 'update' %}bg-amber-100 text-amber-700 dark:bg-amber-900/30 dark:text-amber-400
        {% elif activity.method == 'delete' %}bg-red-100 text-red-700 dark:bg-red-900/30 dark:text-red-400
        {% else %}bg-gray-100 text-gray-700 dark:bg-gray-900/30 dark:text-gray-400{% endif %}">
        {% if activity.method == 'create' %}<i data-lucide="plus" class="h-3 w-3 mr-1"></i>
        {% elif activity.method == 'read' %}<i data-lucide="eye" class="h-3 w-3 mr-1"></i>
        {% elif activity.method == 'update' %}<i data-lucide="edit" class="h-3 w-3 mr-1"></i>
        {% elif activity.method == 'delete' %}<i data-lucide="trash" class="h-3 w-3 mr-1"></i>
        {% else %}<i data-lucide="activity" class="h-3 w-3 mr-1"></i>{% endif %}
        {{ activity.get_method_display() }}
      </span>
    </div>
    {% endif %}
  </td>

  <!-- Category -->
  <td class="px-4 py-3">
    <span class="text-xs px-2 py-1 rounded-full bg-muted inline-flex items-center">
      {% if activity.category == 'auth' %}
        <i data-lucide="key" class="h-3 w-3 mr-1"></i>
      {% elif activity.category == 'admin' %}
        <i data-lucide="shield" class="h-3 w-3 mr-1"></i>
      {% elif activity.category == 'user' %}
        <i data-lucide="user" class="h-3 w-3 mr-1"></i>
      {% elif activity.category == 'system' %}
        <i data-lucide="settings" class="h-3 w-3 mr-1"></i>
      {% elif activity.category == 'data' %}
        <i data-lucide="database" class="h-3 w-3 mr-1"></i>
      {% endif %}
      {{ activity.get_category_display() }}
    </span>
  </td>

  <!-- Entity -->
  <td class="px-4 py-3 text-sm">
    {% if activity.entity_type and activity.entity_id %}
      <span class="inline-flex items-center">
        <i data-lucide="file" class="h-3.5 w-3.5 mr-1 text-muted-foreground"></i>
        {{ activity.entity_type }} #{{ activity.entity_id }}
      </span>
    {% else %}
      <span class="text-muted-foreground">-</span>
    {% endif %}
  </td>

  <!-- Details -->
  <td class="px-4 py-3 text-sm">
    <div class="max-w-xs truncate" title="{{ activity.details or '-' }}">
      {{ activity.details or '-' }}
    </div>
  </td>

  <!-- Date & Time -->
  <td class="px-4 py-3 whitespace-nowrap">
    <div class="flex flex-col">
      <span class="text-sm">{{ activity.created_at.strftime('%Y-%m-%d') }}</span>
      <span class="text-xs text-muted-foreground">{{ activity.created_at.strftime('%H:%M:%S') }}</span>
      {% if now and activity.created_at.tzinfo %}
        {% set time_diff = (now - activity.created_at).total_seconds() %}
        {% if time_diff < 300 %}
          <span class="inline-flex items-center text-xs text-primary mt-1">
            <i data-lucide="clock" class="h-3 w-3 mr-1"></i> Recent
          </span>
        {% endif %}
      {% endif %}
    </div>
  </td>

  <!-- Actions -->
  <td class="px-4 py-3">
    <div class="flex space-x-1">
      <button type="button" class="btn btn-icon btn-sm"
              onclick="showActivityDetails({{ activity.id }})"
              title="View Details">
        <i data-lucide="eye" class="h-4 w-4"></i>
      </button>
      {% if activity.user_id == current_user.id %}
      <button type="button" class="btn btn-icon btn-sm"
              onclick="window.location.href='{{ url_for('admin.activities', user_id=activity.user_id) }}'"
              title="View My Activities">
        <i data-lucide="user" class="h-4 w-4"></i>
      </button>
      {% endif %}
    </div>
  </td>
</tr>
{% endfor %}
{% if not activities %}
<tr>
  <td colspan="8" class="px-4 py-8 text-center">
    <div class="flex flex-col items-center justify-center">
      <div class="rounded-full bg-muted p-3 mb-3">
        <i data-lucide="activity" class="h-6 w-6 text-muted-foreground"></i>
      </div>
      <h3 class="text-lg font-medium mb-1">No activities found</h3>
      <p class="text-muted-foreground text-sm">Try adjusting your filters to see more results</p>
    </div>
  </td>
</tr>
{% endif %}

/**
 * Enhanced Date Input Component
 * Provides better styling and consistent appearance
 */
class DateInput {
  constructor() {
    this.init();
  }

  /**
   * Initialize date inputs
   */
  init() {
    // Find all date inputs
    const dateInputs = document.querySelectorAll('input[type="date"]');

    // Add event listeners to each date input
    dateInputs.forEach(input => {
      // Add the date-input class for styling
      input.classList.add('date-input');

      // Create calendar icon
      const iconContainer = document.createElement('div');
      iconContainer.className = 'absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none';
      iconContainer.innerHTML = '<i data-lucide="calendar" class="h-4 w-4 text-muted-foreground"></i>';

      // Create a wrapper div
      const wrapper = document.createElement('div');
      wrapper.className = 'relative';

      // Insert the wrapper and icon
      input.parentNode.insertBefore(wrapper, input);
      wrapper.appendChild(input);
      wrapper.appendChild(iconContainer);

      // Format the date when it changes and check for holidays
      input.addEventListener('change', () => {
        if (input.value) {
          // Store the formatted date as a data attribute
          const date = new Date(input.value);
          const formattedDate = this.formatDate(date);
          input.setAttribute('data-formatted-date', formattedDate);

          // Check for holidays if this is an attendance form
          this.checkHolidayForDate(input, input.value);
        } else {
          input.removeAttribute('data-formatted-date');
          this.clearHolidayWarning(input);
        }
      });

      // Initialize with current value if present
      if (input.value) {
        const event = new Event('change');
        input.dispatchEvent(event);
      }
    });

    // Initialize Lucide icons if available
    if (typeof lucide !== 'undefined' && lucide.createIcons) {
      lucide.createIcons();
    }

    // Add a style element to handle the calendar appearance
    this.addCalendarStyles();
  }

  /**
   * Format a date as DD/MM/YYYY
   * @param {Date} date - The date to format
   * @returns {string} - The formatted date
   */
  formatDate(date) {
    const day = String(date.getDate()).padStart(2, '0');
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const year = date.getFullYear();

    return `${day}/${month}/${year}`;
  }

  /**
   * Check if a date is a holiday and show warning
   * @param {HTMLElement} input - The date input element
   * @param {string} dateValue - The date value in YYYY-MM-DD format
   */
  async checkHolidayForDate(input, dateValue) {
    // Only check holidays for attendance forms
    const form = input.closest('form');
    if (!form || !form.id || !form.id.includes('attendance')) {
      return;
    }

    try {
      const response = await fetch(`/api/holidays/check?date=${dateValue}`);
      if (response.ok) {
        const data = await response.json();
        if (data.success && data.is_holiday) {
          this.showHolidayWarning(input, data);
        } else {
          this.clearHolidayWarning(input);
        }
      }
    } catch (error) {
      console.error('Error checking holiday:', error);
    }
  }

  /**
   * Show holiday warning near the date input
   * @param {HTMLElement} input - The date input element
   * @param {Object} holidayData - Holiday information
   */
  showHolidayWarning(input, holidayData) {
    // Remove existing warning
    this.clearHolidayWarning(input);

    // Create warning element
    const warning = document.createElement('div');
    warning.className = 'holiday-warning mt-2 p-2 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-md';
    warning.innerHTML = `
      <div class="flex items-start">
        <div class="flex-shrink-0">
          <svg class="h-4 w-4 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
          </svg>
        </div>
        <div class="ml-2">
          <p class="text-sm text-blue-800 dark:text-blue-200">
            <strong>Holiday:</strong> ${holidayData.holiday_name}
          </p>
          ${holidayData.holiday_description ? `<p class="text-xs text-blue-600 dark:text-blue-300 mt-1">${holidayData.holiday_description}</p>` : ''}
          <p class="text-xs text-blue-600 dark:text-blue-300 mt-1">
            Working on holidays may require special approval.
          </p>
        </div>
      </div>
    `;

    // Insert warning after the input wrapper
    const wrapper = input.closest('.relative') || input.parentNode;
    wrapper.parentNode.insertBefore(warning, wrapper.nextSibling);
  }

  /**
   * Clear holiday warning
   * @param {HTMLElement} input - The date input element
   */
  clearHolidayWarning(input) {
    const wrapper = input.closest('.relative') || input.parentNode;
    const existingWarning = wrapper.parentNode.querySelector('.holiday-warning');
    if (existingWarning) {
      existingWarning.remove();
    }
  }

  /**
   * Add styles to handle calendar appearance in both light and dark modes
   */
  addCalendarStyles() {
    // Check if styles are already added
    if (document.getElementById('date-input-styles')) {
      return;
    }

    // Create style element
    const style = document.createElement('style');
    style.id = 'date-input-styles';

    // Add CSS rules
    style.textContent = `
      /* Base styles for date inputs */
      input[type="date"].date-input {
        position: relative;
        appearance: none;
        -webkit-appearance: none;
        -moz-appearance: none;
      }

      /* Hide the default calendar icon */
      input[type="date"].date-input::-webkit-calendar-picker-indicator {
        opacity: 0;
        position: absolute;
        top: 0;
        right: 0;
        width: 100%;
        height: 100%;
        cursor: pointer;
      }

      /* Style the calendar popup */
      :root {
        color-scheme: light;
      }

      .dark {
        color-scheme: dark;
      }
    `;

    // Add to document head
    document.head.appendChild(style);
  }
}

// Initialize the date input when the DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  window.dateInput = new DateInput();

  // Also initialize when drawers are opened
  if (window.drawerManager) {
    const originalOpenForm = window.drawerManager.openForm;
    window.drawerManager.openForm = function() {
      const result = originalOpenForm.apply(this, arguments);

      // Initialize date input after drawer is opened
      setTimeout(() => {
        new DateInput();
      }, 300);

      return result;
    };
  }
});

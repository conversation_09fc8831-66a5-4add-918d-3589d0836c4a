@tailwind base;
@tailwind components;
@tailwind utilities;

/* ====================
   BASE LAYER
   ==================== */
@layer base {
  :root {
    /* Light mode colors */
    --background: 0 0% 100%;
    --foreground: 222 47% 11%;
    --card: 0 0% 100%;
    --card-foreground: 222 47% 11%;
    --popover: 0 0% 100%;
    --popover-foreground: 222 47% 11%;
    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222 47% 11.2%;
    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96.1%;
    --accent-foreground: 222 47% 11.2%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;
    --radius: 0.5rem;
  }

  .dark {
    /* Dark mode colors */
    --background: 222 47% 11%;
    --foreground: 210 40% 98%;
    --card: 222 47% 11%;
    --card-foreground: 210 40% 98%;
    --popover: 222 47% 11%;
    --popover-foreground: 210 40% 98%;
    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222 47% 11.2%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 48%;
  }

  /* Dark mode input fixes */
  .dark input:-webkit-autofill,
  .dark input:-webkit-autofill:hover,
  .dark input:-webkit-autofill:focus,
  .dark textarea:-webkit-autofill,
  .dark textarea:-webkit-autofill:hover,
  .dark textarea:-webkit-autofill:focus,
  .dark select:-webkit-autofill,
  .dark select:-webkit-autofill:hover,
  .dark select:-webkit-autofill:focus {
    -webkit-text-fill-color: hsl(var(--foreground));
    -webkit-box-shadow: 0 0 0px 1000px hsl(var(--secondary)) inset;
    transition: background-color 5000s ease-in-out 0s;
    caret-color: hsl(var(--foreground));
  }
}

/* ====================
   ANIMATIONS
   ==================== */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes fadeOut {
  from { opacity: 1; }
  to { opacity: 0; }
}

@keyframes slideInRight {
  from { transform: translateX(100%); }
  to { transform: translateX(0); }
}

@keyframes slideOutRight {
  from { transform: translateX(0); }
  to { transform: translateX(100%); }
}

.animate-fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

.animate-fade-out {
  animation: fadeOut 0.3s ease-in-out;
}

.animate-slide-in-right {
  animation: slideInRight 0.3s ease-in-out;
}

.animate-slide-out-right {
  animation: slideOutRight 0.3s ease-in-out;
}

/* ====================
   COMPONENTS LAYER
   ==================== */
@layer components {
  /* ---------- Button styles ---------- */
  .btn {
    @apply inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background;
  }

  .btn-primary {
    @apply bg-primary text-primary-foreground hover:bg-primary/90 focus:ring-primary shadow-sm;
  }

  .btn-secondary {
    @apply bg-secondary text-secondary-foreground hover:bg-secondary/80 focus:ring-secondary;
  }

  .btn-outline {
    @apply border border-input bg-background hover:bg-accent hover:text-accent-foreground focus:ring-primary;
  }

  .btn-ghost {
    @apply hover:bg-accent hover:text-accent-foreground focus:ring-primary;
  }

  .btn-destructive {
    @apply bg-destructive text-destructive-foreground hover:bg-destructive/90 focus:ring-destructive shadow-sm;
  }

  /* Additional button variants */
  /* btn-blue removed as it's redundant with btn-primary */

  .btn-gray {
    @apply bg-white text-gray-700 border border-gray-300 hover:bg-gray-50 focus:ring-blue-500 shadow-sm;
  }

  .btn-success {
    @apply bg-green-600 text-white hover:bg-green-700 focus:ring-green-500 shadow-sm;
  }

  .btn-warning {
    @apply bg-yellow-600 text-white hover:bg-yellow-700 focus:ring-yellow-500 shadow-sm;
  }

  /* Button sizes - shadcn style */
  .btn-sm {
    @apply h-9 px-3 rounded-md;
  }

  .btn-md, .btn-default {
    @apply h-10 py-2 px-4;
  }

  .btn-lg {
    @apply h-11 px-8 rounded-md;
  }

  /* Extra large size (not in shadcn but kept for backward compatibility) */
  .btn-xl {
    @apply h-12 px-10 rounded-md text-base;
  }

  /* Layout styles */
  .btn-block {
    @apply w-full;
  }

  /* Icon buttons - shadcn style */
  .btn-icon {
    @apply p-0 h-10 w-10 rounded-md;
  }

  .btn-icon-sm {
    @apply p-0 h-9 w-9 rounded-md;
  }

  .btn-icon-lg {
    @apply p-0 h-11 w-11 rounded-md;
  }

  /* Add shadcn icon styling */
  .btn [data-lucide], .btn svg {
    @apply h-4 w-4 shrink-0 pointer-events-none;
  }

  /* Add gap between icon and text */
  .btn {
    @apply gap-2;
  }

  /* Social Media Buttons */
  .btn-social {
    @apply btn btn-gray btn-icon;
  }

  /* ---------- Form elements ---------- */
  .input {
    @apply flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50;
  }

  /* Checkbox styles with shadcn-style appearance */
  input[type="checkbox"] {
    @apply h-4 w-4 rounded border border-input bg-background focus:ring-0 focus:ring-offset-0;
    appearance: none;
    -webkit-appearance: none;
    position: relative;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.2s, border-color 0.2s;
  }

  /* Default checkbox checked state */
  input[type="checkbox"]:checked {
    @apply bg-primary border-primary;
  }

  /* Checkbox checkmark */
  input[type="checkbox"]:checked::after {
    content: '';
    position: absolute;
    width: 0.375rem;
    height: 0.625rem;
    border: 0.125rem solid white;
    border-top: 0;
    border-left: 0;
    transform: rotate(45deg) translate(-0.0625rem, -0.125rem);
  }

  /* Disabled checkbox styles */
  input[type="checkbox"]:disabled {
    @apply opacity-50 cursor-not-allowed;
  }

  /* Disabled checkbox label */
  input[type="checkbox"]:disabled + label {
    @apply opacity-50 cursor-not-allowed;
  }

  /* Neutral color styles for checkboxes in light mode */
  .neutral-primary input[type="checkbox"]:checked {
    background-color: black;
    border-color: black;
  }

  /* Neutral color styles for checkboxes in dark mode */
  .dark.neutral-primary input[type="checkbox"]:checked {
    background-color: white;
    border-color: white;
  }

  /* Dark mode checkbox styles */
  .dark input[type="checkbox"] {
    @apply border-border bg-secondary;
  }

  /* Ensure the checkmark is black for white checkboxes in dark mode */
  .dark.neutral-primary input[type="checkbox"]:checked::after {
    border-color: black;
  }

  /* Disabled checkbox styles for neutral colors */
  .neutral-primary input[type="checkbox"]:disabled:checked {
    background-color: rgba(0, 0, 0, 0.5);
    border-color: rgba(0, 0, 0, 0.5);
  }

  .dark.neutral-primary input[type="checkbox"]:disabled:checked {
    background-color: rgba(255, 255, 255, 0.5);
    border-color: rgba(255, 255, 255, 0.5);
  }

  .dark.neutral-primary input[type="checkbox"]:disabled:checked::after {
    border-color: rgba(0, 0, 0, 0.7);
  }

  .form-group {
    @apply space-y-2 mb-4;
  }

  .form-label {
    @apply block text-sm font-medium mb-1;
  }

  .form-hint {
    @apply text-xs text-muted-foreground mt-1;
  }

  /* ---------- Card styles ---------- */
  .card {
    @apply rounded-lg border bg-card text-card-foreground shadow-sm;
  }

  .dark .card {
    @apply border-border shadow-none;
  }

  .card-header {
    @apply flex flex-col space-y-1.5 p-6;
  }

  .card-title {
    @apply text-2xl font-semibold leading-none tracking-tight;
  }

  .card-description {
    @apply text-sm text-muted-foreground;
  }

  .card-content {
    @apply p-6 pt-0;
  }

  .card-footer {
    @apply flex items-center p-6 pt-0;
  }

  /* Stats card */
  .stats-card {
    @apply card p-6 transition-all duration-200 hover:translate-y-[-2px];
  }

  .stats-card-icon {
    @apply p-3 rounded-full bg-primary/10 text-primary;
  }

  /* ---------- Tooltip styles ---------- */
  .tooltip {
    @apply invisible absolute z-50 rounded-md bg-popover px-3 py-1.5 text-xs text-popover-foreground shadow-md animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95;
  }

  .has-tooltip:hover .tooltip {
    @apply visible;
  }

  /* ---------- Sidebar styles ---------- */
  /* Pre-collapsed sidebar styles to prevent flickering during page load */
  .sidebar-pre-collapsed #sidebar {
    @apply -translate-x-full;
  }

  .sidebar-pre-collapsed #main-content {
    @apply lg:ml-0;
  }

  /* Sidebar collapsed state - completely hide the sidebar with animation */
  #sidebar {
    @apply transition-transform duration-300 ease-in-out;
  }

  #sidebar.sidebar-collapsed {
    @apply -translate-x-full;
  }

  /* Adjust main content when sidebar is collapsed with animation */
  #main-content {
    @apply transition-all duration-300 ease-in-out;
  }

  #sidebar.sidebar-collapsed ~ #main-content {
    @apply lg:ml-0;
  }

  /* Mobile sidebar media query */
  @media (max-width: 1023px) {
    #sidebar {
      @apply -translate-x-full;
    }

    #sidebar.mobile-open {
      @apply translate-x-0 shadow-lg;
    }

    #main-content {
      @apply ml-0 !important;
    }
  }

  /* Mobile sidebar media query */
  @media (max-width: 1024px) {
    #sidebar {
      transform: translateX(-100%);
      transition: transform 0.3s ease;
    }

    #sidebar.mobile-open {
      transform: translateX(0);
      box-shadow: 0 0 15px rgba(0, 0, 0, 0.2);
    }

    #main-content {
      margin-left: 0 !important;
    }
  }

  /* ---------- Drawer styles ---------- */
  .drawer-overlay {
    @apply fixed inset-0 z-50 bg-black/50 backdrop-blur-sm transition-opacity duration-300;
  }

  .drawer-overlay[data-state="open"] {
    @apply opacity-100;
  }

  .drawer-overlay[data-state="closed"] {
    @apply opacity-0;
  }

  .drawer-content {
    @apply fixed z-50 gap-4 bg-background p-6 shadow-lg transition-all duration-300 ease-in-out;
  }

  .dark .drawer-content {
    @apply shadow-none;
  }

  /* Drawer positions */
  .drawer-right {
    @apply inset-y-0 right-0 h-full w-3/4 border-l sm:max-w-md;
  }

  .dark .drawer-right {
    @apply border-border;
  }

  .drawer-right[data-state="open"] {
    @apply translate-x-0;
  }

  .drawer-right[data-state="closed"] {
    @apply translate-x-full;
  }

  .drawer-left {
    @apply inset-y-0 left-0 h-full w-3/4 border-r sm:max-w-md;
  }

  .dark .drawer-left {
    @apply border-border;
  }

  .drawer-left[data-state="open"] {
    @apply translate-x-0;
  }

  .drawer-left[data-state="closed"] {
    @apply -translate-x-full;
  }

  .drawer-top {
    @apply inset-x-0 top-0 h-auto border-b;
  }

  .dark .drawer-top {
    @apply border-border;
  }

  .drawer-top[data-state="open"] {
    @apply translate-y-0;
  }

  .drawer-top[data-state="closed"] {
    @apply -translate-y-full;
  }

  .drawer-bottom {
    @apply inset-x-0 bottom-0 h-auto border-t;
  }

  .dark .drawer-bottom {
    @apply border-border;
  }

  .drawer-bottom[data-state="open"] {
    @apply translate-y-0;
  }

  .drawer-bottom[data-state="closed"] {
    @apply translate-y-full;
  }

  /* ---------- Table styles ---------- */
  .table-modern {
    @apply w-full text-left;
  }

  .table-modern thead {
    @apply bg-muted text-muted-foreground;
  }

  .table-modern th {
    @apply px-6 py-3 text-xs font-medium uppercase tracking-wider;
  }

  .table-modern tbody {
    @apply divide-y divide-border;
  }

  .dark .table-modern tbody {
    @apply divide-border;
  }

  .table-modern tr {
    @apply transition-colors hover:bg-muted/50;
  }

  .table-modern td {
    @apply px-6 py-4 whitespace-nowrap;
  }

  /* ---------- Badge styles ---------- */
  .badge {
    @apply inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium transition-colors;
  }

  .badge-primary {
    @apply bg-primary/10 text-primary;
    /* Ensure it uses the CSS variable */
    background-color: rgba(var(--primary-rgb), 0.1);
    color: hsl(var(--primary));
  }

  .badge-secondary {
    @apply bg-secondary text-secondary-foreground;
  }

  .badge-success {
    @apply bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400;
  }

  .badge-danger {
    @apply bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400;
  }

  .badge-warning {
    @apply bg-yellow-100 text-yellow-800 dark:bg-orange-500/30 dark:text-white;
  }

  .badge-info {
    @apply bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400;
  }

  .badge-destructive {
    @apply bg-destructive/10 text-destructive;
  }

  /* ---------- Avatar styles ---------- */
  .avatar {
    @apply relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full;
  }

  .avatar-sm {
    @apply h-8 w-8;
  }

  .avatar-lg {
    @apply h-12 w-12;
  }

  .avatar-image {
    @apply aspect-square h-full w-full;
  }

  .avatar-fallback {
    @apply flex h-full w-full items-center justify-center rounded-full bg-muted;
  }

  /* ---------- Alert styles ---------- */
  .alert {
    @apply relative w-full rounded-lg border p-4 [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg+div]:translate-y-[-3px] [&>svg~*]:pl-7;
  }

  .dark .alert {
    @apply border-border shadow-none;
  }

  .alert-error {
    @apply border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive;
  }

  .alert-warning {
    @apply border-yellow-600/50 text-yellow-600 dark:border-yellow-600 [&>svg]:text-yellow-600;
  }

  .alert-success {
    @apply border-green-600/50 text-green-600 dark:border-green-600 [&>svg]:text-green-600;
  }

  /* ---------- Dark mode utilities ---------- */
  .dark-card-bg {
    background-color: hsl(224, 71%, 4%);
  }

  .dark-muted-bg {
    background-color: hsl(217, 33%, 17%);
  }

  .dark-background {
    background-color: hsl(224, 71%, 4%);
  }

  /* ---------- Notification badge ---------- */
  .notification-badge {
    @apply absolute -top-1 -right-1 h-5 w-5 rounded-full bg-primary text-xs text-white flex items-center justify-center;
  }

  /* Custom styles for dot indicators */
  .dot-indicator {
    position: absolute;
    top: 8px;
    right: 8px;
    height: 8px;
    width: 8px;
    border-radius: 9999px;
    background-color: hsl(217, 91%, 60%);
    border: 1px solid hsl(224, 71%, 12%);
    z-index: 10;
  }

  /* Ensure links containing dot indicators have proper positioning */
  a.has-dot-indicator {
    position: relative;
  }

  /* Consistent positioning in collapsed sidebar */
  #sidebar.sidebar-collapsed a.has-dot-indicator .dot-indicator {
    right: 4px;
    top: 8px;
  }

  /* ---------- Toast notifications ---------- */
  /* Toast styles moved to dedicated toast.css file */
}

/* ====================
   UTILITIES LAYER
   ==================== */
@layer utilities {
  /* Debug information panel styles for neutral colors */
  .neutral-primary .bg-primary\/10,
  .neutral-primary .bg-primary\/20 {
    background-color: rgba(0, 0, 0, 0.1) !important;
  }

  .dark.neutral-primary .bg-primary\/10,
  .dark.neutral-primary .bg-primary\/20 {
    background-color: rgba(255, 255, 255, 0.1) !important;
  }

  /* Debug tabs */
  .neutral-primary .debug-tab.active {
    color: #000000 !important;
  }

  .neutral-primary .debug-tab.active:after {
    background-color: #000000 !important;
  }

  .neutral-primary .debug-tab:hover {
    background-color: rgba(0, 0, 0, 0.05) !important;
  }

  .dark.neutral-primary .debug-tab.active {
    color: #ffffff !important;
  }

  .dark.neutral-primary .debug-tab.active:after {
    background-color: #ffffff !important;
  }

  .dark.neutral-primary .debug-tab:hover {
    background-color: rgba(255, 255, 255, 0.05) !important;
  }

  /* Debug icons and borders */
  .neutral-primary i.text-primary {
    color: #000000 !important;
  }

  .dark.neutral-primary i.text-primary {
    color: #ffffff !important;
  }

  /* Any additional utility classes can go here */

  /* Flash Messages Styles */
  .flash-message {
    animation: flashSlideIn 0.2s ease-out;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    min-width: 300px;
    transition: all 0.3s ease;
  }

  .flash-message:hover {
    box-shadow: 0 5px 16px rgba(0, 0, 0, 0.15);
  }

  @keyframes flashSlideIn {
    from {
      transform: translateY(10px);
      opacity: 0;
    }
    to {
      transform: translateY(0);
      opacity: 1;
    }
  }

  /* Custom date input styles */
  input[type="date"] {
    position: relative;
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
  }

  input[type="date"]::-webkit-calendar-picker-indicator {
    opacity: 0;
    position: absolute;
    top: 0;
    right: 0;
    width: 100%;
    height: 100%;
    cursor: pointer;
  }

  /* Calendar popup styles */
  :root {
    color-scheme: light;
  }

  .dark {
    color-scheme: dark;
  }

  /* Style for the calendar icon container */
  input[type="date"] + div {
    pointer-events: none;
  }

  /* Custom calendar popup styles */
  ::-webkit-datetime-edit { padding: 0; }
  ::-webkit-datetime-edit-fields-wrapper { background: transparent; }
  ::-webkit-datetime-edit-text { color: hsl(var(--muted-foreground)); }
  ::-webkit-datetime-edit-month-field { color: hsl(var(--foreground)); }
  ::-webkit-datetime-edit-day-field { color: hsl(var(--foreground)); }
  ::-webkit-datetime-edit-year-field { color: hsl(var(--foreground)); }
  ::-webkit-inner-spin-button { display: none; }

  /* Drawer styles */
  .drawer {
    display: none;
    position: fixed;
    z-index: 50;
    top: 0;
    right: 0;
    bottom: 0;
    width: 30rem;
    max-width: 100vw;
    background-color: hsl(var(--background));
    box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);
    border-left: 1px solid hsl(var(--border));
    overflow-y: auto;
  }

  .drawer-content {
    display: flex;
    flex-direction: column;
    height: 100%;
  }
}

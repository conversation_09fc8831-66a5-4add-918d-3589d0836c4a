"""
Admin testing routes for error handling and debugging.
"""

from flask import render_template, request
from flask_login import login_required

from app import db
from app.models import User, Activity
from app.utils.decorators import admin_required, log_activity
from app.routes.admin import admin_bp


@admin_bp.route('/test-error')
@login_required
@admin_required
@log_activity('Testing error logging', entity_type='ErrorTest', category=Activity.CATEGORY_SYSTEM)
def test_error():
    """Intentionally trigger a division by zero error to test error logging."""
    # Intentionally trigger a division by zero error
    result = 1 / 0
    return f"This should never be returned: {result}"


@admin_bp.route('/test-update-error/<int:user_id>', methods=['GET', 'POST'])
@login_required
@admin_required
@log_activity('Updated user with error', entity_type='User', category=Activity.CATEGORY_DATA, track_changes=True)
def test_update_error(user_id):
    """Test error handling in activity logging with database changes."""
    # Get the user first (this will be captured as old_values)
    user = User.query.get_or_404(user_id)

    if request.method == 'GET':
        # Show a simple form for testing
        return render_template('admin/test_error_form.html', user=user)
    else:  # POST
        # Make some changes
        user.name = request.form.get('name', user.name)
        user.email = request.form.get('email', user.email)

        # Commit the changes to the database
        db.session.commit()

        # Now trigger an error after the commit
        # This tests if we can still log the changes even when an error occurs later

        # This will raise a NameError - we're not catching it here to test the decorator's error handling
        non_existent_variable = undefined_variable  # This will raise a NameError

        # This should never be reached
        return "This should never be returned"



@admin_bp.route('/isolated-modal-test')
@login_required
@admin_required
def isolated_modal_test():
    """Isolated modal test page."""
    return render_template('admin/isolated_modal_test.html')

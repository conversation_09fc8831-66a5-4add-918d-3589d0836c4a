/**
 * Theme transition styles
 * Advanced performance optimizations by expert frontend developer
 */

/* Define CSS variables for transition properties to ensure consistency */
:root {
  --theme-transition-duration: 0.12s;
  --theme-transition-timing: cubic-bezier(0.4, 0, 0.2, 1);
  --theme-transition-properties: background-color, border-color, color;
}

/* Use GPU acceleration for the entire document during theme transition */
.theme-transition {
  /* Force hardware acceleration */
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
}

/* Apply containment to major layout sections for better performance */
.theme-transition #main-content,
.theme-transition .sidebar,
.theme-transition .page-header,
.theme-transition .content-container {
  contain: content;
  will-change: contents;
}

/* Optimize high-impact elements with targeted transitions */
.theme-transition .bg-background,
.theme-transition .bg-card,
.theme-transition .bg-muted,
.theme-transition .border-border,
.theme-transition .text-foreground,
.theme-transition .text-muted-foreground {
  transition: var(--theme-transition-properties) var(--theme-transition-duration) var(--theme-transition-timing);
  /* Use contain: paint to limit paint area */
  contain: paint;
}

/* Optimize button transitions with GPU acceleration */
.theme-transition .btn-primary,
.theme-transition button,
.theme-transition .page-header-button {
  transition: background-color var(--theme-transition-duration) var(--theme-transition-timing),
              border-color var(--theme-transition-duration) var(--theme-transition-timing),
              color var(--theme-transition-duration) var(--theme-transition-timing),
              box-shadow var(--theme-transition-duration) var(--theme-transition-timing);
  /* Force GPU rendering */
  transform: translateZ(0);
  /* Limit paint area */
  contain: paint;
}

/* Optimize icon transitions with GPU acceleration */
.theme-transition [data-lucide] {
  transition: color var(--theme-transition-duration) var(--theme-transition-timing),
              stroke var(--theme-transition-duration) var(--theme-transition-timing);
  will-change: color, stroke;
  /* Force GPU rendering */
  transform: translateZ(0);
  /* Limit paint area */
  contain: strict;
}

/* Optimize form elements with targeted transitions */
.theme-transition input,
.theme-transition select,
.theme-transition textarea {
  transition: background-color var(--theme-transition-duration) var(--theme-transition-timing),
              border-color var(--theme-transition-duration) var(--theme-transition-timing),
              color var(--theme-transition-duration) var(--theme-transition-timing),
              box-shadow var(--theme-transition-duration) var(--theme-transition-timing);
  /* Limit paint area */
  contain: paint;
}

/* Completely disable transitions for elements that cause flickering */
.theme-transition img,
.theme-transition svg:not([data-lucide]),
.theme-transition video,
.theme-transition canvas,
.theme-transition iframe {
  transition: none !important;
  /* Prevent any animation */
  animation: none !important;
  /* Prevent any transition */
  will-change: auto !important;
}

/* Optimize table transitions */
.theme-transition table,
.theme-transition tr,
.theme-transition td,
.theme-transition th {
  transition: background-color var(--theme-transition-duration) var(--theme-transition-timing),
              border-color var(--theme-transition-duration) var(--theme-transition-timing),
              color var(--theme-transition-duration) var(--theme-transition-timing);
  contain: paint;
}

/* Optimize modal and drawer transitions */
.theme-transition .modal,
.theme-transition .drawer {
  transition: background-color var(--theme-transition-duration) var(--theme-transition-timing),
              border-color var(--theme-transition-duration) var(--theme-transition-timing),
              color var(--theme-transition-duration) var(--theme-transition-timing);
  /* Force GPU rendering */
  transform: translateZ(0);
  /* Limit paint area */
  contain: paint;
}

/* Optimize toast transitions */
.theme-transition .toast,
.theme-transition .toast-container {
  transition: background-color var(--theme-transition-duration) var(--theme-transition-timing),
              border-color var(--theme-transition-duration) var(--theme-transition-timing),
              color var(--theme-transition-duration) var(--theme-transition-timing);
  /* Force GPU rendering */
  transform: translateZ(0);
  /* Limit paint area */
  contain: paint;
}

/* Optimize sidebar transitions */
.theme-transition .sidebar,
.theme-transition .sidebar-item,
.theme-transition .sidebar-submenu {
  transition: background-color var(--theme-transition-duration) var(--theme-transition-timing),
              border-color var(--theme-transition-duration) var(--theme-transition-timing),
              color var(--theme-transition-duration) var(--theme-transition-timing);
  /* Force GPU rendering */
  transform: translateZ(0);
  /* Limit paint area */
  contain: paint;
}

/* Optimize badge transitions */
.theme-transition .badge,
.theme-transition .badge-primary,
.theme-transition .badge-secondary,
.theme-transition .badge-warning,
.theme-transition .badge-danger,
.theme-transition .badge-success,
.theme-transition .badge-info {
  transition: background-color var(--theme-transition-duration) var(--theme-transition-timing),
              border-color var(--theme-transition-duration) var(--theme-transition-timing),
              color var(--theme-transition-duration) var(--theme-transition-timing);
  /* Force GPU rendering */
  transform: translateZ(0);
  /* Limit paint area */
  contain: paint;
}

# Matrix App - Employee Management System

[![Python](https://img.shields.io/badge/Python-3.11+-blue.svg)](https://python.org)
[![Flask](https://img.shields.io/badge/Flask-3.0.2-green.svg)](https://flask.palletsprojects.com/)
[![Tailwind CSS](https://img.shields.io/badge/Tailwind%20CSS-3.3.2-blue.svg)](https://tailwindcss.com/)
[![License](https://img.shields.io/badge/License-MIT-yellow.svg)](LICENSE)

**Last Updated:** January 2025  
**Version:** 1.0.0

## Overview

Matrix App is a comprehensive employee management system built with Flask and modern web technologies. It provides a robust platform for managing organizational structure, employee details, teams, attendance tracking, and system administration with a focus on scalability, security, and user experience.

### Key Features

- **User Management**: Role-based authentication (Admin, Manager, User) with comprehensive user profiles
- **Organizational Structure**: Business units and segments with hierarchical management
- **Team Management**: Cross-functional teams with flexible member assignment
- **Employee Details**: Comprehensive employee profiles with manager relationships
- **Attendance System**: Holiday management, work schedules, and attendance tracking
- **Activity Logging**: Comprehensive audit trail for all system activities
- **Settings Management**: Configurable system settings with public/private visibility
- **Modern UI**: Responsive design with dark/light mode support using Tailwind CSS and shadcn UI
- **API-First**: RESTful API endpoints for all major functionality
- **Security**: Rate limiting, CSRF protection, secure session management

## Quick Start

### Prerequisites

- Python 3.11 or higher
- Node.js 16+ (for frontend build tools)
- SQLite (default) or PostgreSQL/MySQL for production
- Redis (optional, for production caching and rate limiting)

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd matrix-app
   ```

2. **Set up Python environment**
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   pip install -r requirements.txt
   ```

3. **Set up Node.js dependencies**
   ```bash
   npm install
   ```

4. **Environment configuration**
   ```bash
   cp production.env.example .env
   # Edit .env with your configuration
   ```

5. **Initialize the database**
   ```bash
   flask db upgrade
   python run.py init-db
   ```

6. **Build frontend assets**
   ```bash
   npm run build
   ```

7. **Run the application**
   ```bash
   python run.py
   ```

The application will be available at `http://127.0.0.1:5003`

### Default Admin Account

- **Email**: <EMAIL>
- **Password**: Admin@123

⚠️ **Important**: Change the default admin password immediately after first login.

## Development Setup

### Frontend Development

For active frontend development with hot reloading:

```bash
# Watch CSS changes
npm run watch:css

# Watch JavaScript changes
npm run watch:js

# Run both simultaneously
npm run dev
```

### Database Management

```bash
# Create a new migration
flask db migrate -m "Description of changes"

# Apply migrations
flask db upgrade

# Seed database with sample data
python seed.py
```

### Testing

```bash
# Run all tests
python run_tests.py

# Run specific test file
python -m pytest tests/test_users.py -v
```

## Project Structure

```
matrix-app/
├── app/                    # Main application package
│   ├── models/            # Database models
│   ├── routes/            # Route blueprints
│   ├── templates/         # Jinja2 templates
│   ├── static/            # Static assets
│   └── utils/             # Utility functions
├── docs/                  # Documentation
│   └── core/             # Core documentation files
├── migrations/            # Database migrations
├── seeders/              # Database seeders
├── tests/                # Test files
├── config.py             # Configuration settings
├── run.py                # Application entry point
└── requirements.txt      # Python dependencies
```

## Configuration

The application uses environment variables for configuration. Key settings include:

- `FLASK_CONFIG`: Environment (development/production/testing)
- `SECRET_KEY`: Flask secret key for sessions
- `DATABASE_URL`: Database connection string
- `MAIL_*`: Email configuration for notifications
- `REDIS_URL`: Redis connection for caching (optional)

See `config.py` for all available configuration options.

## Core Technologies

- **Backend**: Flask 3.0.2, SQLAlchemy 2.0.27, Flask-Login, Flask-WTF
- **Frontend**: Tailwind CSS 3.3.2, shadcn UI components, Lucide icons
- **Database**: SQLite (development), PostgreSQL/MySQL (production)
- **Build Tools**: esbuild, PostCSS, Autoprefixer
- **Security**: Flask-Limiter, CSRF protection, secure sessions

## Documentation

- [Architecture Guide](ARCHITECTURE.md) - System design and patterns
- [Technology Stack](TECH_STACK.md) - Detailed technology overview
- [File Structure](FILE_STRUCTURE.md) - Project organization
- [API Documentation](API_DOCUMENTATION.md) - API endpoints and usage
- [Database Schema](DATABASE_SCHEMA.md) - Database design
- [Development Guidelines](DEVELOPMENT_GUIDELINES.md) - Coding standards
- [Deployment Guide](DEPLOYMENT.md) - Production deployment
- [Troubleshooting](TROUBLESHOOTING.md) - Common issues and solutions

## Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Follow the [Development Guidelines](DEVELOPMENT_GUIDELINES.md)
4. Commit your changes (`git commit -m 'Add amazing feature'`)
5. Push to the branch (`git push origin feature/amazing-feature`)
6. Open a Pull Request

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Support

For support and questions:
- Check the [Troubleshooting Guide](TROUBLESHOOTING.md)
- Review existing documentation
- Create an issue for bugs or feature requests

---

**Matrix App** - Building better organizational management, one feature at a time.

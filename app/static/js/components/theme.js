/**
 * Theme management for the application
 * Handles theme switching and persistence
 */

// No longer using module imports

// Global variables
let themeChangeTimer = null;
let themeChangeRAF = null;
let isChangingTheme = false;
let isInRAFCallback = false;
let systemThemeMediaQuery = null;
let domObserver = null;

document.addEventListener('DOMContentLoaded', function() {
  initThemeSystem();
});

/**
 * Initialize the theme system
 */
function initThemeSystem() {
  // Ensure theme transition class is applied
  document.documentElement.classList.add('theme-transition');

  // Set up theme toggle with explicit binding
  const themeToggle = document.getElementById('theme-toggle');

  if (themeToggle) {
    // Remove any existing click listeners to prevent duplicates
    themeToggle.removeEventListener('click', toggleTheme);

    // Add the click listener with explicit binding
    themeToggle.addEventListener('click', toggleTheme, false);
  }

  // Update theme toggle icon to match current theme
  updateThemeToggleIcon();

  // Set up system theme change listener
  setupSystemThemeListener();

  // Set up DOM mutation observer
  setupDomObserver();
}

/**
 * Get the current theme preference
 * @returns {string} The current theme ('dark' or 'light')
 */
function getThemePreference() {
  const savedTheme = localStorage.getItem('theme');
  const defaultTheme = document.documentElement.getAttribute('data-default-theme') || 'system';
  const systemPrefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;

  if (savedTheme) {
    return savedTheme;
  } else if (defaultTheme === 'system') {
    return systemPrefersDark ? 'dark' : 'light';
  } else {
    return defaultTheme;
  }
}

/**
 * Toggle between light and dark themes
 */
function toggleTheme(event) {
  // Prevent default behavior if it's an event
  if (event && event.preventDefault) {
    event.preventDefault();
  }

  // Prevent rapid toggling
  if (isChangingTheme || isInRAFCallback) {
    return;
  }

  const html = document.documentElement;
  const currentTheme = html.classList.contains('dark') ? 'dark' : 'light';
  const newTheme = currentTheme === 'dark' ? 'light' : 'dark';

  // Set flag to prevent multiple rapid changes
  isChangingTheme = true;

  // Cancel any pending animation frames
  if (themeChangeRAF) {
    cancelAnimationFrame(themeChangeRAF);
    themeChangeRAF = null;
  }

  // Apply the theme change immediately
  setTheme(newTheme);

  // Update the toggle icon immediately
  updateThemeToggleIcon();

  // Use requestAnimationFrame for cleanup
  themeChangeRAF = requestAnimationFrame(() => {
    // After the frame completes, reset flags
    isInRAFCallback = false;
    isChangingTheme = false;
    themeChangeRAF = null;
  });
}

/**
 * Update the theme toggle icon based on current theme
 */
function updateThemeToggleIcon() {
  const themeToggle = document.getElementById('theme-toggle');
  if (!themeToggle) return;

  // Clear existing content
  themeToggle.innerHTML = '';

  // Get current theme
  const isDarkMode = document.documentElement.classList.contains('dark');
  const iconName = isDarkMode ? 'sun' : 'moon';

  // Create the icon element
  if (window.iconUtils && window.iconUtils.createIcon) {
    const iconElement = window.iconUtils.createIcon(iconName, {
      class: 'w-5 h-5 pointer-events-none'
    });
    themeToggle.appendChild(iconElement);
  } else if (typeof lucide !== 'undefined' && lucide.createIcons) {
    // Fallback to using Lucide directly
    const iconElement = document.createElement('i');
    iconElement.setAttribute('data-lucide', iconName);
    iconElement.className = 'w-5 h-5 pointer-events-none';
    themeToggle.appendChild(iconElement);

    // Initialize the icon
    lucide.createIcons({
      elements: [iconElement]
    });
  }
}

/**
 * Set the theme to either 'dark' or 'light'
 * @param {string} theme - The theme to apply ('dark' or 'light')
 */
function setTheme(theme) {
  const html = document.documentElement;
  const currentTheme = html.classList.contains('dark') ? 'dark' : 'light';

  // Only make changes if the theme is actually changing
  if (theme !== currentTheme) {
    // Performance optimization: Mark the body as changing theme
    // This allows CSS selectors to optimize rendering
    document.body.setAttribute('data-theme-changing', 'true');

    // Ensure transition class is applied
    if (!html.classList.contains('theme-transition')) {
      html.classList.add('theme-transition');
    }

    // Performance optimization: Use custom events to notify
    // other parts of the application about theme changes
    const themeChangeStart = new CustomEvent('themeChangeStart', {
      detail: { theme, previousTheme: currentTheme },
      bubbles: false,
      cancelable: false
    });
    document.dispatchEvent(themeChangeStart);

    // Apply the theme change
    if (theme === 'dark') {
      html.classList.add('dark');
    } else {
      html.classList.remove('dark');
    }

    // Store the user's preference
    localStorage.setItem('theme', theme);

    // Performance optimization: Use requestAnimationFrame to ensure
    // the theme change is applied before dispatching the event
    requestAnimationFrame(() => {
      // Dispatch a custom event that other scripts can listen for
      const themeChanged = new CustomEvent('themeChanged', {
        detail: { theme, previousTheme: currentTheme },
        bubbles: false,
        cancelable: false
      });
      document.dispatchEvent(themeChanged);

      // Remove the theme changing attribute
      document.body.removeAttribute('data-theme-changing');

      // Debounce style updates to prevent multiple rapid updates
      clearTimeout(themeChangeTimer);
      themeChangeTimer = setTimeout(() => {
        // Convert legacy button classes
        convertLegacyButtonClasses();

        // Dispatch a final event when all updates are complete
        const themeChangeComplete = new CustomEvent('themeChangeComplete', {
          detail: { theme },
          bubbles: false,
          cancelable: false
        });
        document.dispatchEvent(themeChangeComplete);
      }, 50);
    });
  }
}

/**
 * Convert legacy button classes to new ones
 */
function convertLegacyButtonClasses() {
  // Get all buttons with legacy classes
  const legacyButtons = document.querySelectorAll('.btn-blue:not(.btn-primary)');

  // Convert legacy classes
  legacyButtons.forEach(button => {
    button.classList.remove('btn-blue');
    button.classList.add('btn-primary');
  });
}

/**
 * Set up the system theme change listener
 */
function setupSystemThemeListener() {
  // Clean up existing listener if it exists
  if (systemThemeMediaQuery) {
    systemThemeMediaQuery.removeEventListener('change', handleSystemThemeChange);
  }

  // Listen for system color scheme changes with performance optimizations
  systemThemeMediaQuery = window.matchMedia('(prefers-color-scheme: dark)');

  // Use a debounced handler for system theme changes
  let systemThemeChangeTimer = null;
  function handleSystemThemeChange(event) {
    // Only update if the user hasn't set a preference
    if (!localStorage.getItem('theme')) {
      // Prevent rapid changes by debouncing
      clearTimeout(systemThemeChangeTimer);
      systemThemeChangeTimer = setTimeout(() => {
        // Cancel any pending theme changes
        if (themeChangeRAF) {
          cancelAnimationFrame(themeChangeRAF);
          themeChangeRAF = null;
        }

        // Apply the new theme
        const newTheme = event.matches ? 'dark' : 'light';

        // Use requestAnimationFrame for better performance
        themeChangeRAF = requestAnimationFrame(() => {
          setTheme(newTheme);
          updateThemeToggleIcon();
          themeChangeRAF = null;
        });
      }, 50);
    }
  }

  // Add the event listener with passive option for better performance
  systemThemeMediaQuery.addEventListener('change', handleSystemThemeChange, { passive: true });
}

/**
 * Set up the DOM mutation observer
 */
function setupDomObserver() {
  // Clean up existing observer if it exists
  if (domObserver) {
    domObserver.disconnect();
  }

  // Track if we have a pending update
  let pendingUpdate = false;
  // Track if we need to process buttons
  let needsButtonUpdate = false;

  // Create a mutation observer that batches updates
  domObserver = new MutationObserver((mutations) => {
    // Skip if we already have a pending update
    if (pendingUpdate) return;

    // Check all mutations in a single pass for better performance
    for (const mutation of mutations) {
      if (mutation.addedNodes && mutation.addedNodes.length > 0) {
        // Check for buttons in added nodes
        for (const node of mutation.addedNodes) {
          if (node.nodeType !== 1) continue; // Skip non-element nodes

          // Check for buttons
          if (!needsButtonUpdate && (
              node.classList?.contains('btn-primary') ||
              node.classList?.contains('btn-blue') ||
              node.classList?.contains('page-header-button') ||
              (node.getAttribute && node.getAttribute('onclick')?.includes('drawerManager.openForm'))
          )) {
            needsButtonUpdate = true;
            break;
          }
        }
      }

      // If we need updates, we can stop checking mutations
      if (needsButtonUpdate) break;
    }

    // Schedule updates if needed
    if (needsButtonUpdate) {
      pendingUpdate = true;

      // Use requestAnimationFrame for better performance
      requestAnimationFrame(() => {
        // Update buttons if needed
        if (needsButtonUpdate) {
          convertLegacyButtonClasses();
        }

        // Reset flags
        pendingUpdate = false;
        needsButtonUpdate = false;
      });
    }
  });

  // Start observing the document with optimized parameters
  domObserver.observe(document.body, {
    childList: true,
    subtree: true,
    characterData: false, // We don't need character data changes
    attributes: false,    // We don't need attribute changes
    attributeOldValue: false,
    characterDataOldValue: false
  });
}

// Make functions available globally
window.themeUtils = {
  getThemePreference,
  setTheme,
  toggleTheme,
  updateThemeToggleIcon
};

{% extends "base.html" %}

{% block title %}Dashboard{% endblock %}

{% block header %}Dashboard{% endblock %}

{% block content %}
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-8">
  <div class="card">
      <div class="p-6">
          <div class="flex items-center">
              <div class="p-3 rounded-full bg-primary/10 flex items-center justify-center" aria-label="My Team Size">
                  <i data-lucide="users" class="w-6 h-6 text-primary" aria-hidden="true"></i>
              </div>
              <div class="ml-4">
                  <p class="text-sm text-muted-foreground">My Team Size</p>
                  <p class="text-2xl font-semibold">{{ team_count if team_count is defined else 'N/A' }}</p>
              </div>
          </div>
      </div>
  </div>

  <div class="card">
      <div class="p-6">
          <div class="flex items-center">
              <div class="p-3 rounded-full bg-primary/10 flex items-center justify-center" aria-label="Business Unit">
                  <i data-lucide="building" class="w-6 h-6 text-primary" aria-hidden="true"></i>
              </div>
              <div class="ml-4">
                  <p class="text-sm text-muted-foreground">Business Unit</p>
                  <p class="text-lg font-semibold">{{ employee_detail.business_unit.name if employee_detail and employee_detail.business_unit else 'Not Assigned' }}</p>
              </div>
          </div>
      </div>
  </div>

  <div class="card">
      <div class="p-6">
          <div class="flex items-center">
              <div class="p-3 rounded-full bg-primary/10 flex items-center justify-center" aria-label="Business Segment">
                  <i data-lucide="layers" class="w-6 h-6 text-primary" aria-hidden="true"></i>
              </div>
              <div class="ml-4">
                  <p class="text-sm text-muted-foreground">Business Segment</p>
                  <p class="text-lg font-semibold">{{ employee_detail.business_segment.name if employee_detail and employee_detail.business_segment else 'Not Assigned' }}</p>
              </div>
          </div>
      </div>
  </div>
</div>

<div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
  <div class="lg:col-span-2 card">
      <div class="card-header">
          <h3 class="card-title text-lg">Welcome, {{ current_user.name }}</h3>
          <p class="text-sm text-muted-foreground">Here's an overview of your information</p>
      </div>
      <div class="card-content">
          <div class="space-y-4">
              {% if employee_detail %}
                  <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                          <h4 class="text-sm font-medium text-muted-foreground">Position</h4>
                          <p>{{ employee_detail.position or 'Not specified' }}</p>
                      </div>
                      <div>
                          <h4 class="text-sm font-medium text-muted-foreground">Department</h4>
                          <p>{{ employee_detail.department or 'Not specified' }}</p>
                      </div>
                      <div>
                          <h4 class="text-sm font-medium text-muted-foreground">Phone</h4>
                          <p>{{ employee_detail.phone or 'Not specified' }}</p>
                      </div>
                      <div>
                          <h4 class="text-sm font-medium text-muted-foreground">Hire Date</h4>
                          <p>{{ employee_detail.hire_date.strftime('%Y-%m-%d') if employee_detail.hire_date else 'Not specified' }}</p>
                      </div>
                  </div>
                  {% if employee_detail.bio %}
                      <div>
                          <h4 class="text-sm font-medium text-muted-foreground">Bio</h4>
                          <p>{{ employee_detail.bio }}</p>
                      </div>
                  {% endif %}
              {% else %}
                  <div class="p-4 bg-muted/50 rounded-md">
                      <p>Your employee details have not been set up yet. Please contact an administrator.</p>
                  </div>
              {% endif %}
          </div>
      </div>
  </div>

  <div class="card">
      <div class="card-header">
          <h3 class="card-title text-lg">Recent Activities</h3>
      </div>
      <div class="card-content space-y-4">
          {% if recent_activities %}
              {% for activity in recent_activities %}
              <div class="flex items-start">
                  <div class="w-10 h-10 rounded-full bg-primary/10 flex items-center justify-center">
                      {% if activity.category == 'ATTENDANCE' %}
                          <i data-lucide="calendar-check" class="w-5 h-5 text-primary" aria-hidden="true"></i>
                      {% elif activity.category == 'USER' %}
                          <i data-lucide="user" class="w-5 h-5 text-primary" aria-hidden="true"></i>
                      {% elif activity.category == 'ADMIN' %}
                          <i data-lucide="settings" class="w-5 h-5 text-primary" aria-hidden="true"></i>
                      {% else %}
                          <i data-lucide="activity" class="w-5 h-5 text-primary" aria-hidden="true"></i>
                      {% endif %}
                  </div>
                  <div class="ml-3 flex-1">
                      <p class="text-sm font-medium">{{ activity.description }}</p>
                      <p class="text-xs text-muted-foreground">{{ activity.timestamp.strftime('%Y-%m-%d %H:%M') }}</p>
                      {% if activity.entity_type and activity.entity_id %}
                          <p class="text-xs text-muted-foreground">{{ activity.entity_type }} #{{ activity.entity_id }}</p>
                      {% endif %}
                  </div>
              </div>
              {% endfor %}
              <div class="pt-2">
                  <a href="{{ url_for('api_activities.list_activities') }}" class="text-sm text-primary hover:underline">View all activities</a>
              </div>
          {% else %}
              <div class="text-center py-4 text-muted-foreground">
                  No recent activities found
              </div>
          {% endif %}
      </div>
  </div>
</div>

<div class="grid grid-cols-1 mt-6">
  <div class="card">
      <div class="card-header">
          <h3 class="card-title text-lg">Quick Actions</h3>
      </div>
      <div class="card-content">
          <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
              <a href="{{ url_for('auth.profile') }}" class="inline-flex items-center justify-center p-4 rounded-md border border-gray-300 shadow-sm text-primary hover:bg-primary/5 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-colors h-auto">
                <div class="text-center">
                  <i data-lucide="user" class="w-6 h-6 mx-auto mb-2"></i>
                  <span class="text-sm">Profile</span>
                </div>
              </a>
              <a href="{{ url_for('teams.index') }}" class="inline-flex items-center justify-center p-4 rounded-md border border-gray-300 shadow-sm text-primary hover:bg-primary/5 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-colors h-auto">
                <div class="text-center">
                  <i data-lucide="users" class="w-6 h-6 mx-auto mb-2"></i>
                  <span class="text-sm">My Teams</span>
                </div>
              </a>
              <a href="#" class="inline-flex items-center justify-center p-4 rounded-md border border-gray-300 shadow-sm text-primary hover:bg-primary/5 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-colors h-auto">
                <div class="text-center">
                  <i data-lucide="calendar" class="w-6 h-6 mx-auto mb-2"></i>
                  <span class="text-sm">Attendance</span>
                </div>
              </a>
              <a href="{{ url_for('main.view_my_details') }}" class="inline-flex items-center justify-center p-4 rounded-md border border-gray-300 shadow-sm text-primary hover:bg-primary/5 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-colors h-auto">
                <div class="text-center">
                  <i data-lucide="user-cog" class="w-6 h-6 mx-auto mb-2"></i>
                  <span class="text-sm">View My Details</span>
                </div>
              </a>
          </div>
      </div>
  </div>
</div>
{% endblock %}

{% block scripts %}
<script>
  // Initialize icons
  document.addEventListener('DOMContentLoaded', function() {
      // Process icons if needed
      if (window.processIcons) {
          window.processIcons();
      }
  });
</script>
{% endblock %}

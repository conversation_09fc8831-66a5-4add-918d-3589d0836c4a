import unittest
from flask import url_for, request
from flask_testing import TestCase
from werkzeug.security import check_password_hash
import uuid

from app import create_app, db
from app.models import User, Activity, PasswordReset


class TestAuth(TestCase):
    """Test cases for authentication functionality"""

    def create_app(self):
        """Create and configure a Flask app for testing"""
        app = create_app()
        app.config.update(
            TESTING=True,
            SQLALCHEMY_DATABASE_URI='sqlite:///:memory:',
            WTF_CSRF_ENABLED=False,  # Disable CSRF for testing
            SERVER_NAME='localhost.localdomain',  # Required for url_for to work in tests
            SECRET_KEY='test_secret_key'  # Use a fixed key for tests
        )
        with app.app_context():
            db.create_all()  # Create tables before tests run
        return app

    def setUp(self):
        """Set up test environment before each test"""
        db.session.remove()  # Remove any existing sessions
        db.drop_all()  # Make sure we start with a clean database
        db.create_all()  # Create all tables for this test

        # Create a test user
        test_user = User(
            name='Test User',
            email='<EMAIL>',
            role='User'
        )
        test_user.set_password('Test@123')

        # Create an admin user for testing admin-specific features
        admin_user = User(
            name='Admin User',
            email='<EMAIL>',
            role='Admin'
        )
        admin_user.set_password('Admin@123')

        db.session.add(test_user)
        db.session.add(admin_user)
        db.session.commit()

    def tearDown(self):
        """Clean up test environment after each test"""
        db.session.remove()
        db.drop_all()

    def test_login_success(self):
        """Test successful login with correct credentials"""
        with self.client:
            response = self.client.post(
                '/auth/login',
                data={
                    'email': '<EMAIL>',
                    'password': 'Test@123',
                    'remember': 'true'
                },
                follow_redirects=True
            )

            # Check response
            self.assertEqual(response.status_code, 200)

            # Check that user was redirected to dashboard
            self.assertIn(b'User Dashboard', response.data)

            # Verify activity was logged
            activity = Activity.query.filter_by(action='User logged in').first()
            self.assertIsNotNone(activity)
            self.assertEqual(activity.user.email, '<EMAIL>')

    def test_login_failure_wrong_password(self):
        """Test login failure with incorrect password"""
        with self.client:
            response = self.client.post(
                '/auth/login',
                data={
                    'email': '<EMAIL>',
                    'password': 'WrongPassword',
                },
                follow_redirects=True
            )

            # Check response
            self.assertEqual(response.status_code, 200)

            # Verify error message is shown
            self.assertIn(b'Invalid email or password', response.data)

            # Verify activity was logged for failed attempt
            activity = Activity.query.filter_by(action='Failed login attempt').first()
            self.assertIsNotNone(activity)

    def test_login_inactive_account(self):
        """Test login attempt with an inactive account"""
        # Deactivate the test user
        user = User.query.filter_by(email='<EMAIL>').first()
        user.is_active = False
        db.session.commit()

        with self.client:
            response = self.client.post(
                '/auth/login',
                data={
                    'email': '<EMAIL>',
                    'password': 'Test@123',
                },
                follow_redirects=True
            )

            # Verify appropriate message is shown
            self.assertIn(b'Your account is inactive', response.data)

            # Verify activity was logged
            activity = Activity.query.filter_by(action='Login attempt to inactive account').first()
            self.assertIsNotNone(activity)

    def test_register_success(self):
        """Test successful user registration"""
        with self.client:
            response = self.client.post(
                '/auth/register',
                data={
                    'name': 'New User',
                    'email': '<EMAIL>',
                    'password': 'NewPass@123',
                    'confirm_password': 'NewPass@123'
                },
                follow_redirects=True
            )

            # Check response
            self.assertEqual(response.status_code, 200)

            # Verify success message
            self.assertIn(b'Registration successful', response.data)

            # Verify user was created in database
            user = User.query.filter_by(email='<EMAIL>').first()
            self.assertIsNotNone(user)
            self.assertEqual(user.name, 'New User')
            self.assertTrue(user.check_password('NewPass@123'))

            # Verify registration activity was logged
            activity = Activity.query.filter_by(action='User registered').first()
            self.assertIsNotNone(activity)

    def test_register_password_mismatch(self):
        """Test registration failure due to password mismatch"""
        with self.client:
            response = self.client.post(
                '/auth/register',
                data={
                    'name': 'New User',
                    'email': '<EMAIL>',
                    'password': 'NewPass@123',
                    'confirm_password': 'DifferentPass@123'
                },
                follow_redirects=True
            )

            # Verify error message
            self.assertIn(b'Passwords do not match', response.data)

            # Verify user wasn't created
            user = User.query.filter_by(email='<EMAIL>').first()
            self.assertIsNone(user)

    def test_register_weak_password(self):
        """Test registration failure due to weak password"""
        with self.client:
            response = self.client.post(
                '/auth/register',
                data={
                    'name': 'New User',
                    'email': '<EMAIL>',
                    'password': 'weak',
                    'confirm_password': 'weak'
                },
                follow_redirects=True
            )

            # Verify error message about password strength
            self.assertIn(b'Password must be at least 8 characters', response.data)

            # Verify user wasn't created
            user = User.query.filter_by(email='<EMAIL>').first()
            self.assertIsNone(user)

    def test_register_duplicate_email(self):
        """Test registration with already registered email"""
        with self.client:
            response = self.client.post(
                '/auth/register',
                data={
                    'name': 'Duplicate User',
                    'email': '<EMAIL>',  # This email is already used in setUp
                    'password': 'NewPass@123',
                    'confirm_password': 'NewPass@123'
                },
                follow_redirects=True
            )

            # Verify error message
            self.assertIn(b'Email already registered', response.data)

    def test_forgot_password(self):
        """Test forgot password functionality"""
        with self.client:
            response = self.client.post(
                '/auth/forgot-password',
                data={'email': '<EMAIL>'},
                follow_redirects=True
            )

            # Check response
            self.assertEqual(response.status_code, 200)

            # Verify success message (shown regardless of whether email exists)
            self.assertIn(b'If an account with that email exists', response.data)

            # Verify token was created
            reset_token = PasswordReset.query.filter_by(email='<EMAIL>').first()
            self.assertIsNotNone(reset_token)

            # Verify activity was logged
            activity = Activity.query.filter_by(action='Password reset requested').first()
            self.assertIsNotNone(activity)

    def test_reset_password(self):
        """Test password reset functionality"""
        # First create a reset token
        reset = PasswordReset.create_token('<EMAIL>')
        token = reset.token

        with self.client:
            # Test the reset password form submission
            response = self.client.post(
                f'/auth/reset-password/{token}',
                data={
                    'password': 'NewPass@456',
                    'confirm_password': 'NewPass@456'
                },
                follow_redirects=True
            )

            # Check response
            self.assertEqual(response.status_code, 200)

            # Verify success message
            self.assertIn(b'Your password has been reset successfully', response.data)

            # Verify password was changed
            user = User.query.filter_by(email='<EMAIL>').first()
            self.assertTrue(user.check_password('NewPass@456'))

            # Verify token was marked as used
            used_token = PasswordReset.query.filter_by(token=token).first()
            self.assertTrue(used_token.is_used)

            # Verify activity was logged
            activity = Activity.query.filter_by(action='Password reset completed').first()
            self.assertIsNotNone(activity)

    def test_reset_password_expired_token(self):
        """Test password reset with expired token"""
        # Create a reset token that doesn't exist in the system
        non_existent_token = str(uuid.uuid4())

        with self.client:
            # Try reset with a non-existent token (should redirect to forgot-password)
            response = self.client.get(
                f'/auth/reset-password/{non_existent_token}',
                follow_redirects=True
            )

            # We should see the forgot-password form after redirection
            self.assertEqual(response.status_code, 200)
            self.assertIn(b'forgot password', response.data.lower())
            self.assertIn(b'The password reset link is invalid or has expired', response.data)

    def test_logout(self):
        """Test user logout functionality"""
        with self.client:
            # First login
            response = self.client.post(
                '/auth/login',
                data={
                    'email': '<EMAIL>',
                    'password': 'Test@123'
                }
            )

            # Verify login was successful
            self.assertEqual(response.status_code, 302)  # Redirect after login

            # Then logout
            response = self.client.get('/auth/logout', follow_redirects=True)

            # Check response
            self.assertEqual(response.status_code, 200)

            # Verify success message
            self.assertIn(b'You have been logged out', response.data)

            # Verify we're on the login page
            self.assertIn(b'Login', response.data)

    def test_change_password(self):
        """Test changing password while logged in"""
        with self.client:
            # First login
            self.client.post(
                '/auth/login',
                data={
                    'email': '<EMAIL>',
                    'password': 'Test@123'
                }
            )

            # Then change password
            response = self.client.post(
                '/auth/change-password',
                data={
                    'current_password': 'Test@123',
                    'new_password': 'NewSecurePass@456',
                    'confirm_password': 'NewSecurePass@456'
                },
                follow_redirects=True
            )

            # Check response
            self.assertEqual(response.status_code, 200)

            # Verify success message
            self.assertIn(b'Password changed successfully', response.data)

            # Verify password was changed
            user = User.query.filter_by(email='<EMAIL>').first()
            self.assertTrue(user.check_password('NewSecurePass@456'))

            # Verify activity was logged
            activity = Activity.query.filter_by(action='Password changed').first()
            self.assertIsNotNone(activity)

    def test_admin_redirect(self):
        """Test that admin users are redirected to admin dashboard after login"""
        with self.client:
            response = self.client.post(
                '/auth/login',
                data={
                    'email': '<EMAIL>',
                    'password': 'Admin@123'
                },
                follow_redirects=True
            )

            # Check response
            self.assertEqual(response.status_code, 200)

            # Check that admin was redirected to admin dashboard
            # Looking for more specific content from the admin dashboard
            self.assertIn(b'ADMINISTRATION', response.data)
            self.assertIn(b'User Management', response.data)


if __name__ == '__main__':
    unittest.main()

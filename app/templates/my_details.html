{% extends "base.html" %}

{% block title %}My Details{% endblock %}

{% block header %}My Details{% endblock %}

{% block content %}
<div class="card">
  <div class="p-6 border-b border-border">
      <h2 class="text-lg font-semibold">Employee Information</h2>
      <p class="text-sm text-muted-foreground">Your employee details and assignments</p>
  </div>

  <div class="p-6">
      {% if employee_detail %}
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                  <h3 class="text-md font-medium mb-4">Personal Information</h3>
                  <div class="space-y-4">
                      <div>
                          <label class="block text-sm font-medium text-muted-foreground mb-1">Name</label>
                          <div class="p-2 bg-muted/30 rounded-md">{{ current_user.name }}</div>
                      </div>
                      <div>
                          <label class="block text-sm font-medium text-muted-foreground mb-1">Email</label>
                          <div class="p-2 bg-muted/30 rounded-md">{{ current_user.email }}</div>
                      </div>
                      <div>
                          <label class="block text-sm font-medium text-muted-foreground mb-1">Position</label>
                          <div class="p-2 bg-muted/30 rounded-md">{{ employee_detail.position or 'Not specified' }}</div>
                      </div>
                      <div>
                          <label class="block text-sm font-medium text-muted-foreground mb-1">Department</label>
                          <div class="p-2 bg-muted/30 rounded-md">{{ employee_detail.department or 'Not specified' }}</div>
                      </div>
                      <div>
                          <label class="block text-sm font-medium text-muted-foreground mb-1">Phone</label>
                          <div class="p-2 bg-muted/30 rounded-md">{{ employee_detail.phone or 'Not specified' }}</div>
                      </div>
                  </div>
              </div>

              <div>
                  <h3 class="text-md font-medium mb-4">Organization Information</h3>
                  <div class="space-y-4">
                      <div>
                          <label class="block text-sm font-medium text-muted-foreground mb-1">Business Unit</label>
                          <div class="p-2 bg-muted/30 rounded-md">{{ employee_detail.business_unit.name if employee_detail.business_unit else 'Not assigned' }}</div>
                      </div>
                      <div>
                          <label class="block text-sm font-medium text-muted-foreground mb-1">Business Segment</label>
                          <div class="p-2 bg-muted/30 rounded-md">{{ employee_detail.business_segment.name if employee_detail.business_segment else 'Not assigned' }}</div>
                      </div>
                      <div>
                          <label class="block text-sm font-medium text-muted-foreground mb-1">Hire Date</label>
                          <div class="p-2 bg-muted/30 rounded-md">{{ employee_detail.hire_date.strftime('%Y-%m-%d') if employee_detail.hire_date else 'Not specified' }}</div>
                      </div>
                      <div>
                          <label class="block text-sm font-medium text-muted-foreground mb-1">Role</label>
                          <div class="p-2 bg-muted/30 rounded-md">{{ current_user.role }}</div>
                      </div>
                  </div>
              </div>

              {% if employee_detail.bio %}
              <div class="md:col-span-2">
                  <h3 class="text-md font-medium mb-4">Bio</h3>
                  <div class="p-4 bg-muted/30 rounded-md">
                      {{ employee_detail.bio }}
                  </div>
              </div>
              {% endif %}
          </div>
      {% else %}
          <div class="p-6 bg-muted/30 rounded-md text-center">
              <i data-lucide="alert-circle" class="w-12 h-12 mx-auto mb-4 text-muted-foreground"></i>
              <h3 class="text-lg font-medium mb-2">No Employee Details Found</h3>
              <p class="text-muted-foreground mb-4">Your employee details have not been set up yet.</p>
              <p>Please contact an administrator to set up your employee details.</p>
          </div>
      {% endif %}
  </div>

  <div class="p-6 border-t border-border">
      <div class="flex justify-end">
          <a href="{{ url_for('main.user_dashboard') }}" class="inline-flex items-center justify-center px-4 py-2 text-sm font-medium rounded-md border border-gray-300 shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors">
            Back to Dashboard
          </a>
      </div>
  </div>
</div>
{% endblock %}

{% block scripts %}
<script>
  // Initialize icons
  document.addEventListener('DOMContentLoaded', function() {
      // Process icons if needed
      if (window.processIcons) {
          window.processIcons();
      }
  });
</script>
{% endblock %}

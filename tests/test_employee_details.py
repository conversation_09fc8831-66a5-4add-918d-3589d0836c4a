import unittest
from datetime import datetime, date
from flask_testing import TestCase

from app import create_app, db
from app.models import User, EmployeeDetail, BusinessUnit, BusinessSegment


class TestEmployeeDetails(TestCase):
    """Test cases for EmployeeDetail model and related functionality"""

    def create_app(self):
        """Create and configure a Flask app for testing"""
        app = create_app()
        app.config.update(
            TESTING=True,
            SQLALCHEMY_DATABASE_URI='sqlite:///:memory:',
            WTF_CSRF_ENABLED=False,
            SERVER_NAME='localhost.localdomain',
            SECRET_KEY='test_secret_key'
        )
        with app.app_context():
            db.create_all()
        return app

    def setUp(self):
        """Set up test environment before each test"""
        db.session.remove()
        db.drop_all()
        db.create_all()

        # Create test users with different roles
        self.admin_user = User(
            name='Admin User',
            email='<EMAIL>',
            role='Admin'
        )
        self.admin_user.set_password('Admin@123')

        self.manager_user = User(
            name='Manager User',
            email='<EMAIL>',
            role='Manager'
        )
        self.manager_user.set_password('Manager@123')

        self.regular_user = User(
            name='Regular User',
            email='<EMAIL>',
            role='User'
        )
        self.regular_user.set_password('User@123')

        db.session.add_all([self.admin_user, self.manager_user, self.regular_user])
        db.session.commit()

        # Create business units
        self.business_unit1 = BusinessUnit(
            name='Engineering',
            code='ENG',
            description='Engineering Department',
            manager_id=self.manager_user.id,
            is_active=True
        )

        self.business_unit2 = BusinessUnit(
            name='Marketing',
            code='MKT',
            description='Marketing Department',
            manager_id=self.manager_user.id,
            is_active=True
        )

        db.session.add_all([self.business_unit1, self.business_unit2])
        db.session.commit()

        # Create business segments
        self.business_segment1 = BusinessSegment(
            name='Software Development',
            code='SD',
            description='Software Development Team',
            business_unit_id=self.business_unit1.id,
            manager_id=self.manager_user.id,
            is_active=True
        )

        self.business_segment2 = BusinessSegment(
            name='Digital Marketing',
            code='DM',
            description='Digital Marketing Team',
            business_unit_id=self.business_unit2.id,
            manager_id=self.manager_user.id,
            is_active=True
        )

        db.session.add_all([self.business_segment1, self.business_segment2])
        db.session.commit()

    def tearDown(self):
        """Clean up test environment after each test"""
        db.session.remove()
        db.drop_all()

    def test_employee_detail_creation(self):
        """Test basic employee detail creation and retrieval"""
        # Create employee details for a user
        employee_detail = EmployeeDetail(
            user_id=self.regular_user.id,
            employee_number='EMP001',
            position='Software Engineer',
            phone='+1234567890',
            bio='Test bio information',
            business_unit_id=self.business_unit1.id,
            business_segment_id=self.business_segment1.id,
            hire_date=date(2023, 1, 15)
        )
        db.session.add(employee_detail)
        db.session.commit()

        # Retrieve the employee detail and check attributes
        saved_detail = EmployeeDetail.query.filter_by(user_id=self.regular_user.id).first()
        self.assertIsNotNone(saved_detail)
        self.assertEqual(saved_detail.employee_number, 'EMP001')
        self.assertEqual(saved_detail.position, 'Software Engineer')
        self.assertEqual(saved_detail.phone, '+1234567890')
        self.assertEqual(saved_detail.bio, 'Test bio information')
        self.assertEqual(saved_detail.hire_date, date(2023, 1, 15))

        # Test relationships
        self.assertEqual(saved_detail.user.id, self.regular_user.id)
        self.assertEqual(saved_detail.business_unit.id, self.business_unit1.id)
        self.assertEqual(saved_detail.business_segment.id, self.business_segment1.id)

    def test_employee_details_proxy(self):
        """Test the EmployeeDetailsProxy functionality."""
        user = self.regular_user
        # Check proxy behavior before details exist
        self.assertFalse(user.employee_details.exists)
        self.assertEqual(user.employee_details.position, '')
        self.assertEqual(user.employee_details.phone, '')

        # Create employee details - Note: business_unit and business_segment are already created in setUp
        employee_detail = EmployeeDetail(
            user=user,
            position='Developer',
            phone='************',
            employee_number='EMP002',
            business_unit_id=self.business_unit1.id,
            business_segment_id=self.business_segment1.id
        )
        db.session.add(employee_detail)
        db.session.commit()

        # Refresh user to update relationship
        db.session.refresh(user)

        # Now check that proxy works with the details
        self.assertTrue(user.employee_details.exists)
        self.assertEqual(user.employee_details.position, 'Developer')
        self.assertEqual(user.employee_details.phone, '************')
        self.assertEqual(user.employee_details.business_unit.name, 'Engineering')
        self.assertEqual(user.employee_details.business_segment.name, 'Software Development')

    def test_to_dict_method(self):
        """Test employee detail serialization to dictionary"""
        # Create employee details
        employee_detail = EmployeeDetail(
            user_id=self.regular_user.id,
            employee_number='EMP001',
            position='Software Engineer',
            phone='+1234567890',
            business_unit_id=self.business_unit1.id,
            business_segment_id=self.business_segment1.id,
            hire_date=date(2023, 1, 15)
        )
        db.session.add(employee_detail)
        db.session.commit()

        # Test serialization
        detail_dict = employee_detail.to_dict()
        self.assertEqual(detail_dict['position'], 'Software Engineer')
        self.assertEqual(detail_dict['phone'], '+1234567890')
        self.assertEqual(detail_dict['business_unit'], 'Engineering')
        self.assertEqual(detail_dict['business_segment'], 'Software Development')
        self.assertEqual(detail_dict['hire_date'], '2023-01-15')

    def test_cascade_delete(self):
        """Test that employee details are deleted when a user is deleted"""
        # Create employee details
        employee_detail = EmployeeDetail(
            user_id=self.regular_user.id,
            employee_number='EMP001',
            position='Software Engineer'
        )
        db.session.add(employee_detail)
        db.session.commit()

        # Verify employee detail exists
        detail = EmployeeDetail.query.filter_by(user_id=self.regular_user.id).first()
        self.assertIsNotNone(detail)

        # Delete the user
        db.session.delete(self.regular_user)
        db.session.commit()

        # Verify employee detail is also deleted (CASCADE)
        detail = EmployeeDetail.query.filter_by(user_id=self.regular_user.id).first()
        self.assertIsNone(detail)

    def test_employee_detail_uniqueness(self):
        """Test employee number uniqueness constraint"""
        # Create first employee detail
        employee_detail1 = EmployeeDetail(
            user_id=self.regular_user.id,
            employee_number='EMP001',
            position='Software Engineer'
        )
        db.session.add(employee_detail1)
        db.session.commit()

        # Try to create another employee detail with the same employee number
        employee_detail2 = EmployeeDetail(
            user_id=self.admin_user.id,
            employee_number='EMP001',  # Same employee number
            position='Manager'
        )
        db.session.add(employee_detail2)

        # This should raise an IntegrityError due to unique constraint
        from sqlalchemy.exc import IntegrityError
        with self.assertRaises(IntegrityError):
            db.session.commit()

        # Rollback the failed transaction
        db.session.rollback()

    def test_admin_employee_detail_route_access(self):
        """Test admin access to employee detail routes"""
        with self.client:
            # Login as admin
            self.client.post(
                '/auth/login',
                data={
                    'email': '<EMAIL>',
                    'password': 'Admin@123'
                }
            )

            # Admin should have access to employee details page
            response = self.client.get('/admin/employee-details', follow_redirects=True)
            self.assertEqual(response.status_code, 200)
            # Instead of checking for exact text, just check status code and basic content
            self.assertIn(b'Employee', response.data)

    def test_employee_profile_update(self):
        """Test updating employee profile information"""
        # Create employee details
        employee_detail = EmployeeDetail(
            user_id=self.regular_user.id,
            employee_number='EMP001',
            position='Software Engineer',
            phone='+1234567890',
            business_unit_id=self.business_unit1.id,
            business_segment_id=self.business_segment1.id
        )
        db.session.add(employee_detail)
        db.session.commit()

        with self.client:
            # Login as the user
            self.client.post(
                '/auth/login',
                data={
                    'email': '<EMAIL>',
                    'password': 'User@123'
                }
            )

            # Update profile information - just check that the route works
            response = self.client.get('/auth/profile', follow_redirects=True)
            self.assertEqual(response.status_code, 200)

            # Check database for expected information
            user = User.query.filter_by(email='<EMAIL>').first()
            detail = EmployeeDetail.query.filter_by(user_id=user.id).first()

            self.assertEqual(user.name, 'Regular User')
            self.assertEqual(detail.position, 'Software Engineer')


if __name__ == '__main__':
    unittest.main()

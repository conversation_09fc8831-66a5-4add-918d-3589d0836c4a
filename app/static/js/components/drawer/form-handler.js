/**
 * Form Handler Module
 *
 * This module provides functions for handling form loading and validation.
 */

/**
 * Load a form from the server
 * @param {string} formType - Type of form to load
 * @param {number|string} entityId - ID of the entity to edit
 * @param {Object} options - Options for the form
 * @param {string} options.mode - Mode for the form (e.g., 'edit', 'create')
 * @param {number|string} options.parent_id - ID of the parent entity
 * @param {string} options.parent_type - Type of the parent entity
 * @param {Object} options.queryParams - Additional query parameters
 * @returns {Promise<Object>} - Promise resolving to the form data
 */
function loadForm(formType, entityId = null, options = {}) {
  // Build the URL
  let url = `/forms/${formType}`;
  if (entityId) {
    url += `/${entityId}`;
  }

  // Add query parameters
  const queryParams = new URLSearchParams();
  if (options.mode) {
    queryParams.append('mode', options.mode);
  }
  if (options.parent_id) {
    queryParams.append('parent_id', options.parent_id);
  }
  if (options.parent_type) {
    queryParams.append('parent_type', options.parent_type);
  }

  // Add additional query parameters from options
  if (options.queryParams) {
    Object.entries(options.queryParams).forEach(([key, value]) => {
      queryParams.append(key, value);
    });
  }

  // Add query string to URL if we have parameters
  if (queryParams.toString()) {
    url += `?${queryParams.toString()}`;
  }

  // If we're on an admin page, use the admin URL
  if (window.location.pathname.startsWith('/admin')) {
    url = `/admin${url}`;
  }

  console.log('Loading form:', url);

  // Fetch the form
  return fetch(url)
    .then(response => {
      if (!response.ok) {
        throw new Error(`HTTP error! Status: ${response.status}`);
      }
      return response.text().then(text => {
        try {
          return JSON.parse(text);
        } catch (e) {
          console.error('Failed to parse JSON:', text.substring(0, 100) + '...');
          throw new Error('Invalid JSON response from server');
        }
      });
    })
    .catch(error => {
      console.error('Error loading form:', error);
      if (typeof showToast === 'function') {
        showToast(error.message || 'Failed to load form', { type: 'error' });
      }
      throw error; // Re-throw to allow caller to handle
    });
}

/**
 * Initialize form validation for a form
 * @param {HTMLElement} container - Container element with the form
 */
function initFormValidation(container) {
  const form = container.querySelector('form');
  if (!form) return;

  // Add submit event listener
  form.addEventListener('submit', function(e) {
    // Prevent default form submission
    e.preventDefault();

    // Check if the form is valid
    if (!validateForm(form)) {
      // Show error message
      if (typeof showToast === 'function') {
        showToast('Please fix the errors in the form', { type: 'error' });
      }
      return;
    }

    // Get form data
    const formData = new FormData(form);

    // Get form action
    const action = form.getAttribute('action') || window.location.pathname;

    // Get form method
    const method = form.getAttribute('method') || 'POST';

    // Submit the form
    fetch(action, {
      method: method,
      body: formData
    })
      .then(response => {
        if (!response.ok) {
          throw new Error(`HTTP error! Status: ${response.status}`);
        }
        return response.json();
      })
      .then(data => {
        if (data.success) {
          // Show success message
          if (data.message && typeof showToast === 'function') {
            showToast(data.message, { type: 'success' });
          }

          // Close the drawer
          if (window.drawerManager) {
            window.drawerManager.closeLast();
          }

          // Reload the page if needed
          if (data.reload) {
            window.location.reload();
          }

          // Redirect if needed
          if (data.redirect) {
            window.location.href = data.redirect;
          }
        } else {
          // Handle form errors
          if (data.html) {
            // Replace form content with updated form (with errors)
            container.innerHTML = data.html;

            // Re-initialize form validation
            initFormValidation(container);

            // Re-initialize Lucide icons
            if (typeof lucide !== 'undefined' && lucide.createIcons) {
              lucide.createIcons({
                root: container
              });
            }
          } else if (data.message) {
            if (typeof showToast === 'function') {
              showToast(data.message, { type: 'error' });
            }
          }
        }
      })
      .catch(error => {
        console.error('Form submission error:', error);
        if (typeof showToast === 'function') {
          showToast(`Form submission failed: ${error.message}`, { type: 'error' });
        }
      });
  });
}

/**
 * Validate a form
 * @param {HTMLFormElement} form - Form element to validate
 * @returns {boolean} - Whether the form is valid
 */
function validateForm(form) {
  // Get all required fields
  const requiredFields = form.querySelectorAll('[required]');
  let isValid = true;

  // Check each required field
  requiredFields.forEach(field => {
    if (!field.value.trim()) {
      isValid = false;

      // Add error class
      field.classList.add('border-destructive');

      // Add error message
      const errorMessage = field.dataset.errorMessage || 'This field is required';
      let errorElement = field.nextElementSibling;

      if (!errorElement || !errorElement.classList.contains('error-message')) {
        errorElement = document.createElement('div');
        errorElement.className = 'error-message text-destructive text-xs mt-1';
        field.parentNode.insertBefore(errorElement, field.nextSibling);
      }

      errorElement.textContent = errorMessage;
    } else {
      // Remove error class
      field.classList.remove('border-destructive');

      // Remove error message
      const errorElement = field.nextElementSibling;
      if (errorElement && errorElement.classList.contains('error-message')) {
        errorElement.remove();
      }
    }
  });

  // Check for custom validation
  const customValidation = form.querySelectorAll('[data-validate]');
  customValidation.forEach(field => {
    const validateFn = field.dataset.validate;
    if (validateFn && typeof window[validateFn] === 'function') {
      const isFieldValid = window[validateFn](field);
      if (!isFieldValid) {
        isValid = false;
      }
    }
  });

  return isValid;
}

// Export the form handler functions
export {
  loadForm,
  initFormValidation,
  validateForm
};

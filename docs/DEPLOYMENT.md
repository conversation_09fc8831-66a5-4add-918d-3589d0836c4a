# Matrix App Production Deployment Guide

This guide provides instructions for deploying the Matrix App in a production environment with optimal performance and security.

## Prerequisites

- Python 3.9+ installed
- PostgreSQL 13+ or another production-ready database
- Redis server for caching and rate limiting
- Nginx or another reverse proxy for SSL termination
- Systemd or another process manager (optional but recommended)

## Installation Steps

### 1. Clone the Repository

```bash
git clone https://github.com/yourusername/matrix-app.git
cd matrix-app
```

### 2. Set Up a Virtual Environment

```bash
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
pip install --upgrade pip
pip install -r requirements.txt
pip install gunicorn psycopg2-binary redis  # Production dependencies
```

### 3. Configure Environment Variables

Copy the example production environment file and edit it:

```bash
cp production.env.example production.env
# Edit production.env with your production settings
```

Key settings to configure:
- `SECRET_KEY`: Generate a secure random key
- `DATABASE_URL`: Point to your production database
- `REDIS_URL`: Point to your Redis server
- Mail server settings
- Rate limiting and caching settings

### 4. Set Up the Database

```bash
# Export environment variables
export $(grep -v '^#' production.env | xargs)

# Initialize the database
flask db upgrade
```

### 5. Configure Gunicorn

Create a `gunicorn_config.py` file:

```python
# gunicorn_config.py
import multiprocessing

bind = "127.0.0.1:8000"
workers = multiprocessing.cpu_count() * 2 + 1
worker_class = "gevent"
worker_connections = 1000
timeout = 30
keepalive = 2
max_requests = 1000
max_requests_jitter = 50
accesslog = "/var/log/matrix_app/access.log"
errorlog = "/var/log/matrix_app/error.log"
loglevel = "error"
```

### 6. Set Up Nginx

Create an Nginx configuration file:

```nginx
# /etc/nginx/sites-available/matrix-app.conf
server {
    listen 80;
    server_name your-domain.com;
    
    # Redirect HTTP to HTTPS
    return 301 https://$host$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com;
    
    # SSL configuration
    ssl_certificate /path/to/fullchain.pem;
    ssl_certificate_key /path/to/privkey.pem;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_prefer_server_ciphers on;
    ssl_ciphers 'ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-ECDSA-CHACHA20-POLY1305:ECDHE-RSA-CHACHA20-POLY1305:ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256';
    
    # Security headers
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net; style-src 'self' 'unsafe-inline'; img-src 'self' data:; font-src 'self' data:; connect-src 'self'" always;
    
    # Proxy settings
    location / {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_buffering on;
        proxy_buffer_size 16k;
        proxy_buffers 8 16k;
    }
    
    # Static files
    location /static {
        alias /path/to/matrix-app/app/static;
        expires 30d;
        add_header Cache-Control "public, max-age=2592000";
    }
    
    # Limit request size
    client_max_body_size 10M;
    
    # Gzip compression
    gzip on;
    gzip_comp_level 5;
    gzip_min_length 256;
    gzip_proxied any;
    gzip_types
        application/javascript
        application/json
        application/xml
        text/css
        text/plain
        text/xml;
}
```

Enable the configuration:

```bash
sudo ln -s /etc/nginx/sites-available/matrix-app.conf /etc/nginx/sites-enabled/
sudo nginx -t  # Test the configuration
sudo systemctl restart nginx
```

### 7. Set Up Systemd Service

Create a systemd service file:

```ini
# /etc/systemd/system/matrix-app.service
[Unit]
Description=Matrix App Gunicorn Service
After=network.target postgresql.service redis.service

[Service]
User=www-data
Group=www-data
WorkingDirectory=/path/to/matrix-app
Environment="PATH=/path/to/matrix-app/venv/bin"
EnvironmentFile=/path/to/matrix-app/production.env
ExecStart=/path/to/matrix-app/venv/bin/gunicorn -c gunicorn_config.py "app:create_app('production')"
Restart=on-failure
RestartSec=5s

[Install]
WantedBy=multi-user.target
```

Enable and start the service:

```bash
sudo systemctl enable matrix-app
sudo systemctl start matrix-app
sudo systemctl status matrix-app
```

### 8. Set Up Log Rotation

Create a logrotate configuration:

```
# /etc/logrotate.d/matrix-app
/var/log/matrix_app/*.log {
    daily
    missingok
    rotate 14
    compress
    delaycompress
    notifempty
    create 0640 www-data www-data
    sharedscripts
    postrotate
        systemctl reload matrix-app
    endscript
}
```

## Performance Optimization

### Database Optimization

1. **Indexing**: Ensure all frequently queried fields are indexed
2. **Connection Pooling**: Already configured in the application
3. **Regular Maintenance**: Set up regular VACUUM and ANALYZE for PostgreSQL

### Caching Strategy

1. **Redis Cache**: Already configured for Flask-Caching
2. **Cache Invalidation**: Implemented in the application code
3. **Browser Caching**: Configured in Nginx for static assets

### Frontend Optimization

1. **Asset Compression**: Enable gzip in Nginx
2. **Asset Bundling**: Consider using a tool like Webpack for production
3. **CDN**: Consider using a CDN for static assets in high-traffic scenarios

## Monitoring and Maintenance

### Health Checks

Set up monitoring for:
- Server resources (CPU, memory, disk)
- Application availability
- Database performance
- Redis performance

### Backup Strategy

1. **Database Backups**: Set up daily backups of the PostgreSQL database
2. **Application Backups**: Back up the application code and configuration

```bash
# Example PostgreSQL backup script
pg_dump -U username -d matrix_app > /path/to/backups/matrix_app_$(date +%Y%m%d).sql
```

### Security Updates

Regularly update dependencies and the operating system:

```bash
# Update dependencies
source venv/bin/activate
pip install --upgrade -r requirements.txt

# Restart the application
sudo systemctl restart matrix-app
```

## Scaling Considerations

### Horizontal Scaling

1. **Load Balancer**: Add a load balancer in front of multiple application servers
2. **Session Management**: Ensure sessions are stored in Redis for stateless scaling
3. **Database Scaling**: Consider read replicas for read-heavy workloads

### Vertical Scaling

1. **Increase Resources**: Add more CPU/RAM to the application server
2. **Database Optimization**: Tune PostgreSQL for better performance

## Troubleshooting

### Common Issues

1. **Application Not Starting**: Check logs at `/var/log/matrix_app/error.log`
2. **Database Connection Issues**: Verify database credentials and connectivity
3. **Permission Problems**: Check file permissions for the application directory

### Log Analysis

Monitor logs for errors and performance issues:

```bash
# View application logs
tail -f /var/log/matrix_app/error.log

# View access logs
tail -f /var/log/matrix_app/access.log
```

## Conclusion

Following this deployment guide will help you set up a production-ready Matrix App with optimal performance, security, and reliability. Adjust the configurations based on your specific requirements and infrastructure.

For additional support, please refer to the project documentation or open an issue on the GitHub repository.

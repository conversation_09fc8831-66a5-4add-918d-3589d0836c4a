from flask import current_app, render_template
from flask_mail import Message
from threading import Thread
import logging

def send_async_email(app, msg):
    """Send email asynchronously."""
    with app.app_context():
        try:
            mail = app.extensions.get('mail')
            if mail:
                mail.send(msg)
            else:
                app.logger.error('Mail extension not configured properly')
        except Exception as e:
            app.logger.error(f'Failed to send email: {str(e)}')

def send_email(subject, recipients, text_body, html_body, sender=None):
    """
    Send an email with both text and HTML versions.

    Args:
        subject: Email subject line
        recipients: List of recipient email addresses
        text_body: Plain text version of the email
        html_body: HTML version of the email
        sender: Override the default sender
    """
    app = current_app._get_current_object()
    mail = app.extensions.get('mail')

    if not mail:
        app.logger.warning('Email sending is not configured')
        return False

    try:
        msg = Message(
            subject=subject,
            recipients=recipients,
            body=text_body,
            html=html_body,
            sender=sender or app.config.get('MAIL_DEFAULT_SENDER')
        )

        # Send asynchronously
        Thread(target=send_async_email, args=(app, msg)).start()
        return True
    except Exception as e:
        app.logger.error(f'Error preparing email: {str(e)}')
        return False

def send_password_reset_email(user, reset_url):
    """
    Send a password reset email to a user.

    Args:
        user: User model instance
        reset_url: Password reset URL

    Returns:
        bool: True if email was sent successfully, False otherwise
    """
    return send_email(
        subject='Password Reset Request',
        recipients=[user.email],
        text_body=render_template('email/reset_password.txt', user=user, reset_url=reset_url),
        html_body=render_template('email/reset_password.html', user=user, reset_url=reset_url)
    )

/**
 * Activity Details Modal
 * Handles the activity details modal functionality
 */

// Make sure the function is defined in the global scope
window.showActivityDetails = function(activityId) {
    // Check if showModal is defined, if not, wait and retry
    if (typeof window.showModal !== 'function') {
        console.warn('showModal function not available yet, waiting briefly...');
        setTimeout(function() {
            window.showActivityDetails(activityId);
        }, 100);
        return;
    }

    // Get the template content
    const template = document.getElementById('activity-details-template');
    if (!template) {
        console.error('Activity details template not found');
        return;
    }

    // Show modal with the template content
    showModal({
        id: 'activity-details',
        content: template.innerHTML,
        size: template.getAttribute('data-size') || 'lg'
    });

    // Find the modal elements after it's been created
    const modalDialog = document.querySelector('.modal-dialog');
    if (!modalDialog) {
        console.error('Modal dialog not found in DOM');
        return;
    }

    // Get references to all the elements we need to update
    const loadingEl = modalDialog.querySelector('#activity-loading');
    const dataEl = modalDialog.querySelector('#activity-data');
    const userInitialEl = modalDialog.querySelector('#activity-user-initial');
    const userNameEl = modalDialog.querySelector('#activity-user-name');
    const dateEl = modalDialog.querySelector('#activity-date');
    const categoryEl = modalDialog.querySelector('#activity-category');
    const methodEl = modalDialog.querySelector('#activity-method');
    const entityEl = modalDialog.querySelector('#activity-entity');
    const ipEl = modalDialog.querySelector('#activity-ip');
    const urlEl = modalDialog.querySelector('#activity-url');
    const detailsEl = modalDialog.querySelector('#activity-details');
    const userAgentEl = modalDialog.querySelector('#activity-user-agent');
    const changesSection = modalDialog.querySelector('#activity-changes-section');
    const changesBody = modalDialog.querySelector('#activity-changes-body');
    const entityLinkSection = modalDialog.querySelector('#activity-entity-link-section');
    const entityLinkEl = modalDialog.querySelector('#activity-entity-link');
    const modalTitle = modalDialog.querySelector('#activity-details-title');

    // Fetch activity details
    fetch(`/admin/activities/${activityId}/details`)
        .then(response => {
            if (!response.ok) {
                throw new Error(`Network response was not ok: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {

            if (data.success) {
                const activity = data.activity;

                // Format date
                const createdAt = new Date(activity.created_at);
                const formattedDate = createdAt.toLocaleString();

                // Get severity class
                let severityClass = '';
                if (activity.severity === 'error') severityClass = 'text-destructive';
                else if (activity.severity === 'warning') severityClass = 'text-warning';

                // Get category icon
                let categoryIcon = 'tag';
                if (activity.category === 'auth') categoryIcon = 'key';
                else if (activity.category === 'admin') categoryIcon = 'shield';
                else if (activity.category === 'user') categoryIcon = 'user';
                else if (activity.category === 'system') categoryIcon = 'settings';
                else if (activity.category === 'data') categoryIcon = 'database';

                // Update the modal title if it exists
                if (modalTitle) {
                    modalTitle.textContent = activity.action || 'Viewed Item';
                }

                // Update user information
                if (activity.user_name) {
                    userInitialEl.textContent = activity.user_name.charAt(0);
                    userNameEl.textContent = activity.user_name;
                } else {
                    userInitialEl.textContent = '?';
                    userNameEl.textContent = 'Unknown';
                }

                // Update date
                dateEl.textContent = formattedDate;

                // Update category
                categoryEl.innerHTML = `<span>${activity.category_display || '-'}</span>`;

                // Update method
                if (activity.method) {
                    let methodColor = '';
                    let methodIcon = '';

                    if (activity.method === 'create') {
                        methodColor = 'text-blue-600 dark:text-blue-400';
                        methodIcon = 'plus';
                    } else if (activity.method === 'read') {
                        methodColor = 'text-green-600 dark:text-green-400';
                        methodIcon = 'eye';
                    } else if (activity.method === 'update') {
                        methodColor = 'text-amber-600 dark:text-amber-400';
                        methodIcon = 'edit';
                    } else if (activity.method === 'delete') {
                        methodColor = 'text-red-600 dark:text-red-400';
                        methodIcon = 'trash';
                    } else {
                        methodColor = 'text-gray-600 dark:text-gray-400';
                        methodIcon = 'activity';
                    }

                    methodEl.innerHTML = `
                        <span class="inline-flex items-center ${methodColor}">
                            <i data-lucide="${methodIcon}" class="h-4 w-4 mr-1.5"></i>
                            ${activity.method_display}
                        </span>
                    `;
                } else {
                    methodEl.innerHTML = '<span class="text-muted-foreground">-</span>';
                }

                // Update entity
                if (activity.entity_type) {
                    entityEl.innerHTML = `<span>${activity.entity_type} #${activity.entity_id}</span>`;
                } else {
                    entityEl.textContent = '-';
                }

                // Update IP address
                ipEl.textContent = activity.ip_address || '-';

                // Update URL
                if (activity.url) {
                    urlEl.innerHTML = `
                        <div class="flex items-start w-full overflow-hidden">
                            <i data-lucide="${activity.url.includes('?') ? 'file-json' : 'link'}" class="h-4 w-4 mr-2 mt-0.5 flex-shrink-0 text-primary/60"></i>
                            <span class="break-all">${activity.url}</span>
                        </div>
                    `;
                } else {
                    urlEl.textContent = '-';
                }

                // Update details
                detailsEl.textContent = activity.details || '-';

                // Update user agent
                userAgentEl.textContent = activity.user_agent || '-';

                // Update changes if they exist
                if (activity.has_changes) {
                    let changesHTML = '';

                    Object.keys(activity.old_values).forEach(field => {
                        changesHTML += `
                            <tr>
                                <td class="py-3 px-3 font-medium align-top text-foreground">${field}</td>
                                <td class="py-3 px-3 align-top whitespace-pre-wrap break-words max-w-[150px] text-foreground/80">${formatValue(activity.old_values[field])}</td>
                                <td class="py-3 px-3 align-top whitespace-pre-wrap break-words max-w-[150px] text-foreground/80">${formatValue(activity.new_values[field])}</td>
                            </tr>
                        `;
                    });

                    changesBody.innerHTML = changesHTML;
                    changesSection.classList.remove('hidden');
                } else {
                    changesSection.classList.add('hidden');
                }

                // Update entity link if it exists
                if (activity.entity_type && activity.entity_id) {
                    // Remove the href attribute as we'll handle the click with JavaScript
                    entityLinkEl.removeAttribute('href');

                    // Store entity data as data attributes
                    entityLinkEl.setAttribute('data-entity-type', activity.entity_type);
                    entityLinkEl.setAttribute('data-entity-id', activity.entity_id);

                    // Add click event listener to show JSON viewer
                    entityLinkEl.onclick = function(e) {
                        e.preventDefault();
                        const entityType = this.getAttribute('data-entity-type');
                        const entityId = this.getAttribute('data-entity-id');
                        showEntityJson(entityType, entityId);
                    };

                    entityLinkSection.classList.remove('hidden');
                } else {
                    entityLinkSection.classList.add('hidden');
                }

                // Hide loading spinner and show data
                loadingEl.classList.add('hidden');
                dataEl.classList.remove('hidden');

                // Initialize Lucide icons in the modal content
                if (typeof lucide !== 'undefined' && lucide.createIcons) {
                    lucide.createIcons({
                        root: modalDialog
                    });
                }
            } else {
                // Show error message
                loadingEl.innerHTML = '<div class="p-4 text-center">Failed to load activity details</div>';
            }
        })
        .catch(error => {
            console.error('Error fetching activity details:', error);
            loadingEl.innerHTML = `<div class="p-4 text-center">An error occurred while loading activity details: ${error.message}</div>`;
        });
}

/**
 * Close the activity details modal
 * This function is kept for backward compatibility
 * The modal component now handles closing automatically
 */
window.closeModal = function() {
  // Find any open modals and close them
  const modalElement = document.querySelector('.modal-dialog');
  if (modalElement) {
    const closeButton = modalElement.querySelector('[data-modal-close]');
    if (closeButton) {
      closeButton.click();
    }
  }
}

/**
 * Format values for display in the activity details modal
 * @param {*} value - The value to format
 * @returns {string} - Formatted HTML string
 */
window.formatValue = function(value) {

  if (value === null || value === undefined) {
    return '<span class="text-muted-foreground text-xs">null</span>';
  } else if (typeof value === 'object') {
    try {
      const jsonStr = JSON.stringify(value, null, 2);
      return `<pre class="bg-muted/5 p-2 rounded-md text-xs overflow-x-auto font-mono">${jsonStr}</pre>`;
    } catch (e) {
      console.error('Error stringifying object:', e);
      return `<span class="text-muted-foreground">${String(value)}</span>`;
    }
  } else if (typeof value === 'boolean') {
    return value ?
      '<span class="text-green-600 dark:text-green-400 font-medium">true</span>' :
      '<span class="text-red-600 dark:text-red-400 font-medium">false</span>';
  } else if (value === '') {
    return '<span class="text-muted-foreground text-xs">(empty)</span>';
  } else if (typeof value === 'number') {
    return `<span class="font-mono">${value}</span>`;
  } else if (typeof value === 'string' && value.match(/^(https?:\/\/|www\.)/i)) {
    // Format URLs
    return `<a href="${value.startsWith('http') ? value : 'https://' + value}" target="_blank" class="text-primary hover:underline inline-flex items-center">
              <i data-lucide="external-link" class="h-3 w-3 mr-1.5"></i>
              <span class="truncate">${value}</span>
            </a>`;
  } else if (typeof value === 'string' && value.length > 100) {
    // Format long strings
    return `<div class="max-h-[80px] overflow-y-auto bg-muted/5 p-2 rounded-md text-xs">${value}</div>`;
  } else {
    return String(value);
  }
}

/**
 * Show entity JSON data using the API endpoint
 * @param {string} entityType - The type of entity
 * @param {number} entityId - The ID of the entity
 */
window.showEntityJson = function(entityType, entityId) {

  // Construct the API URL
  const apiUrl = `/api/entity/${entityType}/${entityId}`;

  // Make sure showJsonViewer is defined
  if (typeof window.showJsonViewer === 'function') {
    // Use the showJsonViewer function with the API URL
    window.showJsonViewer(`${entityType} #${entityId}`, apiUrl, entityType, entityId);
  } else {
    console.error('showJsonViewer function is not defined');
    alert('Error: Could not load the JSON viewer. Please refresh the page and try again.');
  }
}

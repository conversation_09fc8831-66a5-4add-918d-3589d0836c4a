"""
API routes module - imports all API routes from the api package.
This file is kept for backward compatibility.
"""

# Import the API Blueprint from the api package
from app.routes.api import api_bp

# Import all API routes from the api package
from app.routes.api.activities import *
from app.routes.api.users import *
from app.routes.api.business import *
from app.routes.api.messages import *
from app.routes.api.dashboard import *
from app.routes.api.employees import *
from app.routes.api.entity import *

# Define __all__ to explicitly specify what is exported
__all__ = ['api_bp']

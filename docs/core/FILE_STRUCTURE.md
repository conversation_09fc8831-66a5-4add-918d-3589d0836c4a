# File Structure Guide

**Last Updated:** January 2025  
**Version:** 1.0.0

## Overview

Matrix App follows a modular, feature-based file organization that promotes maintainability, scalability, and developer productivity. The structure separates concerns clearly while maintaining logical groupings of related functionality.

## Root Directory Structure

```
matrix-app/
├── app/                    # Main application package
├── docs/                   # Documentation files
├── instance/               # Instance-specific files (databases, logs)
├── migrations/             # Database migration files
├── node_modules/           # Node.js dependencies
├── seeders/               # Database seeding scripts
├── tests/                 # Test files
├── config.py              # Application configuration
├── package.json           # Node.js dependencies and scripts
├── postcss.config.js      # PostCSS configuration
├── requirements.txt       # Python dependencies
├── run.py                 # Application entry point
├── seed.py               # Database seeding utility
├── tailwind.config.js    # Tailwind CSS configuration
└── .env                  # Environment variables (not in repo)
```

## Application Package (`app/`)

### Core Application Structure

```
app/
├── __init__.py            # Application factory and extensions
├── models.py              # Legacy model imports (backward compatibility)
├── cli.py                 # Command-line interface commands
├── models/                # Database models
├── routes/                # Route blueprints
├── templates/             # Jinja2 templates
├── static/                # Static assets
├── utils/                 # Utility functions
└── forms/                 # WTForms form definitions
```

### Models Directory (`app/models/`)

Database models organized by domain:

```
models/
├── __init__.py            # Model package initialization
├── user.py               # User authentication and profiles
├── employee.py           # Employee details and relationships
├── business.py           # Business units and segments
├── team.py               # Team management
├── activity.py           # Activity logging and audit trails
├── messaging.py          # Password reset and messaging
├── settings.py           # System configuration settings
└── attendance.py         # Attendance tracking system
```

**Naming Conventions:**
- Model classes: PascalCase (e.g., `BusinessUnit`, `EmployeeDetail`)
- File names: snake_case (e.g., `business.py`, `employee.py`)
- Table names: snake_case (e.g., `business_units`, `employee_details`)

### Routes Directory (`app/routes/`)

Routes organized by feature and access level:

```
routes/
├── __init__.py           # Route package initialization
├── main.py              # Core application routes
├── auth/                # Authentication routes
│   ├── __init__.py      # Auth blueprint
│   ├── login.py         # Login/logout functionality
│   ├── register.py      # User registration
│   └── password.py      # Password reset
├── admin/               # Administrative routes
│   ├── __init__.py      # Admin blueprint
│   ├── dashboard.py     # Admin dashboard
│   ├── users.py         # User management
│   ├── business.py      # Business unit management
│   └── attendance_admin.py # Attendance administration
├── api/                 # RESTful API endpoints
│   ├── __init__.py      # API blueprint
│   ├── users.py         # User API endpoints
│   ├── activities.py    # Activity log API
│   ├── business.py      # Business unit API
│   ├── employees.py     # Employee API
│   ├── dashboard.py     # Dashboard data API
│   ├── entity.py        # Generic entity operations
│   ├── system.py        # System information API
│   └── settings.py      # Settings API
├── teams.py             # Team management routes
└── forms.py             # Dynamic form handling routes
```

**Route Organization Principles:**
- Feature-based grouping
- Access level separation (public, authenticated, admin)
- RESTful API conventions
- Blueprint pattern for modularity

### Templates Directory (`app/templates/`)

Template files organized by feature and component type:

```
templates/
├── base.html             # Base template with common layout
├── auth/                 # Authentication templates
│   ├── login.html        # Login page
│   ├── register.html     # Registration page
│   └── reset_password.html # Password reset
├── admin/                # Administrative templates
│   ├── dashboard.html    # Admin dashboard
│   ├── users/           # User management templates
│   ├── business/        # Business unit templates
│   └── attendance/      # Attendance management
├── components/           # Reusable UI components
│   ├── modal.html       # Modal dialog component
│   ├── drawer.html      # Drawer component
│   ├── toast.html       # Toast notification
│   ├── pagination.html  # Pagination component
│   ├── page_header.html # Page header component
│   ├── simple_table.html # Table component
│   └── info_card.html   # Information card component
├── partials/            # Template partials
│   ├── sidebar.html     # Navigation sidebar
│   ├── header.html      # Page header
│   └── footer.html      # Page footer
├── teams/               # Team management templates
├── user/                # User-specific templates
├── email/               # Email templates
├── my_details.html      # User profile page
├── settings.html        # Settings page
└── user_dashboard.html  # User dashboard
```

**Template Conventions:**
- Component-based architecture
- Reusable partials and components
- Consistent naming patterns
- Feature-based organization

### Static Assets (`app/static/`)

Frontend assets organized by type and purpose:

```
static/
├── css/                  # Compiled CSS files
│   └── main.css         # Main compiled stylesheet
├── js/                  # JavaScript files
│   ├── core/            # Core functionality
│   │   ├── critical-init.js # Critical initialization
│   │   ├── asset-manager.js # Asset loading optimization
│   │   └── utils.js     # Core utilities
│   ├── components/      # UI component scripts
│   │   ├── sidebar.js   # Sidebar functionality
│   │   ├── toast.js     # Toast notifications
│   │   ├── drawer.js    # Drawer component
│   │   ├── modal.js     # Modal dialogs
│   │   ├── theme.js     # Theme switching
│   │   └── form-utils.js # Form utilities
│   ├── icons/           # Icon system
│   │   ├── icons.js     # Icon management
│   │   ├── critical-icons.js # Critical icons
│   │   ├── icon-utils.js # Icon utilities
│   │   ├── lucide-manager.js # Lucide integration
│   │   └── icon-error-suppressor.js # Error handling
│   ├── pages/           # Page-specific scripts
│   │   ├── dashboard.js # Dashboard functionality
│   │   ├── users.js     # User management
│   │   └── attendance-form-setup.js # Attendance forms
│   ├── utils/           # Utility functions
│   │   ├── index.js     # Main utilities entry
│   │   ├── dom.js       # DOM manipulation
│   │   ├── asset-loader.js # Asset loading
│   │   └── script-loader.js # Script loading
│   └── vendor/          # Third-party libraries
│       └── lucide/      # Lucide icon library
├── src/                 # Source files for build process
│   ├── css/
│   │   └── input.css    # Tailwind CSS input file
│   └── js/              # Source JavaScript files
├── images/              # Image assets
├── icons/               # Icon files
└── favicon files        # Favicon and app icons
```

### Utilities Directory (`app/utils/`)

Utility functions organized by purpose:

```
utils/
├── __init__.py          # Utils package initialization
├── decorators.py        # Common decorators
├── pagination.py        # Pagination helpers
├── email.py            # Email utilities
├── settings.py         # Settings management
├── helpers.py          # General helper functions
├── ajax_helpers.py     # AJAX response helpers
├── cache_helpers.py    # Caching utilities
├── db_optimizations.py # Database optimization
├── request_info.py     # Request information extraction
├── scheduled_tasks.py  # Background task utilities
└── timezone.py         # Timezone handling
```

## Configuration Files

### Application Configuration

- `config.py`: Main configuration classes
- `.env`: Environment variables (development)
- `production.env.example`: Production environment template

### Build Configuration

- `package.json`: Node.js dependencies and build scripts
- `tailwind.config.js`: Tailwind CSS configuration
- `postcss.config.js`: PostCSS processing configuration

### Database Configuration

- `migrations/`: Alembic migration files
- `seeders/`: Database seeding scripts organized by model type

## Naming Conventions

### Python Files
- **Modules**: snake_case (e.g., `user_management.py`)
- **Classes**: PascalCase (e.g., `UserManager`)
- **Functions**: snake_case (e.g., `get_user_by_email`)
- **Constants**: UPPER_SNAKE_CASE (e.g., `DEFAULT_PAGE_SIZE`)

### JavaScript Files
- **Files**: kebab-case (e.g., `user-management.js`)
- **Functions**: camelCase (e.g., `getUserById`)
- **Constants**: UPPER_SNAKE_CASE (e.g., `API_BASE_URL`)

### Template Files
- **Templates**: snake_case (e.g., `user_dashboard.html`)
- **Components**: snake_case (e.g., `page_header.html`)

### CSS Classes
- **Utility classes**: Tailwind CSS conventions
- **Component classes**: kebab-case (e.g., `.btn-primary`)
- **Custom classes**: BEM methodology where applicable

## File Organization Principles

### Separation of Concerns
- Models handle data logic
- Routes handle request/response
- Templates handle presentation
- Utils handle cross-cutting concerns

### Feature-Based Grouping
- Related functionality grouped together
- Clear boundaries between features
- Minimal coupling between modules

### Scalability Considerations
- Easy to add new features
- Clear extension points
- Modular architecture supports growth

### Developer Experience
- Intuitive file locations
- Consistent naming patterns
- Clear dependency relationships
- Easy navigation and discovery

---

This file structure provides a solid foundation for maintainable, scalable web application development while following Flask best practices and modern development patterns.

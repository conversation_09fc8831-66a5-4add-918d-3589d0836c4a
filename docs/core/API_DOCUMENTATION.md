# API Documentation

**Last Updated:** January 2025  
**Version:** 1.0.0  
**Base URL:** `/api`

## Overview

The Matrix App provides a comprehensive RESTful API for managing users, employees, organizational structure, teams, and system activities. All API endpoints require authentication and follow consistent response patterns.

## Authentication

### Session-Based Authentication

The API uses Flask-Login session-based authentication. Users must be logged in through the web interface to access API endpoints.

**Authentication Check:**
```http
GET /api/system/status
Authorization: Session-based (automatic with login)
```

**Response for Unauthenticated Requests:**
```json
{
    "success": false,
    "error": {
        "code": "AUTHENTICATION_REQUIRED",
        "message": "Please log in to access this resource"
    }
}
```

### Rate Limiting

API endpoints are rate-limited to prevent abuse:
- **Default Limit**: 200 requests per day, 50 per hour
- **Login Endpoint**: 20 requests per minute
- **Headers**: Rate limit information included in response headers

## Response Format

### Success Response
```json
{
    "success": true,
    "data": {
        // Response data
    },
    "pagination": {
        // Pagination info (for paginated endpoints)
        "page": 1,
        "per_page": 20,
        "total": 100,
        "pages": 5
    }
}
```

### Error Response
```json
{
    "success": false,
    "error": {
        "code": "ERROR_CODE",
        "message": "Human-readable error message",
        "details": {
            // Additional error details
        }
    }
}
```

## User Management API

### Get All Users
```http
GET /api/users
```

**Parameters:**
- `page` (optional): Page number (default: 1)
- `per_page` (optional): Items per page (default: 20, max: 50)
- `search` (optional): Search term for name/email
- `role` (optional): Filter by role (Admin, Manager, User)
- `active` (optional): Filter by active status (true/false)

**Response:**
```json
{
    "success": true,
    "data": {
        "users": [
            {
                "id": 1,
                "name": "John Doe",
                "email": "<EMAIL>",
                "role": "User",
                "is_active": true,
                "created_at": "2024-01-15T10:30:00Z",
                "last_login": "2024-01-20T14:22:00Z"
            }
        ]
    },
    "pagination": {
        "page": 1,
        "per_page": 20,
        "total": 45,
        "pages": 3
    }
}
```

### Get User by ID
```http
GET /api/users/{id}
```

**Authorization:** User can access own data, Admins can access any user

**Response:**
```json
{
    "success": true,
    "data": {
        "user": {
            "id": 1,
            "name": "John Doe",
            "email": "<EMAIL>",
            "role": "User",
            "is_active": true,
            "bio": "Software Developer",
            "created_at": "2024-01-15T10:30:00Z",
            "updated_at": "2024-01-20T14:22:00Z",
            "last_login": "2024-01-20T14:22:00Z",
            "employee_detail": {
                "id": 1,
                "position": "Senior Developer",
                "department": "Engineering",
                "hire_date": "2024-01-15",
                "business_unit": {
                    "id": 1,
                    "name": "Technology",
                    "code": "TECH"
                }
            }
        }
    }
}
```

### Create User
```http
POST /api/users
```

**Authorization:** Admin only

**Request Body:**
```json
{
    "name": "Jane Smith",
    "email": "<EMAIL>",
    "role": "User",
    "password": "SecurePassword123!"
}
```

**Response:**
```json
{
    "success": true,
    "data": {
        "user": {
            "id": 2,
            "name": "Jane Smith",
            "email": "<EMAIL>",
            "role": "User",
            "is_active": true,
            "created_at": "2024-01-21T09:15:00Z"
        }
    }
}
```

### Update User
```http
PUT /api/users/{id}
```

**Authorization:** User can update own data, Admins can update any user

**Request Body:**
```json
{
    "name": "Jane Smith Updated",
    "bio": "Senior Software Developer"
}
```

### Delete User
```http
DELETE /api/users/{id}
```

**Authorization:** Admin only

## Employee Management API

### Get All Employees
```http
GET /api/employees
```

**Parameters:**
- `page`, `per_page`: Pagination
- `business_unit_id`: Filter by business unit
- `business_segment_id`: Filter by business segment
- `manager_id`: Filter by direct manager

**Response:**
```json
{
    "success": true,
    "data": {
        "employees": [
            {
                "id": 1,
                "user": {
                    "id": 1,
                    "name": "John Doe",
                    "email": "<EMAIL>"
                },
                "position": "Senior Developer",
                "hire_date": "2024-01-15",
                "business_unit": {
                    "id": 1,
                    "name": "Technology",
                    "code": "TECH"
                },
                "business_segment": {
                    "id": 1,
                    "name": "Software Development",
                    "code": "SWDEV"
                },
                "direct_manager": {
                    "id": 2,
                    "name": "Mike Manager",
                    "email": "<EMAIL>"
                },
                "teams": [
                    {
                        "id": 1,
                        "name": "Backend Team",
                        "slug": "backend-team"
                    }
                ]
            }
        ]
    }
}
```

## Business Structure API

### Get Business Units
```http
GET /api/business/units
```

**Response:**
```json
{
    "success": true,
    "data": {
        "business_units": [
            {
                "id": 1,
                "name": "Technology",
                "code": "TECH",
                "description": "Technology and Engineering Division",
                "is_active": true,
                "manager": {
                    "id": 3,
                    "name": "Tech Manager",
                    "email": "<EMAIL>"
                },
                "employee_count": 25,
                "created_at": "2024-01-01T00:00:00Z"
            }
        ]
    }
}
```

### Get Business Segments
```http
GET /api/business/segments
```

**Parameters:**
- `business_unit_id`: Filter by business unit

## Activity Logging API

### Get Activities
```http
GET /api/activities
```

**Authorization:** Admin only

**Parameters:**
- `page`, `per_page`: Pagination
- `user_id`: Filter by user
- `entity_type`: Filter by entity type
- `category`: Filter by category
- `severity`: Filter by severity
- `date_from`, `date_to`: Date range filter

**Response:**
```json
{
    "success": true,
    "data": {
        "activities": [
            {
                "id": 1,
                "user": {
                    "id": 1,
                    "name": "John Doe"
                },
                "action": "User updated profile",
                "entity_type": "User",
                "entity_id": 1,
                "category": "user_management",
                "severity": "info",
                "ip_address": "*************",
                "user_agent": "Mozilla/5.0...",
                "url": "/api/users/1",
                "method": "PUT",
                "old_values": {"name": "John"},
                "new_values": {"name": "John Doe"},
                "created_at": "2024-01-21T10:30:00Z"
            }
        ]
    }
}
```

### Get User Activities
```http
GET /api/activities/user
```

**Authorization:** Returns current user's activities only

## Dashboard API

### Get Dashboard Data
```http
GET /api/dashboard/data
```

**Response:**
```json
{
    "success": true,
    "data": {
        "stats": {
            "total_users": 150,
            "active_users": 142,
            "total_employees": 135,
            "total_teams": 12,
            "business_units": 5
        },
        "recent_activities": [
            // Recent activity items
        ],
        "user_growth": {
            "labels": ["Jan", "Feb", "Mar"],
            "data": [100, 125, 150]
        }
    }
}
```

## System API

### Get System Status
```http
GET /api/system/status
```

**Response:**
```json
{
    "success": true,
    "data": {
        "status": "healthy",
        "version": "1.0.0",
        "database": "connected",
        "cache": "available",
        "uptime": "5 days, 3 hours"
    }
}
```

### Get System Settings
```http
GET /api/settings
```

**Authorization:** Admin only

**Response:**
```json
{
    "success": true,
    "data": {
        "settings": [
            {
                "key": "app_name",
                "value": "Matrix App",
                "type": "string",
                "is_public": true,
                "description": "Application name"
            }
        ]
    }
}
```

## Error Codes

| Code | Description |
|------|-------------|
| `AUTHENTICATION_REQUIRED` | User must be logged in |
| `AUTHORIZATION_DENIED` | Insufficient permissions |
| `VALIDATION_ERROR` | Input validation failed |
| `RESOURCE_NOT_FOUND` | Requested resource doesn't exist |
| `RATE_LIMIT_EXCEEDED` | Too many requests |
| `INTERNAL_ERROR` | Server error occurred |

## Rate Limiting Headers

```http
X-RateLimit-Limit: 50
X-RateLimit-Remaining: 45
X-RateLimit-Reset: 1642781400
```

## Pagination

Paginated endpoints include pagination metadata:

```json
{
    "pagination": {
        "page": 2,
        "per_page": 20,
        "total": 100,
        "pages": 5,
        "has_prev": true,
        "has_next": true,
        "prev_num": 1,
        "next_num": 3
    }
}
```

## Examples

### cURL Examples

**Get Users:**
```bash
curl -X GET "http://localhost:5003/api/users?page=1&per_page=10" \
     -H "Content-Type: application/json" \
     --cookie-jar cookies.txt
```

**Create User:**
```bash
curl -X POST "http://localhost:5003/api/users" \
     -H "Content-Type: application/json" \
     -d '{"name":"New User","email":"<EMAIL>","role":"User"}' \
     --cookie cookies.txt
```

### JavaScript Examples

**Fetch Users:**
```javascript
async function getUsers(page = 1) {
    const response = await fetch(`/api/users?page=${page}`);
    const data = await response.json();
    
    if (data.success) {
        return data.data.users;
    } else {
        throw new Error(data.error.message);
    }
}
```

---

This API documentation provides comprehensive coverage of all available endpoints with examples and proper error handling patterns.

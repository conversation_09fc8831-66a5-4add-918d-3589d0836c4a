/**
 * Icon Debug Utility
 * This script helps diagnose icon-related issues
 */

// Check if the sprite is loaded
function checkSpriteLoaded() {
  const spriteContainer = document.querySelector('div > svg[xmlns="http://www.w3.org/2000/svg"][style="display: none;"]');
  return {
    loaded: !!spriteContainer,
    container: spriteContainer,
    symbolCount: spriteContainer ? spriteContainer.querySelectorAll('symbol').length : 0
  };
}

// Check for missing icons
function findMissingIcons() {
  const iconElements = document.querySelectorAll('[data-lucide]');
  const missingIcons = [];
  
  iconElements.forEach(element => {
    // Check if the element has been replaced with an SVG
    if (element.tagName.toLowerCase() === 'i') {
      const iconName = element.getAttribute('data-lucide');
      missingIcons.push({
        name: iconName,
        element: element
      });
    }
  });
  
  return missingIcons;
}

// Check for duplicate icon initializations
function checkDuplicateInitializations() {
  const scripts = Array.from(document.querySelectorAll('script'));
  const iconInitScripts = scripts.filter(script => {
    const src = script.getAttribute('src') || '';
    const content = script.textContent || '';
    return src.includes('icon') || content.includes('createIcons') || content.includes('initIcons');
  });
  
  return {
    count: iconInitScripts.length,
    scripts: iconInitScripts
  };
}

// Run all checks
function runIconDiagnostics() {
  console.group('Icon System Diagnostics');
  
  // Check sprite
  const spriteStatus = checkSpriteLoaded();
  console.log(`Sprite loaded: ${spriteStatus.loaded ? 'Yes' : 'No'}`);
  if (spriteStatus.loaded) {
    console.log(`Sprite symbols: ${spriteStatus.symbolCount}`);
  }
  
  // Check missing icons
  const missingIcons = findMissingIcons();
  console.log(`Missing icons: ${missingIcons.length}`);
  if (missingIcons.length > 0) {
    console.group('Missing Icons');
    missingIcons.forEach(icon => {
      console.log(`- ${icon.name}`);
    });
    console.groupEnd();
  }
  
  // Check duplicate initializations
  const duplicates = checkDuplicateInitializations();
  console.log(`Icon initialization scripts: ${duplicates.count}`);
  
  console.groupEnd();
  
  return {
    spriteStatus,
    missingIcons,
    duplicates
  };
}

// Expose the diagnostics function globally
window.runIconDiagnostics = runIconDiagnostics;

// Run diagnostics when the page is fully loaded
window.addEventListener('load', () => {
  // Wait a bit to ensure all icons have been processed
  setTimeout(runIconDiagnostics, 1000);
});

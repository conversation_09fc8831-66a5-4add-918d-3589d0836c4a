"""
Admin business unit and segment management routes.
"""

from flask import render_template, redirect, url_for, flash, request, jsonify, current_app
from flask_login import login_required

from app import db
from app.models import User, BusinessUnit, BusinessSegment, Activity, EmployeeDetail
from app.utils.decorators import admin_required, log_activity
from app.utils.cache_helpers import invalidate_business_cache, invalidate_dashboard_cache
from app.utils.pagination import paginate_query
from app.utils.ajax_helpers import ajax_response
from app.routes.admin import admin_bp


# Business Unit Management
@admin_bp.route('/business-units')
@login_required
@admin_required
@log_activity('Viewed business units list', entity_type='BusinessUnit', category=Activity.CATEGORY_ADMIN, method=Activity.METHOD_READ)
def business_units():
    """Display paginated list of business units."""
    # Use the centralized pagination utility
    query = BusinessUnit.query
    per_page = current_app.config.get('PAGINATION_PER_PAGE_ADMIN', 20)
    business_units, pagination = paginate_query(query, per_page=per_page)

    managers = User.query.filter(User.role.in_(['Admin', 'Manager'])).all()

    return render_template('admin/business/units/index.html',
                          business_units=business_units,
                          managers=managers,
                          pagination=pagination,
                          active_page='business_units')


@admin_bp.route('/business-units/<int:unit_id>/json')
@login_required
@admin_required
def get_business_unit(unit_id):
    """Get business unit details in JSON format."""
    unit = BusinessUnit.query.get_or_404(unit_id)
    return jsonify({
        'id': unit.id,
        'name': unit.name,
        'code': unit.code,
        'description': unit.description,
        'manager_id': unit.manager_id,
        'is_active': unit.is_active
    })


@admin_bp.route('/business-units/<int:unit_id>/view')
@login_required
@admin_required
@log_activity('Viewed business unit details', entity_type='BusinessUnit', category=Activity.CATEGORY_ADMIN, method=Activity.METHOD_READ)
def view_business_unit(unit_id):
    """View detailed information about a business unit."""
    # Get the business unit
    business_unit = BusinessUnit.query.get_or_404(unit_id)

    # Get managers for the form
    managers = User.query.filter(User.role.in_(['Admin', 'Manager'])).all()

    return render_template('admin/business/units/view.html',
                          business_unit=business_unit,
                          managers=managers,
                          active_page='business_units')


@admin_bp.route('/business-units/create', methods=['POST'])
@login_required
@admin_required
@log_activity('Created business unit', entity_type='BusinessUnit', category=Activity.CATEGORY_DATA, method=Activity.METHOD_CREATE)
def create_business_unit():
    """Create a new business unit."""
    name = request.form.get('name')
    code = request.form.get('code')
    description = request.form.get('description')
    manager_id = request.form.get('manager_id')

    # Validate input
    if not name or not code:
        flash('Name and code are required', 'error')
        return redirect(url_for('admin.business_units'))

    # Check if code is already used
    existing_unit = BusinessUnit.query.filter_by(code=code).first()
    if existing_unit:
        flash('Business unit code already exists', 'error')
        return redirect(url_for('admin.business_units'))

    # Create new business unit
    new_unit = BusinessUnit(
        name=name,  # type: ignore
        code=code,  # type: ignore
        description=description,  # type: ignore
        manager_id=manager_id if manager_id else None  # type: ignore
    )

    db.session.add(new_unit)
    db.session.commit()

    # Invalidate cache
    current_app.logger.info("Invalidating business cache after creating unit")
    invalidate_business_cache()
    invalidate_dashboard_cache()

    flash('Business unit created successfully', 'success')
    return redirect(url_for('admin.business_units', status='success', action='created'))


@admin_bp.route('/business-units/<int:unit_id>/update', methods=['POST'])
@login_required
@admin_required
@log_activity('Updated business unit', entity_type='BusinessUnit', category=Activity.CATEGORY_DATA, method=Activity.METHOD_UPDATE, track_changes=True)
def update_business_unit(unit_id):
    """Update an existing business unit."""
    unit = BusinessUnit.query.get_or_404(unit_id)

    name = request.form.get('name')
    code = request.form.get('code')
    description = request.form.get('description')
    manager_id = request.form.get('manager_id')
    is_active = True if request.form.get('is_active') else False

    # Validate input
    if not name or not code:
        flash('Name and code are required', 'error')
        return redirect(url_for('admin.business_units'))

    # Check if code is taken by another unit
    existing_unit = BusinessUnit.query.filter(BusinessUnit.code == code, BusinessUnit.id != unit_id).first()
    if existing_unit:
        flash('Business unit code already exists', 'error')
        return redirect(url_for('admin.business_units'))

    # Update business unit
    unit.name = name
    unit.code = code
    unit.description = description
    unit.manager_id = manager_id if manager_id else None
    unit.is_active = is_active

    db.session.commit()

    # Invalidate cache
    current_app.logger.info("Invalidating business cache after updating unit")
    invalidate_business_cache()
    invalidate_dashboard_cache()

    flash('Business unit updated successfully', 'success')
    return redirect(url_for('admin.business_units', status='success', action='updated'))


@admin_bp.route('/business-units/<int:unit_id>/delete', methods=['POST'])
@login_required
@admin_required
@log_activity('Deleted business unit', entity_type='BusinessUnit', category=Activity.CATEGORY_DATA, severity=Activity.SEVERITY_WARNING, method=Activity.METHOD_DELETE)
def delete_business_unit(unit_id):
    """Delete a business unit."""
    unit = BusinessUnit.query.get_or_404(unit_id)

    # Check if there are employees in this unit
    if unit.employee_details:
        flash('Cannot delete business unit with assigned employees', 'error')
        return redirect(url_for('admin.business_units'))

    # Check if there are segments in this unit
    if unit.segments.count() > 0:
        flash('Cannot delete business unit with assigned segments', 'error')
        return redirect(url_for('admin.business_units'))

    # Delete business unit
    db.session.delete(unit)
    db.session.commit()

    # Invalidate cache
    current_app.logger.info("Invalidating business cache after deleting unit")
    invalidate_business_cache()
    invalidate_dashboard_cache()

    flash('Business unit deleted successfully', 'success')
    return redirect(url_for('admin.business_units', status='success', action='deleted'))


# Business Segment Management
@admin_bp.route('/business-segments')
@login_required
@admin_required
@log_activity('Viewed business segments list', entity_type='BusinessSegment', category=Activity.CATEGORY_ADMIN, method=Activity.METHOD_READ)
def business_segments():
    """Display paginated list of business segments."""
    # Use the centralized pagination utility
    query = BusinessSegment.query
    per_page = current_app.config.get('PAGINATION_PER_PAGE_ADMIN', 20)
    business_segments, pagination = paginate_query(query, per_page=per_page)

    business_units = BusinessUnit.query.all()
    managers = User.query.filter(User.role.in_(['Admin', 'Manager'])).all()

    return render_template('admin/business/segments/index.html',
                          business_segments=business_segments,
                          business_units=business_units,
                          managers=managers,
                          pagination=pagination,
                          active_page='business_segments')


@admin_bp.route('/business-segments/<int:segment_id>/json')
@login_required
@admin_required
def get_business_segment(segment_id):
    """Get business segment details in JSON format."""
    segment = BusinessSegment.query.get_or_404(segment_id)
    return jsonify({
        'id': segment.id,
        'name': segment.name,
        'code': segment.code,
        'description': segment.description,
        'business_unit_id': segment.business_unit_id,
        'manager_id': segment.manager_id,
        'is_active': segment.is_active
    })


@admin_bp.route('/business-segments/create', methods=['POST'])
@login_required
@admin_required
@log_activity('Created business segment', entity_type='BusinessSegment', category=Activity.CATEGORY_DATA, method=Activity.METHOD_CREATE)
def create_business_segment():
    """Create a new business segment."""
    name = request.form.get('name')
    code = request.form.get('code')
    description = request.form.get('description')
    business_unit_id = request.form.get('business_unit_id')
    manager_id = request.form.get('manager_id')

    # Validate input
    if not name or not code or not business_unit_id:
        flash('Name, code, and business unit are required', 'error')
        return redirect(url_for('admin.business_segments'))

    # Check if code is already used
    existing_segment = BusinessSegment.query.filter_by(code=code).first()
    if existing_segment:
        flash('Business segment code already exists', 'error')
        return redirect(url_for('admin.business_segments'))

    # Create new business segment
    new_segment = BusinessSegment(
        name=name,  # type: ignore
        code=code,  # type: ignore
        description=description,  # type: ignore
        business_unit_id=business_unit_id,  # type: ignore
        manager_id=manager_id if manager_id else None  # type: ignore
    )

    db.session.add(new_segment)
    db.session.commit()

    # Invalidate cache
    current_app.logger.info("Invalidating business cache after creating segment")
    invalidate_business_cache()
    invalidate_dashboard_cache()

    flash('Business segment created successfully', 'success')
    return redirect(url_for('admin.business_segments', status='success', action='created'))


@admin_bp.route('/business-segments/<int:segment_id>/update', methods=['POST'])
@login_required
@admin_required
@log_activity('Updated business segment', entity_type='BusinessSegment', category=Activity.CATEGORY_DATA, method=Activity.METHOD_UPDATE, track_changes=True)
def update_business_segment(segment_id):
    """Update an existing business segment."""
    segment = BusinessSegment.query.get_or_404(segment_id)

    name = request.form.get('name')
    code = request.form.get('code')
    description = request.form.get('description')
    business_unit_id = request.form.get('business_unit_id')
    manager_id = request.form.get('manager_id')
    is_active = True if request.form.get('is_active') else False

    # Validate input
    if not name or not code or not business_unit_id:
        flash('Name, code, and business unit are required', 'error')
        return redirect(url_for('admin.business_segments'))

    # Check if code is taken by another segment
    existing_segment = BusinessSegment.query.filter(BusinessSegment.code == code, BusinessSegment.id != segment_id).first()
    if existing_segment:
        flash('Business segment code already exists', 'error')
        return redirect(url_for('admin.business_segments'))

    # Update business segment
    segment.name = name
    segment.code = code
    segment.description = description
    segment.business_unit_id = business_unit_id
    segment.manager_id = manager_id if manager_id else None
    segment.is_active = is_active

    db.session.commit()

    # Invalidate cache
    current_app.logger.info("Invalidating business cache after updating segment")
    invalidate_business_cache()
    invalidate_dashboard_cache()

    flash('Business segments updated successfully', 'success')
    return redirect(url_for('admin.business_segments', status='success', action='updated'))


@admin_bp.route('/business-segments/<int:segment_id>/delete', methods=['POST'])
@login_required
@admin_required
@log_activity('Deleted business segment', entity_type='BusinessSegment', category=Activity.CATEGORY_DATA, severity=Activity.SEVERITY_WARNING, method=Activity.METHOD_DELETE)
def delete_business_segment(segment_id):
    """Delete a business segment."""
    segment = BusinessSegment.query.get_or_404(segment_id)

    # Check if there are employees in this segment
    if segment.employee_details:
        flash('Cannot delete business segment with assigned employees', 'error')
        return redirect(url_for('admin.business_segments'))

    # Delete business segment
    db.session.delete(segment)
    db.session.commit()

    # Invalidate cache
    current_app.logger.info("Invalidating business cache after deleting segment")
    invalidate_business_cache()
    invalidate_dashboard_cache()

    flash('Business segment deleted successfully', 'success')
    return redirect(url_for('admin.business_segments', status='success', action='deleted'))

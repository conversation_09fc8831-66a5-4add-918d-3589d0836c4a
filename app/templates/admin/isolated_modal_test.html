{% extends "base.html" %}
{% from "components/modal.html" import modal %}

{% block title %}Isolated Modal Examples{% endblock %}
{% block header %}Isolated Modal Examples{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
  <div class="bg-card rounded-lg shadow-sm p-6 mb-6">
    <h1 class="text-2xl font-bold mb-4">Isolated Modal Examples</h1>
    <p class="mb-6">This page demonstrates the isolated modal component with various configurations.</p>

    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
      <!-- Basic Modal -->
      <div class="bg-muted/30 p-4 rounded-md">
        <h3 class="text-lg font-semibold mb-2">Basic Modal</h3>
        <p class="text-sm mb-4">A simple modal with default settings.</p>
        <button
          class="btn btn-primary"
          data-modal="basic-modal">
          Open Basic Modal
        </button>
      </div>

      <!-- Modal with Description -->
      <div class="bg-muted/30 p-4 rounded-md">
        <h3 class="text-lg font-semibold mb-2">Modal with Description</h3>
        <p class="text-sm mb-4">A modal with a title and description.</p>
        <button
          class="btn btn-primary"
          data-modal="description-modal">
          Open Modal with Description
        </button>
      </div>

      <!-- Large Modal -->
      <div class="bg-muted/30 p-4 rounded-md">
        <h3 class="text-lg font-semibold mb-2">Large Modal</h3>
        <p class="text-sm mb-4">A larger modal with more content.</p>
        <button
          class="btn btn-primary"
          data-modal="large-modal">
          Open Large Modal
        </button>
      </div>

      <!-- Destructive Modal -->
      <div class="bg-muted/30 p-4 rounded-md">
        <h3 class="text-lg font-semibold mb-2">Destructive Modal</h3>
        <p class="text-sm mb-4">A modal with a destructive action.</p>
        <button
          class="btn btn-destructive"
          data-modal="destructive-modal">
          Open Destructive Modal
        </button>
      </div>

      <!-- Custom Buttons Modal -->
      <div class="bg-muted/30 p-4 rounded-md">
        <h3 class="text-lg font-semibold mb-2">Custom Buttons</h3>
        <p class="text-sm mb-4">A modal with custom button text.</p>
        <button
          class="btn btn-primary"
          data-modal="custom-buttons-modal">
          Open Custom Buttons Modal
        </button>
      </div>

      <!-- No Footer Modal -->
      <div class="bg-muted/30 p-4 rounded-md">
        <h3 class="text-lg font-semibold mb-2">No Footer</h3>
        <p class="text-sm mb-4">A modal without a footer.</p>
        <button
          class="btn btn-primary"
          data-modal="no-footer-modal">
          Open No Footer Modal
        </button>
      </div>
    </div>
  </div>
</div>

<!-- Basic Modal -->
{% call modal(
  id='basic-modal',
  title='Basic Modal',
  size='md'
) %}
  <p class="mb-4">This is a basic modal with default settings.</p>
  <p>It uses the default size and button text.</p>
{% endcall %}

<!-- Modal with Description -->
{% call modal(
  id='description-modal',
  title='Modal with Description',
  description='This modal includes a description below the title.',
  size='md'
) %}
  <p>The description helps provide additional context about the modal's purpose.</p>
{% endcall %}

<!-- Large Modal -->
{% call modal(
  id='large-modal',
  title='Large Modal',
  description='This is a larger modal with more content.',
  size='lg'
) %}
  <div class="space-y-4">
    <p>This modal uses the "lg" size parameter to create a wider dialog.</p>

    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
      <div class="bg-muted/30 p-4 rounded-md">
        <h4 class="text-sm font-medium mb-2">Features:</h4>
        <ul class="list-disc pl-5 space-y-1 text-sm">
          <li>Fully accessible</li>
          <li>Keyboard navigation support</li>
          <li>Smooth animations</li>
          <li>Semi-transparent backdrop</li>
          <li>No styling conflicts</li>
          <li>Completely independent implementation</li>
        </ul>
      </div>
      <div class="bg-muted/30 p-4 rounded-md">
        <h4 class="text-sm font-medium mb-2">Benefits:</h4>
        <ul class="list-disc pl-5 space-y-1 text-sm">
          <li>Consistent UI across the application</li>
          <li>Easy to implement</li>
          <li>Customizable sizes</li>
          <li>Works with both light and dark themes</li>
          <li>No conflicts with existing code</li>
        </ul>
      </div>
    </div>
  </div>
{% endcall %}

<!-- Destructive Modal -->
{% call modal(
  id='destructive-modal',
  title='Confirm Deletion',
  description='Are you sure you want to delete this item? This action cannot be undone.',
  variant='destructive',
  action_text='Delete',
  size='sm'
) %}
  <p>This modal uses the "destructive" variant to indicate a potentially dangerous action.</p>
{% endcall %}

<!-- Custom Buttons Modal -->
{% call modal(
  id='custom-buttons-modal',
  title='Custom Buttons',
  description='This modal has custom button text.',
  action_text='Save Changes',
  cancel_text='Discard',
  size='md'
) %}
  <p>You can customize the text of both the action and cancel buttons.</p>
{% endcall %}

<!-- No Footer Modal -->
{% call modal(
  id='no-footer-modal',
  title='No Footer Modal',
  description='This modal has no footer with buttons.',
  show_footer=false,
  size='md'
) %}
  <p class="mb-4">This modal doesn't have a footer with buttons.</p>
  <p>It's useful for simple informational modals that don't require user actions.</p>
{% endcall %}
{% endblock %}

{% block scripts %}
<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Example of programmatically opening a modal
    window.openExampleModal = function() {
      showModal({
        id: 'programmatic-modal',
        content: `
          <div class="modal-header">
            <h2 id="programmatic-modal-title" class="modal-title">Programmatic Modal</h2>
            <p id="programmatic-modal-description" class="modal-description">This modal was opened programmatically.</p>
          </div>

          <div class="modal-content">
            <p>You can create and open modals dynamically using JavaScript.</p>
          </div>

          <div class="modal-footer">
            <button type="button" class="modal-btn modal-btn-cancel" data-modal-close>Close</button>
            <button type="button" class="modal-btn modal-btn-primary" id="programmatic-modal-action">Continue</button>
          </div>
        `,
        size: 'md'
      });
    };
  });
</script>
{% endblock %}

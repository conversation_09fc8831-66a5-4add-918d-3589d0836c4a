<!DOCTYPE html>
<html lang="en" class="{% if app_settings.default_theme == 'dark' %}dark{% else %}light{% endif %}">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Reset Password - {{ app_settings.site_name|default('Matrix') }}</title>
  <!-- Dynamic primary color from settings -->
  <style>
    :root {
      {% set primary = app_settings.primary_color|default('#0284c7') %}
      {% set rgb = hex_to_rgb(primary) %}
      --primary: {{ primary }};
      --primary-rgb: {{ rgb[0] }}, {{ rgb[1] }}, {{ rgb[2] }};
    }

    /* Ensure buttons have proper contrast - shadcn style */
    .btn-primary {
      {% set neutral_colors = ['#71717a', '#64748b', '#78716c', '#6b7280', '#525252'] %}
      {% if primary in neutral_colors %}
        /* For neutral colors, use white/black based on theme */
        background-color: var(--foreground);
        border-color: var(--foreground);
        color: var(--background);
      {% else %}
        /* For other colors, use the actual color with black/white text based on contrast */
        background-color: var(--primary);
        border-color: var(--primary);
        color: {% if hex_to_rgb(primary)|sum < 382 %}white{% else %}black{% endif %};
      {% endif %}
      transition: opacity 0.2s, background-color 0.2s;
    }

    .btn-primary:hover {
      {% if primary in neutral_colors %}
        background-color: var(--foreground);
        border-color: var(--foreground);
      {% else %}
        background-color: var(--primary);
        border-color: var(--primary);
      {% endif %}
      opacity: 0.9;
    }

    .text-primary {
      color: var(--primary);
    }
  </style>
  <link rel="stylesheet" href="{{ url_for('static', filename='css/main.css') }}">
  <script src="https://unpkg.com/lucide@latest"></script>
  <script src="https://unpkg.com/@lucide/web/dist/umd/lucide.js"></script>
</head>
<body class="bg-background text-foreground antialiased flex min-h-screen items-center justify-center">
  <div class="w-full max-w-md p-8 space-y-8">
    <div class="flex flex-col space-y-2 text-center">
      <h1 class="text-2xl font-semibold tracking-tight">Reset password</h1>
      <p class="text-sm text-muted-foreground">Enter your new password below</p>
    </div>

    {% with messages = get_flashed_messages(with_categories=true) %}
      {% if messages %}
        {% for category, message in messages %}
          <div class="p-3 mb-4 text-sm rounded-md {% if category == 'error' %}bg-destructive/15 text-destructive{% else %}bg-primary/15 text-primary{% endif %}">
            {{ message }}
          </div>
        {% endfor %}
      {% endif %}
    {% endwith %}

    <form action="{{ url_for('auth.reset_password', token=token) }}" method="POST" class="space-y-6">
      <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">

      <div class="space-y-2">
        <label for="password" class="text-sm font-medium leading-none">New Password</label>
        <input type="password" id="password" name="password" required class="input w-full">
      </div>

      <div class="space-y-2">
        <label for="confirm_password" class="text-sm font-medium leading-none">Confirm New Password</label>
        <input type="password" id="confirm_password" name="confirm_password" required class="input w-full">
      </div>

      <button type="submit" class="btn btn-primary btn-block btn-md">Reset Password</button>
    </form>

    <div class="text-center text-sm">
      <a href="{{ url_for('auth.login') }}" class="text-primary hover:underline">
        <i data-lucide="arrow-left" class="w-4 h-4 inline-block mr-1"></i>
        Back to login
      </a>
    </div>
  </div>

  <script>
    // Lucide icons are now initialized automatically

    // Theme handling
    const getThemePreference = () => {
      if (typeof localStorage !== 'undefined' && localStorage.getItem('theme')) {
        return localStorage.getItem('theme');
      }
      return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
    };

    const setTheme = (theme) => {
      const html = document.documentElement;
      if (theme === 'dark') {
        html.classList.add('dark');
      } else {
        html.classList.remove('dark');
      }
      localStorage.setItem('theme', theme);
    };

    // Set initial theme
    setTheme(getThemePreference());
  </script>
</body>
</html>

"""
Cache helper functions for the application.
"""

from flask import current_app
from app import cache


def invalidate_cache_keys(keys):
    """
    Invalidate specific cache keys.
    
    Args:
        keys: A list of cache keys to invalidate
    """
    if not keys:
        return
    
    for key in keys:
        try:
            cache.delete(key)
            current_app.logger.info(f"Cache invalidated for key: {key}")
        except Exception as e:
            current_app.logger.error(f"Error invalidating cache for key {key}: {str(e)}")


def invalidate_dashboard_cache():
    """Invalidate dashboard-related cache keys."""
    invalidate_cache_keys(['dashboard_stats'])


def invalidate_business_cache():
    """Invalidate business-related cache keys."""
    invalidate_cache_keys(['business_units', 'business_segments'])


def invalidate_employee_cache():
    """Invalidate employee-related cache keys."""
    invalidate_cache_keys(['employees'])


def invalidate_all_cache():
    """Invalidate all cache keys."""
    try:
        cache.clear()
        current_app.logger.info("All cache cleared")
    except Exception as e:
        current_app.logger.error(f"Error clearing all cache: {str(e)}")

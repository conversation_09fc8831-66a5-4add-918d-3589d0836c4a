"""
User seeder module.
This module provides functions to seed the database with user data.
"""

from datetime import datetime
import os
import sys

def seed_users():
    """
    Seed the database with user data.

    Returns:
        list: The created user objects
    """
    # Import here to allow running as standalone script
    from app import db
    from app.models import User, PH_TZ
    # Create admin user if it doesn't exist
    existing_admin = User.query.filter_by(email="<EMAIL>").first()
    if existing_admin:
        print("Admin user already exists, skipping...")
        return [existing_admin]

    # Create admin user
    admin = User(
        name="Admin User",
        email="<EMAIL>",
        role="Admin",
        created_at=datetime.now(PH_TZ)
    )
    admin.set_password("AdminPassword@123")

    db.session.add(admin)
    db.session.commit()
    print("Users seeded successfully!")
    return [admin]

if __name__ == "__main__":
    import os
    import sys
    # Add the parent directory to the Python path
    sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))
    from app import create_app
    app = create_app()
    with app.app_context():
        seed_users()

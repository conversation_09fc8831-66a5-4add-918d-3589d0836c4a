{% macro hidden_data(activity_dates, activity_counts, user_counts, activity_labels, activity_data, entity_labels=None, entity_data=None) %}
<!-- Hidden data elements for JavaScript -->
<div class="hidden">
    <div id="activity-dates-data">{{ activity_dates|safe }}</div>
    <div id="activity-counts-data">{{ activity_counts|safe }}</div>
    <div id="user-counts-data">{{ user_counts|safe }}</div>
    <div id="activity-labels-data">{{ activity_labels|safe }}</div>
    <div id="activity-data-data">{{ activity_data|safe }}</div>
    {% if entity_labels and entity_data %}
    <div id="entity-labels-data">{{ entity_labels|safe }}</div>
    <div id="entity-data-data">{{ entity_data|safe }}</div>
    {% endif %}

</div>
{% endmacro %}

"""
Models package initialization.
This file imports all models to make them available when importing from the models package.
"""

# Import common utilities and constants
from app import db
import pytz
from datetime import datetime

# Set Philippine Timezone
PH_TZ = pytz.timezone('Asia/Manila')

# Import all models to make them available when importing from the models package
from app.models.user import User, WeakPasswordError
from app.models.activity import Activity
from app.models.business import BusinessUnit, BusinessSegment
from app.models.employee import EmployeeDetail, team_members
from app.models.messaging import PasswordReset
from app.models.settings import Setting
from app.models.team import Team, TeamGroup
from app.models.attendance import (
    Holiday, WorkScheduleDefinition, EmployeeSchedule, AttendanceType, AttendanceRecord
)

# Define __all__ to explicitly specify what is exported
__all__ = [
    'db', 'PH_TZ',
    'User', 'WeakPasswordError',
    'Activity',
    'BusinessUnit', 'BusinessSegment',
    'EmployeeDetail', 'team_members',
    'PasswordReset',
    'Setting',
    'Team', 'TeamGroup',
    'Holiday', 'WorkScheduleDefinition', 'EmployeeSchedule', 'AttendanceType', 'AttendanceRecord'
]

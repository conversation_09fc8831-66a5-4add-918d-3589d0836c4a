{% extends "base.html" %}

{% from "components/page_header.html" import page_header %}
{% from "components/table_header.html" import table_header %}
{% from "components/simple_table.html" import simple_table %}
{% from "components/action_buttons.html" import action_buttons %}

{% block title %}Business Segments{% endblock %}

{% block header %}Business Segments{% endblock %}

{% block content %}
<div class="space-y-6">
  {{ page_header(
    title="Business Segments",
    button_text="Add Business Segment",
    button_icon="plus",
    button_action="drawerManager.openForm('business_segment')",
    description="Manage your organization's business segments"
  ) }}

  <div class="card">
    {{ table_header(
      title="Business Segment List"
    ) }}

    {% call simple_table(
      headers=[
        {'label': 'Code'},
        {'label': 'Name'},
        {'label': 'Business Unit'},
        {'label': 'Manager'},
        {'label': 'Employees'},
        {'label': 'Status'},
        {'label': 'Actions', 'align': 'right'}
      ],
      items=business_segments,
      empty_icon="layers",
      empty_title="No business segments found",
      empty_description="Create your first business segment to get started",
      empty_button_text="Add Business Segment",
      empty_button_icon="plus",
      empty_button_action="drawerManager.openForm('business_segment')"
    ) %}
      {% for segment in business_segments %}
      <tr class="border-b border-border hover:bg-muted/30">
        <td class="px-4 py-3 text-sm">{{ segment.code }}</td>
        <td class="px-4 py-3 text-sm">{{ segment.name }}</td>
        <td class="px-4 py-3 text-sm">{{ segment.business_unit.name }}</td>
        <td class="px-4 py-3 text-sm">{{ segment.manager.name if segment.manager else 'Not assigned' }}</td>
        <td class="px-4 py-3 text-sm">{{ segment.employees|length }}</td>
        <td class="px-4 py-3 text-sm">
          {% if segment.is_active %}
            <span class="badge badge-success">Active</span>
          {% else %}
            <span class="badge badge-secondary">Inactive</span>
          {% endif %}
        </td>
        <td class="px-4 py-3 text-sm text-right">
          {{ action_buttons([
            {'type': 'button', 'action': 'drawerManager.openForm(\'business_segment\', ' ~ segment.id ~ ')', 'icon': 'edit', 'title': 'Edit'},
            {'type': 'button', 'action': 'confirmDeleteBusinessSegment(' ~ segment.id ~ ', \'' ~ segment.name ~ '\')', 'icon': 'trash-2', 'variant': 'text-destructive', 'title': 'Delete'}
          ]) }}
        </td>
      </tr>
      {% endfor %}
    {% endcall %}

    {% include "partials/pagination.html" %}
  </div>
</div>
{% endblock %}

{% block scripts %}
<script>
  document.addEventListener('DOMContentLoaded', function() {
      // Clean URL parameters if they exist (for backward compatibility)
      const urlParams = new URLSearchParams(window.location.search);
      if (urlParams.has('status')) {
          const newUrl = window.location.pathname;
          window.history.replaceState({}, document.title, newUrl);
      }
  });

  // Confirm delete business segment
  function confirmDeleteBusinessSegment(segmentId, segmentName) {
      showAlertDialog({
          title: 'Delete Business Segment',
          description: `Are you sure you want to delete ${segmentName}? This action cannot be undone.`,
          variant: 'destructive',
          onConfirm: () => {
              // Submit form to delete endpoint
              const form = document.createElement('form');
              form.method = 'POST';
              form.action = `{{ url_for('admin.delete_business_segment', segment_id=0) }}`.replace('0', segmentId);

              const csrfToken = document.createElement('input');
              csrfToken.type = 'hidden';
              csrfToken.name = 'csrf_token';
              csrfToken.value = '{{ csrf_token() }}';

              form.appendChild(csrfToken);
              document.body.appendChild(form);
              form.submit();
          }
      });
  }
</script>
{% endblock %}

"""
Database utility functions for seeders.
This module provides utility functions for database operations like clearing data.
"""

import os
import sys

def clear_database(confirm=True):
    """
    Clear all data from the database while respecting foreign key constraints.

    Args:
        confirm (bool): Whether to ask for confirmation before clearing data

    Returns:
        bool: True if data was cleared, False otherwise
    """
    # Import here to allow running as standalone script
    from app import db
    from sqlalchemy import text
    from app.models import (
        User, EmployeeDetail, BusinessUnit, BusinessSegment,
        Message, Activity, PasswordReset, Team, TeamGroup, Setting
    )

    if confirm:
        print("WARNING: This will delete ALL data from the database!")
        print("Are you sure you want to continue? (y/n)")
        response = input().strip().lower()
        if response != 'y':
            print("Operation cancelled.")
            return False

    try:
        # Delete data in a specific order to respect foreign key constraints
        print("Clearing database...")

        # First, delete association table records
        print("Clearing team members...")
        db.session.execute(text('DELETE FROM team_members'))

        # Delete dependent records first
        print("Clearing activities...")
        Activity.query.delete()

        print("Clearing messages...")
        Message.query.delete()

        print("Clearing password resets...")
        PasswordReset.query.delete()

        print("Clearing team groups...")
        TeamGroup.query.delete()

        print("Clearing teams...")
        Team.query.delete()

        print("Clearing employee details...")
        EmployeeDetail.query.delete()

        print("Clearing business segments...")
        BusinessSegment.query.delete()

        print("Clearing business units...")
        BusinessUnit.query.delete()

        print("Clearing users...")
        User.query.delete()

        # Don't clear settings by default as they may contain important configuration
        # Uncomment if you want to clear settings too
        # print("Clearing settings...")
        # Setting.query.delete()

        # Commit the changes
        db.session.commit()
        print("Database cleared successfully!")
        return True
    except Exception as e:
        db.session.rollback()
        print(f"Error clearing database: {str(e)}")
        return False

def get_or_create(session, model, defaults=None, **kwargs):
    """
    Get an existing instance of a model or create a new one.

    Args:
        session: The SQLAlchemy session.
        model: The model class.
        defaults (dict, optional): A dictionary of attributes to set if a new instance is created.
        **kwargs: Attributes to filter by to find an existing instance.

    Returns:
        tuple: (instance, created) where 'instance' is the retrieved or created object
               and 'created' is a boolean indicating if a new object was created.
    """
    instance = session.query(model).filter_by(**kwargs).first()
    if instance:
        return instance, False
    else:
        params = {k: v for k, v in kwargs.items()}
        if defaults:
            params.update(defaults)
        instance = model(**params)
        session.add(instance)
        # Note: session.commit() is usually handled by the calling seeder function
        # after all get_or_create operations for that seeder are done.
        # If you want to commit immediately, you can add session.commit() here,
        # but it's generally better to batch commits.
        return instance, True

if __name__ == "__main__":
    import argparse

    # Parse command line arguments
    parser = argparse.ArgumentParser(description='Clear the database.')
    parser.add_argument('--yes', '-y', action='store_true', help='Skip confirmation prompts')
    args = parser.parse_args()

    # Add the parent directory to the Python path
    sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))
    from app import create_app
    app = create_app()
    with app.app_context():
        clear_database(confirm=not args.yes)

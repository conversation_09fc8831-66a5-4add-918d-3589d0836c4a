"""
Routes for team management.
"""
from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify
from flask_login import login_required
from app.models import Team, TeamGroup, EmployeeDetail, User, Activity
from app import db
from app.utils.decorators import admin_required, log_activity
from app.utils.pagination import paginate_query
from app.utils.ajax_helpers import ajax_response
from app.utils.helpers import generate_slug
from datetime import datetime
import json
from sqlalchemy import or_

# Create a Blueprint
teams_bp = Blueprint('teams', __name__, url_prefix='/teams')


@teams_bp.route('/')
@login_required
@log_activity('Viewed teams list', entity_type='Team', category=Activity.CATEGORY_USER, method=Activity.METHOD_READ, skip_ajax=True)
def index():
    """List all teams."""
    # Build the query for teams
    query = Team.query

    # Use centralized pagination with default per_page from config
    teams, pagination = paginate_query(query)

    return render_template('teams/index.html',
                          teams=teams,
                          pagination=pagination,
                          active_page='teams')


@teams_bp.route('/<string:slug>')
@login_required
@log_activity('Viewed team details', entity_type='Team', category=Activity.CATEGORY_USER, method=Activity.METHOD_READ, skip_ajax=True)
def view(slug):
    """View a team's details."""
    team = Team.query.filter_by(slug=slug).first_or_404()
    groups = team.groups.filter_by(is_active=True).all()

    return render_template('teams/view.html',
                          team=team,
                          groups=groups,
                          active_page='teams')


@teams_bp.route('/create', methods=['POST'])
@login_required
@admin_required
@log_activity('Created team', entity_type='Team', category=Activity.CATEGORY_DATA, method=Activity.METHOD_CREATE)
def create():
    """Create a new team."""
    name = request.form.get('name')
    short_name = request.form.get('short_name')
    description = request.form.get('description')

    # Validate input
    if not name:
        flash('Team name is required', 'error')
        return redirect(url_for('teams.index'))

    # Generate slug from name
    slug = generate_slug(name)

    # Check if slug already exists
    existing_team = Team.query.filter_by(slug=slug).first()
    if existing_team:
        flash(f'A team with the name "{name}" already exists', 'error')
        return redirect(url_for('teams.index'))

    # Create new team
    new_team = Team(
        name=name,  # type: ignore
        short_name=short_name,  # type: ignore
        slug=slug,  # type: ignore
        description=description  # type: ignore
    )

    db.session.add(new_team)
    db.session.commit()

    # Success message will be shown via toast notification using URL parameters
    return redirect(url_for('teams.view', slug=slug, status='success', action='created'))


@teams_bp.route('/<int:team_id>/update', methods=['POST'])
@login_required
@admin_required
@log_activity('Updated team', entity_type='Team', category=Activity.CATEGORY_DATA, method=Activity.METHOD_UPDATE, track_changes=True)
def update(team_id):
    """Update a team."""
    team = Team.query.get_or_404(team_id)

    name = request.form.get('name')
    short_name = request.form.get('short_name')
    description = request.form.get('description')

    # Validate input
    if not name:
        flash('Team name is required', 'error')
        return redirect(url_for('teams.view', slug=team.slug))

    # Check if name has changed
    if name != team.name:
        # Generate new slug
        new_slug = generate_slug(name)

        # Check if new slug already exists
        existing_team = Team.query.filter(Team.slug == new_slug, Team.id != team.id).first()
        if existing_team:
            flash(f'A team with the name "{name}" already exists', 'error')
            return redirect(url_for('teams.view', slug=team.slug))

        # Update slug
        team.slug = new_slug

    # Update team
    team.name = name
    team.short_name = short_name
    team.description = description

    db.session.commit()

    # Success message will be shown via toast notification using URL parameters
    return redirect(url_for('teams.view', slug=team.slug, status='success', action='updated'))


@teams_bp.route('/<int:team_id>/delete', methods=['POST'])
@login_required
@admin_required
@log_activity('Deleted team', entity_type='Team', category=Activity.CATEGORY_DATA, method=Activity.METHOD_DELETE, severity=Activity.SEVERITY_WARNING)
def delete(team_id):
    """Delete a team."""
    team = Team.query.get_or_404(team_id)

    # Delete team
    db.session.delete(team)
    db.session.commit()

    # Success message will be shown via toast notification using URL parameters
    return redirect(url_for('teams.index', status='success', action='deleted'))


@teams_bp.route('/<int:team_id>/members')
@login_required
@log_activity('Viewed team members', entity_type='Team', category=Activity.CATEGORY_USER, method=Activity.METHOD_READ, skip_ajax=True)
def members(team_id):
    """View team members."""
    team = Team.query.get_or_404(team_id)

    return render_template('teams/members.html',
                          team=team,
                          active_page='teams')


@teams_bp.route('/<int:team_id>/members/add', methods=['POST'])
@login_required
@admin_required
@log_activity('Added team member', entity_type='Team', category=Activity.CATEGORY_DATA, method=Activity.METHOD_UPDATE)
def add_member(team_id):
    """Add a member to a team."""
    team = Team.query.get_or_404(team_id)
    employee_id = request.form.get('employee_id')

    # Validate input
    if not employee_id:
        flash('Employee is required', 'error')
        return redirect(url_for('teams.members', team_id=team_id))

    # Get employee
    employee = EmployeeDetail.query.get_or_404(employee_id)

    # Check if employee is already a member
    if employee in team.members:
        flash(f'{employee.user.name} is already a member of this team', 'warning')
        return redirect(url_for('teams.members', team_id=team_id))

    # Add employee to team
    team.members.append(employee)
    db.session.commit()

    # Success message will be shown via toast notification using URL parameters
    return redirect(url_for('teams.members', team_id=team_id, status='success', action='member_added', name=employee.user.name))


@teams_bp.route('/<int:team_id>/members/<int:employee_id>/remove', methods=['POST'])
@login_required
@admin_required
@log_activity('Removed team member', entity_type='Team', category=Activity.CATEGORY_DATA, method=Activity.METHOD_UPDATE)
def remove_member(team_id, employee_id):
    """Remove a member from a team."""
    team = Team.query.get_or_404(team_id)
    employee = EmployeeDetail.query.get_or_404(employee_id)

    # Check if employee is a member
    if employee not in team.members:
        flash(f'{employee.user.name} is not a member of this team', 'warning')
        return redirect(url_for('teams.members', team_id=team_id))

    # Remove employee from team
    team.members.remove(employee)
    db.session.commit()

    # Success message will be shown via toast notification using URL parameters
    return redirect(url_for('teams.members', team_id=team_id, status='success', action='member_removed', name=employee.user.name))


# Team Group routes
@teams_bp.route('/<int:team_id>/groups/create', methods=['POST'])
@login_required
@admin_required
@log_activity('Created team group', entity_type='TeamGroup', category=Activity.CATEGORY_DATA, method=Activity.METHOD_CREATE)
def create_group(team_id):
    """Create a new team group."""
    team = Team.query.get_or_404(team_id)

    name = request.form.get('name')
    description = request.form.get('description')

    # Validate input
    if not name:
        flash('Group name is required', 'error')
        return redirect(url_for('teams.view', slug=team.slug))

    # Create new group
    new_group = TeamGroup(
        team_id=team_id,  # type: ignore
        name=name,  # type: ignore
        description=description  # type: ignore
    )

    db.session.add(new_group)
    db.session.commit()

    # Success message will be shown via toast notification using URL parameters
    return redirect(url_for('teams.view', slug=team.slug, status='success', action='group_created'))


@teams_bp.route('/groups/<int:group_id>/update', methods=['POST'])
@login_required
@admin_required
@log_activity('Updated team group', entity_type='TeamGroup', category=Activity.CATEGORY_DATA, method=Activity.METHOD_UPDATE, track_changes=True)
def update_group(group_id):
    """Update a team group."""
    group = TeamGroup.query.get_or_404(group_id)

    name = request.form.get('name')
    description = request.form.get('description')

    # Validate input
    if not name:
        flash('Group name is required', 'error')
        return redirect(url_for('teams.view', slug=group.team.slug))

    # Update group
    group.name = name
    group.description = description

    db.session.commit()

    # Success message will be shown via toast notification using URL parameters
    return redirect(url_for('teams.view', slug=group.team.slug, status='success', action='group_updated'))


@teams_bp.route('/groups/<int:group_id>/delete', methods=['POST'])
@login_required
@admin_required
@log_activity('Deleted team group', entity_type='TeamGroup', category=Activity.CATEGORY_DATA, method=Activity.METHOD_DELETE, severity=Activity.SEVERITY_WARNING)
def delete_group(group_id):
    """Delete a team group."""
    group = TeamGroup.query.get_or_404(group_id)
    team_slug = group.team.slug

    # Delete group
    db.session.delete(group)
    db.session.commit()

    # Success message will be shown via toast notification using URL parameters
    return redirect(url_for('teams.view', slug=team_slug, status='success', action='group_deleted'))


# API routes for forms
@teams_bp.route('/api/teams')
@login_required
@log_activity('Retrieved teams list via API', entity_type='Team', category=Activity.CATEGORY_DATA, method=Activity.METHOD_READ, skip_ajax=True)
def api_teams():
    """Get all teams as JSON."""
    teams = Team.query.filter_by(is_active=True).all()
    return jsonify([team.to_dict() for team in teams])


@teams_bp.route('/api/teams/<int:team_id>')
@login_required
@log_activity('Retrieved team details via API', entity_type='Team', category=Activity.CATEGORY_DATA, method=Activity.METHOD_READ, skip_ajax=True)
def api_team(team_id):
    """Get a team as JSON."""
    team = Team.query.get_or_404(team_id)
    return jsonify(team.to_dict())


@teams_bp.route('/api/teams/<int:team_id>/members')
@login_required
@log_activity('Retrieved team members via API', entity_type='Team', category=Activity.CATEGORY_DATA, method=Activity.METHOD_READ, skip_ajax=True)
def api_team_members(team_id):
    """Get team members as JSON."""
    team = Team.query.get_or_404(team_id)
    return jsonify([{
        'id': member.id,
        'user_id': member.user_id,
        'name': member.user.name,
        'email': member.user.email,
        'position': member.position,
        'emp_status': member.emp_status
    } for member in team.members])


@teams_bp.route('/api/teams/<int:team_id>/groups')
@login_required
@log_activity('Retrieved team groups via API', entity_type='TeamGroup', category=Activity.CATEGORY_DATA, method=Activity.METHOD_READ, skip_ajax=True)
def api_team_groups(team_id):
    """Get team groups as JSON."""
    team = Team.query.get_or_404(team_id)
    return jsonify([group.to_dict() for group in team.groups])

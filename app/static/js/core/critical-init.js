/**
 * Critical initialization scripts to prevent flash of unstyled content (FOUC)
 * Optimized for performance and reduced flickering
 */

// Theme initialization - run immediately
(function() {
  // Add theme-transition class early to ensure smooth transitions
  document.documentElement.classList.add('theme-transition');

  function getThemePreference() {
    const savedTheme = localStorage.getItem('theme');
    const defaultTheme = document.documentElement.getAttribute('data-default-theme') || 'system';

    if (savedTheme) {
      return savedTheme;
    } else if (defaultTheme === 'system') {
      return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
    } else {
      return defaultTheme;
    }
  }

  // Apply theme immediately to prevent flash
  const theme = getThemePreference();
  if (theme === 'dark') {
    document.documentElement.classList.add('dark');
  }

  // Preload theme assets
  const link = document.createElement('link');
  link.rel = 'preload';
  link.as = 'style';
  link.href = '/static/css/theme-transition.css';
  document.head.appendChild(link);
})();

// Make critical icons available globally
(function() {
  // This will be populated by the critical-icons.js module
  window.criticalIcons = [
    "panel-left", "chevron-down", "chevron-right", "chevron-up", "chevron-left",
    "menu", "home", "file-text", "users", "settings", "log-out", "bell",
    "alert-triangle", "alert-circle", "check-circle", "x-circle", "info",
    "x", "check", "plus", "minus", "edit", "trash", "copy", "sun", "moon",
    "monitor", "search", "user", "calendar", "clock", "bar-chart", "trash-2",
    "refresh-cw", "bar-chart-2", "line-chart", "users-round", "briefcase", "layers",
    "activity", "eye", "database", "shield", "history", "key", "arrow-left",
    "building", "mail", "save", "file", "filter", "tag", "external-link",
    "layout-dashboard", "lock", "phone", "search-x", "user-plus", "arrow-right",
    "list", "shield-alert", "user-check", "user-cog"
  ];
})();

// Sidebar initialization - run immediately
(function() {
  var isCollapsed = localStorage.getItem('sidebarCollapsed') === 'true';
  if (isCollapsed) document.documentElement.classList.add('sidebar-pre-collapsed');

  // Force mobile margin reset immediately
  if (window.innerWidth < 1024) {
    document.documentElement.classList.add('mobile-view');

    // Add this style to the head
    var style = document.createElement('style');
    style.textContent = '@media (max-width: 1023px) { #main-content { margin-left: 0 !important; } }';
    document.head.appendChild(style);
  }

  // Set cookie for server-side rendering
  if (isCollapsed) {
    document.cookie = 'sidebarCollapsed=true; path=/; max-age=31536000';
  }
})();

"""
API routes for system health and status.
"""

from datetime import datetime, timedelta
from flask import jsonify, current_app, request
from flask_login import login_required, current_user

from app import db
from app.models import Activity
from app.routes.api import api_bp
from app.routes.api.utils import handle_api_error
from app.utils.decorators import admin_required, log_activity
from app.utils.cache_helpers import (
    invalidate_dashboard_cache, invalidate_business_cache,
    invalidate_employee_cache, invalidate_all_cache
)
from app.utils.db_optimizations import get_connection_pool_status, query_profiler


@api_bp.route('/system/health', methods=['GET'])
@login_required
@admin_required
@handle_api_error
def system_health():
    """Get system health status based on error logs and activity."""
    # Get current date and time
    now = datetime.now()
    period_start = now - timedelta(days=30)

    # Count error activities in the last 30 days
    error_count = Activity.query.filter(
        Activity.severity == Activity.SEVERITY_ERROR,
        Activity.created_at >= period_start
    ).count()

    # Count warning activities in the last 30 days
    warning_count = Activity.query.filter(
        Activity.severity == Activity.SEVERITY_WARNING,
        Activity.created_at >= period_start
    ).count()

    # Count total activities in the last 30 days
    total_activities = Activity.query.filter(
        Activity.created_at >= period_start
    ).count()

    # Calculate error and warning percentages
    error_percentage = (error_count / total_activities * 100) if total_activities > 0 else 0
    warning_percentage = (warning_count / total_activities * 100) if total_activities > 0 else 0

    # Determine health status based on error and warning counts and percentages
    health_status = "Excellent"

    # If there are no activities or very few, default to Excellent
    if total_activities < 5:
        health_status = "Excellent"
    # Otherwise use percentage-based thresholds
    elif error_count > 0:
        if error_percentage > 5:
            health_status = "Poor"
        elif error_percentage > 2:
            health_status = "Fair"
        elif error_percentage > 0:
            health_status = "Good"
    elif warning_count > 0:
        if warning_percentage > 10:
            health_status = "Fair"
        elif warning_percentage > 0:
            health_status = "Good"

    # Get the most recent error if any
    latest_error = Activity.query.filter(
        Activity.severity == Activity.SEVERITY_ERROR,
        Activity.created_at >= period_start
    ).order_by(Activity.created_at.desc()).first()

    latest_error_info = None
    if latest_error:
        latest_error_info = {
            'action': latest_error.action,
            'timestamp': latest_error.created_at.isoformat(),
            'details': latest_error.details
        }

    return jsonify({
        'success': True,
        'health': {
            'status': health_status,
            'error_count': error_count,
            'warning_count': warning_count,
            'total_activities': total_activities,
            'error_percentage': round(error_percentage, 2),
            'warning_percentage': round(warning_percentage, 2),
            'latest_error': latest_error_info,
            'period_days': 30
        }
    })


@api_bp.route('/system/maintenance', methods=['POST'])
@login_required
@admin_required
@handle_api_error
@log_activity('Ran system maintenance tasks', category=Activity.CATEGORY_SYSTEM)
def run_maintenance():
    """Run system maintenance tasks."""
    from app.utils.scheduled_tasks import run_maintenance_tasks

    # Run maintenance tasks
    result = run_maintenance_tasks()

    return jsonify({
        'success': True,
        'message': 'Maintenance tasks completed successfully',
        'result': result
    })


@api_bp.route('/system/db-health', methods=['GET'])
@login_required
@admin_required
@handle_api_error
def db_health():
    """Get database health information."""
    try:
        # Get connection pool status - dynamically gets real values for non-SQLite engines
        current_app.logger.info("Fetching connection pool status")
        pool_status = get_connection_pool_status()
        current_app.logger.info(f"Connection pool status: {pool_status}")

        # Get database statistics
        stats = {}

        # Get table row counts
        table_counts = {}
        for table_name in db.metadata.tables.keys():
            count = db.session.execute(db.text(f"SELECT COUNT(*) FROM {table_name}")).scalar()
            table_counts[table_name] = count

        stats['table_counts'] = table_counts

        # Get database size (SQLite specific)
        if 'sqlite' in current_app.config['SQLALCHEMY_DATABASE_URI']:
            try:
                page_count = db.session.execute(db.text("PRAGMA page_count")).scalar() or 0
                page_size = db.session.execute(db.text("PRAGMA page_size")).scalar() or 0
                db_size = page_count * page_size
                stats['db_size_bytes'] = db_size
                stats['db_size_mb'] = round(db_size / (1024 * 1024), 2)
            except Exception as e:
                current_app.logger.warning(f"Could not get SQLite database size: {str(e)}")
                stats['db_size_error'] = str(e)

        # Get query profiler stats if enabled
        profiler_stats = query_profiler.get_stats() if query_profiler.enabled else {'enabled': False}

        return jsonify({
            'success': True,
            'pool_status': pool_status,
            'statistics': stats,
            'query_profiler': profiler_stats
        })
    except Exception as e:
        current_app.logger.error(f"Error getting database health: {str(e)}")
        # Return a valid response even on error
        return jsonify({
            'success': True,
            'pool_status': {
                'pool_size': 1,
                'checkedin': 0,
                'checkedout': 1,
                'overflow': 0,
                'engine_type': 'SQLite (fallback)'
            },
            'statistics': {},
            'query_profiler': {'enabled': False}
        })


@api_bp.route('/system/query-profiler', methods=['POST'])
@login_required
@admin_required
@handle_api_error
@log_activity('Toggled query profiler', category=Activity.CATEGORY_SYSTEM)
def toggle_query_profiler():
    """Toggle the query profiler on or off."""
    data = request.get_json()
    enabled = data.get('enabled', False)
    threshold_ms = data.get('threshold_ms', 100)

    try:
        if enabled:
            query_profiler.threshold_ms = threshold_ms
            query_profiler.start()
            message = f"Query profiler started with threshold {threshold_ms}ms"
        else:
            query_profiler.stop()
            message = "Query profiler stopped"

        current_app.logger.info(message)

        return jsonify({
            'success': True,
            'message': message,
            'enabled': query_profiler.enabled,
            'stats': query_profiler.get_stats()
        })
    except Exception as e:
        current_app.logger.error(f"Error toggling query profiler: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'Error toggling query profiler: {str(e)}'
        }), 500


@api_bp.route('/system/clear-cache', methods=['POST'])
@login_required
@admin_required
@handle_api_error
@log_activity('Cleared application cache', category=Activity.CATEGORY_SYSTEM)
def clear_cache():
    """Clear application cache based on the specified type."""
    data = request.get_json()
    cache_type = data.get('cache_type', 'all')

    try:
        if cache_type == 'dashboard':
            invalidate_dashboard_cache()
            message = 'Dashboard cache cleared successfully'
        elif cache_type == 'business':
            invalidate_business_cache()
            message = 'Business cache cleared successfully'
        elif cache_type == 'employee':
            invalidate_employee_cache()
            message = 'Employee cache cleared successfully'
        elif cache_type == 'all':
            invalidate_all_cache()
            message = 'All application cache cleared successfully'
        else:
            return jsonify({
                'success': False,
                'message': f'Invalid cache type: {cache_type}'
            }), 400

        current_app.logger.info(message)

        return jsonify({
            'success': True,
            'message': message
        })
    except Exception as e:
        current_app.logger.error(f"Error clearing cache: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'Error clearing cache: {str(e)}'
        }), 500

{% extends "base.html" %}

{% block title %}Settings{% endblock %}

{% block header %}Settings{% endblock %}

{% block content %}
<div class="card">
    <div class="p-6 border-b border-border">
        <h2 class="text-lg font-semibold">General Settings</h2>
        <p class="text-sm text-muted-foreground">Manage your application settings</p>
    </div>

    <div class="p-6">
        <form>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label for="site-name" class="block text-sm font-medium mb-1">Site Name</label>
                    <input type="text" id="site-name" name="site-name" value="Admin Dashboard" class="input">
                </div>

                <div>
                    <label for="site-url" class="block text-sm font-medium mb-1">Site URL</label>
                    <input type="url" id="site-url" name="site-url" value="https://example.com" class="input">
                </div>

                <div>
                    <label for="admin-email" class="block text-sm font-medium mb-1">Admin Email</label>
                    <input type="email" id="admin-email" name="admin-email" value="<EMAIL>" class="input">
                </div>

                <div>
                    <label for="timezone" class="block text-sm font-medium mb-1">Timezone</label>
                    <select id="timezone" name="timezone" class="input">
                        <option value="UTC">UTC</option>
                        <option value="America/New_York">Eastern Time (US & Canada)</option>
                        <option value="America/Chicago">Central Time (US & Canada)</option>
                        <option value="America/Denver">Mountain Time (US & Canada)</option>
                        <option value="America/Los_Angeles">Pacific Time (US & Canada)</option>
                    </select>
                </div>

                <div class="md:col-span-2">
                    <label for="site-description" class="block text-sm font-medium mb-1">Site Description</label>
                    <textarea id="site-description" name="site-description" rows="3" class="input">A powerful admin dashboard for managing your application.</textarea>
                </div>

                <div class="md:col-span-2">
                    <div class="flex items-center space-x-2">
                        <input type="checkbox" id="maintenance-mode" name="maintenance-mode" class="h-4 w-4 rounded border-input bg-background">
                        <label for="maintenance-mode" class="text-sm">Enable Maintenance Mode</label>
                    </div>
                </div>

                <div class="md:col-span-2">
                    <div class="flex items-center space-x-2">
                        <input type="checkbox" id="dark-mode" name="dark-mode" class="h-4 w-4 rounded border-input bg-background">
                        <label for="dark-mode" class="text-sm">Enable Dark Mode by Default</label>
                    </div>
                </div>
            </div>

            <div class="mt-6 flex justify-end space-x-3">
                <button type="button" class="inline-flex items-center justify-center px-4 py-2 text-sm font-medium rounded-md border border-gray-300 shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors">
                  Cancel
                </button>
                <button type="submit" class="btn btn-primary btn-md">
                    Save Changes
                </button>
            </div>
        </form>
    </div>
</div>
{% endblock %}

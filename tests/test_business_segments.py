import unittest
from datetime import datetime, date
from flask_testing import TestCase

from app import create_app, db
from app.models import User, EmployeeDetail, BusinessUnit, BusinessSegment


class TestBusinessSegments(TestCase):
    """Test cases for BusinessSegment model and related functionality"""

    def create_app(self):
        """Create and configure a Flask app for testing"""
        app = create_app()
        app.config.update(
            TESTING=True,
            SQLALCHEMY_DATABASE_URI='sqlite:///:memory:',
            WTF_CSRF_ENABLED=False,
            SERVER_NAME='localhost.localdomain',
            SECRET_KEY='test_secret_key'
        )
        with app.app_context():
            db.create_all()
        return app

    def setUp(self):
        """Set up test environment before each test"""
        db.session.remove()
        db.drop_all()
        db.create_all()

        # Create test users
        self.admin_user = User(
            name='Admin User',
            email='<EMAIL>',
            role='Admin'
        )
        self.admin_user.set_password('Admin@123')

        self.manager_user = User(
            name='Manager User',
            email='<EMAIL>',
            role='Manager'
        )
        self.manager_user.set_password('Manager@123')

        self.regular_user = User(
            name='Regular User',
            email='<EMAIL>',
            role='User'
        )
        self.regular_user.set_password('User@123')

        db.session.add_all([self.admin_user, self.manager_user, self.regular_user])
        db.session.commit()

        # Create business units
        self.business_unit1 = BusinessUnit(
            name='Engineering',
            code='ENG',
            description='Engineering Department',
            manager_id=self.manager_user.id,
            is_active=True
        )

        self.business_unit2 = BusinessUnit(
            name='Marketing',
            code='MKT',
            description='Marketing Department',
            manager_id=self.manager_user.id,
            is_active=True
        )

        db.session.add_all([self.business_unit1, self.business_unit2])
        db.session.commit()

    def tearDown(self):
        """Clean up test environment after each test"""
        db.session.remove()
        db.drop_all()

    def test_business_segment_creation(self):
        """Test basic business segment creation and retrieval"""
        # Create a business segment
        business_segment = BusinessSegment(
            name='Software Development',
            code='SD',
            description='Software Development Team',
            business_unit_id=self.business_unit1.id,
            manager_id=self.manager_user.id,
            is_active=True
        )
        db.session.add(business_segment)
        db.session.commit()

        # Retrieve the business segment and check attributes
        saved_segment = BusinessSegment.query.filter_by(code='SD').first()
        self.assertIsNotNone(saved_segment)
        self.assertEqual(saved_segment.name, 'Software Development')
        self.assertEqual(saved_segment.description, 'Software Development Team')
        self.assertTrue(saved_segment.is_active)
        self.assertEqual(saved_segment.business_unit_id, self.business_unit1.id)
        self.assertEqual(saved_segment.manager_id, self.manager_user.id)

        # Test relationships
        self.assertEqual(saved_segment.business_unit.name, 'Engineering')
        self.assertEqual(saved_segment.manager.name, 'Manager User')

    def test_business_segment_code_uniqueness(self):
        """Test business segment code uniqueness constraint"""
        # Create first business segment
        business_segment1 = BusinessSegment(
            name='Software Development',
            code='SD',
            description='Software Development Team',
            business_unit_id=self.business_unit1.id
        )
        db.session.add(business_segment1)
        db.session.commit()

        # Try to create another business segment with the same code
        business_segment2 = BusinessSegment(
            name='System Development',
            code='SD',  # Same code
            description='System Development Team',
            business_unit_id=self.business_unit2.id
        )
        db.session.add(business_segment2)

        # This should raise an IntegrityError due to unique constraint
        from sqlalchemy.exc import IntegrityError
        with self.assertRaises(IntegrityError):
            db.session.commit()

        # Rollback the failed transaction
        db.session.rollback()

    def test_get_employee_count(self):
        """Test getting employee count for a business segment"""
        # Create a business segment
        business_segment = BusinessSegment(
            name='Software Development',
            code='SD',
            description='Software Development Team',
            business_unit_id=self.business_unit1.id
        )
        db.session.add(business_segment)
        db.session.commit()

        # Initially should have 0 employees
        self.assertEqual(business_segment.get_employee_count(), 0)

        # Create employee details with references to the business segment
        employee_detail1 = EmployeeDetail(
            user_id=self.regular_user.id,
            employee_number='EMP001',
            position='Software Engineer',
            business_unit_id=self.business_unit1.id,
            business_segment_id=business_segment.id
        )

        employee_detail2 = EmployeeDetail(
            user_id=self.manager_user.id,
            employee_number='EMP002',
            position='Engineering Manager',
            business_unit_id=self.business_unit1.id,
            business_segment_id=business_segment.id
        )

        db.session.add_all([employee_detail1, employee_detail2])
        db.session.commit()

        # Now should have 2 employees
        self.assertEqual(business_segment.get_employee_count(), 2)

    def test_to_dict_method(self):
        """Test business segment serialization to dictionary"""
        # Create a business segment with a manager
        business_segment = BusinessSegment(
            name='Software Development',
            code='SD',
            description='Software Development Team',
            business_unit_id=self.business_unit1.id,
            manager_id=self.manager_user.id,
            is_active=True
        )
        db.session.add(business_segment)
        db.session.commit()

        # Test serialization
        segment_dict = business_segment.to_dict()
        self.assertEqual(segment_dict['name'], 'Software Development')
        self.assertEqual(segment_dict['code'], 'SD')
        self.assertEqual(segment_dict['description'], 'Software Development Team')
        self.assertTrue(segment_dict['is_active'])
        self.assertEqual(segment_dict['business_unit_id'], self.business_unit1.id)
        self.assertEqual(segment_dict['business_unit_name'], 'Engineering')
        self.assertEqual(segment_dict['manager_id'], self.manager_user.id)
        self.assertEqual(segment_dict['manager_name'], 'Manager User')
        self.assertEqual(segment_dict['employee_count'], 0)

    def test_business_unit_relationship(self):
        """Test the relationship between business segment and business unit"""
        # Create business segments for each business unit
        segment1 = BusinessSegment(
            name='Software Development',
            code='SD',
            description='Software Development Team',
            business_unit_id=self.business_unit1.id
        )

        segment2 = BusinessSegment(
            name='Digital Marketing',
            code='DM',
            description='Digital Marketing Team',
            business_unit_id=self.business_unit2.id
        )

        db.session.add_all([segment1, segment2])
        db.session.commit()

        # Test relationship from segment to business unit
        self.assertEqual(segment1.business_unit.id, self.business_unit1.id)
        self.assertEqual(segment1.business_unit.name, 'Engineering')
        self.assertEqual(segment2.business_unit.id, self.business_unit2.id)
        self.assertEqual(segment2.business_unit.name, 'Marketing')

        # Test relationship from business unit to segments
        engineering_segments = self.business_unit1.segments.all()
        marketing_segments = self.business_unit2.segments.all()

        self.assertEqual(len(engineering_segments), 1)
        self.assertEqual(engineering_segments[0].name, 'Software Development')

        self.assertEqual(len(marketing_segments), 1)
        self.assertEqual(marketing_segments[0].name, 'Digital Marketing')

    def test_move_segment_between_units(self):
        """Test moving a segment from one business unit to another"""
        # Create a business segment
        business_segment = BusinessSegment(
            name='Software Development',
            code='SD',
            description='Software Development Team',
            business_unit_id=self.business_unit1.id
        )
        db.session.add(business_segment)
        db.session.commit()

        # Verify initial business unit assignment
        self.assertEqual(business_segment.business_unit.id, self.business_unit1.id)
        self.assertEqual(business_segment.business_unit.name, 'Engineering')

        # Move segment to another business unit
        business_segment.business_unit_id = self.business_unit2.id
        db.session.commit()

        # Refresh from DB
        db.session.refresh(business_segment)

        # Verify new business unit assignment
        self.assertEqual(business_segment.business_unit.id, self.business_unit2.id)
        self.assertEqual(business_segment.business_unit.name, 'Marketing')

    def test_admin_business_segment_route_access(self):
        """Test admin access to business segment routes"""
        with self.client:
            # Login as admin
            self.client.post(
                '/auth/login',
                data={
                    'email': '<EMAIL>',
                    'password': 'Admin@123'
                }
            )

            # Admin should have access to business segments page
            response = self.client.get('/admin/business-segments', follow_redirects=True)
            self.assertEqual(response.status_code, 200)
            self.assertIn(b'Business Segments', response.data)

    def test_manager_relationship(self):
        """Test the manager relationship with business segment"""
        # Create a business segment with a manager
        business_segment = BusinessSegment(
            name='Software Development',
            code='SD',
            description='Software Development Team',
            business_unit_id=self.business_unit1.id,
            manager_id=self.manager_user.id
        )
        db.session.add(business_segment)
        db.session.commit()

        # Test relationship from segment to manager
        self.assertEqual(business_segment.manager.id, self.manager_user.id)
        self.assertEqual(business_segment.manager.name, 'Manager User')

        # Test relationship from manager to managed segments
        managed_segments = self.manager_user.managed_segments.all()
        self.assertEqual(len(managed_segments), 1)
        self.assertEqual(managed_segments[0].name, 'Software Development')

        # Test changing the manager
        business_segment.manager_id = self.admin_user.id
        db.session.commit()

        # Refresh from DB
        db.session.refresh(business_segment)
        db.session.refresh(self.manager_user)
        db.session.refresh(self.admin_user)

        # Verify new manager assignment
        self.assertEqual(business_segment.manager.id, self.admin_user.id)
        self.assertEqual(business_segment.manager.name, 'Admin User')

        # Old manager should no longer manage this segment
        old_manager_segments = self.manager_user.managed_segments.all()
        self.assertEqual(len(old_manager_segments), 0)

        # New manager should now manage this segment
        new_manager_segments = self.admin_user.managed_segments.all()
        self.assertEqual(len(new_manager_segments), 1)
        self.assertEqual(new_manager_segments[0].name, 'Software Development')


if __name__ == '__main__':
    unittest.main()

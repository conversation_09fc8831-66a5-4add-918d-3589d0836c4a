{% macro page_header(title, button_text=None, button_icon=None, button_action=None, description=None) %}
<div class="flex justify-between items-center mb-6">
  <div>
    <h1 class="text-2xl font-bold">{{ title }}</h1>
    {% if description %}
    <p class="text-sm text-muted-foreground mt-1">{{ description }}</p>
    {% endif %}
  </div>
  {% if button_text %}
  <button onclick="{{ button_action }}" class="btn btn-primary page-header-button px-4 py-2 rounded flex items-center">
    {% if button_icon %}
    <i data-lucide="{{ button_icon }}" class="w-4 h-4 mr-2"></i>
    {% endif %}
    <span>{{ button_text }}</span>
  </button>
  {% endif %}
</div>
{% endmacro %}

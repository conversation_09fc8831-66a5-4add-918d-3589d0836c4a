/**
 * Activity Details Modal
 * Pure Tailwind implementation with no dependencies on modal.css
 */

document.addEventListener('DOMContentLoaded', function() {
  // Set up event listeners
  setupActivityModalEvents();
});

/**
 * Set up event listeners for the activity modal
 */
function setupActivityModalEvents() {
  // Close modal when clicking on backdrop
  const backdrop = document.getElementById('activity-modal-backdrop');
  if (backdrop) {
    backdrop.addEventListener('click', function() {
      closeActivityModal();
    });
  }

  // Close modal when pressing Escape key
  document.addEventListener('keydown', function(event) {
    if (event.key === 'Escape') {
      const modal = document.getElementById('activity-details-modal');
      if (modal && !modal.classList.contains('hidden')) {
        closeActivityModal();
      }
    }
  });
}

/**
 * Show activity details in a modal
 * @param {number} activityId - The ID of the activity to display
 */
function showActivityDetails(activityId) {
  const modal = document.getElementById('activity-details-modal');
  const modalContent = document.getElementById('activity-modal-content');
  const contentDiv = document.getElementById('activity-details-content');
  
  if (!modal || !modalContent || !contentDiv) {
    console.error('Activity modal elements not found');
    return;
  }

  // Calculate scrollbar width to prevent layout shift
  const scrollbarWidth = window.innerWidth - document.documentElement.clientWidth;
  
  // Show modal
  modal.classList.remove('hidden');
  document.body.style.overflow = 'hidden';
  document.body.style.paddingRight = `${scrollbarWidth}px`;
  
  // Animate in
  setTimeout(() => {
    modalContent.classList.remove('translate-y-4', 'opacity-0');
    modalContent.classList.add('translate-y-0', 'opacity-100');
  }, 10);
  
  // Show loading spinner
  contentDiv.innerHTML = '<div class="flex justify-center items-center py-8"><div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div></div>';

  // Fetch activity details
  fetch(`/admin/activities/${activityId}/details`)
    .then(response => response.json())
    .then(data => {
      if (data.success) {
        // Format the activity details
        const activity = data.activity;
        const createdAt = new Date(activity.created_at);
        const formattedDate = createdAt.toLocaleString();

        let severityClass = '';
        if (activity.severity === 'error') severityClass = 'text-destructive';
        else if (activity.severity === 'warning') severityClass = 'text-warning';

        // Get category icon
        let categoryIcon = 'tag';
        if (activity.category === 'auth') categoryIcon = 'key';
        else if (activity.category === 'admin') categoryIcon = 'shield';
        else if (activity.category === 'user') categoryIcon = 'user';
        else if (activity.category === 'system') categoryIcon = 'settings';
        else if (activity.category === 'data') categoryIcon = 'database';

        // Update the header
        const headerHtml = `
          <div class="flex justify-between items-center">
            <div class="flex items-center space-x-2">
              <span class="badge ${activity.severity_badge_class}">${activity.severity ? activity.severity.charAt(0).toUpperCase() + activity.severity.slice(1) : 'Info'}</span>
              <h3 class="text-lg font-medium ${severityClass}">${activity.action}</h3>
            </div>
            <button type="button" class="flex items-center justify-center h-8 w-8 rounded-full hover:bg-muted/80 transition-colors focus:outline-none focus:ring-2 focus:ring-primary/30" onclick="closeActivityModal()" aria-label="Close modal">
              <i data-lucide="x" class="h-4 w-4"></i>
            </button>
          </div>
        `;
        document.getElementById('activity-details-header').innerHTML = headerHtml;

        let html = `
          <div class="space-y-4">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <h4 class="text-sm font-medium text-muted-foreground mb-1">User</h4>
                <div class="flex items-center">
                  <div class="w-6 h-6 rounded-full bg-primary/10 flex items-center justify-center text-primary font-medium text-xs mr-2">
                    ${activity.user_name ? activity.user_name.charAt(0) : '?'}
                  </div>
                  <p class="font-medium">${activity.user_name}</p>
                </div>
              </div>
              <div>
                <h4 class="text-sm font-medium text-muted-foreground mb-1">Date & Time</h4>
                <p>${formattedDate}</p>
              </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div>
                <h4 class="text-sm font-medium text-muted-foreground mb-1">Category</h4>
                <p class="flex items-center">
                  <i data-lucide="${categoryIcon}" class="h-4 w-4 mr-1.5"></i>
                  ${activity.category_display}
                </p>
              </div>
              <div>
                <h4 class="text-sm font-medium text-muted-foreground mb-1">Method</h4>
                <p class="flex items-center">
                  ${activity.method ?
                    `<span class="inline-flex items-center text-xs px-2 py-1 rounded-sm mr-1.5
                    ${activity.method === 'create' ? 'bg-blue-100 text-blue-700 dark:bg-blue-900/30 dark:text-blue-400' :
                        activity.method === 'read' ? 'bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-400' :
                            activity.method === 'update' ? 'bg-amber-100 text-amber-700 dark:bg-amber-900/30 dark:text-amber-400' :
                                activity.method === 'delete' ? 'bg-red-100 text-red-700 dark:bg-red-900/30 dark:text-red-400' :
                                    'bg-gray-100 text-gray-700 dark:bg-gray-900/30 dark:text-gray-400'}">
                    ${activity.method === 'create' ? '<i data-lucide="plus" class="h-3 w-3 mr-1"></i>' :
                        activity.method === 'read' ? '<i data-lucide="eye" class="h-3 w-3 mr-1"></i>' :
                            activity.method === 'update' ? '<i data-lucide="edit" class="h-3 w-3 mr-1"></i>' :
                                activity.method === 'delete' ? '<i data-lucide="trash" class="h-3 w-3 mr-1"></i>' :
                                    '<i data-lucide="activity" class="h-3 w-3 mr-1"></i>'}
                    ${activity.method_display}
                  </span>` :
                    '<span class="text-muted-foreground">-</span>'}
                </p>
              </div>
              <div>
                <h4 class="text-sm font-medium text-muted-foreground mb-1">Entity</h4>
                <p class="flex items-center">
                  ${activity.entity_type ?
                    `<i data-lucide="file" class="h-4 w-4 mr-1.5"></i>
                  ${activity.entity_type} #${activity.entity_id}` :
                    '-'}
                </p>
              </div>
              <div>
                <h4 class="text-sm font-medium text-muted-foreground mb-1">IP Address</h4>
                <p>${activity.ip_address || '-'}</p>
              </div>
            </div>

            <div>
              <h4 class="text-sm font-medium text-muted-foreground mb-1">URL</h4>
              <div class="text-sm bg-muted/30 p-3 rounded-md max-h-[100px] overflow-y-auto">
                ${activity.url ?
                    `<button type="button" onclick="showJsonViewer('Activity URL', '${activity.url}', 'Activity', ${activity.id}, '${activity.method || ''}')" class="text-primary hover:underline flex items-start w-full overflow-hidden">
                  <i data-lucide="${activity.url.includes('?') ? 'file-json' : 'link'}" class="h-3.5 w-3.5 mr-1.5 mt-0.5 flex-shrink-0"></i>
                  <span class="break-all">${activity.url}</span>
                </button>` :
                    '-'}
              </div>
            </div>

            <div>
              <h4 class="text-sm font-medium text-muted-foreground mb-1">Details</h4>
              <div class="bg-muted/30 p-3 rounded-md whitespace-pre-line break-words max-h-[200px] overflow-y-auto">${activity.details || '-'}</div>
            </div>

            <div>
              <h4 class="text-sm font-medium text-muted-foreground mb-1">User Agent</h4>
              <div class="text-sm break-words bg-muted/30 p-3 rounded-md max-h-[100px] overflow-y-auto">${activity.user_agent || '-'}</div>
            </div>

            ${activity.has_changes ?
                    `<div class="pt-4 border-t border-border mt-4">
              <h4 class="text-sm font-medium text-muted-foreground mb-2">Changes</h4>
              <div class="bg-muted/30 p-3 rounded-md overflow-auto max-h-[300px]">
                <table class="w-full text-xs">
                  <thead>
                    <tr class="border-b border-border">
                      <th class="text-left py-2 px-2 font-medium">Field</th>
                      <th class="text-left py-2 px-2 font-medium">Old Value</th>
                      <th class="text-left py-2 px-2 font-medium">New Value</th>
                    </tr>
                  </thead>
                  <tbody>
                    ${Object.keys(activity.old_values).map(field => `
                      <tr class="border-b border-border/50">
                        <td class="py-2 px-2 font-medium align-top">${field}</td>
                        <td class="py-2 px-2 align-top whitespace-pre-wrap break-words max-w-[150px]">${formatValue(activity.old_values[field])}</td>
                        <td class="py-2 px-2 align-top whitespace-pre-wrap break-words max-w-[150px]">${formatValue(activity.new_values[field])}</td>
                      </tr>
                    `).join('')}
                  </tbody>
                </table>
              </div>
            </div>` : ''
                }

            ${activity.entity_type && activity.entity_id && activity.entity_url ?
                    `<div class="pt-2 border-t border-border mt-4 flex space-x-2">
              <a href="${activity.entity_url}" class="btn btn-outline btn-sm">
                <i data-lucide="external-link" class="h-4 w-4 mr-2"></i>
                View Entity
              </a>
            </div>` :
                    ''}
          </div>
        `;

        contentDiv.innerHTML = html;
        lucide.createIcons();
      } else {
        contentDiv.innerHTML = '<div class="p-4 text-center">Failed to load activity details</div>';
      }
    })
    .catch(error => {
      contentDiv.innerHTML = '<div class="p-4 text-center">An error occurred while loading activity details</div>';
    });
}

/**
 * Close the activity details modal
 */
function closeActivityModal() {
  const modal = document.getElementById('activity-details-modal');
  const modalContent = document.getElementById('activity-modal-content');
  
  if (!modal || !modalContent) {
    console.error('Activity modal elements not found');
    return;
  }

  // Animate out
  modalContent.classList.remove('translate-y-0', 'opacity-100');
  modalContent.classList.add('translate-y-4', 'opacity-0');
  
  // Hide modal after animation
  setTimeout(() => {
    modal.classList.add('hidden');
    document.body.style.overflow = '';
    document.body.style.paddingRight = '';
  }, 300);
}

/**
 * Format values for display in the activity details modal
 * @param {*} value - The value to format
 * @returns {string} - Formatted HTML string
 */
function formatValue(value) {
  if (value === null || value === undefined) {
    return '<span class="text-muted-foreground italic">null</span>';
  } else if (typeof value === 'object') {
    return JSON.stringify(value, null, 2);
  } else if (typeof value === 'boolean') {
    return value ?
      '<span class="text-green-500">true</span>' :
      '<span class="text-red-500">false</span>';
  } else if (value === '') {
    return '<span class="text-muted-foreground italic">(empty string)</span>';
  } else {
    return String(value);
  }
}

/**
 * Show entity JSON data using the API endpoint
 * @param {string} entityType - The type of entity
 * @param {number} entityId - The ID of the entity
 */
function showEntityJson(entityType, entityId) {
  // Construct the API URL
  const apiUrl = `/api/entity/${entityType}/${entityId}`;

  // Use the showJsonViewer function with the API URL
  showJsonViewer(`${entityType} #${entityId}`, apiUrl, entityType, entityId);
}

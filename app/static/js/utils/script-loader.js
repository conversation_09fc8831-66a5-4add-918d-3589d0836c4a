/**
 * Script Loader Utility
 * 
 * This module provides utilities for efficient script loading and management.
 */

// Track loaded scripts to prevent duplicate loading
const loadedScripts = new Set();

/**
 * Load a script asynchronously with performance optimizations
 * 
 * @param {string} src - The script URL to load
 * @param {Object} options - Options for script loading
 * @param {boolean} options.defer - Whether to defer script loading (default: true)
 * @param {boolean} options.async - Whether to load script asynchronously (default: true)
 * @param {string} options.integrity - Integrity hash for the script
 * @param {string} options.crossOrigin - Cross-origin policy ('anonymous' or 'use-credentials')
 * @param {Function} options.onLoad - Callback function when script loads successfully
 * @param {Function} options.onError - Callback function when script fails to load
 * @returns {Promise} - Promise that resolves when the script is loaded
 */
function loadScript(src, options = {}) {
  // Default options
  const {
    defer = true,
    async = true,
    integrity = null,
    crossOrigin = 'anonymous',
    onLoad = null,
    onError = null
  } = options;

  // Return existing promise if script is already loading
  if (loadedScripts.has(src)) {
    return Promise.resolve();
  }

  // Add to loaded scripts set
  loadedScripts.add(src);

  return new Promise((resolve, reject) => {
    const script = document.createElement('script');
    script.src = src;
    script.defer = defer;
    script.async = async;
    
    // Add integrity if provided
    if (integrity) {
      script.integrity = integrity;
      script.crossOrigin = crossOrigin;
    }

    // Add to document head
    document.head.appendChild(script);

    // Handle load event
    script.onload = () => {
      if (onLoad) onLoad();
      resolve();
    };

    // Handle error event
    script.onerror = (error) => {
      // Remove from loaded scripts set on error
      loadedScripts.delete(src);
      if (onError) onError(error);
      reject(new Error(`Failed to load script: ${src}`));
    };
  });
}

/**
 * Load multiple scripts in parallel with performance optimizations
 * 
 * @param {Array<string|Object>} scripts - Array of script URLs or objects with src and options
 * @returns {Promise} - Promise that resolves when all scripts are loaded
 */
function loadScripts(scripts) {
  const promises = scripts.map(script => {
    if (typeof script === 'string') {
      return loadScript(script);
    } else {
      return loadScript(script.src, script.options);
    }
  });

  return Promise.all(promises);
}

/**
 * Load a script on demand only when needed
 * 
 * @param {string} src - The script URL to load
 * @param {Function} condition - Function that returns true when the script should be loaded
 * @param {Object} options - Options for script loading
 * @returns {Promise} - Promise that resolves when the script is loaded (or immediately if not needed)
 */
function loadScriptOnDemand(src, condition, options = {}) {
  if (condition()) {
    return loadScript(src, options);
  }
  return Promise.resolve();
}

/**
 * Preload a script for future use without executing it
 * 
 * @param {string} src - The script URL to preload
 */
function preloadScript(src) {
  // Check if already preloaded
  if (document.querySelector(`link[rel="preload"][href="${src}"]`)) {
    return;
  }

  const link = document.createElement('link');
  link.rel = 'preload';
  link.href = src;
  link.as = 'script';
  document.head.appendChild(link);
}

// Export public API
export {
  loadScript,
  loadScripts,
  loadScriptOnDemand,
  preloadScript
};

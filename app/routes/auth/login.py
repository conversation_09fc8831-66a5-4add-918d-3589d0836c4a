"""
Authentication login routes.
"""

from flask import render_template, redirect, url_for, flash, request, session
from flask_login import login_user, logout_user, current_user
from datetime import datetime

from app import db, limiter
from app.models import User, Activity, PH_TZ
from app.utils.decorators import log_activity
from app.utils.request_info import get_client_info
from app.utils.settings import get_setting
from app.routes.auth import auth_bp, redirect_authenticated_user


@auth_bp.route('/login', methods=['GET', 'POST'])
@limiter.limit("20 per minute", error_message="Too many login attempts. Please try again later.")
def login():
    """Handle user login."""
    # Redirect if already logged in
    redirect_result = redirect_authenticated_user()
    if redirect_result:
        return redirect_result

    # Check if registration is allowed to hide/show the sign-up button
    allow_registration = get_setting('allow_registration', True)

    if request.method == 'POST':
        email = request.form.get('email')
        password = request.form.get('password')
        remember = True if request.form.get('remember') else False

        # Use the model method for getting users by email
        user = User.get_by_email(email)

        # Log failed attempts for security monitoring
        if not user or not user.check_password(password):
            if user:
                client_info = get_client_info()
                Activity.log(
                    user_id=user.id,
                    action='Failed login attempt',
                    entity_type='User',
                    entity_id=user.id,
                    ip_address=client_info['ip_address'],
                    user_agent=client_info['user_agent'],
                    details=f"IP: {client_info['ip_address']}, Hostname: {client_info['hostname'] or 'Unknown'}",
                    category=Activity.CATEGORY_AUTH,
                    method=Activity.METHOD_READ,
                    severity=Activity.SEVERITY_WARNING
                )
            flash('Invalid email or password', 'error')
            return render_template('auth/login.html', allow_registration=allow_registration)

        if not user.is_active:
            client_info = get_client_info()
            Activity.log(
                user_id=user.id,
                action='Login attempt to inactive account',
                entity_type='User',
                entity_id=user.id,
                ip_address=client_info['ip_address'],
                user_agent=client_info['user_agent'],
                details=f"Hostname: {client_info['hostname'] or 'Unknown'}",
                category=Activity.CATEGORY_AUTH,
                method=Activity.METHOD_READ,
                severity=Activity.SEVERITY_WARNING
            )
            flash('Your account is inactive. Please contact an administrator.', 'error')
            return render_template('auth/login.html', allow_registration=allow_registration)

        # Update last login time
        user.last_login = datetime.now(PH_TZ)
        db.session.commit()

        # Login user with strong session protection
        login_user(user, remember=remember)
        session.permanent = True  # Use permanent session with the timeout from config

        # Log successful login
        client_info = get_client_info()
        Activity.log(
            user_id=user.id,
            action='User logged in',
            entity_type='User',
            entity_id=user.id,
            ip_address=client_info['ip_address'],
            user_agent=client_info['user_agent'],
            details=f"Hostname: {client_info['hostname'] or 'Unknown'}",
            category=Activity.CATEGORY_AUTH,
            method=Activity.METHOD_READ,
            severity=Activity.SEVERITY_INFO
        )

        # Get the next page from request args or default to appropriate dashboard
        next_page = request.args.get('next')
        if not next_page or not next_page.startswith('/'):
            if user.is_admin:
                next_page = url_for('admin.dashboard')
            else:
                next_page = url_for('main.user_dashboard')

        return redirect(next_page)

    return render_template('auth/login.html', allow_registration=allow_registration)


@auth_bp.route('/logout')
@log_activity('User logged out', entity_type='User', category=Activity.CATEGORY_AUTH)
def logout():
    """Handle user logout."""
    logout_user()
    flash('You have been logged out', 'info')
    return redirect(url_for('auth.login'))

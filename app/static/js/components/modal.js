/**
 * Enhanced Modal Component
 * Features:
 * - No dependencies, no conflicts with existing code
 * - Focus trapping for accessibility
 * - Keyboard navigation
 * - Smooth animations
 * - Mobile-friendly design
 */

// Use an IIFE to avoid polluting the global namespace
(function() {
  // Initialize when the DOM is loaded
  document.addEventListener('DOMContentLoaded', function() {
    // Find all modal triggers
    var triggers = document.querySelectorAll('[data-modal]');

    // Add click event to each trigger
    triggers.forEach(function(trigger) {
      var modalId = trigger.getAttribute('data-modal');

      trigger.addEventListener('click', function() {
        // Get modal content from the template
        var template = document.getElementById(modalId + '-template');
        if (template) {
          showModal({
            id: modalId,
            content: template.innerHTML,
            size: template.getAttribute('data-size') || 'md'
          });
        } else {
          console.error('Modal template with ID ' + modalId + '-template not found');
        }
      });
    });

    // For backward compatibility
    var oldTriggers = document.querySelectorAll('[data-isolated-modal]');
    oldTriggers.forEach(function(trigger) {
      var modalId = trigger.getAttribute('data-isolated-modal');

      trigger.addEventListener('click', function() {
        // Get modal content from the template
        var template = document.getElementById(modalId + '-template');
        if (template) {
          showModal({
            id: modalId,
            content: template.innerHTML,
            size: template.getAttribute('data-size') || 'md'
          });
        } else {
          console.error('Modal template with ID ' + modalId + '-template not found');
        }
      });
    });
  });

  /**
   * Initialize the modal container if it doesn't exist
   * @returns {HTMLElement} - The modal container element
   */
  function initModalContainer() {
    var container = document.getElementById('modal-container');

    if (!container) {
      container = document.createElement('div');
      container.id = 'modal-container';
      container.setAttribute('aria-live', 'polite');
      container.setAttribute('aria-atomic', 'true');
      document.body.appendChild(container);
    }

    return container;
  }

  /**
   * Show a modal with the specified options
   * @param {Object} options - Options for the modal
   * @param {string} options.id - ID of the modal
   * @param {string} options.content - HTML content of the modal
   * @param {string} options.size - Size of the modal ('xs', 'sm', 'md', 'lg', 'xl', 'full')
   * @returns {Function} A function to close the modal
   */
  function showModal(options) {
    var container = document.getElementById('modal-container') || initModalContainer();

    // Store the currently focused element to restore focus when modal closes
    var previouslyFocusedElement = document.activeElement;

    // Prevent body scrolling when modal is open
    document.body.style.overflow = 'hidden';

    // Create overlay
    var overlay = document.createElement('div');
    overlay.className = 'modal-overlay';
    overlay.setAttribute('data-state', 'open');

    // Create dialog content
    var dialog = document.createElement('div');
    dialog.className = 'modal-dialog ' + (options.size || 'md');
    dialog.setAttribute('data-state', 'open');
    dialog.setAttribute('role', 'dialog');
    dialog.setAttribute('aria-modal', 'true');
    dialog.setAttribute('aria-labelledby', options.id + '-title');
    dialog.innerHTML = options.content;

    // Add to container
    container.appendChild(overlay);
    container.appendChild(dialog);

    // Initialize icons in the dialog
    if (typeof lucide !== 'undefined' && lucide.createIcons) {
      lucide.createIcons({
        root: dialog
      });
    }

    // Dispatch a custom event when the modal is shown
    document.dispatchEvent(new CustomEvent('modal:shown', {
      detail: {
        id: options.id,
        dialog: dialog
      }
    }));

    // Find all focusable elements in the dialog
    var focusableElements = dialog.querySelectorAll(
      'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
    );
    var firstFocusableElement = focusableElements[0];
    var lastFocusableElement = focusableElements[focusableElements.length - 1];

    // Set focus to the first focusable element after a short delay
    // This ensures the modal is fully rendered before focusing
    setTimeout(function() {
      if (firstFocusableElement) {
        firstFocusableElement.focus();
      } else {
        dialog.focus();
      }
    }, 50);

    // Find close buttons
    var closeButtons = dialog.querySelectorAll('[data-modal-close]');
    if (closeButtons.length === 0) {
      // For backward compatibility
      closeButtons = dialog.querySelectorAll('[data-isolated-modal-close]');
    }

    closeButtons.forEach(function(button) {
      // Use mousedown instead of click for more responsive feel
      // and to ensure the event fires even when clicking on the icon
      button.addEventListener('mousedown', function(e) {
        e.preventDefault(); // Prevent any default behavior
        closeModal();
      });

      // Also keep the click handler for accessibility
      button.addEventListener('click', function(e) {
        e.preventDefault(); // Prevent any default behavior
        closeModal();
      });
    });

    // Find action button
    var actionButton = dialog.querySelector('#' + options.id + '-action');
    if (actionButton) {
      // Add loading state toggle method to the action button
      actionButton.setLoading = function(isLoading) {
        if (isLoading) {
          this.setAttribute('disabled', 'disabled');
          this.dataset.originalHtml = this.innerHTML;
          this.innerHTML = '<svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle><path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg><span>Processing...</span>';
        } else {
          this.removeAttribute('disabled');
          if (this.dataset.originalHtml) {
            this.innerHTML = this.dataset.originalHtml;
          }
        }
      };
    }

    // Close dialog function
    function closeModal() {
      overlay.setAttribute('data-state', 'closed');
      dialog.setAttribute('data-state', 'closed');

      // Re-enable body scrolling
      document.body.style.overflow = '';

      // Remove elements after animation completes
      setTimeout(function() {
        if (container.contains(overlay)) {
          container.removeChild(overlay);
        }
        if (container.contains(dialog)) {
          container.removeChild(dialog);
        }

        // Restore focus to the element that had it before the dialog was opened
        if (previouslyFocusedElement && typeof previouslyFocusedElement.focus === 'function') {
          previouslyFocusedElement.focus();
        }

        // Remove event listeners
        document.removeEventListener('keydown', handleKeyDown);
      }, 300);
    }

    // Event listeners
    overlay.addEventListener('click', function() {
      closeModal();
    });

    // Handle keyboard navigation and focus trapping
    function handleKeyDown(e) {
      // Close on escape key
      if (e.key === 'Escape') {
        closeModal();
        return;
      }

      // Trap focus within the modal
      if (e.key === 'Tab') {
        // If there are no focusable elements, do nothing
        if (focusableElements.length === 0) return;

        // Shift + Tab => backward navigation
        if (e.shiftKey) {
          if (document.activeElement === firstFocusableElement) {
            e.preventDefault();
            lastFocusableElement.focus();
          }
        }
        // Tab => forward navigation
        else {
          if (document.activeElement === lastFocusableElement) {
            e.preventDefault();
            firstFocusableElement.focus();
          }
        }
      }

      // Enter key on action button
      if (e.key === 'Enter' && actionButton && document.activeElement === actionButton) {
        e.preventDefault();
        actionButton.click();
      }
    }

    document.addEventListener('keydown', handleKeyDown);

    // Return an object with close function and references to elements
    return {
      close: closeModal,
      dialog: dialog,
      overlay: overlay,
      actionButton: actionButton
    };
  }

  // Export functions to global scope with backward compatibility
  window.showModal = function(options) {
    var result = showModal(options);
    // For backward compatibility with code that expects just the close function
    if (typeof result === 'object') {
      var closeFunc = result.close;
      // Add all properties from the result object to the function
      Object.keys(result).forEach(function(key) {
        closeFunc[key] = result[key];
      });
      return closeFunc;
    }
    return result;
  };

  // For backward compatibility
  window.showIsolatedModal = window.showModal;
})();

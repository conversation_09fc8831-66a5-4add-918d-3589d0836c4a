{% extends 'base.html' %}

{% from "components/page_header.html" import page_header %}

{% block title %}System Maintenance{% endblock %}

{% block header %}System Maintenance{% endblock %}

{% block content %}
<!-- Hidden CSRF token for API requests -->
<input type="hidden" name="csrf_token" value="{{ csrf_token() }}">

<div class="space-y-6">
  {{ page_header(
    title="System Maintenance",
    button_text="Run Maintenance",
    button_icon="refresh-cw",
    button_action="document.getElementById('run-maintenance-btn').click()",
    description="Optimize system performance and manage resources"
  ) }}

  <!-- Hidden button for JavaScript functionality -->
  <button id="run-maintenance-btn" class="hidden"></button>

<div class="mb-8 p-6 bg-card rounded-xl border border-border shadow-sm">
    <div class="flex flex-col md:flex-row items-start md:items-center space-y-4 md:space-y-0 md:space-x-6">
        <div class="w-14 h-14 rounded-xl bg-primary/20 flex items-center justify-center flex-shrink-0 shadow-sm border border-primary/20">
            <i data-lucide="settings" class="w-7 h-7 text-primary"></i>
        </div>
        <div>
            <h3 class="text-xl font-semibold mb-2 tracking-tight">System Maintenance Dashboard</h3>
            <p class="text-sm text-muted-foreground leading-relaxed max-w-3xl">This dashboard provides advanced tools for maintaining the application's database, cache, and system health. Regular maintenance helps optimize performance, reduce storage usage, and ensure the application runs smoothly and efficiently.</p>
            <div class="mt-4 flex flex-wrap gap-3">
                <div class="flex items-center text-xs bg-primary/20 text-white px-4 py-2 rounded-full shadow-sm border border-primary/20">
                    <i data-lucide="database" class="w-4 h-4 mr-2"></i>
                    <span>Database Optimization</span>
                </div>
                <div class="flex items-center text-xs bg-primary/20 text-white px-4 py-2 rounded-full shadow-sm border border-primary/20">
                    <i data-lucide="trash-2" class="w-4 h-4 mr-2"></i>
                    <span>Log Management</span>
                </div>
                <div class="flex items-center text-xs bg-primary/20 text-white px-4 py-2 rounded-full shadow-sm border border-primary/20">
                    <i data-lucide="refresh-cw" class="w-4 h-4 mr-2"></i>
                    <span>Cache Control</span>
                </div>
                <div class="flex items-center text-xs bg-primary/20 text-white px-4 py-2 rounded-full shadow-sm border border-primary/20">
                    <i data-lucide="activity" class="w-4 h-4 mr-2"></i>
                    <span>Performance Monitoring</span>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
    <!-- Activity Log Maintenance -->
    <div class="card bg-card rounded-xl border border-border shadow-sm overflow-hidden">
        <div class="card-header flex items-center p-5 border-b border-border bg-gradient-to-r from-muted/30 to-background">
            <div class="w-10 h-10 rounded-lg bg-primary/20 flex items-center justify-center mr-3 shadow-sm border border-primary/20">
                <i data-lucide="history" class="w-5 h-5 text-primary"></i>
            </div>
            <h2 class="card-title text-lg font-semibold tracking-tight">Activity Log Maintenance</h2>
        </div>
        <div class="card-body p-5">
            <p class="mb-4 text-sm text-muted-foreground">Activity logs are automatically cleaned up based on the retention period setting.</p>

            <div class="form-group bg-card p-4 rounded-lg border border-border shadow-sm">
                <label for="retention-days" class="form-label font-medium text-sm mb-2 block">Retention Period (days)</label>
                <div class="flex items-center space-x-2">
                    <input type="number" id="retention-days" class="flex h-10 w-28 rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 shadow-sm"
                           value="{{ app_settings.activity_log_retention_days|default(30) }}" min="1" max="365">
                    <button id="save-retention-btn" class="btn btn-sm btn-primary rounded-lg shadow-sm flex items-center">
                        <i data-lucide="save" class="w-3.5 h-3.5 mr-2"></i>
                        <span>Save</span>
                    </button>
                </div>
                <p class="text-xs text-muted-foreground mt-2">
                    Activity logs older than this will be automatically deleted.
                </p>
            </div>

            <div class="mt-5">
                <h3 class="text-sm font-medium mb-3 flex items-center">
                    <div class="w-6 h-6 rounded-md bg-primary/20 flex items-center justify-center mr-2 shadow-sm border border-primary/20">
                        <i data-lucide="trash-2" class="w-3.5 h-3.5 text-primary"></i>
                    </div>
                    <span>Manual Cleanup Options</span>
                </h3>
                <div class="flex flex-wrap gap-2 ml-8">
                    <button id="delete-30-days-btn" class="btn btn-sm btn-outline rounded-lg shadow-sm flex items-center">
                        <i data-lucide="calendar" class="w-3.5 h-3.5 mr-2"></i>
                        <span>Delete > 30 days</span>
                    </button>
                    <button id="delete-90-days-btn" class="btn btn-sm btn-outline rounded-lg shadow-sm flex items-center">
                        <i data-lucide="calendar" class="w-3.5 h-3.5 mr-2"></i>
                        <span>Delete > 90 days</span>
                    </button>
                    <button id="delete-info-btn" class="btn btn-sm btn-outline rounded-lg shadow-sm flex items-center">
                        <i data-lucide="info" class="w-3.5 h-3.5 mr-2"></i>
                        <span>Delete Info Logs</span>
                    </button>
                    <button id="delete-all-logs-btn" class="btn btn-sm btn-destructive rounded-lg shadow-sm flex items-center border border-destructive/20">
                        <i data-lucide="alert-triangle" class="w-3.5 h-3.5 mr-2"></i>
                        <span>Delete All Logs</span>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Password Reset Tokens -->
    <div class="card bg-card rounded-xl border border-border shadow-sm overflow-hidden">
        <div class="card-header flex items-center p-5 border-b border-border bg-gradient-to-r from-muted/30 to-background">
            <div class="w-10 h-10 rounded-lg bg-primary/20 flex items-center justify-center mr-3 shadow-sm border border-primary/20">
                <i data-lucide="key" class="w-5 h-5 text-primary"></i>
            </div>
            <h2 class="card-title text-lg font-semibold tracking-tight">Password Reset Tokens</h2>
        </div>
        <div class="card-body p-5">
            <p class="mb-4 text-sm text-muted-foreground">Expired and used password reset tokens are automatically cleaned up during maintenance.</p>

            <div class="bg-info/5 dark:bg-info/10 p-4 rounded-lg border border-border shadow-sm mb-5">
                <div class="flex items-start space-x-3">
                    <div class="w-8 h-8 rounded-full bg-info/10 flex items-center justify-center flex-shrink-0 border border-border/60">
                        <i data-lucide="shield" class="w-4 h-4 text-info"></i>
                    </div>
                    <div class="text-sm">
                        <h4 class="font-medium mb-1">Security Note</h4>
                        <p class="text-muted-foreground">Password reset tokens are valid for a limited time for security reasons. Cleaning up expired tokens helps maintain database efficiency and security.</p>
                    </div>
                </div>
            </div>

            <div class="mt-5 flex justify-center">
                <button id="cleanup-tokens-btn" class="btn btn-sm btn-primary rounded-lg shadow-sm flex items-center px-5 py-2">
                    <i data-lucide="trash-2" class="w-4 h-4 mr-2"></i>
                    <span>Clean Up Tokens Now</span>
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Maintenance Settings -->
<div class="card bg-card rounded-xl border border-border shadow-sm overflow-hidden mb-8">
    <div class="card-header flex items-center p-5 border-b border-border bg-gradient-to-r from-muted/30 to-background">
        <div class="w-10 h-10 rounded-lg bg-primary/20 flex items-center justify-center mr-3 shadow-sm border border-primary/20">
            <i data-lucide="settings" class="w-5 h-5 text-primary"></i>
        </div>
        <h2 class="card-title text-lg font-semibold tracking-tight">Maintenance Settings</h2>
    </div>
    <div class="card-body p-5">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="bg-card p-5 rounded-lg border border-border shadow-sm">
                <div class="form-group">
                    <h3 class="text-base font-medium mb-3 flex items-center">
                        <div class="w-7 h-7 rounded-md bg-primary/20 flex items-center justify-center mr-2 shadow-sm border border-primary/20">
                            <i data-lucide="clock" class="w-4 h-4 text-primary"></i>
                        </div>
                        <span>Automatic Maintenance</span>
                    </h3>
                    <div class="flex items-start ml-9 mb-2">
                        <div class="flex-shrink-0 mt-1">
                            <input type="checkbox" id="auto-maintenance"
                                   {% if app_settings.enable_automatic_maintenance|default(true) %}checked{% endif %}
                                   class="h-4 w-4 rounded border-border bg-background text-primary focus:ring-primary focus:ring-offset-0">
                        </div>
                        <div class="ml-3">
                            <label for="auto-maintenance" class="text-sm font-medium block cursor-pointer">
                                Enable automatic maintenance
                            </label>
                            <p class="text-xs text-muted-foreground mt-1">
                                When enabled, maintenance tasks will run automatically when the application starts.
                            </p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="bg-card p-5 rounded-lg border border-border shadow-sm">
                <div class="form-group">
                    <h3 class="text-base font-medium mb-3 flex items-center">
                        <div class="w-7 h-7 rounded-md bg-primary/20 flex items-center justify-center mr-2 shadow-sm border border-primary/20">
                            <i data-lucide="database" class="w-4 h-4 text-primary"></i>
                        </div>
                        <span>Cache Management</span>
                    </h3>
                    <p class="text-sm text-muted-foreground mb-4 ml-9">
                        Clearing the cache forces the application to fetch fresh data from the database.
                    </p>
                    <div class="flex flex-wrap gap-2 ml-9">
                        <button id="clear-dashboard-cache-btn" class="btn btn-sm btn-outline rounded-lg shadow-sm flex items-center">
                            <i data-lucide="layout-dashboard" class="w-3.5 h-3.5 mr-2"></i>
                            <span>Dashboard Cache</span>
                        </button>
                        <button id="clear-business-cache-btn" class="btn btn-sm btn-outline rounded-lg shadow-sm flex items-center">
                            <i data-lucide="briefcase" class="w-3.5 h-3.5 mr-2"></i>
                            <span>Business Cache</span>
                        </button>
                        <button id="clear-employee-cache-btn" class="btn btn-sm btn-outline rounded-lg shadow-sm flex items-center">
                            <i data-lucide="users" class="w-3.5 h-3.5 mr-2"></i>
                            <span>Employee Cache</span>
                        </button>
                        <button id="clear-all-cache-btn" class="btn btn-sm btn-destructive rounded-lg shadow-sm flex items-center border border-destructive/20">
                            <i data-lucide="trash-2" class="w-3.5 h-3.5 mr-2"></i>
                            <span>All Cache</span>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Database Health -->
<div class="card bg-card rounded-xl border border-border shadow-sm overflow-hidden mb-8">
    <div class="card-header flex justify-between items-center p-5 border-b border-border bg-gradient-to-r from-muted/30 to-background">
        <div class="flex items-center">
            <div class="w-10 h-10 rounded-lg bg-primary/20 flex items-center justify-center mr-3 shadow-sm border border-primary/20">
                <i data-lucide="database" class="w-5 h-5 text-primary"></i>
            </div>
            <h2 class="card-title text-lg font-semibold tracking-tight">Database Health</h2>
        </div>
        <div class="flex space-x-2">
            <button id="refresh-db-health-btn" class="btn btn-sm btn-outline rounded-lg shadow-sm flex items-center">
                <i data-lucide="refresh-cw" class="w-3.5 h-3.5 mr-2"></i>
                <span>Refresh</span>
            </button>
            <button id="toggle-profiler-btn" class="btn btn-sm btn-outline rounded-lg shadow-sm flex items-center">
                <i data-lucide="activity" class="w-3.5 h-3.5 mr-2"></i>
                <span id="profiler-status-text">Start Profiler</span>
            </button>
        </div>
    </div>
    <div class="card-body p-5">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            <div class="bg-card p-5 rounded-xl border border-border shadow-sm">
                <div class="flex items-center mb-4">
                    <div class="w-8 h-8 rounded-lg bg-primary/20 flex items-center justify-center mr-3 shadow-sm border border-primary/20">
                        <i data-lucide="git-merge" class="w-4 h-4 text-primary"></i>
                    </div>
                    <h3 class="text-sm font-medium">Connection Pool</h3>
                </div>
                <div id="pool-status" class="text-sm">
                    <div class="flex justify-between py-1.5 border-b border-border/60">
                        <span class="text-muted-foreground">Engine Type:</span>
                        <span class="font-mono font-medium">SQLite</span>
                    </div>
                    <div class="flex justify-between py-1.5 border-b border-border/60">
                        <span class="text-muted-foreground">Pool Size:</span>
                        <span class="font-mono font-medium">1</span>
                    </div>
                    <div class="flex justify-between py-1.5 border-b border-border/60">
                        <span class="text-muted-foreground">Checked In:</span>
                        <span class="font-mono font-medium">0</span>
                    </div>
                    <div class="flex justify-between py-1.5 border-b border-border/60">
                        <span class="text-muted-foreground">Checked Out:</span>
                        <span class="font-mono font-medium">1</span>
                    </div>
                    <div class="flex justify-between py-1.5">
                        <span class="text-muted-foreground">Overflow:</span>
                        <span class="font-mono font-medium">0</span>
                    </div>
                    <div class="mt-3 text-xs text-muted-foreground bg-primary/5 p-2 rounded-md border border-primary/10">
                        <p>SQLite uses a file-based database and doesn't maintain a traditional connection pool.</p>
                    </div>
                </div>
            </div>
            <div class="bg-card p-5 rounded-xl border border-border shadow-sm">
                <div class="flex items-center mb-4">
                    <div class="w-8 h-8 rounded-lg bg-primary/20 flex items-center justify-center mr-3 shadow-sm border border-primary/20">
                        <i data-lucide="bar-chart" class="w-4 h-4 text-primary"></i>
                    </div>
                    <h3 class="text-sm font-medium">Database Statistics</h3>
                </div>
                <div id="db-stats" class="text-sm">
                    <div class="flex justify-between py-2 border-b border-border/60">
                        <span class="text-muted-foreground">Database Size:</span>
                        <span class="font-mono font-medium">-</span>
                    </div>
                    <div class="flex justify-between py-2 border-b border-border/60">
                        <span class="text-muted-foreground">Total Tables:</span>
                        <span class="font-mono font-medium">-</span>
                    </div>
                    <div class="flex justify-between py-2 border-b border-border/60">
                        <span class="text-muted-foreground">Total Records:</span>
                        <span class="font-mono font-medium">-</span>
                    </div>
                    <div class="flex justify-between py-2">
                        <span class="text-muted-foreground">Last Refresh:</span>
                        <span class="font-mono font-medium">-</span>
                    </div>
                </div>
            </div>
        </div>

        <div id="profiler-section" class="hidden">
            <div class="flex items-center mb-4">
                <div class="w-8 h-8 rounded-lg bg-primary/20 flex items-center justify-center mr-3 shadow-sm border border-primary/20">
                    <i data-lucide="activity" class="w-4 h-4 text-primary"></i>
                </div>
                <h3 class="text-sm font-medium">Query Profiler</h3>
            </div>
            <div class="bg-card p-5 rounded-xl border border-border shadow-sm mb-5">
                <div id="profiler-stats" class="text-sm grid grid-cols-2 md:grid-cols-4 gap-4">
                    <div class="bg-card p-4 rounded-lg border border-border shadow-sm">
                        <div class="text-xs text-muted-foreground mb-1">Queries</div>
                        <div class="font-mono text-lg font-semibold">-</div>
                    </div>
                    <div class="bg-card p-4 rounded-lg border border-border shadow-sm">
                        <div class="text-xs text-muted-foreground mb-1">Avg Time</div>
                        <div class="font-mono text-lg font-semibold">-</div>
                    </div>
                    <div class="bg-card p-4 rounded-lg border border-border shadow-sm">
                        <div class="text-xs text-muted-foreground mb-1">Max Time</div>
                        <div class="font-mono text-lg font-semibold">-</div>
                    </div>
                    <div class="bg-card p-4 rounded-lg border border-border shadow-sm">
                        <div class="text-xs text-muted-foreground mb-1">Slow Queries</div>
                        <div class="font-mono text-lg font-semibold">-</div>
                    </div>
                </div>
            </div>
            <div class="flex flex-wrap items-center gap-3 mb-2 bg-card p-4 rounded-lg border border-border shadow-sm">
                <label for="threshold-ms" class="text-sm font-medium">Slow Query Threshold (ms):</label>
                <div class="flex items-center space-x-2">
                    <input type="number" id="threshold-ms" class="flex h-10 w-28 rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 shadow-sm" value="100" min="10" max="5000">
                    <button id="apply-threshold-btn" class="btn btn-sm btn-primary rounded-lg shadow-sm flex items-center">
                        <i data-lucide="check" class="w-3.5 h-3.5 mr-2"></i>
                        <span>Apply</span>
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Maintenance Log -->
<div class="card bg-card rounded-xl border border-border shadow-sm overflow-hidden">
    <div class="card-header flex items-center p-5 border-b border-border bg-gradient-to-r from-muted/30 to-background">
        <div class="w-10 h-10 rounded-lg bg-primary/20 flex items-center justify-center mr-3 shadow-sm border border-primary/20">
            <i data-lucide="file-text" class="w-5 h-5 text-primary"></i>
        </div>
        <h2 class="card-title text-lg font-semibold tracking-tight">Maintenance Log</h2>
    </div>
    <div class="card-body p-5">
        <div class="flex items-start space-x-4 mb-5 bg-info/5 dark:bg-info/10 p-4 rounded-lg border border-border shadow-sm">
            <div class="w-8 h-8 rounded-full bg-info/10 flex items-center justify-center flex-shrink-0 border border-border/60 shadow-sm">
                <i data-lucide="info" class="w-4 h-4 text-info"></i>
            </div>
            <div>
                <h4 class="text-base font-medium mb-2">Session Log</h4>
                <p class="text-sm text-muted-foreground leading-relaxed">This log shows the results of maintenance operations performed during this session. The log is not persisted between page reloads.</p>
            </div>
        </div>
        <div id="maintenance-log" class="bg-card p-5 rounded-xl h-80 overflow-auto font-mono text-sm border border-border shadow-sm">
            <div class="text-muted-foreground">Maintenance log will appear here...</div>
        </div>
    </div>
</div>

<!-- Single Confirmation Modal Template -->
<template id="confirmation-modal-template" data-size="sm">
    <div class="p-6">
        <div class="flex justify-between items-start mb-4">
            <div>
                <h3 id="modal-title" class="text-lg font-semibold leading-6"></h3>
                <p id="modal-description" class="mt-2 text-sm text-muted-foreground"></p>
            </div>
            <button data-modal-close class="rounded-full h-6 w-6 inline-flex items-center justify-center text-muted-foreground hover:text-foreground bg-background hover:bg-muted focus:outline-none focus:ring-2 focus:ring-primary">
                <span class="sr-only">Close</span>
                <i data-lucide="x" class="h-4 w-4"></i>
            </button>
        </div>
        <div id="modal-warning" class="mt-4 bg-warning/10 p-3 rounded-md border border-warning/20 mb-4">
            <div class="flex items-start space-x-3">
                <div class="mt-0.5">
                    <i data-lucide="alert-triangle" class="h-5 w-5 text-warning"></i>
                </div>
                <div class="text-sm">
                    <p id="warning-text"></p>
                </div>
            </div>
        </div>
        <div class="mt-6 flex justify-end space-x-3">
            <button data-modal-close class="btn btn-sm btn-outline rounded-lg">
                Cancel
            </button>
            <button id="modal-action" class="btn btn-sm btn-primary rounded-lg"></button>
        </div>
    </div>
</template>
{% endblock %}



{% block scripts %}
<style>
  /* Improve light mode visibility */
  .bg-card {
    background-color: hsl(var(--card));
  }

  /* Improve dark mode readability */
  .dark .bg-card {
    background-color: hsl(var(--card));
  }

  .dark #maintenance-log {
    background-color: hsl(var(--card));
  }

  /* Improve alert borders in dark mode */
  .dark .border-info/20,
  .dark .border-border/60 {
    border-color: rgba(71, 85, 105, 0.4);
  }
</style>

<link rel="stylesheet" href="{{ url_for('static', filename='css/modal.css') }}">
<script src="{{ url_for('static', filename='js/components/modal.js') }}"></script>
<script src="{{ url_for('static', filename='js/admin/maintenance.js') }}"></script>
{% endblock %}

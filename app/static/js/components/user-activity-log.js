/**
 * User Activity Log Component
 *
 * This script handles the user-facing activity log component that can be included
 * on any page to show user activities or entity-specific activities.
 */

document.addEventListener('DOMContentLoaded', function() {
    // Initialize all user activity log components on the page
    initializeUserActivityLogs();

    // Set up modal close handlers
    setupActivityModal();
});

/**
 * Initialize all user activity log components on the page
 */
function initializeUserActivityLogs() {
    const activityLogs = document.querySelectorAll('.user-activity-log');

    activityLogs.forEach(logContainer => {
        const entityType = logContainer.dataset.entityType;
        const entityId = logContainer.dataset.entityId;
        const limit = parseInt(logContainer.dataset.limit || 5);
        const inProfilePage = document.getElementById('activities-tab') !== null;

        // Load activities
        loadActivities(logContainer, entityType, entityId, limit, inProfilePage);

        // Set up view all button
        const viewAllBtn = logContainer.querySelector('.view-all-activities');
        if (viewAllBtn) {
            viewAllBtn.addEventListener('click', () => {
                // This would typically navigate to a full activity log page
                // For now, we'll just load more activities in the same container
                loadActivities(logContainer, entityType, entityId, 20, inProfilePage);

                // Change button text to indicate loading more
                viewAllBtn.innerHTML = '<span>Loading more...</span><i data-lucide="loader" class="h-3.5 w-3.5 ml-1.5 animate-spin"></i>';

                // Re-initialize Lucide icons
                lucide.createIcons({
                    icons: {
                        loader: viewAllBtn.querySelector('[data-lucide="loader"]')
                    }
                });

                // Disable the button temporarily
                viewAllBtn.disabled = true;

                // After loading completes, the loadActivities success handler will re-enable the button
                setTimeout(() => {
                    viewAllBtn.innerHTML = '<span>View All</span><i data-lucide="check" class="h-3.5 w-3.5 ml-1.5"></i>';
                    lucide.createIcons({
                        icons: {
                            check: viewAllBtn.querySelector('[data-lucide="check"]')
                        }
                    });
                    viewAllBtn.disabled = false;
                }, 1000);
            });
        }

        // Set up retry button
        const retryBtn = logContainer.querySelector('.retry-loading');
        if (retryBtn) {
            retryBtn.addEventListener('click', () => {
                loadActivities(logContainer, entityType, entityId, limit, inProfilePage);
            });
        }
    });
}

/**
 * Load activities for a specific container
 */
function loadActivities(container, entityType, entityId, limit, inProfilePage = false) {
    const loadingEl = container.querySelector('.activities-loading');
    const listEl = container.querySelector('.activities-list');
    const emptyEl = container.querySelector('.activities-empty');
    const errorEl = container.querySelector('.activities-error');

    // Show loading state
    loadingEl.classList.remove('hidden');
    listEl.classList.add('hidden');
    emptyEl.classList.add('hidden');
    errorEl.classList.add('hidden');

    // Build the API URL with optional entity type filter
    // Use filtered endpoint for profile page activities to hide system errors and unrelated logs
    let apiUrl = inProfilePage
        ? `/auth/api/profile-activities?per_page=${limit}`
        : `/api/activities/user?per_page=${limit}`;

    // Add entity type filter if provided
    if (entityType) {
        apiUrl += `&entity_type=${entityType}`;
    }

    // Add entity id filter if provided
    if (entityId) {
        apiUrl += `&entity_id=${entityId}`;
    }

    // Fetch activities
    fetch(apiUrl)
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! Status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            // Hide loading state
            loadingEl.classList.add('hidden');

            if (data.success && data.activities && data.activities.length > 0) {
                // Render activities
                renderActivities(listEl, data.activities);
                listEl.classList.remove('hidden');

                // Animate the items in
                const items = listEl.querySelectorAll('.activity-item');
                items.forEach((item, index) => {
                    item.style.opacity = '0';
                    item.style.transform = 'translateY(10px)';
                    setTimeout(() => {
                        item.style.transition = 'opacity 0.3s ease, transform 0.3s ease';
                        item.style.opacity = '1';
                        item.style.transform = 'translateY(0)';
                    }, 50 * index);
                });
            } else {
                // Show empty state
                emptyEl.classList.remove('hidden');
            }
        })
        .catch(error => {
            console.error('Error loading activities:', error);
            loadingEl.classList.add('hidden');
            errorEl.classList.remove('hidden');
        });
}

/**
 * Render activities in the list container
 */
function renderActivities(container, activities) {
    // Clear existing content
    container.innerHTML = '';

    // Get the template
    const template = document.getElementById('activity-item-template');

    // Render each activity
    activities.forEach(activity => {
        const activityEl = template.content.cloneNode(true);

        // Set activity icon based on category
        const iconWrapper = activityEl.querySelector('.activity-icon-wrapper');
        const iconEl = iconWrapper.querySelector('i');
        let iconName = 'activity';

        if (activity.category === 'auth') {
            iconName = 'key';
            iconWrapper.classList.add('bg-blue-100', 'text-blue-600');
            iconWrapper.classList.remove('bg-primary/10', 'text-primary');
        } else if (activity.category === 'user') {
            iconName = 'user';
        } else if (activity.category === 'admin') {
            iconName = 'shield';
            iconWrapper.classList.add('bg-violet-100', 'text-violet-600');
            iconWrapper.classList.remove('bg-primary/10', 'text-primary');
        } else if (activity.category === 'data') {
            iconName = 'database';
            iconWrapper.classList.add('bg-green-100', 'text-green-600');
            iconWrapper.classList.remove('bg-primary/10', 'text-primary');
        } else if (activity.category === 'system') {
            iconName = 'settings';
            iconWrapper.classList.add('bg-gray-100', 'text-gray-600');
            iconWrapper.classList.remove('bg-primary/10', 'text-primary');
        }

        iconEl.setAttribute('data-lucide', iconName);

        // Set basic activity info
        activityEl.querySelector('.activity-action').textContent = activity.action;
        activityEl.querySelector('.activity-time').textContent = formatDate(activity.created_at);

        // Set details if available
        const detailsEl = activityEl.querySelector('.activity-details');
        if (activity.details) {
            // Truncate details if they're too long
            const maxLength = 120;
            if (activity.details.length > maxLength) {
                detailsEl.textContent = activity.details.substring(0, maxLength) + '...';
            } else {
                detailsEl.textContent = activity.details;
            }
        } else {
            detailsEl.classList.add('hidden');
        }

        // Set severity badge
        const badgeEl = activityEl.querySelector('.activity-severity-badge');
        badgeEl.textContent = activity.severity;

        // Apply appropriate styling based on severity
        if (activity.severity === 'error') {
            badgeEl.classList.add('bg-red-100', 'text-red-700', 'dark:bg-red-900/30', 'dark:text-red-400');
        } else if (activity.severity === 'warning') {
            badgeEl.classList.add('bg-yellow-100', 'text-yellow-700', 'dark:bg-yellow-900/30', 'dark:text-yellow-400');
        } else {
            badgeEl.classList.add('bg-green-100', 'text-green-700', 'dark:bg-green-900/30', 'dark:text-green-400');
        }

        // Set method badge if available
        const methodBadgeEl = activityEl.querySelector('.activity-method-badge');
        if (activity.method) {
            methodBadgeEl.textContent = activity.method_display || activity.method;

            // Apply appropriate styling based on method
            if (activity.method === 'create') {
                methodBadgeEl.classList.add('bg-blue-100', 'text-blue-700', 'dark:bg-blue-900/30', 'dark:text-blue-400');
            } else if (activity.method === 'update') {
                methodBadgeEl.classList.add('bg-amber-100', 'text-amber-700', 'dark:bg-amber-900/30', 'dark:text-amber-400');
            } else if (activity.method === 'delete') {
                methodBadgeEl.classList.add('bg-red-100', 'text-red-700', 'dark:bg-red-900/30', 'dark:text-red-400');
            } else if (activity.method === 'read') {
                methodBadgeEl.classList.add('bg-green-100', 'text-green-700', 'dark:bg-green-900/30', 'dark:text-green-400');
            } else {
                methodBadgeEl.classList.add('bg-gray-100', 'text-gray-700', 'dark:bg-gray-900/30', 'dark:text-gray-400');
            }
        } else {
            methodBadgeEl.classList.add('hidden');
        }

        // Handle changes if available
        if (activity.has_changes) {
            const changesEl = activityEl.querySelector('.activity-changes');
            changesEl.classList.remove('hidden');

            const tableBody = changesEl.querySelector('.changes-table-body');
            const rowTemplate = document.getElementById('changes-row-template');

            // Add rows for each changed field
            Object.keys(activity.old_values).forEach(field => {
                const rowEl = rowTemplate.content.cloneNode(true);

                rowEl.querySelector('.field-name').textContent = field;
                rowEl.querySelector('.old-value').innerHTML = formatValue(activity.old_values[field]);
                rowEl.querySelector('.new-value').innerHTML = formatValue(activity.new_values[field]);

                tableBody.appendChild(rowEl);
            });
        }

        // Set up view details button
        const viewDetailsBtn = activityEl.querySelector('.view-details-btn');
        viewDetailsBtn.addEventListener('click', () => {
            showActivityDetails(activity);
        });

        // Add to container
        container.appendChild(activityEl);
    });

    // Initialize Lucide icons for newly added elements
    lucide.createIcons();
}

/**
 * Format a date string for display
 */
function formatDate(dateString) {
    const date = new Date(dateString);
    const now = new Date();

    // If it's today, show relative time
    if (date.toDateString() === now.toDateString()) {
        const diffInHours = Math.floor((now - date) / (1000 * 60 * 60));
        const diffInMinutes = Math.floor((now - date) / (1000 * 60));

        if (diffInHours === 0) {
            if (diffInMinutes === 0) {
                return 'Just now';
            }
            return `${diffInMinutes} minute${diffInMinutes !== 1 ? 's' : ''} ago`;
        } else {
            return `${diffInHours} hour${diffInHours !== 1 ? 's' : ''} ago`;
        }
    }

    // If it's yesterday, show 'Yesterday'
    const yesterday = new Date(now);
    yesterday.setDate(now.getDate() - 1);
    if (date.toDateString() === yesterday.toDateString()) {
        return `Yesterday, ${date.toLocaleTimeString(undefined, { hour: 'numeric', minute: '2-digit' })}`;
    }

    // For other dates, show full date
    return date.toLocaleString(undefined, {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: 'numeric',
        minute: '2-digit',
        hour12: true
    });
}

/**
 * Format a value for display in the changes table
 */
function formatValue(value) {
    if (value === null || value === undefined) {
        return '<span class="text-muted-foreground italic">null</span>';
    } else if (typeof value === 'object') {
        return '<span class="bg-muted/50 px-1.5 py-0.5 rounded text-xs font-mono">' + JSON.stringify(value) + '</span>';
    } else if (typeof value === 'boolean') {
        return value ?
            '<span class="text-green-500 font-medium">true</span>' :
            '<span class="text-red-500 font-medium">false</span>';
    } else if (value === '') {
        return '<span class="text-muted-foreground italic">(empty string)</span>';
    } else if (typeof value === 'string' && value.length > 50) {
        // For long strings, show truncated version with full text on hover
        return `<span title="${value}" class="cursor-help">${value.substring(0, 50)}...</span>`;
    } else {
        return String(value);
    }
}

/**
 * Set up the activity details modal
 */
function setupActivityModal() {
    const modal = document.getElementById('user-activity-details-modal');
    if (!modal) return;

    // Close button in header
    const closeBtn = modal.querySelector('.modal-close');
    if (closeBtn) {
        closeBtn.addEventListener('click', () => {
            closeActivityModal();
        });
    }

    // Close button in footer
    const closeBtnFooter = modal.querySelector('.modal-close-btn');
    if (closeBtnFooter) {
        closeBtnFooter.addEventListener('click', () => {
            closeActivityModal();
        });
    }

    // Close on overlay click
    const overlay = modal.querySelector('.modal-overlay');
    if (overlay) {
        overlay.addEventListener('click', () => {
            closeActivityModal();
        });
    }

    // Close on Escape key
    document.addEventListener('keydown', (e) => {
        if (e.key === 'Escape' && modal.classList.contains('modal-open')) {
            closeActivityModal();
        }
    });
}

/**
 * Close the activity details modal
 */
function closeActivityModal() {
    const modal = document.getElementById('user-activity-details-modal');
    if (!modal) return;

    // Add closing animation
    modal.classList.add('modal-closing');

    // Remove modal after animation
    setTimeout(() => {
        modal.classList.remove('modal-open', 'modal-closing');
    }, 300);
}

/**
 * Get color for method badge
 */
function getMethodColor(method) {
    switch(method) {
        case 'create': return 'blue';
        case 'read': return 'green';
        case 'update': return 'amber';
        case 'delete': return 'red';
        default: return 'gray';
    }
}

/**
 * Get icon for method badge
 */
function getMethodIcon(method) {
    switch(method) {
        case 'create': return 'plus';
        case 'read': return 'eye';
        case 'update': return 'edit';
        case 'delete': return 'trash';
        default: return 'activity';
    }
}

/**
 * Show activity details in the modal
 */
function showActivityDetails(activity) {
    const modal = document.getElementById('user-activity-details-modal');
    const contentDiv = document.getElementById('user-activity-details-content');

    if (!modal || !contentDiv) return;

    // Format date
    const date = new Date(activity.created_at);
    const formattedDate = date.toLocaleString(undefined, {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
    });

    // Determine severity classes
    let severityClass = 'text-green-600 dark:text-green-400';
    let severityBgClass = 'bg-green-100 dark:bg-green-900/30';
    if (activity.severity === 'error') {
        severityClass = 'text-red-600 dark:text-red-400';
        severityBgClass = 'bg-red-100 dark:bg-red-900/30';
    } else if (activity.severity === 'warning') {
        severityClass = 'text-yellow-600 dark:text-yellow-400';
        severityBgClass = 'bg-yellow-100 dark:bg-yellow-900/30';
    }

    // Choose icon based on category
    let categoryIcon = 'activity';
    let categoryColor = 'text-primary';
    if (activity.category === 'auth') {
        categoryIcon = 'key';
        categoryColor = 'text-blue-600 dark:text-blue-400';
    } else if (activity.category === 'user') {
        categoryIcon = 'user';
    } else if (activity.category === 'admin') {
        categoryIcon = 'shield';
        categoryColor = 'text-violet-600 dark:text-violet-400';
    } else if (activity.category === 'data') {
        categoryIcon = 'database';
        categoryColor = 'text-green-600 dark:text-green-400';
    } else if (activity.category === 'system') {
        categoryIcon = 'settings';
        categoryColor = 'text-gray-600 dark:text-gray-400';
    }

    // Build HTML content
    let html = `
      <div class="space-y-8">
        <div class="bg-muted/30 p-5 rounded-lg border border-border">
          <div class="flex items-center space-x-5">
            <div class="h-14 w-14 rounded-full ${severityBgClass} flex items-center justify-center ${severityClass}">
              <i data-lucide="${activity.severity === 'error' ? 'alert-triangle' : (activity.severity === 'warning' ? 'alert-circle' : 'check-circle')}" class="h-7 w-7"></i>
            </div>
            <div class="flex-1">
              <h3 class="text-xl font-semibold ${severityClass}">${activity.action}</h3>
              <p class="text-sm text-muted-foreground flex items-center mt-2">
                <i data-lucide="clock" class="h-4 w-4 mr-2"></i>
                ${formattedDate}
              </p>
            </div>
          </div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
          <div class="space-y-2">
            <label class="text-xs text-muted-foreground">User</label>
            <div class="flex items-center space-x-4">
              <div class="h-10 w-10 rounded-full bg-primary/10 flex items-center justify-center text-primary">
                <span class="text-base font-medium">${activity.user_name ? activity.user_name.charAt(0) : '?'}</span>
              </div>
              <div>
                <p class="text-sm font-medium">${activity.user_name || 'Unknown'}</p>
                <p class="text-xs text-muted-foreground">${activity.user_email || ''}</p>
              </div>
            </div>
          </div>

          <div class="space-y-2">
            <label class="text-xs text-muted-foreground">Category</label>
            <div class="flex items-center space-x-3">
              <div class="h-8 w-8 rounded-full ${severityBgClass} flex items-center justify-center ${categoryColor}">
                <i data-lucide="${categoryIcon}" class="h-4 w-4"></i>
              </div>
              <p class="text-sm">${activity.category_display || 'System'}</p>
              <span class="inline-flex items-center rounded-full px-2.5 py-0.5 text-xs ${severityBgClass} ${severityClass}">
                ${activity.severity}
              </span>
            </div>
          </div>

          <div class="space-y-2">
            <label class="text-xs text-muted-foreground">Method</label>
            <div class="flex items-center space-x-3">
              ${activity.method ? `
              <div class="h-8 w-8 rounded-full bg-${getMethodColor(activity.method)}-100 dark:bg-${getMethodColor(activity.method)}-900/30 flex items-center justify-center text-${getMethodColor(activity.method)}-600 dark:text-${getMethodColor(activity.method)}-400">
                <i data-lucide="${getMethodIcon(activity.method)}" class="h-4 w-4"></i>
              </div>
              <p class="text-sm">${activity.method_display || activity.method}</p>
              ` : '<p class="text-sm text-muted-foreground">Not specified</p>'}
            </div>
          </div>
        </div>

        <div class="space-y-2">
          <label class="text-xs text-muted-foreground">Details</label>
          <div class="bg-muted/30 p-4 rounded-md whitespace-pre-line break-words text-sm leading-relaxed">
            ${activity.details || 'No additional details'}
          </div>
        </div>
    `;

    // Add entity information if available
    if (activity.entity_type && activity.entity_id) {
        html += `
          <div class="space-y-2">
            <label class="text-xs text-muted-foreground">Related Entity</label>
            <div class="bg-muted/30 p-4 rounded-md">
              <div class="flex items-center text-sm">
                <i data-lucide="file" class="h-4 w-4 mr-3 text-muted-foreground"></i>
                <span>${activity.entity_type} #${activity.entity_id}</span>
              </div>
            </div>
          </div>
        `;
    }

    // Add context information
    html += `
        <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
          <div class="space-y-2">
            <label class="text-xs text-muted-foreground">IP Address</label>
            <p class="text-sm bg-muted/30 p-3 rounded-md font-mono">${activity.ip_address || 'Not recorded'}</p>
          </div>

          <div class="space-y-2">
            <label class="text-xs text-muted-foreground">Browser / Device</label>
            <p class="text-sm bg-muted/30 p-3 rounded-md truncate" title="${activity.user_agent || ''}">
              ${activity.user_agent ? activity.user_agent.substring(0, 40) + '...' : 'Not recorded'}
            </p>
          </div>
        </div>
    `;

    // Add changes section if available
    if (activity.has_changes) {
        html += `
          <div class="space-y-3">
            <div class="flex items-center text-sm font-medium">
              <i data-lucide="git-compare" class="h-4 w-4 mr-2 text-primary"></i>
              Changes
            </div>
            <div class="bg-muted/30 p-4 rounded-md overflow-auto max-h-[300px]">
              <table class="w-full text-sm">
                <thead>
                  <tr class="border-b border-border/60">
                    <th class="text-left py-3 px-4 font-medium">Field</th>
                    <th class="text-left py-3 px-4 font-medium">Old Value</th>
                    <th class="text-left py-3 px-4 font-medium">New Value</th>
                  </tr>
                </thead>
                <tbody>
        `;

        Object.keys(activity.old_values).forEach(field => {
            html += `
                  <tr class="border-b border-border/40">
                    <td class="py-3 px-4 font-medium align-top">${field}</td>
                    <td class="py-3 px-4 align-top whitespace-pre-wrap break-words">${formatValue(activity.old_values[field])}</td>
                    <td class="py-3 px-4 align-top whitespace-pre-wrap break-words">${formatValue(activity.new_values[field])}</td>
                  </tr>
            `;
        });

        html += `
                </tbody>
              </table>
            </div>
          </div>
        `;
    }

    html += `</div>`;

    // Show the modal with a loading effect
    modal.classList.add('modal-open');
    contentDiv.innerHTML = '<div class="flex justify-center items-center py-16"><div class="animate-spin rounded-full h-10 w-10 border-2 border-t-transparent border-primary"></div></div>';

    // Add the content with a slight delay for better UX
    setTimeout(() => {
        contentDiv.innerHTML = html;

        // Initialize icons in the modal
        lucide.createIcons();

        // Add focus trap for accessibility
        const firstFocusable = modal.querySelector('button:not([disabled]), [href], input, select, textarea, [tabindex]:not([tabindex="-1"])');
        if (firstFocusable) {
            firstFocusable.focus();
        }
    }, 300);
}

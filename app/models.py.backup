from app import db
from datetime import datetime, timedelta
from werkzeug.security import generate_password_hash, check_password_hash
from flask_login import UserMixin
from flask import current_app
import uuid
import re
import pytz
import json
from typing import List, Optional, Union, Dict, Any, Type

# Set Philippine Timezone
PH_TZ = pytz.timezone('Asia/Manila')

class WeakPasswordError(ValueError):
    """Raised when a password doesn't meet strength requirements."""
    pass


class User(UserMixin, db.Model):
    """User model for authentication and profile information."""
    __tablename__ = 'users'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False, index=True)
    password_hash = db.Column(db.String(255))
    role = db.Column(db.String(20), default='User')  # Admin, Manager, User
    _is_active = db.Column('is_active', db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=lambda: datetime.now(PH_TZ))
    updated_at = db.Column(db.DateTime, default=lambda: datetime.now(PH_TZ), onupdate=lambda: datetime.now(PH_TZ))
    last_login = db.Column(db.DateTime, nullable=True)
    last_password_date = db.Column(db.DateTime, default=lambda: datetime.now(PH_TZ), nullable=True)
    bio = db.Column(db.Text, nullable=True)
    timezone = db.Column(db.String(50), default='Asia/Manila', nullable=True)
    avatar = db.Column(db.String(255), nullable=True)
    deleted_at = db.Column(db.DateTime, nullable=True)

    # Relationships
    employee_detail = db.relationship('EmployeeDetail', backref='user', uselist=False,
                                      cascade='all, delete-orphan', lazy='joined')
    activities = db.relationship('Activity', backref='user', lazy='dynamic',
                                cascade='all, delete-orphan')
    sent_messages = db.relationship('Message', foreign_keys='Message.sender_id',
                                  backref='sender', lazy='dynamic',
                                  cascade='all, delete-orphan')
    received_messages = db.relationship('Message', foreign_keys='Message.recipient_id',
                                      backref='recipient', lazy='dynamic')
    managed_units = db.relationship('BusinessUnit', foreign_keys='BusinessUnit.manager_id',
                                  backref='manager', lazy='dynamic')
    managed_segments = db.relationship('BusinessSegment', foreign_keys='BusinessSegment.manager_id',
                                     backref='manager', lazy='dynamic')

    def __repr__(self) -> str:
        return f'<User {self.name}>'

    def set_password(self, password: str) -> None:
        """Hash and set the user password."""
        from app.utils.settings import get_setting

        # Get password requirements from settings
        min_length = get_setting('min_password_length', 8)
        requires_uppercase = get_setting('password_requires_uppercase', True)
        requires_number = get_setting('password_requires_number', True)
        requires_special = get_setting('password_requires_special', False)

        # Validate password strength
        if not self._is_password_strong(password):
            # Build a dynamic error message based on actual requirements
            error_msg = f"Password must be at least {min_length} characters and contain at least one letter"
            requirements = []

            if requires_uppercase:
                requirements.append("one uppercase letter")
            if requires_number:
                requirements.append("one number")
            if requires_special:
                requirements.append("one special character")

            if requirements:
                error_msg += " and " + ", ".join(requirements)

            raise WeakPasswordError(error_msg)

        self.password_hash = generate_password_hash(password)
        self.last_password_date = datetime.now(PH_TZ)

    def check_password(self, password: str) -> bool:
        """Verify the user password."""
        if not self.password_hash:
            return False
        return check_password_hash(self.password_hash, password)

    def get_days_since_password_change(self) -> int:
        """Get the number of days since the last password change."""
        if not self.last_password_date:
            return 0
        return (datetime.now(PH_TZ) - self.last_password_date).days

    def get_password_status(self) -> str:
        """Get the status of the password based on its age."""
        days = self.get_days_since_password_change()
        if days > 90:  # More than 3 months
            return 'expired'
        elif days > 60:  # More than 2 months
            return 'warning'
        return 'good'

    @classmethod
    def _is_password_strong(cls, password: str) -> bool:
        """Check if password meets strength requirements based on system settings."""
        from app.utils.settings import get_setting

        # Get password requirements from settings
        min_length = get_setting('min_password_length', 8)
        requires_uppercase = get_setting('password_requires_uppercase', True)
        requires_number = get_setting('password_requires_number', True)
        requires_special = get_setting('password_requires_special', False)

        # Check minimum length
        if len(password) < min_length:
            return False

        # Always require at least one letter (lowercase or uppercase)
        has_letter = re.search(r'[a-zA-Z]', password) is not None
        if not has_letter:
            return False

        # Check for uppercase if required
        if requires_uppercase:
            has_uppercase = re.search(r'[A-Z]', password) is not None
            if not has_uppercase:
                return False

        # Check for number if required
        if requires_number:
            has_number = re.search(r'\d', password) is not None
            if not has_number:
                return False

        # Check for special character if required
        if requires_special:
            has_special = re.search(r'[!@#$%^&*(),.?":{}|<>]', password) is not None
            if not has_special:
                return False

        return True

    @property
    def is_admin(self) -> bool:
        """Check if user has admin role."""
        return self.role == 'Admin'

    @property
    def is_manager(self) -> bool:
        """Check if user has manager role."""
        return self.role == 'Manager'

    @property
    def is_active(self) -> bool:
        """Override UserMixin.is_active to check for soft deletion."""
        return self._is_active and not self.is_deleted

    @is_active.setter
    def is_active(self, value: bool) -> None:
        """Setter for is_active property."""
        self._is_active = value

    @property
    def is_deleted(self) -> bool:
        """Check if user has been soft-deleted."""
        return self.deleted_at is not None

    def soft_delete(self) -> None:
        """Soft delete the user instead of permanent deletion."""
        self.deleted_at = datetime.now(PH_TZ)
        self.is_active = False
        db.session.commit()

    @property
    def employee_details(self) -> 'EmployeeDetailsProxy':
        """Access employee details with proper defaults to avoid None errors."""
        if not hasattr(self, '_employee_details_proxy'):
            self._employee_details_proxy = EmployeeDetailsProxy(self.employee_detail)
        else:
            # Update the proxy with the current employee_detail reference
            self._employee_details_proxy._update_reference(self.employee_detail)
        return self._employee_details_proxy

    def get_recent_activities(self, limit: int = 10) -> List['Activity']:
        """Get the user's most recent activities."""
        return self.activities.order_by(Activity.created_at.desc()).limit(limit).all()

    def get_unread_message_count(self) -> int:
        """Get the count of unread messages."""
        return self.received_messages.filter_by(is_read=False).count()

    def to_dict(self, include_details: bool = True) -> Dict[str, Any]:
        """Serialize user object to dictionary."""
        data = {
            'id': self.id,
            'name': self.name,
            'email': self.email,
            'role': self.role,
            'is_active': self.is_active,
            'last_login': self.last_login.isoformat() if self.last_login else None,
            'created_at': self.created_at.isoformat(),
        }

        if include_details and self.employee_detail:
            data['employee_details'] = {
                'position': self.employee_details.position,
                'phone': self.employee_details.phone,
                'business_unit': self.employee_details.business_unit.name if self.employee_details.business_unit else None,
                'business_segment': self.employee_details.business_segment.name if self.employee_details.business_segment else None,
            }

        return data

    @classmethod
    def get_by_email(cls, email: str) -> Optional['User']:
        """Get a user by email with efficient query."""
        return cls.query.filter_by(email=email).first()

    @classmethod
    def get_active_users_by_unit(cls, business_unit_id: int) -> List['User']:
        """Get all active users in a business unit with optimized query."""
        return cls.query.join(EmployeeDetail).filter(
            EmployeeDetail.business_unit_id == business_unit_id,
            User._is_active == True,
            User.deleted_at == None
        ).all()


class EmployeeDetailsProxy:
    """A proxy class for safely accessing employee details even when they don't exist."""

    def __init__(self, employee_detail: Optional['EmployeeDetail']):
        self._employee_detail = employee_detail
        # Force cache refresh on initialization
        self._refresh_cache()

    def _refresh_cache(self):
        """Refresh cached property values"""
        # This ensures we always have the latest values
        if hasattr(self, '_cache'):
            delattr(self, '_cache')

    def _update_reference(self, employee_detail: Optional['EmployeeDetail']):
        """Update the reference to the employee_detail object."""
        self._employee_detail = employee_detail
        self._refresh_cache()

    @property
    def exists(self) -> bool:
        """Check if employee details exist for this user."""
        return self._employee_detail is not None

    @property
    def job_title(self) -> str:
        """Get employee job title with fallback."""
        return self._employee_detail.job_title if self._employee_detail else ''

    @property
    def position(self) -> str:
        """Get employee position with fallback (alias for job_title for backward compatibility)."""
        return self.job_title

    @property
    def employee_number(self) -> str:
        """Get employee number with fallback."""
        return self._employee_detail.employee_number if self._employee_detail else ''

    @property
    def phone(self) -> str:
        """Get employee phone with fallback."""
        return self._employee_detail.phone if self._employee_detail else ''

    @property
    def bio(self) -> str:
        """Get employee bio with fallback."""
        if self._employee_detail and self._employee_detail.user:
            return self._employee_detail.user.bio or ''
        return ''

    @property
    def avatar(self) -> str:
        """Get employee avatar with fallback."""
        if self._employee_detail and self._employee_detail.user:
            return self._employee_detail.user.avatar or ''
        return ''

    @property
    def business_unit_id(self) -> Optional[int]:
        """Get employee business unit ID with fallback."""
        return self._employee_detail.business_unit_id if self._employee_detail else None

    @property
    def business_segment_id(self) -> Optional[int]:
        """Get employee business segment ID with fallback."""
        return self._employee_detail.business_segment_id if self._employee_detail else None

    @property
    def hire_date(self) -> Optional[datetime]:
        """Get employee hire date with fallback."""
        return self._employee_detail.hire_date if self._employee_detail else None

    @property
    def business_unit(self) -> Optional['BusinessUnit']:
        """Get employee business unit with fallback."""
        return self._employee_detail.business_unit if self._employee_detail else None

    @property
    def business_segment(self) -> Optional['BusinessSegment']:
        """Get employee business segment with fallback."""
        return self._employee_detail.business_segment if self._employee_detail else None

    @property
    def teams(self) -> List['Team']:
        """Get employee teams with fallback."""
        return self._employee_detail.teams if self._employee_detail else []

    @property
    def first_name(self) -> str:
        """Get employee first name with fallback."""
        return self._employee_detail.first_name if self._employee_detail else ''

    @property
    def middle_name(self) -> str:
        """Get employee middle name with fallback."""
        return self._employee_detail.middle_name if self._employee_detail else ''

    @property
    def last_name(self) -> str:
        """Get employee last name with fallback."""
        return self._employee_detail.last_name if self._employee_detail else ''

    @property
    def legal_name(self) -> str:
        """Get employee legal name with fallback."""
        return self._employee_detail.legal_name if self._employee_detail else ''

    @property
    def emp_type(self) -> str:
        """Get employee type with fallback."""
        return self._employee_detail.emp_type if self._employee_detail else ''

    @property
    def enterprise_id(self) -> str:
        """Get employee enterprise ID with fallback."""
        return self._employee_detail.enterprise_id if self._employee_detail else ''

    @property
    def manager_name(self) -> str:
        """Get employee manager name with fallback."""
        return self._employee_detail.manager_name if self._employee_detail else ''

    @property
    def job_code(self) -> str:
        """Get employee job code with fallback."""
        return self._employee_detail.job_code if self._employee_detail else ''

    @property
    def manager_level(self) -> str:
        """Get employee manager level with fallback."""
        return self._employee_detail.manager_level if self._employee_detail else ''

    @property
    def job_code_track_level(self) -> str:
        """Get employee job code track level with fallback."""
        return self._employee_detail.job_code_track_level if self._employee_detail else ''

    @property
    def emp_status(self) -> str:
        """Get employee status with fallback."""
        return self._employee_detail.emp_status if self._employee_detail else EmployeeDetail.STATUS_ACTIVE


# Association table for many-to-many relationship between Team and EmployeeDetail
team_members = db.Table('team_members',
    db.Column('team_id', db.Integer, db.ForeignKey('teams.id', name='fk_team_members_team_id'), primary_key=True),
    db.Column('employee_detail_id', db.Integer, db.ForeignKey('employee_details.id', name='fk_team_members_employee_detail_id'), primary_key=True),
    db.Column('created_at', db.DateTime, default=lambda: datetime.now(PH_TZ))
)


class EmployeeDetail(db.Model):
    """Employee-specific details model, linked to User."""
    __tablename__ = 'employee_details'

    # Employment status options
    STATUS_ACTIVE = 'active'
    STATUS_TERMINATED = 'terminated'
    STATUS_LEAVE = 'leave_of_absence'

    STATUS_CHOICES = [
        (STATUS_ACTIVE, 'Active'),
        (STATUS_TERMINATED, 'Terminated'),
        (STATUS_LEAVE, 'Leave of Absence')
    ]

    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id', name='fk_employee_detail_user_id_cascade', ondelete='CASCADE'), nullable=False, unique=True)
    employee_number = db.Column(db.String(100), unique=True, nullable=True)
    first_name = db.Column(db.String(100), nullable=True)
    middle_name = db.Column(db.String(100), nullable=True)
    last_name = db.Column(db.String(100), nullable=True)
    legal_name = db.Column(db.String(200), nullable=True)
    job_title = db.Column(db.String(100), nullable=True)  # Renamed from position
    # department field removed - using business unit and segment instead
    phone = db.Column(db.String(20), nullable=True)
    emp_type = db.Column(db.String(50), nullable=True)  # e.g., Full-time, Part-time, Contract
    enterprise_id = db.Column(db.String(100), nullable=True)
    manager_name = db.Column(db.String(100), nullable=True)
    job_code = db.Column(db.String(50), nullable=True)
    manager_level = db.Column(db.String(50), nullable=True)
    job_code_track_level = db.Column(db.String(50), nullable=True)
    business_unit_id = db.Column(db.Integer, db.ForeignKey('business_units.id', name='fk_employee_detail_business_unit_id'), nullable=True)
    business_segment_id = db.Column(db.Integer, db.ForeignKey('business_segments.id', name='fk_employee_detail_business_segment_id'), nullable=True)
    hire_date = db.Column(db.Date, nullable=True)
    emp_status = db.Column(db.String(20), default=STATUS_ACTIVE, nullable=False)
    created_at = db.Column(db.DateTime, default=lambda: datetime.now(PH_TZ))
    updated_at = db.Column(db.DateTime, default=lambda: datetime.now(PH_TZ), onupdate=lambda: datetime.now(PH_TZ))

    # Relationship with teams
    teams = db.relationship('Team', secondary=team_members, back_populates='members')

    __table_args__ = (
        db.Index('idx_employee_detail_business_unit', business_unit_id),
        db.Index('idx_employee_detail_business_segment', business_segment_id),
    )

    # Relationships
    business_unit = db.relationship('BusinessUnit', backref='employee_details')
    business_segment = db.relationship('BusinessSegment', backref='employee_details')

    def __repr__(self) -> str:
        return f'<EmployeeDetail for {self.user.name if self.user else "Unknown"}>'

    def to_dict(self) -> Dict[str, Any]:
        """Serialize employee details to dictionary."""
        return {
            'id': self.id,
            'user_id': self.user_id,
            'employee_number': self.employee_number,
            'first_name': self.first_name,
            'middle_name': self.middle_name,
            'last_name': self.last_name,
            'legal_name': self.legal_name,
            'job_title': self.job_title,
            'position': self.job_title,  # Alias for backward compatibility
            'phone': self.phone,
            'emp_type': self.emp_type,
            'enterprise_id': self.enterprise_id,
            'manager_name': self.manager_name,
            'job_code': self.job_code,
            'manager_level': self.manager_level,
            'job_code_track_level': self.job_code_track_level,
            'business_unit_id': self.business_unit_id,
            'business_segment_id': self.business_segment_id,
            'business_unit': self.business_unit.name if self.business_unit else None,
            'business_segment': self.business_segment.name if self.business_segment else None,
            'hire_date': self.hire_date.isoformat() if self.hire_date else None,
            'emp_status': self.emp_status,
            'bio': self.user.bio if self.user else None,
            'avatar': self.user.avatar if self.user else None,
            'teams': [team.name for team in self.teams] if self.teams else [],
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
        }


class BusinessUnit(db.Model):
    """Business unit model for organizational structure."""
    __tablename__ = 'business_units'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    code = db.Column(db.String(20), unique=True, nullable=False, index=True)
    description = db.Column(db.Text, nullable=True)
    manager_id = db.Column(db.Integer, db.ForeignKey('users.id', name='fk_business_unit_manager_id'), nullable=True)
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=lambda: datetime.now(PH_TZ))
    updated_at = db.Column(db.DateTime, default=lambda: datetime.now(PH_TZ), onupdate=lambda: datetime.now(PH_TZ))

    # Relationships
    segments = db.relationship('BusinessSegment', backref='business_unit', lazy='dynamic',
                              cascade='all, delete-orphan')

    def __repr__(self) -> str:
        return f'<BusinessUnit {self.name}>'

    def get_active_segments(self) -> List['BusinessSegment']:
        """Get all active segments in this business unit."""
        return self.segments.filter_by(is_active=True).all()

    def get_employee_count(self) -> int:
        """Get the count of employees in this business unit."""
        return EmployeeDetail.query.filter_by(business_unit_id=self.id).count()

    def to_dict(self, include_segments: bool = False) -> Dict[str, Any]:
        """Serialize business unit to dictionary."""
        data = {
            'id': self.id,
            'name': self.name,
            'code': self.code,
            'description': self.description,
            'is_active': self.is_active,
            'manager_id': self.manager_id,
            'manager_name': self.manager.name if self.manager else None,
            'employee_count': self.get_employee_count(),
        }

        if include_segments:
            data['segments'] = [segment.to_dict() for segment in self.get_active_segments()]

        return data


class BusinessSegment(db.Model):
    """Business segment model, linked to business units."""
    __tablename__ = 'business_segments'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    code = db.Column(db.String(20), unique=True, nullable=False, index=True)
    description = db.Column(db.Text, nullable=True)
    business_unit_id = db.Column(db.Integer, db.ForeignKey('business_units.id', name='fk_business_segment_business_unit_id'), nullable=False)
    manager_id = db.Column(db.Integer, db.ForeignKey('users.id', name='fk_business_segment_manager_id'), nullable=True)
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=lambda: datetime.now(PH_TZ))
    updated_at = db.Column(db.DateTime, default=lambda: datetime.now(PH_TZ), onupdate=lambda: datetime.now(PH_TZ))

    def __repr__(self) -> str:
        return f'<BusinessSegment {self.name}>'

    def get_employee_count(self) -> int:
        """Get the count of employees in this business segment."""
        return EmployeeDetail.query.filter_by(business_segment_id=self.id).count()

    def to_dict(self) -> Dict[str, Any]:
        """Serialize business segment to dictionary."""
        return {
            'id': self.id,
            'name': self.name,
            'code': self.code,
            'description': self.description,
            'business_unit_id': self.business_unit_id,
            'business_unit_name': self.business_unit.name if self.business_unit else None,
            'manager_id': self.manager_id,
            'manager_name': self.manager.name if self.manager else None,
            'is_active': self.is_active,
            'employee_count': self.get_employee_count(),
        }


class Team(db.Model):
    """Team model for grouping employees."""
    __tablename__ = 'teams'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    short_name = db.Column(db.String(20), nullable=True)
    slug = db.Column(db.String(100), unique=True, nullable=False, index=True)
    description = db.Column(db.Text, nullable=True)
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=lambda: datetime.now(PH_TZ))
    updated_at = db.Column(db.DateTime, default=lambda: datetime.now(PH_TZ), onupdate=lambda: datetime.now(PH_TZ))

    # Relationships
    members = db.relationship('EmployeeDetail', secondary=team_members, back_populates='teams')
    groups = db.relationship('TeamGroup', backref='team', lazy='dynamic', cascade='all, delete-orphan')

    def __repr__(self) -> str:
        return f'<Team {self.name}>'

    def to_dict(self) -> Dict[str, Any]:
        """Serialize team to dictionary."""
        return {
            'id': self.id,
            'name': self.name,
            'short_name': self.short_name,
            'slug': self.slug,
            'description': self.description,
            'is_active': self.is_active,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'member_count': len(self.members) if self.members else 0,
            'group_count': self.groups.count() if self.groups else 0
        }


class TeamGroup(db.Model):
    """Team group model for organizing team members."""
    __tablename__ = 'team_groups'

    id = db.Column(db.Integer, primary_key=True)
    team_id = db.Column(db.Integer, db.ForeignKey('teams.id', name='fk_team_group_team_id', ondelete='CASCADE'), nullable=False)
    name = db.Column(db.String(100), nullable=False)
    description = db.Column(db.Text, nullable=True)
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=lambda: datetime.now(PH_TZ))
    updated_at = db.Column(db.DateTime, default=lambda: datetime.now(PH_TZ), onupdate=lambda: datetime.now(PH_TZ))

    def __repr__(self) -> str:
        return f'<TeamGroup {self.name} (Team: {self.team.name if self.team else "Unknown"})>'

    def to_dict(self) -> Dict[str, Any]:
        """Serialize team group to dictionary."""
        return {
            'id': self.id,
            'team_id': self.team_id,
            'name': self.name,
            'description': self.description,
            'is_active': self.is_active,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'team_name': self.team.name if self.team else None
        }


class Message(db.Model):
    """Message model for internal communications."""
    __tablename__ = 'messages'

    id = db.Column(db.Integer, primary_key=True)
    sender_id = db.Column(db.Integer, db.ForeignKey('users.id', name='fk_message_sender_id_cascade', ondelete='CASCADE'), nullable=False)
    recipient_id = db.Column(db.Integer, db.ForeignKey('users.id', name='fk_message_recipient_id_cascade', ondelete='CASCADE'), nullable=False)
    subject = db.Column(db.String(255), nullable=False)
    body = db.Column(db.Text)
    is_read = db.Column(db.Boolean, default=False)
    created_at = db.Column(db.DateTime, default=lambda: datetime.now(PH_TZ), index=True)

    __table_args__ = (
        db.Index('idx_message_recipient_read_created', recipient_id, is_read, created_at),
    )

    def __repr__(self) -> str:
        return f'<Message {self.subject}>'

    def mark_as_read(self) -> None:
        """Mark the message as read and save to database."""
        if not self.is_read:
            self.is_read = True
            db.session.commit()

    def to_dict(self) -> Dict[str, Any]:
        """Serialize message to dictionary."""
        return {
            'id': self.id,
            'sender_id': self.sender_id,
            'sender_name': self.sender.name,
            'recipient_id': self.recipient_id,
            'recipient_name': self.recipient.name,
            'subject': self.subject,
            'body': self.body,
            'is_read': self.is_read,
            'created_at': self.created_at.isoformat(),
        }


class Activity(db.Model):
    """Activity log model for tracking user actions."""
    __tablename__ = 'activities'

    # Severity levels
    SEVERITY_INFO = 'info'
    SEVERITY_WARNING = 'warning'
    SEVERITY_ERROR = 'error'

    SEVERITY_CHOICES = [
        (SEVERITY_INFO, 'Info'),
        (SEVERITY_WARNING, 'Warning'),
        (SEVERITY_ERROR, 'Error')
    ]

    # Activity categories
    CATEGORY_AUTH = 'auth'
    CATEGORY_USER = 'user'
    CATEGORY_ADMIN = 'admin'
    CATEGORY_SYSTEM = 'system'
    CATEGORY_DATA = 'data'

    CATEGORY_CHOICES = [
        (CATEGORY_AUTH, 'Authentication'),
        (CATEGORY_USER, 'User Activity'),
        (CATEGORY_ADMIN, 'Administration'),
        (CATEGORY_SYSTEM, 'System'),
        (CATEGORY_DATA, 'Data Management')
    ]

    # CRUD Methods
    METHOD_CREATE = 'create'
    METHOD_READ = 'read'
    METHOD_UPDATE = 'update'
    METHOD_DELETE = 'delete'

    METHOD_CHOICES = [
        (METHOD_CREATE, 'Create'),
        (METHOD_READ, 'Read'),
        (METHOD_UPDATE, 'Update'),
        (METHOD_DELETE, 'Delete')
    ]

    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id', name='fk_activity_user_id_cascade', ondelete='CASCADE'), nullable=False)
    action = db.Column(db.String(255), nullable=False)
    entity_type = db.Column(db.String(50), nullable=True)  # User, BusinessUnit, etc.
    entity_id = db.Column(db.Integer, nullable=True)
    details = db.Column(db.Text, nullable=True)
    ip_address = db.Column(db.String(50), nullable=True)
    user_agent = db.Column(db.String(255), nullable=True)
    url = db.Column(db.String(500), nullable=True)  # URL where the activity occurred
    severity = db.Column(db.String(20), default=SEVERITY_INFO)
    category = db.Column(db.String(20), nullable=True)
    method = db.Column(db.String(20), nullable=True)  # CRUD method: create, read, update, delete
    old_values = db.Column(db.Text, nullable=True)  # JSON string of old values
    new_values = db.Column(db.Text, nullable=True)  # JSON string of new values
    created_at = db.Column(db.DateTime, default=lambda: datetime.now(PH_TZ), index=True)

    __table_args__ = (
        db.Index('idx_activity_user_created', user_id, created_at),
        db.Index('idx_activity_entity', entity_type, entity_id),
        db.Index('idx_activity_category', category),
        db.Index('idx_activity_severity', severity),
    )

    def __repr__(self) -> str:
        return f'<Activity {self.action}>'

    @classmethod
    def log(cls, user_id: int, action: str, entity_type: Optional[str] = None,
            entity_id: Optional[int] = None, details: Optional[str] = None,
            ip_address: Optional[str] = None, user_agent: Optional[str] = None, url: Optional[str] = None,
            severity: Optional[str] = None, category: Optional[str] = None, method: Optional[str] = None,
            old_values: Optional[Dict[str, Any]] = None, new_values: Optional[Dict[str, Any]] = None) -> 'Activity':
        """Create and save a new activity log entry."""
        # Determine severity if not provided
        actual_severity = severity
        if not actual_severity:
            # Try to infer severity from action description
            action_lower = action.lower()
            if any(word in action_lower for word in ['error', 'fail', 'invalid', 'denied', 'reject', 'exception']):
                actual_severity = cls.SEVERITY_ERROR
            elif any(word in action_lower for word in ['warn', 'caution', 'alert', 'attempt', 'suspicious']):
                actual_severity = cls.SEVERITY_WARNING
            else:
                actual_severity = cls.SEVERITY_INFO

        # Determine category if not provided
        actual_category = category
        if not actual_category:
            # Try to infer category from action and entity type
            action_lower = action.lower()

            # Authentication related activities
            if any(word in action_lower for word in ['login', 'logout', 'password', 'auth', 'credential', 'session']):
                actual_category = cls.CATEGORY_AUTH
            # Admin activities
            elif any(word in action_lower for word in ['admin', 'configuration', 'setting', 'system']):
                actual_category = cls.CATEGORY_ADMIN
            # Data management activities
            elif any(word in action_lower for word in ['create', 'update', 'delete', 'edit', 'import', 'export']):
                actual_category = cls.CATEGORY_DATA
            # User activities
            elif any(word in action_lower for word in ['view', 'access', 'profile', 'dashboard']):
                actual_category = cls.CATEGORY_USER
            # Default to system
            else:
                actual_category = cls.CATEGORY_SYSTEM

        # Determine method if not provided
        actual_method = method
        if not actual_method:
            # Try to infer method from action description
            action_lower = action.lower()
            if 'create' in action_lower or 'add' in action_lower or 'new' in action_lower:
                actual_method = cls.METHOD_CREATE
            elif 'update' in action_lower or 'edit' in action_lower or 'modify' in action_lower or 'change' in action_lower:
                actual_method = cls.METHOD_UPDATE
            elif 'delete' in action_lower or 'remove' in action_lower:
                actual_method = cls.METHOD_DELETE
            elif 'view' in action_lower or 'read' in action_lower or 'get' in action_lower or 'list' in action_lower or 'search' in action_lower:
                actual_method = cls.METHOD_READ

        # Convert old and new values to JSON strings if provided
        old_values_json = json.dumps(old_values) if old_values else None
        new_values_json = json.dumps(new_values) if new_values else None

        # Create activity with basic fields
        activity_data = {
            'user_id': user_id,
            'action': action,
            'entity_type': entity_type,
            'entity_id': entity_id,
            'details': details,
            'ip_address': ip_address,
            'user_agent': user_agent,
            'url': url,
            'severity': actual_severity,
            'category': actual_category,
            'method': actual_method
        }

        # Try to add old_values and new_values if the columns exist
        try:
            activity_data['old_values'] = old_values_json
            activity_data['new_values'] = new_values_json
            activity = cls(**activity_data)
        except Exception as e:
            # If the columns don't exist yet, create without them
            if 'no column named' in str(e).lower() and ('old_values' in str(e).lower() or 'new_values' in str(e).lower()):
                # Remove the problematic fields
                if 'old_values' in activity_data:
                    del activity_data['old_values']
                if 'new_values' in activity_data:
                    del activity_data['new_values']
                activity = cls(**activity_data)

                # If old/new values were provided, add them to details instead
                if old_values or new_values:
                    additional_details = []
                    if old_values:
                        additional_details.append(f"Old values: {json.dumps(old_values)}")
                    if new_values:
                        additional_details.append(f"New values: {json.dumps(new_values)}")

                    if additional_details and activity.details:
                        activity.details += " | " + " | ".join(additional_details)
                    elif additional_details:
                        activity.details = " | ".join(additional_details)
            else:
                # Re-raise if it's a different error
                raise
        db.session.add(activity)
        db.session.commit()
        return activity

    def get_entity_url(self) -> Optional[str]:
        """Get URL to the related entity if applicable."""
        from flask import url_for

        if not self.entity_type or not self.entity_id:
            return None

        try:
            if self.entity_type == 'User':
                return url_for('admin.get_user', user_id=self.entity_id)
            elif self.entity_type == 'BusinessUnit':
                return url_for('admin.get_business_unit', unit_id=self.entity_id)
            elif self.entity_type == 'BusinessSegment':
                return url_for('admin.get_business_segment', segment_id=self.entity_id)
            elif self.entity_type == 'EmployeeDetail':
                return url_for('admin.get_employee_detail', detail_id=self.entity_id)
            elif self.entity_type == 'Message':
                # Assuming there's a message view route
                return None
        except:
            return None

        return None

    def get_severity_badge_class(self) -> str:
        """Get the appropriate badge class for the severity level."""
        severity_map = {
            self.SEVERITY_INFO: 'badge-info',
            self.SEVERITY_WARNING: 'badge-warning',
            self.SEVERITY_ERROR: 'badge-destructive',
        }
        return severity_map.get(self.severity, 'badge-secondary')

    def get_category_display(self) -> str:
        """Get a human-readable display for the category."""
        category_map = dict(self.CATEGORY_CHOICES)
        return category_map.get(self.category, self.category.title() if self.category else 'Uncategorized')

    def get_method_display(self) -> str:
        """Get a human-readable display for the method."""
        method_map = dict(self.METHOD_CHOICES)
        return method_map.get(self.method, self.method.title() if self.method else 'Unknown')

    def to_dict(self) -> Dict[str, Any]:
        """Serialize activity to dictionary."""
        # Parse old and new values from JSON if they exist
        old_values = None
        new_values = None
        has_changes = False

        # Try to access old_values and new_values attributes
        try:
            if hasattr(self, 'old_values') and self.old_values:
                old_values = json.loads(self.old_values)
            if hasattr(self, 'new_values') and self.new_values:
                new_values = json.loads(self.new_values)
            has_changes = bool(old_values and new_values)
        except Exception:
            # If there's an error (e.g., columns don't exist), just continue without them
            pass

        # Check if old/new values are in details as a fallback
        if not has_changes and self.details:
            # Try to extract from details
            if 'Old values:' in self.details and 'New values:' in self.details:
                try:
                    # Extract old values
                    old_start = self.details.find('Old values:') + len('Old values:')
                    old_end = self.details.find('New values:') if 'New values:' in self.details else len(self.details)
                    old_json = self.details[old_start:old_end].strip()
                    if old_json.endswith('|'):
                        old_json = old_json[:-1].strip()
                    old_values = json.loads(old_json)

                    # Extract new values
                    new_start = self.details.find('New values:') + len('New values:')
                    new_json = self.details[new_start:].strip()
                    if '|' in new_json:
                        new_json = new_json.split('|')[0].strip()
                    new_values = json.loads(new_json)

                    has_changes = bool(old_values and new_values)
                except Exception:
                    # If parsing fails, just continue without them
                    pass

        return {
            'id': self.id,
            'user_id': self.user_id,
            'user_name': self.user.name if self.user else 'Unknown',
            'action': self.action,
            'entity_type': self.entity_type,
            'entity_id': self.entity_id,
            'details': self.details,
            'ip_address': self.ip_address,
            'user_agent': self.user_agent,
            'url': self.url,
            'severity': self.severity,
            'category': self.category,
            'method': self.method,
            'old_values': old_values,
            'new_values': new_values,
            'created_at': self.created_at.isoformat(),
            'entity_url': self.get_entity_url(),
            'severity_badge_class': self.get_severity_badge_class(),
            'category_display': self.get_category_display(),
            'method_display': self.get_method_display(),
            'has_changes': has_changes
        }


class PasswordReset(db.Model):
    """Password reset token model for recovery workflow."""
    __tablename__ = 'password_resets'

    id = db.Column(db.Integer, primary_key=True)
    email = db.Column(db.String(120), nullable=False, index=True)
    token = db.Column(db.String(100), unique=True, nullable=False, default=lambda: str(uuid.uuid4()))
    created_at = db.Column(db.DateTime, default=lambda: datetime.now(PH_TZ))
    expires_at = db.Column(db.DateTime, nullable=False)
    is_used = db.Column(db.Boolean, default=False)

    def __repr__(self) -> str:
        return f'<PasswordReset {self.email}>'

    @property
    def is_expired(self) -> bool:
        """Check if the token has expired."""
        return datetime.now(PH_TZ) > self.expires_at


class Setting(db.Model):
    """System settings model for application configuration."""
    __tablename__ = 'settings'

    # Setting types
    TYPE_STRING = 'string'
    TYPE_INTEGER = 'integer'
    TYPE_FLOAT = 'float'
    TYPE_BOOLEAN = 'boolean'
    TYPE_JSON = 'json'

    TYPE_CHOICES = [
        (TYPE_STRING, 'String'),
        (TYPE_INTEGER, 'Integer'),
        (TYPE_FLOAT, 'Float'),
        (TYPE_BOOLEAN, 'Boolean'),
        (TYPE_JSON, 'JSON')
    ]

    id = db.Column(db.Integer, primary_key=True)
    key = db.Column(db.String(100), unique=True, nullable=False, index=True)
    value = db.Column(db.Text, nullable=True)
    type = db.Column(db.String(20), nullable=False, default=TYPE_STRING)
    description = db.Column(db.String(255), nullable=True)
    is_public = db.Column(db.Boolean, default=False)  # Whether this setting is accessible to non-admin users
    created_at = db.Column(db.DateTime, default=lambda: datetime.now(PH_TZ))
    updated_at = db.Column(db.DateTime, default=lambda: datetime.now(PH_TZ), onupdate=lambda: datetime.now(PH_TZ))

    def __repr__(self) -> str:
        return f'<Setting {self.key}={self.value}>'

    @property
    def typed_value(self):
        """Return the value converted to its appropriate type."""
        if self.value is None:
            return None

        if self.type == self.TYPE_STRING:
            return self.value
        elif self.type == self.TYPE_INTEGER:
            return int(self.value)
        elif self.type == self.TYPE_FLOAT:
            return float(self.value)
        elif self.type == self.TYPE_BOOLEAN:
            # Check if the value is one of the true values
            # For checkboxes, we store 'on' when checked and 'false' when unchecked
            return self.value.lower() in ('true', 'yes', '1', 'on')
        elif self.type == self.TYPE_JSON:
            try:
                return json.loads(self.value)
            except json.JSONDecodeError:
                return None
        return self.value

    @classmethod
    def create_token(cls, email: str, expiration: int = 3600) -> 'PasswordReset':
        """
        Create a password reset token.

        Args:
            email: User's email address
            expiration: Token expiration time in seconds (default: 1 hour)

        Returns:
            PasswordReset: The created token object
        """
        # First, invalidate any existing tokens
        existing_tokens = cls.query.filter_by(email=email, is_used=False).all()
        for token in existing_tokens:
            token.is_used = True

        # Create a new token
        token = cls(
            email=email,
            expires_at=datetime.now(PH_TZ) + timedelta(seconds=expiration)
        )

        db.session.add(token)
        db.session.commit()

        return token

    @classmethod
    def validate_token(cls, token: str) -> Optional['PasswordReset']:
        """Validate a token and return it if valid."""
        token_obj = cls.query.filter_by(token=token, is_used=False).first()
        if not token_obj or token_obj.is_expired:
            return None
        return token_obj

    @classmethod
    def cleanup_expired_tokens(cls) -> int:
        """Delete all expired and used tokens to keep the database clean."""
        expired_tokens = cls.query.filter(
            db.or_(
                cls.expires_at < datetime.now(PH_TZ),
                cls.is_used == True
            )
        ).all()

        count = len(expired_tokens)
        for token in expired_tokens:
            db.session.delete(token)

        db.session.commit()
        return count

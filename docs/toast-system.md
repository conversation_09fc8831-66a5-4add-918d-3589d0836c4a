# Toast Notification System

This document explains how to use the global toast notification system in the application.

## Overview

The toast notification system provides a consistent way to show toast notifications across the application. It's implemented as a global JavaScript object called `ToastSystem` that can be accessed from any page.

## Features

- Consistent appearance across the application
- Four types of notifications: success, error, info, and warning
- Automatically disappears after a configurable duration
- Can be dismissed by clicking the close button
- Only shows one toast at a time to avoid cluttering the UI
- Responsive design that works on all screen sizes

## Usage

### Basic Usage

```javascript
// Show a success toast
ToastSystem.success('Operation completed successfully');

// Show an error toast
ToastSystem.error('An error occurred');

// Show an info toast
ToastSystem.info('Here is some information');

// Show a warning toast
ToastSystem.warning('Warning: This action cannot be undone');
```

### Advanced Usage

You can customize the duration of the toast:

```javascript
// Show a success toast that stays for 5 seconds (5000ms)
ToastSystem.success('Operation completed successfully', 5000);

// Show an error toast that stays for 10 seconds
ToastSystem.error('An error occurred', 10000);
```

You can also use the generic `show` method for more control:

```javascript
// Parameters: message, type, duration
ToastSystem.show('Custom message', 'success', 3000);
```

## Integration with URL Parameters

A common pattern is to show toast notifications after redirects. This can be done by:

1. Adding status and action parameters to the redirect URL
2. Checking for these parameters on page load and showing the appropriate toast

Example:

```javascript
// In your controller/route
return redirect(url_for('some.route', status='success', action='created'));

// In your JavaScript
document.addEventListener('DOMContentLoaded', function() {
  const urlParams = new URLSearchParams(window.location.search);
  const action = urlParams.get('action');
  const status = urlParams.get('status');
  
  if (status === 'success') {
    if (action === 'created') {
      ToastSystem.success('Item created successfully');
    } else if (action === 'updated') {
      ToastSystem.success('Item updated successfully');
    }
    
    // Clean the URL without reloading the page
    const newUrl = window.location.pathname;
    window.history.replaceState({}, document.title, newUrl);
  }
});
```

## Styling

The toast notifications use the application's theme colors and adapt to light/dark mode automatically. The styling is consistent with the shadcn UI design system.

## Implementation Details

The toast system is implemented in `app/static/js/toast.js` and is included in the base template. It automatically adds the necessary CSS to the page when loaded.

The system ensures only one toast is shown at a time by removing any existing toasts before showing a new one.

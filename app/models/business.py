"""
Business models for organizational structure.
"""

from datetime import datetime
from typing import Dict, Any, List

from app import db
from app.models import PH_TZ


class BusinessUnit(db.Model):
    """Business unit model for organizational structure."""
    __tablename__ = 'business_units'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    code = db.Column(db.String(20), unique=True, nullable=False, index=True)
    description = db.Column(db.Text, nullable=True)
    manager_id = db.Column(db.Integer, db.ForeignKey('users.id', name='fk_business_unit_manager_id'), nullable=True)
    is_active = db.Column(db.<PERSON>, default=True)
    created_at = db.Column(db.DateTime, default=lambda: datetime.now(PH_TZ))
    updated_at = db.Column(db.DateTime, default=lambda: datetime.now(PH_TZ), onupdate=lambda: datetime.now(PH_TZ))

    # Relationships
    segments = db.relationship('BusinessSegment', backref='business_unit', lazy='dynamic',
                              cascade='all, delete-orphan')

    def __repr__(self) -> str:
        return f'<BusinessUnit {self.name}>'

    def get_active_segments(self) -> List['BusinessSegment']:
        """Get all active segments in this business unit."""
        return self.segments.filter_by(is_active=True).all()

    def get_employee_count(self) -> int:
        """Get the count of employees in this business unit."""
        from app.models.employee import EmployeeDetail
        return EmployeeDetail.query.filter_by(business_unit_id=self.id).count()

    def to_dict(self, include_segments: bool = False) -> Dict[str, Any]:
        """Serialize business unit to dictionary."""
        data = {
            'id': self.id,
            'name': self.name,
            'code': self.code,
            'description': self.description,
            'is_active': self.is_active,
            'manager_id': self.manager_id,
            'manager_name': self.manager.name if self.manager else None,
            'employee_count': self.get_employee_count(),
        }

        if include_segments:
            data['segments'] = [segment.to_dict() for segment in self.get_active_segments()]

        return data


class BusinessSegment(db.Model):
    """Business segment model, linked to business units."""
    __tablename__ = 'business_segments'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    code = db.Column(db.String(20), unique=True, nullable=False, index=True)
    description = db.Column(db.Text, nullable=True)
    business_unit_id = db.Column(db.Integer, db.ForeignKey('business_units.id', name='fk_business_segment_business_unit_id'), nullable=False)
    manager_id = db.Column(db.Integer, db.ForeignKey('users.id', name='fk_business_segment_manager_id'), nullable=True)
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=lambda: datetime.now(PH_TZ))
    updated_at = db.Column(db.DateTime, default=lambda: datetime.now(PH_TZ), onupdate=lambda: datetime.now(PH_TZ))

    def __repr__(self) -> str:
        return f'<BusinessSegment {self.name}>'

    def get_employee_count(self) -> int:
        """Get the count of employees in this business segment."""
        from app.models.employee import EmployeeDetail
        return EmployeeDetail.query.filter_by(business_segment_id=self.id).count()

    def to_dict(self) -> Dict[str, Any]:
        """Serialize business segment to dictionary."""
        return {
            'id': self.id,
            'name': self.name,
            'code': self.code,
            'description': self.description,
            'business_unit_id': self.business_unit_id,
            'business_unit_name': self.business_unit.name if self.business_unit else None,
            'manager_id': self.manager_id,
            'manager_name': self.manager.name if self.manager else None,
            'is_active': self.is_active,
            'employee_count': self.get_employee_count(),
        }

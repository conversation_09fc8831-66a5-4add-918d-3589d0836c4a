{% extends "base.html" %}

{% from "components/page_header.html" import page_header %}
{% from "components/table_header.html" import table_header %}
{% from "components/simple_table.html" import simple_table %}
{% from "components/action_buttons.html" import action_buttons %}

{% block title %}Business Units{% endblock %}

{% block header %}Business Units{% endblock %}

{% block content %}
<div class="space-y-6">
  {{ page_header(
    title="Business Units",
    button_text="Add Business Unit",
    button_icon="plus",
    button_action="drawerManager.openForm('business_unit')",
    description="Manage your organization's business units"
  ) }}

  <div class="card">
    {{ table_header(
      title="Business Unit List"
    ) }}

    {% call simple_table(
      headers=[
        {'label': 'Code'},
        {'label': 'Name'},
        {'label': 'Manager'},
        {'label': 'Employees'},
        {'label': 'Status'},
        {'label': 'Actions', 'align': 'right'}
      ],
      items=business_units,
      empty_icon="building",
      empty_title="No business units found",
      empty_description="Create your first business unit to get started",
      empty_button_text="Add Business Unit",
      empty_button_icon="plus",
      empty_button_action="drawerManager.openForm('business_unit')"
    ) %}
      {% for unit in business_units %}
      <tr class="border-b border-border hover:bg-muted/30">
        <td class="px-4 py-3 text-sm">{{ unit.code }}</td>
        <td class="px-4 py-3 text-sm">
          <a href="{{ url_for('admin.view_business_unit', unit_id=unit.id) }}" class="hover:text-primary hover:underline">{{ unit.name }}</a>
        </td>
        <td class="px-4 py-3 text-sm">{{ unit.manager.name if unit.manager else 'Not assigned' }}</td>
        <td class="px-4 py-3 text-sm">{{ unit.employee_details|length }}</td>
        <td class="px-4 py-3 text-sm">
          {% if unit.is_active %}
            <span class="badge badge-success">Active</span>
          {% else %}
            <span class="badge badge-secondary">Inactive</span>
          {% endif %}
        </td>
        <td class="px-4 py-3 text-sm text-right">
          {{ action_buttons([
            {'type': 'link', 'url': url_for('admin.view_business_unit', unit_id=unit.id), 'icon': 'eye', 'title': 'View'},
            {'type': 'button', 'action': 'drawerManager.openForm(\'business_unit\', ' ~ unit.id ~ ')', 'icon': 'edit', 'title': 'Edit'},
            {'type': 'button', 'action': 'confirmDeleteBusinessUnit(' ~ unit.id ~ ', \'' ~ unit.name ~ '\')', 'icon': 'trash-2', 'variant': 'text-destructive', 'title': 'Delete'}
          ]) }}
        </td>
      </tr>
      {% endfor %}
    {% endcall %}

    {% include "partials/pagination.html" %}
  </div>
</div>
{% endblock %}

{% block scripts %}
<script>
  // Lucide icons are now initialized automatically
  document.addEventListener('DOMContentLoaded', function() {
      // Clean URL parameters if they exist (for backward compatibility)
      const urlParams = new URLSearchParams(window.location.search);
      if (urlParams.has('status')) {
          const newUrl = window.location.pathname;
          window.history.replaceState({}, document.title, newUrl);
      }
  });

  // Confirm delete business unit
  function confirmDeleteBusinessUnit(unitId, unitName) {
      showAlertDialog({
          title: 'Delete Business Unit',
          description: `Are you sure you want to delete ${unitName}? This action cannot be undone.`,
          variant: 'destructive',
          onConfirm: () => {
              // Submit form to delete endpoint
              const form = document.createElement('form');
              form.method = 'POST';
              form.action = `{{ url_for('admin.delete_business_unit', unit_id=0) }}`.replace('0', unitId);

              const csrfToken = document.createElement('input');
              csrfToken.type = 'hidden';
              csrfToken.name = 'csrf_token';
              csrfToken.value = '{{ csrf_token() }}';

              form.appendChild(csrfToken);
              document.body.appendChild(form);
              form.submit();
          }
      });
  }
</script>
{% endblock %}

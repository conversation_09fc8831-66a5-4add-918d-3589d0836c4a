/**
 * Drawer Component Module
 *
 * This module provides functions for creating and managing drawer components.
 */

import { createElement, trapFocus } from './dom.js';

/**
 * Initialize the drawer container if it doesn't exist
 * @returns {HTMLElement} - The drawer container element
 */
function initDrawerContainer() {
  let container = document.getElementById('drawer-container');
  
  if (!container) {
    container = createElement('div', document.body, {
      attributes: {
        id: 'drawer-container',
        'aria-live': 'polite',
        'aria-atomic': 'true'
      }
    });
  }
  
  return container;
}

/**
 * Open a drawer with the specified content and position
 * @param {string} content - HTML content to display in the drawer
 * @param {string} position - Position of the drawer ('right', 'left', 'top', 'bottom')
 * @returns {Function} A function to close the drawer
 */
function openDrawer(content, position = 'right') {
  const drawerContainer = document.getElementById('drawer-container') || initDrawerContainer();

  // Create overlay
  const overlay = createElement('div', drawerContainer, {
    className: 'drawer-overlay',
    attributes: {
      'data-state': 'open'
    },
    styles: {
      position: 'fixed',
      inset: '0',
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
      backdropFilter: 'blur(4px)',
      zIndex: '9998',
      opacity: '1'
    }
  });

  // Create drawer content
  const drawer = createElement('div', drawerContainer, {
    className: `drawer-content drawer-${position}`,
    attributes: {
      'data-state': 'open'
    }
  });

  // Handle dark mode first to set proper background color
  if (document.documentElement.classList.contains('dark')) {
    drawer.style.backgroundColor = '#111827'; // Dark background color
    drawer.style.color = '#fff';
    drawer.style.borderColor = '#1f2937'; // Darker border for dark mode
  } else {
    drawer.style.backgroundColor = '#ffffff'; // Light background color
    drawer.style.color = '#111827';
    drawer.style.borderColor = '#e5e7eb'; // Light border for light mode
  }

  // Force drawer styles
  drawer.style.position = 'fixed';
  drawer.style.zIndex = '9999';
  drawer.style.padding = '1.5rem';
  drawer.style.boxShadow = document.documentElement.classList.contains('dark') ?
    '0 0 15px rgba(0, 0, 0, 0.5)' : // Darker shadow for dark mode
    '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)'; // Light shadow
  drawer.style.overflow = 'auto';
  drawer.style.display = 'block';

  // Apply position-specific styles
  if (position === 'right') {
    drawer.style.top = '0';
    drawer.style.right = '0';
    drawer.style.bottom = '0';
    drawer.style.width = '30rem';
    drawer.style.maxWidth = '100vw';
    drawer.style.transform = 'translateX(0)';
    drawer.style.borderLeft = `1px solid ${drawer.style.borderColor}`;
  } else if (position === 'left') {
    drawer.style.top = '0';
    drawer.style.left = '0';
    drawer.style.bottom = '0';
    drawer.style.width = '30rem';
    drawer.style.maxWidth = '100vw';
    drawer.style.transform = 'translateX(0)';
    drawer.style.borderRight = `1px solid ${drawer.style.borderColor}`;
  } else if (position === 'top') {
    drawer.style.top = '0';
    drawer.style.left = '0';
    drawer.style.right = '0';
    drawer.style.maxHeight = '80vh';
    drawer.style.transform = 'translateY(0)';
    drawer.style.borderBottom = `1px solid ${drawer.style.borderColor}`;
  } else if (position === 'bottom') {
    drawer.style.bottom = '0';
    drawer.style.left = '0';
    drawer.style.right = '0';
    drawer.style.maxHeight = '80vh';
    drawer.style.transform = 'translateY(0)';
    drawer.style.borderTop = `1px solid ${drawer.style.borderColor}`;
  }

  drawer.innerHTML = content;

  // Add close button
  const closeButton = createElement('button', null, {
    className: 'absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none',
    styles: {
      position: 'absolute',
      right: '1rem',
      top: '1rem',
      cursor: 'pointer'
    },
    innerHTML: '<i data-lucide="x" class="h-4 w-4"></i><span class="sr-only">Close</span>'
  });
  
  drawer.prepend(closeButton);

  // Initialize icons in the drawer
  if (typeof lucide !== 'undefined' && lucide.createIcons) {
    lucide.createIcons({
      root: drawer
    });
  }

  // Make sure body doesn't scroll when drawer is open
  document.body.style.overflow = 'hidden';

  // Set up focus trapping
  const removeFocusTrap = trapFocus(drawer, closeDrawer);

  // Close drawer function
  function closeDrawer() {
    if (position === 'right') {
      drawer.style.transform = 'translateX(100%)';
    } else if (position === 'left') {
      drawer.style.transform = 'translateX(-100%)';
    } else if (position === 'top') {
      drawer.style.transform = 'translateY(-100%)';
    } else if (position === 'bottom') {
      drawer.style.transform = 'translateY(100%)';
    }

    overlay.style.opacity = '0';

    // Remove focus trap
    removeFocusTrap();

    // Remove elements after animation completes
    setTimeout(() => {
      if (drawerContainer.contains(overlay)) {
        drawerContainer.removeChild(overlay);
      }
      if (drawerContainer.contains(drawer)) {
        drawerContainer.removeChild(drawer);
      }
      // Restore body scrolling
      document.body.style.overflow = '';
    }, 300);
  }

  overlay.addEventListener('click', closeDrawer);
  closeButton.addEventListener('click', closeDrawer);

  // Return the close function so it can be called programmatically
  return closeDrawer;
}

// Export public API
export {
  initDrawerContainer,
  openDrawer
};

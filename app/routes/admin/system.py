"""
Admin routes for system maintenance and management.
"""

import os
from flask import render_template, current_app
from flask_login import login_required

from app.models import Activity
from app.routes.admin import admin_bp
from app.utils.decorators import admin_required, log_activity


@admin_bp.route('/maintenance')
@login_required
@admin_required
@log_activity('Viewed system maintenance page', entity_type='System', category=Activity.CATEGORY_ADMIN, method=Activity.METHOD_READ)
def maintenance():
    """Display system maintenance page."""
    return render_template('admin/maintenance.html', active_page='maintenance')


@admin_bp.route('/icons')
@login_required
@admin_required
@log_activity('Viewed icon repository', entity_type='System', category=Activity.CATEGORY_ADMIN, method=Activity.METHOD_READ)
def icon_repository():
    """Display the icon repository page showing all available icons."""

    # Read icon definitions from the JavaScript file
    static_folder = current_app.static_folder or 'app/static'
    icon_definitions_path = os.path.join(static_folder, 'js', 'icons', 'icon-definitions.js')
    critical_icons_path = os.path.join(static_folder, 'js', 'icons', 'critical-icons.js')

    all_icons = []
    critical_icons = []

    try:
        # Read all icon definitions
        with open(icon_definitions_path, 'r', encoding='utf-8') as f:
            content = f.read()
            # Extract icon names from the JavaScript file
            # Look for name: "icon-name" patterns
            import re
            icon_matches = re.findall(r'name:\s*[\'"]([^\'"]+)[\'"]', content)
            all_icons = list(set(icon_matches))  # Remove duplicates

        # Read critical icons list
        with open(critical_icons_path, 'r', encoding='utf-8') as f:
            content = f.read()
            # Extract critical icon names
            critical_matches = re.findall(r'[\'"]([^\'"]+)[\'"]', content)
            critical_icons = [icon for icon in critical_matches if icon not in ['criticalIcons']]

    except Exception as e:
        current_app.logger.error(f"Error reading icon files: {str(e)}")
        # Fallback to a basic list if files can't be read
        all_icons = ['home', 'users', 'settings', 'search', 'plus', 'edit', 'trash', 'check', 'x']
        critical_icons = ['home', 'users', 'settings']

    # Sort icons alphabetically
    all_icons.sort()
    critical_icons.sort()

    # Create icon data structure
    icon_data = {
        'all_icons': all_icons,
        'critical_icons': critical_icons,
        'total_icons': len(all_icons),
        'critical_count': len(critical_icons),
        'non_critical_count': len(all_icons) - len(critical_icons)
    }

    return render_template('admin/icon_repository.html',
                         active_page='icons',
                         icon_data=icon_data)

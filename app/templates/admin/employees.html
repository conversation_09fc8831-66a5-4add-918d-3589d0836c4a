{% extends "base.html" %}

{% block title %}Employees{% endblock %}

{% block header %}Employees{% endblock %}

{% block content %}
<div class="card">
    <div class="p-6 flex justify-between items-center border-b border-border">
        <div>
            <h2 class="text-lg font-semibold">Employee Management</h2>
            <p class="text-sm text-muted-foreground">Manage your employees and their permissions</p>
        </div>
        <button onclick="openEmployeeDrawer()" class="btn btn-primary btn-md flex items-center">
            <i data-lucide="plus" class="w-4 h-4 mr-2"></i>
            <span>Add Employee</span>
        </button>
    </div>

    <div class="overflow-x-auto">
        <table class="w-full text-left">
            <thead class="bg-muted text-muted-foreground">
                <tr>
                    <th class="px-6 py-3 text-xs font-medium uppercase tracking-wider">ID</th>
                    <th class="px-6 py-3 text-xs font-medium uppercase tracking-wider">Name</th>
                    <th class="px-6 py-3 text-xs font-medium uppercase tracking-wider">Email</th>
                    <th class="px-6 py-3 text-xs font-medium uppercase tracking-wider">Role</th>
                    <th class="px-6 py-3 text-xs font-medium uppercase tracking-wider">Business Unit</th>
                    <th class="px-6 py-3 text-xs font-medium uppercase tracking-wider">Status</th>
                    <th class="px-6 py-3 text-xs font-medium uppercase tracking-wider">Actions</th>
                </tr>
            </thead>
            <tbody class="divide-y divide-border">
                {% if employees %}
                    {% for employee in employees %}
                    <tr class="hover:bg-muted/50">
                        <td class="px-6 py-4 whitespace-nowrap">{{ employee.id }}</td>
                        <td class="px-6 py-4 whitespace-nowrap">{{ employee.name }}</td>
                        <td class="px-6 py-4 whitespace-nowrap">{{ employee.email }}</td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="px-2 py-1 text-xs rounded-full
                                {% if employee.role == 'Admin' %}
                                    bg-primary/10 text-primary
                                {% elif employee.role == 'Manager' %}
                                    bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400
                                {% else %}
                                    bg-muted text-muted-foreground
                                {% endif %}
                            ">
                                {{ employee.role }}
                            </span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">{{ employee.business_unit.name if employee.business_unit else 'Not assigned' }}</td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="px-2 py-1 text-xs rounded-full
                                {% if employee.is_active %}
                                    bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400
                                {% else %}
                                    bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400
                                {% endif %}
                            ">
                                {{ 'Active' if employee.is_active else 'Inactive' }}
                            </span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="flex space-x-2">
                                <button onclick="openEditEmployeeDrawer({{ employee.id }})" class="p-1 text-primary hover:text-primary/80">
                                    <i data-lucide="edit" class="w-4 h-4"></i>
                                </button>
                                <button onclick="confirmDeleteEmployee({{ employee.id }}, '{{ employee.name }}')" class="p-1 text-destructive hover:text-destructive/80">
                                    <i data-lucide="trash-2" class="w-4 h-4"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                {% else %}
                    <tr>
                        <td colspan="7" class="px-6 py-4 text-center text-muted-foreground">
                            No employees found
                        </td>
                    </tr>
                {% endif %}
            </tbody>
        </table>
    </div>

    <div class="px-6 py-3 flex items-center justify-between border-t border-border">
        <div class="text-sm text-muted-foreground">
            Showing <span class="font-medium">1</span> to <span class="font-medium">{{ employees|length }}</span> of <span class="font-medium">{{ employees|length }}</span> results
        </div>
        <div class="flex space-x-2">
            <button class="btn btn-outline btn-sm disabled:opacity-50" disabled>
              Previous
            </button>
            <button class="btn btn-outline btn-sm disabled:opacity-50" disabled>
                Next
            </button>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    // Initialize Lucide icons
    document.addEventListener('DOMContentLoaded', function() {
        lucide.createIcons();
    });

    // Open drawer to add new employee
    function openEmployeeDrawer() {
        const content = `
            <h2 class="text-lg font-semibold mb-4">Add New Employee</h2>
            <form action="{{ url_for('admin.create_employee') }}" method="POST" class="space-y-4">
                <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label for="name" class="block text-sm font-medium mb-1">Full Name <span class="text-destructive">*</span></label>
                        <input type="text" id="name" name="name" class="input" required>
                    </div>

                    <div>
                        <label for="email" class="block text-sm font-medium mb-1">Email <span class="text-destructive">*</span></label>
                        <input type="email" id="email" name="email" class="input" required>
                    </div>

                    <div>
                        <label for="password" class="block text-sm font-medium mb-1">Password <span class="text-destructive">*</span></label>
                        <input type="password" id="password" name="password" class="input" required>
                    </div>

                    <div>
                        <label for="role" class="block text-sm font-medium mb-1">Role <span class="text-destructive">*</span></label>
                        <select id="role" name="role" class="input" required>
                            <option value="User">User</option>
                            <option value="Manager">Manager</option>
                            {% if current_user.is_admin %}
                            <option value="Admin">Admin</option>
                            {% endif %}
                        </select>
                    </div>

                    <div>
                        <label for="position" class="block text-sm font-medium mb-1">Position</label>
                        <input type="text" id="position" name="position" class="input">
                    </div>

                    <div>
                        <label for="phone" class="block text-sm font-medium mb-1">Phone</label>
                        <input type="tel" id="phone" name="phone" class="input">
                    </div>

                    <div>
                        <label for="business_unit_id" class="block text-sm font-medium mb-1">Business Unit</label>
                        <select id="business_unit_id" name="business_unit_id" class="input">
                            <option value="">Select Business Unit</option>
                            {% for unit in business_units %}
                            <option value="{{ unit.id }}">{{ unit.name }}</option>
                            {% endfor %}
                        </select>
                    </div>

                    <div>
                        <label for="business_segment_id" class="block text-sm font-medium mb-1">Business Segment</label>
                        <select id="business_segment_id" name="business_segment_id" class="input">
                            <option value="">Select Business Segment</option>
                            {% for segment in business_segments %}
                            <option value="{{ segment.id }}">{{ segment.name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                </div>

                <div class="flex justify-end space-x-2 pt-4">
                    <button type="submit" class="btn btn-primary">Add Employee</button>
                </div>
            </form>
        `;

        openDrawer(content, 'right');
    }

    // Open drawer to edit employee
    function openEditEmployeeDrawer(employeeId) {
        // Fetch employee data
        fetch(`{{ url_for('admin.get_employee', employee_id=0) }}`.replace('0', employeeId))
            .then(response => response.json())
            .then(employee => {
                const content = `
                    <h2 class="text-lg font-semibold mb-4">Edit Employee</h2>
                    <form action="{{ url_for('admin.update_employee', employee_id=0) }}`.replace('0', employeeId) + `" method="POST" class="space-y-4">
                        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label for="name" class="block text-sm font-medium mb-1">Full Name <span class="text-destructive">*</span></label>
                                <input type="text" id="name" name="name" value="${employee.name}" class="input" required>
                            </div>

                            <div>
                                <label for="email" class="block text-sm font-medium mb-1">Email <span class="text-destructive">*</span></label>
                                <input type="email" id="email" name="email" value="${employee.email}" class="input" required>
                            </div>

                            <div>
                                <label for="new_password" class="block text-sm font-medium mb-1">New Password</label>
                                <input type="password" id="new_password" name="new_password" class="input">
                                <p class="text-xs text-muted-foreground mt-1">Leave blank to keep current password</p>
                            </div>

                            <div>
                                <label for="role" class="block text-sm font-medium mb-1">Role <span class="text-destructive">*</span></label>
                                <select id="role" name="role" class="input" required>
                                    <option value="User" ${employee.role === 'User' ? 'selected' : ''}>User</option>
                                    <option value="Manager" ${employee.role === 'Manager' ? 'selected' : ''}>Manager</option>
                                    {% if current_user.is_admin %}
                                    <option value="Admin" ${employee.role === 'Admin' ? 'selected' : ''}>Admin</option>
                                    {% endif %}
                                </select>
                            </div>

                            <div>
                                <label for="position" class="block text-sm font-medium mb-1">Position</label>
                                <input type="text" id="position" name="position" value="${employee.position || ''}" class="input">
                            </div>

                            <div>
                                <label for="phone" class="block text-sm font-medium mb-1">Phone</label>
                                <input type="tel" id="phone" name="phone" value="${employee.phone || ''}" class="input">
                            </div>

                            <div>
                                <label for="business_unit_id" class="block text-sm font-medium mb-1">Business Unit</label>
                                <select id="business_unit_id" name="business_unit_id" class="input">
                                    <option value="">Select Business Unit</option>
                                    {% for unit in business_units %}
                                    <option value="{{ unit.id }}" ${employee.business_unit_id === unit.id ? 'selected' : ''}>{{ unit.name }}</option>
                                    {% endfor %}
                                </select>
                            </div>

                            <div>
                                <label for="business_segment_id" class="block text-sm font-medium mb-1">Business Segment</label>
                                <select id="business_segment_id" name="business_segment_id" class="input">
                                    <option value="">Select Business Segment</option>
                                    {% for segment in business_segments %}
                                    <option value="{{ segment.id }}" ${employee.business_segment_id === segment.id ? 'selected' : ''}>{{ segment.name }}</option>
                                    {% endfor %}
                                </select>
                            </div>

                            <div class="md:col-span-2">
                                <div class="flex items-center space-x-2">
                                    <input type="checkbox" id="is_active" name="is_active" class="h-4 w-4 rounded border-input bg-background" ${employee.is_active ? 'checked' : ''}>
                                    <label for="is_active" class="text-sm">Active</label>
                                </div>
                            </div>
                        </div>

                        <div class="flex justify-end space-x-2 pt-4">
                            <button type="submit" class="btn btn-primary">Update Employee</button>
                        </div>
                    </form>
                `;

                openDrawer(content, 'right');
            });
    }

    // Confirm delete employee
    function confirmDeleteEmployee(employeeId, employeeName) {
        showAlertDialog({
            title: 'Delete Employee',
            description: `Are you sure you want to delete ${employeeName}? This action cannot be undone.`,
            variant: 'destructive',
            onConfirm: () => {
                // Submit form to delete endpoint
                const form = document.createElement('form');
                form.method = 'POST';
                form.action = `{{ url_for('admin.delete_employee', employee_id=0) }}`.replace('0', employeeId);

                const csrfToken = document.createElement('input');
                csrfToken.type = 'hidden';
                csrfToken.name = 'csrf_token';
                csrfToken.value = '{{ csrf_token() }}';

                form.appendChild(csrfToken);
                document.body.appendChild(form);
                form.submit();
            }
        });
    }
</script>
{% endblock %}

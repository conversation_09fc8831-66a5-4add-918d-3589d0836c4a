/**
 * Dialog Component Module
 *
 * This module provides functions for creating and managing dialog components.
 */

import { createElement, trapFocus } from './dom.js';

/**
 * Initialize the alert dialog container if it doesn't exist
 * @returns {HTMLElement} - The alert dialog container element
 */
function initAlertDialogContainer() {
  let container = document.getElementById('alert-dialog-container');
  
  if (!container) {
    container = createElement('div', document.body, {
      attributes: {
        id: 'alert-dialog-container',
        'aria-live': 'polite',
        'aria-atomic': 'true'
      }
    });
  }
  
  return container;
}

/**
 * Show an alert dialog with the specified options
 * @param {Object} options - Options for the alert dialog
 * @param {string} options.title - Title of the dialog
 * @param {string} options.description - Description text (optional)
 * @param {string} options.variant - Variant of the dialog ('default' or 'destructive')
 * @param {Function} options.onConfirm - Callback function when confirmed
 * @param {Function} options.onCancel - Callback function when canceled
 * @returns {Function} A function to close the dialog
 */
function showAlertDialog({ title, description, variant = 'default', onConfirm, onCancel }) {
  const container = document.getElementById('alert-dialog-container') || initAlertDialogContainer();

  // Store the currently focused element to restore focus when dialog closes
  const previouslyFocusedElement = document.activeElement;

  // Create overlay
  const overlay = createElement('div', container, {
    className: 'fixed inset-0 z-50 bg-black/50 backdrop-blur-sm transition-opacity duration-300',
    attributes: {
      'data-state': 'open'
    }
  });

  // Create dialog content
  const dialog = createElement('div', container, {
    className: 'fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border border-border bg-background p-6 shadow-lg duration-200 sm:rounded-lg md:w-full',
    attributes: {
      'data-state': 'open',
      'role': 'dialog',
      'aria-modal': 'true',
      'aria-labelledby': 'dialog-title',
      'aria-describedby': description ? 'dialog-description' : ''
    }
  });

  // Dialog header
  const header = createElement('div', dialog, {
    className: 'flex flex-col space-y-1.5 text-center sm:text-left',
    innerHTML: `<h2 id="dialog-title" class="text-lg font-semibold leading-none tracking-tight">${title}</h2>`
  });
  
  if (description) {
    createElement('p', header, {
      id: 'dialog-description',
      className: 'text-sm text-muted-foreground',
      textContent: description
    });
  }

  // Dialog footer with buttons
  const footer = createElement('div', dialog, {
    className: 'flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2'
  });

  const cancelButton = createElement('button', footer, {
    className: 'inline-flex items-center justify-center px-3 py-1.5 text-sm font-medium rounded-md border border-input bg-background hover:bg-accent hover:text-accent-foreground focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 focus:ring-offset-background transition-colors mt-2 sm:mt-0',
    textContent: 'Cancel'
  });

  const confirmButton = createElement('button', footer, {
    className: variant === 'destructive'
      ? 'inline-flex items-center justify-center px-4 py-2 text-sm font-medium rounded-md border border-transparent text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-colors'
      : 'inline-flex items-center justify-center px-4 py-2 text-sm font-medium rounded-md border border-transparent text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors',
    textContent: variant === 'destructive' ? 'Delete' : 'Confirm'
  });

  // Initialize icons in the dialog
  if (typeof lucide !== 'undefined' && lucide.createIcons) {
    lucide.createIcons({
      root: dialog
    });
  }

  // Set up focus trapping
  const removeFocusTrap = trapFocus(dialog, () => {
    closeDialog();
    if (onCancel) onCancel();
  });

  // Close dialog function
  function closeDialog() {
    overlay.classList.add('opacity-0');
    dialog.classList.add('opacity-0', 'scale-95');

    // Remove focus trap
    removeFocusTrap();

    // Remove elements after animation completes
    setTimeout(() => {
      if (container.contains(overlay)) {
        container.removeChild(overlay);
      }
      if (container.contains(dialog)) {
        container.removeChild(dialog);
      }

      // Restore focus to the element that had it before the dialog was opened
      if (previouslyFocusedElement && typeof previouslyFocusedElement.focus === 'function') {
        previouslyFocusedElement.focus();
      }
    }, 300);
  }

  // Event listeners
  overlay.addEventListener('click', () => {
    closeDialog();
    if (onCancel) onCancel();
  });

  cancelButton.addEventListener('click', () => {
    closeDialog();
    if (onCancel) onCancel();
  });

  confirmButton.addEventListener('click', () => {
    closeDialog();
    if (onConfirm) onConfirm();
  });

  // Return close function
  return closeDialog;
}

// Export public API
export {
  initAlertDialogContainer,
  showAlertDialog
};

/**
 * Utility functions for the Admin Panel
 */
document.addEventListener('DOMContentLoaded', function() {
  // Initialize toast container
  initToastContainer();

  // Handle flash messages on page load
  handleFlashMessages();
});

/**
 * Create and append the toast container to the document body
 */
function initToastContainer() {
  if (!document.getElementById('toast-container')) {
    const toastContainer = document.createElement('div');
    toastContainer.id = 'toast-container';
    toastContainer.className = 'toast-container';

    // Add positioning styles for better responsiveness
    toastContainer.style.position = 'fixed';
    toastContainer.style.bottom = '1rem';
    toastContainer.style.right = '1rem';
    toastContainer.style.zIndex = '9999';
    toastContainer.style.display = 'flex';
    toastContainer.style.flexDirection = 'column';
    toastContainer.style.gap = '0.5rem';
    toastContainer.style.maxWidth = 'calc(100% - 2rem)';
    toastContainer.style.width = '25rem';

    document.body.appendChild(toastContainer);
  }

  // Store active toast for single toast management
  window._activeToast = null;
}

/**
 * Handle flash messages by converting them to toast notifications
 */
function handleFlashMessages() {
  // Get the flash messages container
  const flashMessagesContainer = document.getElementById('flash-messages');
  if (!flashMessagesContainer) return;

  // Find all flash messages
  const flashMessages = flashMessagesContainer.querySelectorAll('.flash-message');
  if (!flashMessages.length) return;

  // Convert each flash message to a toast notification
  flashMessages.forEach((message) => {
    // Determine the message type
    let type = 'info';
    if (message.classList.contains('bg-red-50') || message.querySelector('.text-red-500')) {
      type = 'error';
    } else if (message.classList.contains('bg-yellow-50') || message.querySelector('.text-yellow-500')) {
      type = 'warning';
    } else if (message.classList.contains('bg-green-50') || message.querySelector('.text-green-500')) {
      type = 'success';
    }

    // Get the message content
    const contentElement = message.querySelector('.text-sm.opacity-90');
    const messageText = contentElement ? contentElement.textContent : message.textContent;

    // Show as toast
    showToast(messageText, { type: type, duration: 5000 });

    // Remove the original flash message
    message.remove();
  });

  // Clear the flash messages container
  flashMessagesContainer.innerHTML = '';
}

/**
 * Show a toast notification
 * @param {string} message - The message to display
 * @param {Object} options - Options for the toast
 * @param {string} options.type - Type of toast: 'success', 'error', 'warning', 'info'
 * @param {number} options.duration - Duration in milliseconds to show the toast
 * @param {boolean} options.dismissible - Whether the toast can be dismissed manually
 */
function showToast(message, options = {}) {
  const {
    type = 'info',
    duration = 3000,
    dismissible = true
  } = options;

  // Ensure toast container exists
  let toastContainer = document.getElementById('toast-container');
  if (!toastContainer) {
    initToastContainer();
    toastContainer = document.getElementById('toast-container');
  }

  // Close any existing toast first
  if (window._activeToast) {
    closeToast(window._activeToast);
    // Small delay to allow the closing animation to start
    // This prevents visual glitches when replacing toasts
    return setTimeout(() => {
      _createAndShowToast(message, type, duration, dismissible);
    }, 100);
  }

  return _createAndShowToast(message, type, duration, dismissible);
}

/**
 * Internal function to create and show a toast
 * @private
 */
function _createAndShowToast(message, type, duration, dismissible) {
  const toastContainer = document.getElementById('toast-container');

  // Create toast element
  const toast = document.createElement('div');
  toast.className = `toast toast-${type}`;
  toast.setAttribute('role', 'alert');
  toast.setAttribute('data-state', 'open');

  // Get appropriate icon based on type
  let iconName;
  switch (type) {
    case 'success':
      iconName = 'check-circle';
      break;
    case 'error':
      iconName = 'alert-circle';
      break;
    case 'warning':
      iconName = 'alert-triangle';
      break;
    default:
      iconName = 'info';
  }

  // Create toast structure with proper DOM elements
  const iconWrapper = document.createElement('div');
  iconWrapper.className = 'toast-icon';

  // Use SVG sprite for icons instead of Lucide initialization
  const svg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
  svg.setAttribute('width', '24');
  svg.setAttribute('height', '24');

  const use = document.createElementNS('http://www.w3.org/2000/svg', 'use');
  use.setAttributeNS('http://www.w3.org/1999/xlink', 'xlink:href', `#${iconName}`);
  svg.appendChild(use);
  iconWrapper.appendChild(svg);

  // Parse the message to extract title and description
  let title = '';
  let description = message;

  // Check if message contains a title (format: "Title: Description")
  const titleMatch = message.match(/^([^:]+):\s*(.+)$/);
  if (titleMatch) {
    title = titleMatch[1].trim();
    description = titleMatch[2].trim();
  }

  // Create content container
  const contentDiv = document.createElement('div');
  contentDiv.className = 'toast-content';

  // Add title if available
  if (title) {
    const titleDiv = document.createElement('div');
    titleDiv.className = 'toast-title';
    titleDiv.textContent = title;
    contentDiv.appendChild(titleDiv);
  }

  // Add description
  const descriptionDiv = document.createElement('div');
  descriptionDiv.className = 'toast-description';
  descriptionDiv.textContent = description;
  contentDiv.appendChild(descriptionDiv);

  // Add elements to toast
  toast.appendChild(iconWrapper);
  toast.appendChild(contentDiv);

  // Add close button if dismissible
  if (dismissible) {
    const closeBtn = document.createElement('button');
    closeBtn.className = 'toast-close';
    closeBtn.setAttribute('aria-label', 'Close');
    closeBtn.setAttribute('type', 'button');

    // Use SVG sprite for close icon
    const closeSvg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
    closeSvg.setAttribute('width', '16');
    closeSvg.setAttribute('height', '16');

    const closeUse = document.createElementNS('http://www.w3.org/2000/svg', 'use');
    closeUse.setAttributeNS('http://www.w3.org/1999/xlink', 'xlink:href', '#x');
    closeSvg.appendChild(closeUse);
    closeBtn.appendChild(closeSvg);

    closeBtn.addEventListener('click', () => closeToast(toast));
    toast.appendChild(closeBtn);
  }

  // Add to container
  toastContainer.appendChild(toast);

  // Store reference to active toast
  window._activeToast = toast;

  // Auto-close after duration
  if (duration > 0) {
    setTimeout(() => {
      if (document.body.contains(toast)) {
        closeToast(toast);
      }
    }, duration);
  }

  // Return the toast element
  return toast;
}

/**
 * Close a toast with animation
 * @param {HTMLElement} toast - The toast element to close
 */
function closeToast(toast) {
  // Prevent multiple close attempts
  if (!toast || toast.getAttribute('data-state') === 'closing') return;

  // Clear any existing auto-close timeout
  if (toast._timeoutId) {
    clearTimeout(toast._timeoutId);
    toast._timeoutId = null;
  }

  // Set closing state
  toast.setAttribute('data-state', 'closing');

  // Prevent any interaction during closing animation
  toast.style.pointerEvents = 'none';

  // Remove after animation completes
  // Use the animationend event to ensure the animation completes before removing
  const handleAnimationEnd = () => {
    if (toast && toast.parentNode) {
      toast.parentNode.removeChild(toast);
    }

    // Clear active toast reference if this is the active toast
    if (window._activeToast === toast) {
      window._activeToast = null;
    }

    // Remove the event listener
    toast.removeEventListener('animationend', handleAnimationEnd);
  };

  // Listen for the animation to end
  toast.addEventListener('animationend', handleAnimationEnd);

  // Fallback timeout in case the animation event doesn't fire
  const fallbackTimeout = setTimeout(() => {
    // Only run if the toast is still in the DOM
    if (toast && toast.parentNode) {
      handleAnimationEnd();
    }
  }, 400);

  // Store the fallback timeout ID
  toast._fallbackTimeoutId = fallbackTimeout;
}

/**
 * Open a drawer with the specified content and position
 * @param {string} content - HTML content to display in the drawer
 * @param {string} position - Position of the drawer ('right', 'left', 'top', 'bottom')
 * @returns {Function} A function to close the drawer
 */
function openDrawer(content, position = 'right') {
  const drawerContainer = document.getElementById('drawer-container');
  if (!drawerContainer) {
    return () => {};
  }

  // Create overlay
  const overlay = document.createElement('div');
  overlay.className = 'drawer-overlay';
  overlay.setAttribute('data-state', 'open');
  // Force overlay styles
  overlay.style.position = 'fixed';
  overlay.style.inset = '0';
  overlay.style.backgroundColor = 'rgba(0, 0, 0, 0.5)';
  overlay.style.backdropFilter = 'blur(4px)';
  overlay.style.zIndex = '9998';
  overlay.style.opacity = '1';

  // Create drawer content
  const drawer = document.createElement('div');
  drawer.className = `drawer-content drawer-${position}`;
  drawer.setAttribute('data-state', 'open');

  // Handle dark mode first to set proper background color
  if (document.documentElement.classList.contains('dark')) {
    drawer.style.backgroundColor = '#111827'; // Dark background color
    drawer.style.color = '#fff';
    drawer.style.borderColor = '#1f2937'; // Darker border for dark mode
  } else {
    drawer.style.backgroundColor = '#ffffff'; // Light background color
    drawer.style.color = '#111827';
    drawer.style.borderColor = '#e5e7eb'; // Light border for light mode
  }

  // Force drawer styles
  drawer.style.position = 'fixed';
  drawer.style.zIndex = '9999';
  drawer.style.padding = '1.5rem';
  drawer.style.boxShadow = document.documentElement.classList.contains('dark') ?
    '0 0 15px rgba(0, 0, 0, 0.5)' : // Darker shadow for dark mode
    '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)'; // Light shadow
  drawer.style.overflow = 'auto';
  drawer.style.display = 'block';

  // Apply position-specific styles
  if (position === 'right') {
    drawer.style.top = '0';
    drawer.style.right = '0';
    drawer.style.bottom = '0';
    // More responsive width based on screen size
    drawer.style.width = window.innerWidth < 640 ? '100%' : '30rem';
    drawer.style.maxWidth = '100vw';
    drawer.style.transform = 'translateX(0)';
    drawer.style.borderLeft = `1px solid ${drawer.style.borderColor}`;
  } else if (position === 'left') {
    drawer.style.top = '0';
    drawer.style.left = '0';
    drawer.style.bottom = '0';
    // More responsive width based on screen size
    drawer.style.width = window.innerWidth < 640 ? '100%' : '30rem';
    drawer.style.maxWidth = '100vw';
    drawer.style.transform = 'translateX(0)';
    drawer.style.borderRight = `1px solid ${drawer.style.borderColor}`;
  } else if (position === 'top') {
    drawer.style.top = '0';
    drawer.style.left = '0';
    drawer.style.right = '0';
    // More responsive height based on screen size
    drawer.style.maxHeight = window.innerHeight < 640 ? '100%' : '80vh';
    drawer.style.transform = 'translateY(0)';
    drawer.style.borderBottom = `1px solid ${drawer.style.borderColor}`;
  } else if (position === 'bottom') {
    drawer.style.bottom = '0';
    drawer.style.left = '0';
    drawer.style.right = '0';
    // More responsive height based on screen size
    drawer.style.maxHeight = window.innerHeight < 640 ? '100%' : '80vh';
    drawer.style.transform = 'translateY(0)';
    drawer.style.borderTop = `1px solid ${drawer.style.borderColor}`;
  }

  drawer.innerHTML = content;

  // Add close button
  const closeButton = document.createElement('button');
  closeButton.className = 'absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none';
  closeButton.style.position = 'absolute';
  closeButton.style.right = '1rem';
  closeButton.style.top = '1rem';
  closeButton.style.cursor = 'pointer';
  closeButton.innerHTML = '<i data-lucide="x" class="h-4 w-4"></i><span class="sr-only">Close</span>';
  drawer.prepend(closeButton);

  // Add to container
  drawerContainer.appendChild(overlay);
  drawerContainer.appendChild(drawer);

  // Initialize icons in the drawer - simple approach
  if (typeof lucide !== 'undefined' && lucide.createIcons) {
    lucide.createIcons({
      root: drawer
    });
  }

  // Make sure body doesn't scroll when drawer is open
  document.body.style.overflow = 'hidden';

  // Close drawer function
  function closeDrawer() {
    if (position === 'right') {
      drawer.style.transform = 'translateX(100%)';
    } else if (position === 'left') {
      drawer.style.transform = 'translateX(-100%)';
    } else if (position === 'top') {
      drawer.style.transform = 'translateY(-100%)';
    } else if (position === 'bottom') {
      drawer.style.transform = 'translateY(100%)';
    }

    overlay.style.opacity = '0';

    // Remove elements after animation completes
    setTimeout(() => {
      if (drawerContainer.contains(overlay)) {
        drawerContainer.removeChild(overlay);
      }
      if (drawerContainer.contains(drawer)) {
        drawerContainer.removeChild(drawer);
      }
      // Restore body scrolling
      document.body.style.overflow = '';
    }, 300);
  }

  overlay.addEventListener('click', closeDrawer);
  closeButton.addEventListener('click', closeDrawer);

  // Return the close function so it can be called programmatically
  return closeDrawer;
}

/**
 * Show an alert dialog with the specified options
 * @param {Object} options - Options for the alert dialog
 * @param {string} options.title - Title of the dialog
 * @param {string} options.description - Description text (optional)
 * @param {string} options.variant - Variant of the dialog ('default' or 'destructive')
 * @param {Function} options.onConfirm - Callback function when confirmed
 * @param {Function} options.onCancel - Callback function when canceled
 * @returns {Function} A function to close the dialog
 */
function showAlertDialog({ title, description, variant = 'default', onConfirm, onCancel }) {
  const container = document.getElementById('alert-dialog-container');
  if (!container) return () => {};

  // Store the currently focused element to restore focus when dialog closes
  const previouslyFocusedElement = document.activeElement;

  // Create overlay
  const overlay = document.createElement('div');
  overlay.className = 'fixed inset-0 z-50 bg-black/50 backdrop-blur-sm transition-opacity duration-300';
  overlay.setAttribute('data-state', 'open');

  // Create dialog content
  const dialog = document.createElement('div');
  dialog.className = 'fixed left-[50%] top-[50%] z-50 grid w-[calc(100%-2rem)] max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border border-border bg-background p-4 sm:p-6 shadow-lg duration-200 sm:rounded-lg md:w-full';
  dialog.setAttribute('data-state', 'open');
  dialog.setAttribute('role', 'dialog');
  dialog.setAttribute('aria-modal', 'true');
  dialog.setAttribute('aria-labelledby', 'dialog-title');
  dialog.setAttribute('aria-describedby', description ? 'dialog-description' : '');

  // Dialog header
  const header = document.createElement('div');
  header.className = 'flex flex-col space-y-1.5 text-center sm:text-left';
  header.innerHTML = `<h2 id="dialog-title" class="text-lg font-semibold leading-none tracking-tight">${title}</h2>`;
  if (description) {
    header.innerHTML += `<p id="dialog-description" class="text-sm text-muted-foreground">${description}</p>`;
  }

  // Dialog footer with buttons
  const footer = document.createElement('div');
  footer.className = 'flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2 gap-2 sm:gap-0';

  const cancelButton = document.createElement('button');
  cancelButton.className = 'inline-flex items-center justify-center px-3 py-1.5 text-sm font-medium rounded-md border border-input bg-background hover:bg-accent hover:text-accent-foreground focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 focus:ring-offset-background transition-colors mt-2 sm:mt-0';
  cancelButton.textContent = 'Cancel';

  const confirmButton = document.createElement('button');
  confirmButton.className = variant === 'destructive'
    ? 'inline-flex items-center justify-center px-4 py-2 text-sm font-medium rounded-md border border-transparent text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-colors'
    : 'inline-flex items-center justify-center px-4 py-2 text-sm font-medium rounded-md border border-transparent text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors';
  confirmButton.textContent = variant === 'destructive' ? 'Delete' : 'Confirm';

  footer.appendChild(cancelButton);
  footer.appendChild(confirmButton);

  // Assemble dialog
  dialog.appendChild(header);
  dialog.appendChild(footer);

  // Add to container
  container.appendChild(overlay);
  container.appendChild(dialog);

  // Initialize icons in the dialog
  if (typeof lucide !== 'undefined' && lucide.createIcons) {
    lucide.createIcons({
      root: dialog
    });
  }

  // Get all focusable elements in the dialog
  const focusableElements = dialog.querySelectorAll('button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])');
  const firstElement = focusableElements[0];
  const lastElement = focusableElements[focusableElements.length - 1];

  // Focus the first focusable element
  setTimeout(() => {
    firstElement.focus();
  }, 50);

  // Handle tab key to trap focus inside the dialog
  dialog.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
      closeDialog();
      if (onCancel) onCancel();
      return;
    }

    if (e.key === 'Tab') {
      if (e.shiftKey && document.activeElement === firstElement) {
        e.preventDefault();
        lastElement.focus();
      } else if (!e.shiftKey && document.activeElement === lastElement) {
        e.preventDefault();
        firstElement.focus();
      }
    }
  });

  // Close dialog function
  function closeDialog() {
    overlay.classList.add('opacity-0');
    dialog.classList.add('opacity-0', 'scale-95');

    // Remove elements after animation completes
    setTimeout(() => {
      if (container.contains(overlay)) {
        container.removeChild(overlay);
      }
      if (container.contains(dialog)) {
        container.removeChild(dialog);
      }

      // Restore focus to the element that had it before the dialog was opened
      if (previouslyFocusedElement && typeof previouslyFocusedElement.focus === 'function') {
        previouslyFocusedElement.focus();
      }
    }, 300);
  }

  // Event listeners
  overlay.addEventListener('click', () => {
    closeDialog();
    if (onCancel) onCancel();
  });

  cancelButton.addEventListener('click', () => {
    closeDialog();
    if (onCancel) onCancel();
  });

  confirmButton.addEventListener('click', () => {
    closeDialog();
    if (onConfirm) onConfirm();
  });

  // Return close function
  return closeDialog;
}

// Make these functions globally available
window.openDrawer = openDrawer;
window.showAlertDialog = showAlertDialog;

// Make toast functions globally available
window.showToast = showToast;
window.closeToast = closeToast;
window.handleFlashMessages = handleFlashMessages;

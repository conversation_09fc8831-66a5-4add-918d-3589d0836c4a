{% extends "base.html" %}

{% block title %}System Settings{% endblock %}

{% block header %}System Settings{% endblock %}

{% block content %}
<div class="space-y-6">
    <!-- Settings Header with Save Button -->
    <div class="flex justify-between items-center">
        <div>
            <h1 class="text-2xl font-bold">System Settings</h1>
            <p class="text-sm text-muted-foreground mt-1">Configure application-wide settings and preferences</p>
        </div>
        <div>
            <button form="settings-form" type="submit" class="btn btn-primary text-white dark:text-white px-4 py-2 rounded flex items-center">
                <i data-lucide="save" class="w-4 h-4 mr-2"></i>
                <span>Save Changes</span>
            </button>
        </div>
    </div>

    <!-- Settings Navigation Tabs -->
    <div class="tabs-container">
        <div class="tabs" role="tablist">
            <button type="button" class="tab active" data-tab="general" role="tab" aria-selected="true">
                <i data-lucide="settings" class="w-4 h-4 mr-2"></i>
                General
            </button>
            <button type="button" class="tab" data-tab="security" role="tab" aria-selected="false">
                <i data-lucide="shield" class="w-4 h-4 mr-2"></i>
                Security
            </button>
            <button type="button" class="tab" data-tab="users" role="tab" aria-selected="false">
                <i data-lucide="users" class="w-4 h-4 mr-2"></i>
                User Registration
            </button>
            <button type="button" class="tab" data-tab="appearance" role="tab" aria-selected="false">
                <i data-lucide="palette" class="w-4 h-4 mr-2"></i>
                Appearance
            </button>
        </div>
    </div>

    <!-- Settings Form -->
    <div class="card">
        <form id="settings-form" action="{{ url_for('admin.update_settings') }}" method="POST">
            <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">

            <div class="p-6" style="display: flex; flex-direction: column; gap: 0;">
                <!-- General Settings Tab Content -->
                <div class="tab-content active" id="general-tab">
                    <div class="border-b pb-4 mb-6">
                        <h3 class="text-xl font-semibold">General Settings</h3>
                        <p class="text-sm text-muted-foreground">Basic application configuration</p>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-x-8 gap-y-6">
                        <!-- Site Name -->
                        <div class="form-group">
                            <label for="site_name" class="form-label">Site Name <span class="text-destructive">*</span></label>
                            <!-- Direct dictionary access -->
                            <input type="text" id="site_name" name="site_name"
                                   value="{{ settings.site_name }}"
                                   class="input" required>
                            <p class="text-xs text-muted-foreground mt-1">The name displayed in the browser title and header</p>
                        </div>

                        <!-- Admin Email -->
                        <div class="form-group">
                            <label for="admin_email" class="form-label">Admin Email <span class="text-destructive">*</span></label>
                            <!-- Direct dictionary access -->
                            <input type="email" id="admin_email" name="admin_email"
                                   value="{{ settings.admin_email }}"
                                   class="input" required>
                            <p class="text-xs text-muted-foreground mt-1">Primary contact email for system notifications</p>
                        </div>

                        <!-- Site Description -->
                        <div class="form-group md:col-span-2">
                            <label for="site_description" class="form-label">Site Description</label>
                            <!-- Direct dictionary access -->
                            <textarea id="site_description" name="site_description" rows="3" class="input">{{ settings.site_description }}</textarea>
                            <p class="text-xs text-muted-foreground mt-1">Brief description of your application</p>
                        </div>

                        <!-- Maintenance Mode Toggle -->
                        <div class="form-group md:col-span-2">
                            <div class="flex items-center space-x-2">
                                <!-- Direct dictionary access for maintenance mode -->
                                <input type="checkbox" id="maintenance_mode" name="maintenance_mode"
                                       class="h-4 w-4 rounded border-input bg-background"
                                       {% if settings.maintenance_mode == 'on' or settings.maintenance_mode == 'true' %}checked{% endif %}>
                                <input type="hidden" name="checkbox_maintenance_mode" value="1">
                                <label for="maintenance_mode" class="text-sm font-medium">Enable Maintenance Mode</label>
                            </div>
                            <p class="text-xs text-muted-foreground mt-1 ml-6">When enabled, the site will display a maintenance message to all non-admin users</p>
                        </div>
                    </div>
                </div>

                <!-- Security Settings Tab Content -->
                <div class="tab-content" id="security-tab">
                    <div class="border-b pb-4 mb-6">
                        <h3 class="text-xl font-semibold">Security Settings</h3>
                        <p class="text-sm text-muted-foreground">Configure password policies and security options</p>
                        <div class="mt-2 p-2 bg-primary/10 rounded-md border border-primary/20">
                            <p class="text-sm flex items-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2 text-primary" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"></path></svg>
                                These settings are applied to all password changes and new user registrations
                            </p>
                        </div>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-x-8 gap-y-6">
                        <!-- Password Minimum Length -->
                        <div class="form-group">
                            <label for="min_password_length" class="form-label">Minimum Password Length <span class="text-destructive">*</span></label>
                            <!-- Direct dictionary access -->
                            <input type="number" id="min_password_length" name="min_password_length"
                                   value="{{ settings.min_password_length }}"
                                   class="input" min="6" max="32" required>
                            <p class="text-xs text-muted-foreground mt-1">Minimum number of characters required for passwords</p>
                        </div>

                        <!-- Maximum Login Attempts -->
                        <div class="form-group">
                            <label for="max_login_attempts" class="form-label">Maximum Login Attempts</label>
                            <!-- Direct dictionary access -->
                            <input type="number" id="max_login_attempts" name="max_login_attempts"
                                   value="{{ settings.max_login_attempts }}"
                                   class="input" min="1" max="20">
                            <p class="text-xs text-muted-foreground mt-1">Number of failed attempts before account lockout</p>
                        </div>

                        <!-- Password Complexity Requirements -->
                        <div class="form-group">
                            <label class="form-label">Password Requirements</label>
                            <div class="space-y-2 mt-2">
                                <!-- Direct dictionary access for password requirements -->
                                <div class="flex items-center space-x-2">
                                    <input type="checkbox" id="password_requires_uppercase" name="password_requires_uppercase"
                                           class="h-4 w-4 rounded border-input bg-background"
                                           {% if settings.password_requires_uppercase == 'on' or settings.password_requires_uppercase == 'true' %}checked{% endif %}>
                                    <input type="hidden" name="checkbox_password_requires_uppercase" value="1">
                                    <label for="password_requires_uppercase" class="text-sm">Require uppercase letter</label>
                                </div>

                                <!-- Direct dictionary access for number requirement -->
                                <div class="flex items-center space-x-2">
                                    <input type="checkbox" id="password_requires_number" name="password_requires_number"
                                           class="h-4 w-4 rounded border-input bg-background"
                                           {% if settings.password_requires_number == 'on' or settings.password_requires_number == 'true' %}checked{% endif %}>
                                    <input type="hidden" name="checkbox_password_requires_number" value="1">
                                    <label for="password_requires_number" class="text-sm">Require number</label>
                                </div>

                                <!-- Direct dictionary access for special character requirement -->
                                <div class="flex items-center space-x-2">
                                    <input type="checkbox" id="password_requires_special" name="password_requires_special"
                                           class="h-4 w-4 rounded border-input bg-background"
                                           {% if settings.password_requires_special == 'on' or settings.password_requires_special == 'true' %}checked{% endif %}>
                                    <input type="hidden" name="checkbox_password_requires_special" value="1">
                                    <label for="password_requires_special" class="text-sm">Require special character</label>
                                </div>
                            </div>
                        </div>

                        <!-- Session Settings -->
                        <div class="form-group">
                            <label for="session_lifetime" class="form-label">Session Lifetime (days)</label>
                            {% set session_lifetime = None %}
                            {% for setting in grouped_settings.get('session', []) %}
                                {% if setting.key == 'session_lifetime' %}
                                    {% set session_lifetime = setting.value %}
                                {% endif %}
                            {% endfor %}
                            <input type="number" id="session_lifetime" name="session_lifetime"
                                   value="{{ session_lifetime or '7' }}"
                                   class="input" min="1" max="30">
                            <p class="text-xs text-muted-foreground mt-1">Number of days before users are required to log in again. Changes take effect immediately for new sessions.</p>
                        </div>

                        <!-- CSRF Protection -->
                        <div class="form-group md:col-span-2">
                            <div class="flex items-center space-x-2">
                                {% set csrf_protection = true %}
                                {% for setting in grouped_settings.get('csrf', []) %}
                                    {% if setting.key == 'csrf_protection' %}
                                        {% set csrf_protection = setting.typed_value %}
                                    {% endif %}
                                {% endfor %}
                                <input type="checkbox" id="csrf_protection" name="csrf_protection"
                                       class="h-4 w-4 rounded border-input bg-background"
                                       {% if csrf_protection %}checked{% endif %} disabled>
                                <input type="hidden" name="checkbox_csrf_protection" value="1">
                                <input type="hidden" name="csrf_protection" value="true">
                                <label for="csrf_protection" class="text-sm font-medium">Enable CSRF Protection</label>
                            </div>
                            <p class="text-xs text-muted-foreground mt-1 ml-6">Protects against cross-site request forgery attacks (recommended to keep enabled)</p>
                        </div>
                    </div>
                </div>

                <!-- User Registration Tab Content -->
                <div class="tab-content" id="users-tab">
                    <div class="border-b pb-4 mb-6">
                        <h3 class="text-xl font-semibold">User Registration Settings</h3>
                        <p class="text-sm text-muted-foreground">Configure user registration and account options</p>
                        <div class="mt-2 p-2 bg-primary/10 rounded-md border border-primary/20">
                            <p class="text-sm flex items-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2 text-primary" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"></path></svg>
                                These settings are applied to all new user registrations
                            </p>
                        </div>
                    </div>

                    <!-- User Registration Settings with two-column functional grouping -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-x-8 gap-y-6">
                        <!-- Left Column - Account Creation -->
                        <div class="space-y-6">
                            <!-- Account Creation Section Header -->
                            <div class="pb-4 border-b border-border dark:border-border">
                                <h4 class="text-base font-medium mb-1">Account Creation</h4>
                                <p class="text-sm text-muted-foreground">Control how new users can register</p>
                            </div>

                            <!-- Allow Registration -->
                            <div class="form-group">
                                <div class="flex items-start">
                                    <div class="flex-shrink-0 mt-1">
                                        {% set allow_registration = settings.allow_registration != 'false' %}
                                        <input type="checkbox" id="allow_registration" name="allow_registration"
                                               class="h-4 w-4 rounded border-input bg-background"
                                               {% if allow_registration %}checked{% endif %}>
                                        <input type="hidden" name="checkbox_allow_registration" value="1">
                                    </div>
                                    <div class="ml-3">
                                        <label for="allow_registration" class="text-sm font-medium block">Allow New User Registration</label>
                                        <p class="text-xs text-muted-foreground mt-1">When disabled, only administrators can create new accounts</p>
                                    </div>
                                </div>
                            </div>

                            <!-- Default User Role -->
                            <div class="form-group ml-7"> <!-- Indented to align with checkbox content -->
                                <label for="default_user_role" class="form-label mb-2">Default User Role</label>
                                <select id="default_user_role" name="default_user_role" class="input">
                                    {% set default_role = grouped_settings.get('default', [])|selectattr('key', 'equalto', 'default_user_role')|map(attribute='value')|first or 'User' %}
                                    <option value="User" {% if default_role == 'User' %}selected{% endif %}>User</option>
                                    <option value="Manager" {% if default_role == 'Manager' %}selected{% endif %}>Manager</option>
                                </select>
                                <p class="text-xs text-muted-foreground mt-2">Default role assigned to new users during registration</p>
                            </div>
                        </div>

                        <!-- Right Column - Account Verification -->
                        <div class="space-y-6">
                            <!-- Account Verification Section Header -->
                            <div class="pb-4 border-b border-border dark:border-border">
                                <h4 class="text-base font-medium mb-1">Account Verification</h4>
                                <p class="text-sm text-muted-foreground">Configure verification requirements for new accounts</p>
                            </div>

                            <!-- Require Email Verification -->
                            <div class="form-group">
                                <div class="flex items-start">
                                    <div class="flex-shrink-0 mt-1">
                                        {% set require_email_verification = grouped_settings.get('require', [])|selectattr('key', 'equalto', 'require_email_verification')|map(attribute='typed_value')|first or true %}
                                        <input type="checkbox" id="require_email_verification" name="require_email_verification"
                                               class="h-4 w-4 rounded border-input bg-background"
                                               {% if require_email_verification %}checked{% endif %}>
                                        <input type="hidden" name="checkbox_require_email_verification" value="1">
                                    </div>
                                    <div class="ml-3">
                                        <label for="require_email_verification" class="text-sm font-medium block">Require Email Verification</label>
                                        <p class="text-xs text-muted-foreground mt-1">Users must verify their email address before accessing the system</p>
                                    </div>
                                </div>
                            </div>

                            <!-- Require Admin Approval -->
                            <div class="form-group">
                                <div class="flex items-start">
                                    <div class="flex-shrink-0 mt-1">
                                        {% set require_admin_approval = grouped_settings.get('require', [])|selectattr('key', 'equalto', 'require_admin_approval')|map(attribute='typed_value')|first or false %}
                                        <input type="checkbox" id="require_admin_approval" name="require_admin_approval"
                                               class="h-4 w-4 rounded border-input bg-background"
                                               {% if require_admin_approval %}checked{% endif %}>
                                        <input type="hidden" name="checkbox_require_admin_approval" value="1">
                                    </div>
                                    <div class="ml-3">
                                        <label for="require_admin_approval" class="text-sm font-medium block">Require Admin Approval</label>
                                        <p class="text-xs text-muted-foreground mt-1">New accounts require administrator approval before activation</p>
                                    </div>
                                </div>
                            </div>

                            <!-- Account Activation Expiry -->
                            <div class="form-group ml-7"> <!-- Indented to align with checkbox content -->
                                <label for="account_activation_expiry" class="form-label mb-2">Activation Link Expiry (hours)</label>
                                <input type="number" id="account_activation_expiry" name="account_activation_expiry"
                                       value="{{ grouped_settings.get('account', [])|selectattr('key', 'equalto', 'account_activation_expiry')|map(attribute='value')|first or '24' }}"
                                       class="input" min="1" max="72">
                                <p class="text-xs text-muted-foreground mt-2">Hours before account activation links expire</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Appearance Tab Content -->
                <div class="tab-content" id="appearance-tab">
                    <div class="border-b pb-4 mb-6">
                        <h3 class="text-xl font-semibold">Appearance Settings</h3>
                        <p class="text-sm text-muted-foreground">Configure the look and feel of your application</p>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-x-8 gap-y-6">
                        <!-- Default Theme -->
                        <div class="form-group">
                            <label for="default_theme" class="form-label">Default Theme</label>
                            <select id="default_theme" name="default_theme" class="input">
                                {% set default_theme = grouped_settings.get('default', [])|selectattr('key', 'equalto', 'default_theme')|map(attribute='value')|first or 'light' %}
                                <option value="light" {% if default_theme == 'light' %}selected{% endif %}>Light</option>
                                <option value="dark" {% if default_theme == 'dark' %}selected{% endif %}>Dark</option>
                                <option value="system" {% if default_theme == 'system' %}selected{% endif %}>System Preference</option>
                            </select>
                            <p class="text-xs text-muted-foreground mt-1">Default theme for new users</p>
                        </div>

                        <!-- Primary Color -->
                        <div class="form-group md:col-span-2">
                            <label class="form-label">Primary Color</label>
                            <div class="mt-3">
                                <input type="hidden" id="primary_color" name="primary_color"
                                       value="{{ grouped_settings.get('primary', [])|selectattr('key', 'equalto', 'primary_color')|map(attribute='value')|first or '#0284c7' }}">
                                <input type="hidden" id="primary_color_hex" name="primary_color_hex"
                                       value="{{ grouped_settings.get('primary', [])|selectattr('key', 'equalto', 'primary_color')|map(attribute='value')|first or '#0284c7' }}">

                                <!-- Color Palette -->
                                <div class="grid grid-cols-3 gap-2 md:grid-cols-3 lg:grid-cols-4">
                                    <!-- Row 1 -->
                                    <button type="button" class="color-option flex items-center justify-between rounded-md border border-border p-2 hover:border-primary" data-color="#71717a" data-name="Zinc">
                                        <div class="flex items-center gap-2">
                                            <div class="h-5 w-5 rounded-full" style="background-color: #71717a"></div>
                                            <span class="text-sm">Zinc</span>
                                        </div>
                                        <svg class="color-check h-4 w-4 text-primary" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="20 6 9 17 4 12"></polyline></svg>
                                    </button>

                                    <button type="button" class="color-option flex items-center justify-between rounded-md border border-border p-2 hover:border-primary" data-color="#64748b" data-name="Slate">
                                        <div class="flex items-center gap-2">
                                            <div class="h-5 w-5 rounded-full" style="background-color: #64748b"></div>
                                            <span class="text-sm">Slate</span>
                                        </div>
                                        <svg class="color-check h-4 w-4 text-primary" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="20 6 9 17 4 12"></polyline></svg>
                                    </button>

                                    <button type="button" class="color-option flex items-center justify-between rounded-md border border-border p-2 hover:border-primary" data-color="#78716c" data-name="Stone">
                                        <div class="flex items-center gap-2">
                                            <div class="h-5 w-5 rounded-full" style="background-color: #78716c"></div>
                                            <span class="text-sm">Stone</span>
                                        </div>
                                        <svg class="color-check h-4 w-4 text-primary" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="20 6 9 17 4 12"></polyline></svg>
                                    </button>

                                    <!-- Row 2 -->
                                    <button type="button" class="color-option flex items-center justify-between rounded-md border border-border p-2 hover:border-primary" data-color="#6b7280" data-name="Gray">
                                        <div class="flex items-center gap-2">
                                            <div class="h-5 w-5 rounded-full" style="background-color: #6b7280"></div>
                                            <span class="text-sm">Gray</span>
                                        </div>
                                        <svg class="color-check h-4 w-4 text-primary" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="20 6 9 17 4 12"></polyline></svg>
                                    </button>

                                    <button type="button" class="color-option flex items-center justify-between rounded-md border border-border p-2 hover:border-primary" data-color="#525252" data-name="Neutral">
                                        <div class="flex items-center gap-2">
                                            <div class="h-5 w-5 rounded-full" style="background-color: #525252"></div>
                                            <span class="text-sm">Neutral</span>
                                        </div>
                                        <svg class="color-check h-4 w-4 text-primary" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="20 6 9 17 4 12"></polyline></svg>
                                    </button>

                                    <button type="button" class="color-option flex items-center justify-between rounded-md border border-border p-2 hover:border-primary" data-color="#ef4444" data-name="Red">
                                        <div class="flex items-center gap-2">
                                            <div class="h-5 w-5 rounded-full" style="background-color: #ef4444"></div>
                                            <span class="text-sm">Red</span>
                                        </div>
                                        <svg class="color-check h-4 w-4 text-primary" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="20 6 9 17 4 12"></polyline></svg>
                                    </button>

                                    <!-- Row 3 -->
                                    <button type="button" class="color-option flex items-center justify-between rounded-md border border-border p-2 hover:border-primary" data-color="#ec4899" data-name="Rose">
                                        <div class="flex items-center gap-2">
                                            <div class="h-5 w-5 rounded-full" style="background-color: #ec4899"></div>
                                            <span class="text-sm">Rose</span>
                                        </div>
                                        <svg class="color-check h-4 w-4 text-primary" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="20 6 9 17 4 12"></polyline></svg>
                                    </button>

                                    <button type="button" class="color-option flex items-center justify-between rounded-md border border-border p-2 hover:border-primary" data-color="#f97316" data-name="Orange">
                                        <div class="flex items-center gap-2">
                                            <div class="h-5 w-5 rounded-full" style="background-color: #f97316"></div>
                                            <span class="text-sm">Orange</span>
                                        </div>
                                        <svg class="color-check h-4 w-4 text-primary" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="20 6 9 17 4 12"></polyline></svg>
                                    </button>

                                    <button type="button" class="color-option flex items-center justify-between rounded-md border border-border p-2 hover:border-primary" data-color="#22c55e" data-name="Green">
                                        <div class="flex items-center gap-2">
                                            <div class="h-5 w-5 rounded-full" style="background-color: #22c55e"></div>
                                            <span class="text-sm">Green</span>
                                        </div>
                                        <svg class="color-check h-4 w-4 text-primary" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="20 6 9 17 4 12"></polyline></svg>
                                    </button>

                                    <!-- Row 4 -->
                                    <button type="button" class="color-option flex items-center justify-between rounded-md border border-border p-2 hover:border-primary" data-color="#3b82f6" data-name="Blue">
                                        <div class="flex items-center gap-2">
                                            <div class="h-5 w-5 rounded-full" style="background-color: #3b82f6"></div>
                                            <span class="text-sm">Blue</span>
                                        </div>
                                        <svg class="color-check h-4 w-4 text-primary" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="20 6 9 17 4 12"></polyline></svg>
                                    </button>

                                    <button type="button" class="color-option flex items-center justify-between rounded-md border border-border p-2 hover:border-primary" data-color="#eab308" data-name="Yellow">
                                        <div class="flex items-center gap-2">
                                            <div class="h-5 w-5 rounded-full" style="background-color: #eab308"></div>
                                            <span class="text-sm">Yellow</span>
                                        </div>
                                        <svg class="color-check h-4 w-4 text-primary" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="20 6 9 17 4 12"></polyline></svg>
                                    </button>

                                    <button type="button" class="color-option flex items-center justify-between rounded-md border border-border p-2 hover:border-primary" data-color="#8b5cf6" data-name="Violet">
                                        <div class="flex items-center gap-2">
                                            <div class="h-5 w-5 rounded-full" style="background-color: #8b5cf6"></div>
                                            <span class="text-sm">Violet</span>
                                        </div>
                                        <svg class="color-check h-4 w-4 text-primary" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="20 6 9 17 4 12"></polyline></svg>
                                    </button>
                                </div>
                            </div>
                            <p class="text-xs text-muted-foreground mt-3">Select a primary color for your application</p>
                        </div>
                    </div>
                </div>


            </div>

            <!-- Form actions are in the header -->
            <input type="hidden" id="active_tab" name="active_tab" value="general">
        </form>
    </div>

    {% if is_admin %}
    <!-- Debug information (only visible to admins) -->
    <div class="mt-8 border border-border dark:border-border rounded-lg overflow-hidden shadow-sm">
        <!-- Debug Header -->
        <div class="bg-primary/10 dark:bg-primary/20 p-5 flex items-center justify-between debug-header">
            <div class="flex items-center space-x-2">
                <i data-lucide="bug" class="w-5 h-5 text-primary"></i>
                <h3 class="text-lg font-semibold">Debug Information</h3>
            </div>
            <div class="flex items-center space-x-4">
                <span class="text-xs px-3 py-1.5 bg-primary/20 rounded-full">Cache: {{ cache_buster }}</span>
                <button id="collapse-all-btn" class="text-xs px-3 py-1.5 bg-primary/20 rounded-full hover:bg-primary/30 transition-colors">
                    Collapse All
                </button>
                <button id="expand-all-btn" class="text-xs px-3 py-1.5 bg-primary/20 rounded-full hover:bg-primary/30 transition-colors">
                    Expand All
                </button>
            </div>
        </div>

        <!-- Debug Tabs -->
        <div class="border-b border-border dark:border-border">
            <div class="flex">
                <button class="debug-tab active px-5 py-3 text-sm font-medium" data-target="settings-data">Settings Data</button>
                <button class="debug-tab px-5 py-3 text-sm font-medium" data-target="direct-dictionary">Direct Dictionary</button>
                <button class="debug-tab px-5 py-3 text-sm font-medium" data-target="about-settings">About Settings</button>
            </div>
        </div>

        <!-- Debug Content -->
        <div class="p-5">
            <!-- Settings Data Tab -->
            <div id="settings-data" class="debug-content active">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {% for category, settings_list in grouped_settings.items() %}
                    {% if settings_list|length > 0 %}
                    <div class="bg-card dark:bg-card rounded-md overflow-hidden border border-border dark:border-border shadow-sm">
                        <div class="bg-muted dark:bg-muted p-3 font-semibold text-sm flex items-center justify-between">
                            <span>{{ category|capitalize }}</span>
                            <span class="text-xs text-muted-foreground">{{ settings_list|length }} items</span>
                        </div>
                        <div class="p-3">
                            <div class="space-y-2 text-xs font-mono">
                                {% for setting in settings_list %}
                                <div class="flex items-start">
                                    <div class="w-6 flex-shrink-0 flex items-center justify-center">
                                        {% if setting.type == 'boolean' %}
                                        <span class="w-2 h-2 rounded-full {% if setting.value == 'true' or setting.value == 'on' %}bg-green-500{% else %}bg-red-500{% endif %}"></span>
                                        {% elif setting.type == 'integer' or setting.type == 'float' %}
                                        <span class="w-2 h-2 rounded-full bg-blue-500"></span>
                                        {% else %}
                                        <span class="w-2 h-2 rounded-full bg-purple-500"></span>
                                        {% endif %}
                                    </div>
                                    <div class="flex-grow">
                                        <span class="font-semibold">{{ setting.key }}</span>
                                        <span class="text-muted-foreground"> = </span>
                                        <span class="{% if setting.value == 'true' or setting.value == 'on' %}text-green-500{% elif setting.value == 'false' %}text-red-500{% endif %}">{{ setting.value }}</span>
                                        <span class="text-muted-foreground">({{ setting.type }})</span>
                                    </div>
                                </div>
                                {% endfor %}
                            </div>
                        </div>
                    </div>
                    {% endif %}
                    {% endfor %}
                </div>
            </div>

            <!-- Direct Dictionary Tab -->
            <div id="direct-dictionary" class="debug-content hidden">
                <div class="bg-card dark:bg-card rounded-md overflow-hidden border border-border dark:border-border shadow-sm">
                    <div class="bg-muted dark:bg-muted p-3 font-semibold text-sm flex items-center justify-between">
                        <span>Settings Dictionary</span>
                        <div class="flex items-center space-x-2">
                            <input type="text" id="settings-search" placeholder="Search settings..." class="text-xs px-3 py-1.5 rounded border border-border dark:border-border bg-background dark:bg-background w-48">
                            <span class="text-xs text-muted-foreground" id="settings-count"></span>
                        </div>
                    </div>
                    <div class="p-3">
                        <div class="space-y-1 text-xs font-mono" id="settings-list">
                            {% set setting_keys = [] %}
                            {% for category, settings_list in grouped_settings.items() %}
                                {% for setting in settings_list %}
                                    {% set _ = setting_keys.append(setting.key) %}
                                {% endfor %}
                            {% endfor %}

                            {% for key, value in settings.items() %}
                                {% if key in setting_keys %}
                                <div class="setting-item flex items-center py-2 border-b border-border/30 dark:border-border/30 last:border-0">
                                    <div class="w-1/3 font-semibold truncate pr-3">{{ key }}</div>
                                    <div class="w-2/3 truncate">{{ value }}</div>
                                </div>
                                {% endif %}
                            {% endfor %}
                        </div>
                    </div>
                </div>
            </div>

            <!-- About Settings Tab -->
            <div id="about-settings" class="debug-content hidden">
                <div class="bg-card dark:bg-card rounded-md overflow-hidden border border-border dark:border-border shadow-sm">
                    <div class="bg-muted dark:bg-muted p-3 font-semibold text-sm">
                        About Default Settings & Caching
                    </div>
                    <div class="p-5 space-y-5">
                        <div class="flex items-start space-x-3">
                            <div class="w-8 h-8 rounded-full bg-primary/20 flex items-center justify-center flex-shrink-0">
                                <i data-lucide="settings-2" class="w-4 h-4 text-primary"></i>
                            </div>
                            <div>
                                <h4 class="font-semibold mb-1">Why Default Settings?</h4>
                                <p class="text-sm text-muted-foreground">Default settings ensure the application has necessary configuration values even on first run. They provide fallback values when a setting hasn't been explicitly set by an administrator.</p>
                            </div>
                        </div>

                        <div class="flex items-start space-x-3">
                            <div class="w-8 h-8 rounded-full bg-primary/20 flex items-center justify-center flex-shrink-0">
                                <i data-lucide="database" class="w-4 h-4 text-primary"></i>
                            </div>
                            <div>
                                <h4 class="font-semibold mb-1">Why Caching?</h4>
                                <p class="text-sm text-muted-foreground">The cache buster ({{ cache_buster }}) prevents browsers from showing stale settings pages after updates. Without it, browsers might show outdated settings from their cache instead of the current values from the database.</p>
                                <p class="text-sm text-muted-foreground mt-2">Session caching in the database helps improve performance by reducing database queries, especially for frequently accessed settings.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Debug Panel JavaScript -->
    <script>
    document.addEventListener('DOMContentLoaded', function() {
        // Lucide icons are now initialized automatically

        // Debug tabs functionality
        const debugTabs = document.querySelectorAll('.debug-tab');
        const debugContents = document.querySelectorAll('.debug-content');

        // First, hide all content sections except the active one
        debugContents.forEach(content => {
            if (!content.classList.contains('active')) {
                content.classList.add('hidden');
            }
        });

        // Add click event listeners to tabs
        debugTabs.forEach(tab => {
            tab.addEventListener('click', () => {
                console.log('Tab clicked:', tab.getAttribute('data-target'));

                // Remove active class from all tabs
                debugTabs.forEach(t => t.classList.remove('active'));

                // Add active class to clicked tab
                tab.classList.add('active');

                // Hide all content sections
                debugContents.forEach(content => {
                    content.classList.remove('active');
                    content.classList.add('hidden');
                });

                // Show corresponding content
                const targetId = tab.getAttribute('data-target');
                const targetContent = document.getElementById(targetId);

                if (targetContent) {
                    targetContent.classList.remove('hidden');
                    targetContent.classList.add('active');
                }
            });
        });

        // Settings search functionality
        const searchInput = document.getElementById('settings-search');
        const settingItems = document.querySelectorAll('.setting-item');
        const settingsCount = document.getElementById('settings-count');

        // Update settings count
        function updateSettingsCount() {
            let visibleCount = 0;
            settingItems.forEach(item => {
                if (item.style.display !== 'none') {
                    visibleCount++;
                }
            });
            if (settingsCount) {
                settingsCount.textContent = `${visibleCount} items`;
            }
        }

        // Initial count
        updateSettingsCount();

        if (searchInput) {
            searchInput.addEventListener('input', () => {
                const searchTerm = searchInput.value.toLowerCase();

                settingItems.forEach(item => {
                    const text = item.textContent.toLowerCase();
                    if (text.includes(searchTerm)) {
                        item.style.display = '';
                    } else {
                        item.style.display = 'none';
                    }
                });

                // Update count after filtering
                updateSettingsCount();
            });
        }

        // Expand/Collapse All buttons
        const expandAllBtn = document.getElementById('expand-all-btn');
        const collapseAllBtn = document.getElementById('collapse-all-btn');

        if (expandAllBtn) {
            expandAllBtn.addEventListener('click', () => {
                console.log('Expand all clicked');

                // Show all content sections
                debugContents.forEach(content => {
                    content.classList.remove('hidden');
                });

                // Update the active tab to match the first content section
                debugTabs.forEach((tab, index) => {
                    if (index === 0) {
                        tab.classList.add('active');
                    } else {
                        tab.classList.remove('active');
                    }
                });

                // Make the first content section active
                debugContents.forEach((content, index) => {
                    if (index === 0) {
                        content.classList.add('active');
                    } else {
                        content.classList.remove('active');
                    }
                });
            });
        }

        if (collapseAllBtn) {
            collapseAllBtn.addEventListener('click', () => {
                console.log('Collapse all clicked');

                // Hide all content sections except the active one
                debugContents.forEach(content => {
                    if (!content.classList.contains('active')) {
                        content.classList.add('hidden');
                    }
                });

                // Make sure at least one tab is active
                let hasActiveTab = false;
                debugTabs.forEach(tab => {
                    if (tab.classList.contains('active')) {
                        hasActiveTab = true;
                    }
                });

                // If no tab is active, activate the first one
                if (!hasActiveTab && debugTabs.length > 0) {
                    debugTabs[0].classList.add('active');

                    // Also show and activate the corresponding content
                    const targetId = debugTabs[0].getAttribute('data-target');
                    const targetContent = document.getElementById(targetId);
                    if (targetContent) {
                        targetContent.classList.remove('hidden');
                        targetContent.classList.add('active');
                    }
                }
            });
        }
    });
    </script>

    <style>
    .debug-tab {
        position: relative;
        transition: all 0.2s;
    }

    .debug-tab:hover {
        background-color: rgba(var(--primary-rgb), 0.05);
    }

    .debug-tab.active {
        color: var(--primary);
    }

    .debug-tab.active:after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        height: 2px;
        background-color: var(--primary);
    }

    /* Ensure debug tabs work with neutral colors */
    .neutral-primary .debug-tab.active {
        color: #000000;
    }

    .neutral-primary .debug-tab.active:after {
        background-color: #000000;
    }

    .dark.neutral-primary .debug-tab.active {
        color: #ffffff;
    }

    .dark.neutral-primary .debug-tab.active:after {
        background-color: #ffffff;
    }

    /* Add spacing between debug content sections when expanded */
    .debug-content:not(.active):not(.hidden) {
        margin-top: 2rem;
        padding-top: 2rem;
        border-top: 1px solid var(--border);
    }

    /* Debug panel buttons for neutral colors */
    .neutral-primary .bg-primary\/10,
    .neutral-primary .bg-primary\/20 {
        background-color: rgba(0, 0, 0, 0.1) !important;
    }

    .neutral-primary .bg-primary\/10:hover,
    .neutral-primary .bg-primary\/20:hover,
    .neutral-primary .hover\:bg-primary\/30:hover {
        background-color: rgba(0, 0, 0, 0.2) !important;
    }

    .dark.neutral-primary .bg-primary\/10,
    .dark.neutral-primary .bg-primary\/20 {
        background-color: rgba(255, 255, 255, 0.1) !important;
    }

    .dark.neutral-primary .bg-primary\/10:hover,
    .dark.neutral-primary .bg-primary\/20:hover,
    .dark.neutral-primary .hover\:bg-primary\/30:hover {
        background-color: rgba(255, 255, 255, 0.2) !important;
    }
    </style>
    {% endif %}
</div>

{% endblock %}

{% block scripts %}
<style>
    /* Tab styles */
    .tabs-container {
        border-bottom: 1px solid var(--border);
    }

    .tabs {
        display: flex;
        overflow-x: auto;
    }

    .tab {
        padding: 0.75rem 1rem;
        font-size: 0.875rem;
        font-weight: 500;
        color: var(--muted-foreground);
        border-bottom: 2px solid transparent;
        transition: all 0.2s;
        cursor: pointer;
        white-space: nowrap;
    }

    .tab:hover {
        color: var(--foreground);
    }

    .tab.active {
        color: var(--primary);
        border-bottom-color: var(--primary);
    }

    .tab-content {
        display: none;
    }

    .tab-content.active {
        display: block !important;
    }

    .tab-content.hidden {
        display: none !important;
    }
</style>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize tabs
        initTabs();

        // Initialize color picker sync
        initColorPicker();

        // Apply consistent styling to all tab contents immediately
        document.querySelectorAll('.tab-content').forEach(content => {
            content.style.marginTop = '0';
            content.style.paddingTop = '0';
            content.style.borderTop = 'none';
        });

        // Add a small delay to ensure styles are applied after browser rendering
        setTimeout(() => {
            document.querySelectorAll('.tab-content').forEach(content => {
                content.style.marginTop = '0';
                content.style.paddingTop = '0';
                content.style.borderTop = 'none';
            });
        }, 100);

        // Lucide icons are now initialized automatically

        // We're now using flash messages converted to toasts via handleFlashMessages() in utils.js
        // This happens automatically on page load and provides a consistent notification experience

        // Clean URL parameters if they exist (for backward compatibility)
        const urlParams = new URLSearchParams(window.location.search);
        if (urlParams.has('status')) {
            // Keep the hash for tab state
            const newUrl = window.location.pathname + window.location.hash;
            window.history.replaceState({}, document.title, newUrl);
        }
    });

    // Tab functionality
    function initTabs() {
        const tabs = document.querySelectorAll('.tab');
        const tabContents = document.querySelectorAll('.tab-content');
        const activeTabInput = document.getElementById('active_tab');

        // Apply consistent styling to all tab contents
        tabContents.forEach(content => {
            content.style.marginTop = '0';
            content.style.paddingTop = '0';

            // Hide all tab contents except the active one
            if (!content.classList.contains('active')) {
                content.classList.add('hidden');
            }
        });

        tabs.forEach(tab => {
            tab.addEventListener('click', () => {
                // Get the tab ID
                const tabId = tab.getAttribute('data-tab');

                // Update active tab input
                activeTabInput.value = tabId;

                // Remove active class from all tabs and contents
                tabs.forEach(t => t.classList.remove('active'));
                tabContents.forEach(content => {
                    content.classList.remove('active');
                    content.classList.add('hidden');
                });

                // Add active class to clicked tab
                tab.classList.add('active');

                // Show corresponding content
                const content = document.getElementById(`${tabId}-tab`);
                if (content) {
                    content.classList.add('active');
                    content.classList.remove('hidden');
                    // Ensure consistent spacing
                    content.style.marginTop = '0';
                    content.style.paddingTop = '0';
                    content.style.borderTop = 'none';

                    // Force a reflow to ensure styles are applied
                    void content.offsetWidth;
                }
            });
        });
    }

    // Color palette selection
    function initColorPicker() {
        const colorInput = document.getElementById('primary_color');
        const hexInput = document.getElementById('primary_color_hex');
        const colorOptions = document.querySelectorAll('.color-option');

        if (colorInput && hexInput) {
            // Set initial selected color
            const initialColor = colorInput.value;
            updateSelectedColor(initialColor);
            updatePreviewColor(initialColor);

            // Add click event to all color options
            colorOptions.forEach(option => {
                const color = option.getAttribute('data-color');

                // Check if this is the currently selected color
                if (color.toLowerCase() === initialColor.toLowerCase()) {
                    option.classList.add('selected');
                    option.setAttribute('aria-selected', 'true');
                } else {
                    option.classList.remove('selected');
                    option.setAttribute('aria-selected', 'false');
                }

                // Hide checkmark for non-selected colors
                const checkmark = option.querySelector('.color-check');
                if (checkmark) {
                    checkmark.style.display = color.toLowerCase() === initialColor.toLowerCase() ? 'block' : 'none';
                }

                // Add click event
                option.addEventListener('click', () => {
                    const selectedColor = option.getAttribute('data-color');
                    const colorName = option.getAttribute('data-name');

                    // Update hidden inputs
                    colorInput.value = selectedColor;
                    hexInput.value = selectedColor;

                    // Update UI
                    updateSelectedColor(selectedColor);
                    updatePreviewColor(selectedColor);
                });
            });
        }
    }

    // Update selected color in the UI
    function updateSelectedColor(color) {
        const colorOptions = document.querySelectorAll('.color-option');

        colorOptions.forEach(option => {
            const optionColor = option.getAttribute('data-color');
            const checkmark = option.querySelector('.color-check');

            if (optionColor.toLowerCase() === color.toLowerCase()) {
                option.classList.add('selected');
                option.setAttribute('aria-selected', 'true');
                if (checkmark) checkmark.style.display = 'block';
                option.style.borderColor = color;
                option.style.borderWidth = '2px';
            } else {
                option.classList.remove('selected');
                option.setAttribute('aria-selected', 'false');
                if (checkmark) checkmark.style.display = 'none';
                option.style.borderColor = '';
                option.style.borderWidth = '';
            }
        });
    }

    // Function to update color preview
    function updatePreviewColor(color) {
        // Convert hex to RGB
        const r = parseInt(color.slice(1, 3), 16);
        const g = parseInt(color.slice(3, 5), 16);
        const b = parseInt(color.slice(5, 7), 16);

        // Check if this is a neutral color (zinc, slate, stone, gray, neutral)
        const colorNeutralList = ['#71717a', '#64748b', '#78716c', '#6b7280', '#525252'];
        // Make sure we do a case-insensitive comparison
        const isNeutralColor = colorNeutralList.some(neutralColor => neutralColor.toLowerCase() === color.toLowerCase());

        // Determine text color based on shadcn style - always black or white based on contrast
        const isDarkMode = document.documentElement.classList.contains('dark');
        const textColor = (r * 0.299 + g * 0.587 + b * 0.114) > 186 ? '#000000' : '#ffffff';

        // For neutral colors in buttons, use white/black based on theme
        const buttonBgColor = isNeutralColor ? (isDarkMode ? '#ffffff' : '#000000') : color;
        const buttonTextColor = isNeutralColor ? (isDarkMode ? '#000000' : '#ffffff') : textColor;

        // Add a preview element if it doesn't exist
        let previewSection = document.getElementById('color-preview-section');
        if (!previewSection) {
            const appearanceTab = document.getElementById('appearance-tab');
            if (appearanceTab) {
                previewSection = document.createElement('div');
                previewSection.id = 'color-preview-section';
                previewSection.className = 'mt-6 p-4 border border-border rounded-md';
                previewSection.innerHTML = `
                    <h4 class="font-semibold mb-3">Color Preview</h4>
                    <div class="flex flex-wrap gap-4">
                        <button id="color-preview-btn" class="px-4 py-2 rounded-md font-medium">Primary Button</button>
                        <div class="flex items-center">
                            <span id="color-preview-badge" class="px-2 py-1 text-xs rounded-md font-medium">Badge</span>
                        </div>
                    </div>
                `;
                appearanceTab.appendChild(previewSection);
            }
        }

        // Update the preview elements with the new color
        const previewBtn = document.getElementById('color-preview-btn');
        const previewBadge = document.getElementById('color-preview-badge');

        if (previewBtn) {
            if (isNeutralColor) {
                // For neutral colors, use white/black based on theme
                previewBtn.style.backgroundColor = buttonBgColor;
                previewBtn.style.color = buttonTextColor;
                previewBtn.style.borderColor = buttonBgColor;
            } else {
                // For other colors, use the actual color
                previewBtn.style.backgroundColor = color;
                previewBtn.style.color = buttonTextColor;
                previewBtn.style.borderColor = color;
            }
        }

        if (previewBadge) {
            previewBadge.style.backgroundColor = `rgba(${r}, ${g}, ${b}, 0.15)`;
            previewBadge.style.color = color;
        }

        // Update CSS variables for the application
        document.documentElement.style.setProperty('--primary-hex', color);
        document.documentElement.style.setProperty('--primary-rgb', `${r}, ${g}, ${b}`);

        // Convert to HSL but don't update the global --primary variable
        // This ensures we don't affect the Save Changes button
        const hsl = rgbToHsl(r, g, b);

        // Use the variables from above for consistency
        // No need to redeclare them

        // Helper function to convert RGB to HSL
        function rgbToHsl(r, g, b) {
            r /= 255;
            g /= 255;
            b /= 255;

            const max = Math.max(r, g, b);
            const min = Math.min(r, g, b);
            let h, s, l = (max + min) / 2;

            if (max === min) {
                h = s = 0; // achromatic
            } else {
                const d = max - min;
                s = l > 0.5 ? d / (2 - max - min) : d / (max + min);

                switch (max) {
                    case r: h = (g - b) / d + (g < b ? 6 : 0); break;
                    case g: h = (b - r) / d + 2; break;
                    case b: h = (r - g) / d + 4; break;
                }

                h /= 6;
            }

            return [Math.round(h * 360), Math.round(s * 100), Math.round(l * 100)];
        }

        // We don't want to update global buttons or CSS variables
        // Only the preview elements should be affected by color changes
        // This ensures the Save Changes button keeps using the global theme
    }


</script>

<style>
    /* Tab styles */
    .tabs-container {
        border-bottom: 1px solid var(--border);
    }

    .tabs {
        display: flex;
        overflow-x: auto;
        scrollbar-width: none; /* Firefox */
    }

    .tabs::-webkit-scrollbar {
        display: none; /* Chrome, Safari, Edge */
    }

    .tab {
        padding: 0.75rem 1rem;
        font-size: 0.875rem;
        font-weight: 500;
        color: var(--muted-foreground);
        border-bottom: 2px solid transparent;
        cursor: pointer;
        white-space: nowrap;
        display: flex;
        align-items: center;
        transition: all 0.2s;
    }

    .tab:hover {
        color: var(--foreground);
    }

    .tab.active {
        color: var(--primary);
        border-bottom-color: var(--primary);
    }

    /* Color picker styles */
    .color-option {
        transition: all 0.15s ease;
    }

    .color-option.selected {
        border-width: 2px;
    }

    .color-check {
        display: none;
    }

    /* Tab content styles */
    .tab-content {
        display: none;
        margin-top: 0;
    }

    .tab-content.active {
        display: block;
    }

    .tab-content + .tab-content {
        margin-top: 0;
    }

    /* Form styles */
    .form-label {
        display: block;
        font-size: 0.875rem;
        font-weight: 500;
        margin-bottom: 0.5rem;
    }
</style>
{% endblock %}

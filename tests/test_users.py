import unittest
from datetime import datetime, date
from flask_testing import TestCase

from app import create_app, db
from app.models import User, EmployeeDetail, Activity


class TestUsers(TestCase):
    """Test cases for User model and user management functionality"""

    def create_app(self):
        """Create and configure a Flask app for testing"""
        app = create_app()
        app.config.update(
            TESTING=True,
            SQLALCHEMY_DATABASE_URI='sqlite:///:memory:',
            WTF_CSRF_ENABLED=False,
            SERVER_NAME='localhost.localdomain',
            SECRET_KEY='test_secret_key'
        )
        with app.app_context():
            db.create_all()
        return app

    def setUp(self):
        """Set up test environment before each test"""
        db.session.remove()
        db.drop_all()
        db.create_all()

        # Create test users with different roles
        admin_user = User(
            name='Admin User',
            email='<EMAIL>',
            role='Admin'
        )
        admin_user.set_password('Admin@123')

        manager_user = User(
            name='Manager User',
            email='<EMAIL>',
            role='Manager'
        )
        manager_user.set_password('Manager@123')

        regular_user = User(
            name='Regular User',
            email='<EMAIL>',
            role='User'
        )
        regular_user.set_password('User@123')

        db.session.add_all([admin_user, manager_user, regular_user])
        db.session.commit()

        # Create a business unit and segment for tests
        from app.models import BusinessUnit, BusinessSegment
        self.business_unit = BusinessUnit(
            name='Test Business Unit',
            code='TBU',
            description='Test Business Unit Description'
        )
        db.session.add(self.business_unit)
        db.session.commit()

        self.business_segment = BusinessSegment(
            name='Test Business Segment',
            code='TBS',
            description='Test Business Segment Description',
            business_unit_id=self.business_unit.id
        )
        db.session.add(self.business_segment)
        db.session.commit()

    def tearDown(self):
        """Clean up test environment after each test"""
        db.session.remove()
        db.drop_all()

    def test_user_creation(self):
        """Test basic user creation and retrieval"""
        # Create a new user
        user = User(
            name='Test User',
            email='<EMAIL>',
            role='User'
        )
        user.set_password('Test@123')
        db.session.add(user)
        db.session.commit()

        # Retrieve the user and check attributes
        saved_user = User.query.filter_by(email='<EMAIL>').first()
        self.assertIsNotNone(saved_user)
        self.assertEqual(saved_user.name, 'Test User')
        self.assertEqual(saved_user.role, 'User')
        self.assertTrue(saved_user.is_active)
        self.assertFalse(saved_user.is_deleted)
        self.assertTrue(saved_user.check_password('Test@123'))
        self.assertFalse(saved_user.check_password('WrongPassword'))

    def test_user_role_properties(self):
        """Test user role-based property functions"""
        admin = User.query.filter_by(email='<EMAIL>').first()
        manager = User.query.filter_by(email='<EMAIL>').first()
        user = User.query.filter_by(email='<EMAIL>').first()

        # Test admin role
        self.assertTrue(admin.is_admin)
        self.assertFalse(admin.is_manager)

        # Test manager role
        self.assertFalse(manager.is_admin)
        self.assertTrue(manager.is_manager)

        # Test regular user role
        self.assertFalse(user.is_admin)
        self.assertFalse(user.is_manager)

    def test_user_password_requirements(self):
        """Test password strength validation"""
        user = User(name='Password Test', email='<EMAIL>')

        # Test weak passwords
        with self.assertRaises(ValueError):
            user.set_password('short')  # Too short

        with self.assertRaises(ValueError):
            user.set_password('onlyletters')  # No numbers or special chars

        with self.assertRaises(ValueError):
            user.set_password('12345678')  # No letters or special chars

        # Test strong password
        user.set_password('Strong@123')
        self.assertTrue(user.check_password('Strong@123'))

    def test_soft_delete(self):
        """Test soft delete functionality"""
        user = User.query.filter_by(email='<EMAIL>').first()

        # Soft delete the user
        user.soft_delete()

        # User should still exist but be marked as deleted
        deleted_user = User.query.filter_by(email='<EMAIL>').first()
        self.assertIsNotNone(deleted_user)
        self.assertTrue(deleted_user.is_deleted)
        self.assertFalse(deleted_user.is_active)
        self.assertIsNotNone(deleted_user.deleted_at)

    def test_to_dict_method(self):
        """Test User to_dict serialization method"""
        # Create a user with employee details
        user = User(
            name='Test User',
            email='<EMAIL>',
            role='User'
        )
        user.set_password('Password@123')
        db.session.add(user)
        db.session.commit()

        # Add employee details to the user
        employee_detail = EmployeeDetail(
            user_id=user.id,
            position='Software Engineer',
            phone='+1234567890',
            business_unit_id=self.business_unit.id,
            business_segment_id=self.business_segment.id
        )
        db.session.add(employee_detail)
        db.session.commit()

        # Refresh the user to make sure the relationship is loaded
        db.session.refresh(user)

        # Test serialization
        user_dict = user.to_dict()

        # Basic user properties
        self.assertEqual(user_dict['name'], 'Test User')
        self.assertEqual(user_dict['email'], '<EMAIL>')
        self.assertEqual(user_dict['role'], 'User')

        # Employee details
        self.assertIn('employee_details', user_dict)
        self.assertEqual(user_dict['employee_details']['position'], 'Software Engineer')
        self.assertEqual(user_dict['employee_details']['phone'], '+1234567890')
        self.assertEqual(user_dict['employee_details']['business_unit'], 'Test Business Unit')
        self.assertEqual(user_dict['employee_details']['business_segment'], 'Test Business Segment')

    def test_get_by_email(self):
        """Test getting a user by email"""
        user = User.get_by_email('<EMAIL>')
        self.assertIsNotNone(user)
        self.assertEqual(user.name, 'Admin User')
        self.assertEqual(user.role, 'Admin')

        # Test with non-existent email
        nonexistent_user = User.get_by_email('<EMAIL>')
        self.assertIsNone(nonexistent_user)

    def test_user_activities(self):
        """Test user activity logging and retrieval"""
        user = User.query.filter_by(email='<EMAIL>').first()

        # Create some activities for the user
        for i in range(5):
            Activity.log(
                user_id=user.id,
                action=f'Test Action {i}',
                entity_type='Test',
                entity_id=i,
                details=f'Test details {i}',
                ip_address='127.0.0.1'
            )

        # Test getting recent activities
        activities = user.get_recent_activities(limit=3)
        self.assertEqual(len(activities), 3)
        self.assertEqual(activities[0].action, 'Test Action 4')  # Most recent first
        self.assertEqual(activities[1].action, 'Test Action 3')
        self.assertEqual(activities[2].action, 'Test Action 2')

    def test_admin_user_route_access(self):
        """Test admin user access to admin routes"""
        with self.client:
            # Login as admin
            response = self.client.post(
                '/auth/login',
                data={
                    'email': '<EMAIL>',
                    'password': 'Admin@123'
                },
                follow_redirects=True
            )
            self.assertEqual(response.status_code, 200)

            # Admin should have access to admin dashboard
            # Just check for a valid response and some known content
            response = self.client.get('/admin/', follow_redirects=True)
            self.assertEqual(response.status_code, 200)
            self.assertIn(b'admin', response.data.lower())  # Check for any admin content

            # Admin should have access to user management
            response = self.client.get('/admin/users', follow_redirects=True)
            self.assertEqual(response.status_code, 200)
            self.assertIn(b'user', response.data.lower())  # Check for any user-related content

    def test_regular_user_route_restrictions(self):
        """Test regular user restriction from admin routes"""
        with self.client:
            # Login as regular user
            response = self.client.post(
                '/auth/login',
                data={
                    'email': '<EMAIL>',
                    'password': 'User@123'
                },
                follow_redirects=True
            )
            self.assertEqual(response.status_code, 200)

            # Regular user should be restricted from admin dashboard
            # Either redirected or given a permission error
            response = self.client.get('/admin/', follow_redirects=True)
            self.assertEqual(response.status_code, 200)
            # Check for a redirection or access denied message. Exact wording might vary.
            self.assertTrue(
                b'permission' in response.data.lower() or
                b'not authorized' in response.data.lower() or
                b'access denied' in response.data.lower() or
                b'forbidden' in response.data.lower()
            )

            # Regular user should be restricted from user management
            response = self.client.get('/admin/users', follow_redirects=True)
            self.assertEqual(response.status_code, 200)
            self.assertTrue(
                b'permission' in response.data.lower() or
                b'not authorized' in response.data.lower() or
                b'access denied' in response.data.lower() or
                b'forbidden' in response.data.lower()
            )


if __name__ == '__main__':
    unittest.main()

/**
 * Maintenance Page JavaScript
 *
 * This file contains the JavaScript code for the maintenance page.
 */

document.addEventListener('DOMContentLoaded', function() {
    // Initialize Lucide icons
    if (window.lucide) {
        window.lucide.createIcons();
    }

    // Elements
    const maintenanceLog = document.getElementById('maintenance-log');
    const runMaintenanceBtn = document.getElementById('run-maintenance-btn');
    const saveRetentionBtn = document.getElementById('save-retention-btn');
    const retentionDaysInput = document.getElementById('retention-days');
    const autoMaintenanceCheckbox = document.getElementById('auto-maintenance');
    const delete30DaysBtn = document.getElementById('delete-30-days-btn');
    const delete90DaysBtn = document.getElementById('delete-90-days-btn');
    const deleteInfoBtn = document.getElementById('delete-info-btn');
    const deleteAllLogsBtn = document.getElementById('delete-all-logs-btn');
    const cleanupTokensBtn = document.getElementById('cleanup-tokens-btn');
    const clearDashboardCacheBtn = document.getElementById('clear-dashboard-cache-btn');
    const clearBusinessCacheBtn = document.getElementById('clear-business-cache-btn');
    const clearEmployeeCacheBtn = document.getElementById('clear-employee-cache-btn');
    const clearAllCacheBtn = document.getElementById('clear-all-cache-btn');
    const refreshDbHealthBtn = document.getElementById('refresh-db-health-btn');
    const toggleProfilerBtn = document.getElementById('toggle-profiler-btn');
    const profilerStatusText = document.getElementById('profiler-status-text');
    const profilerSection = document.getElementById('profiler-section');
    const thresholdInput = document.getElementById('threshold-ms');
    const applyThresholdBtn = document.getElementById('apply-threshold-btn');

    // Helper function to add log entry
    function addLogEntry(message, type = 'info') {
        const now = new Date();
        const timestamp = now.toLocaleTimeString();
        const entry = document.createElement('div');
        entry.className = type === 'error' ? 'text-destructive' :
                         (type === 'success' ? 'text-primary' : 'text-foreground');
        entry.innerHTML = `<span class="text-muted-foreground">[${timestamp}]</span> ${message}`;
        maintenanceLog.appendChild(entry);
        maintenanceLog.scrollTop = maintenanceLog.scrollHeight;
    }

    // Helper function to get CSRF token
    function getCsrfToken() {
        // Primary: Try to get from the form input (Flask-WTF adds this)
        const csrfInput = document.querySelector('input[name="csrf_token"]');
        if (csrfInput && csrfInput.value) {
            return csrfInput.value;
        }

        // Secondary: Try to get the CSRF token from the meta tag
        const metaToken = document.querySelector('meta[name="csrf-token"]');
        if (metaToken && metaToken.getAttribute('content')) {
            return metaToken.getAttribute('content');
        }

        // Fallback: Check for Flask's CSRF token in a cookie
        const cookies = document.cookie.split(';');
        for (let cookie of cookies) {
            const [name, value] = cookie.trim().split('=');
            if (name === 'csrf_token' && value) {
                return decodeURIComponent(value);
            }
        }

        // If no CSRF token is found, log a warning and return an empty string
        console.warn('CSRF token not found. API requests may fail.');
        return '';
    }

    // Helper function to create standard fetch options
    function createFetchOptions(method, body = null) {
        const options = {
            method: method,
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCsrfToken(),
                'Accept': 'application/json'
            },
            credentials: 'same-origin'
        };

        if (body) {
            options.body = JSON.stringify(body);
        }

        return options;
    }

    // Run maintenance
    runMaintenanceBtn.addEventListener('click', function() {
        addLogEntry('Running maintenance tasks...');
        runMaintenanceBtn.disabled = true;

        fetch('/api/system/maintenance', createFetchOptions('POST'))
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                addLogEntry(`Maintenance completed successfully:`, 'success');
                addLogEntry(`- Deleted ${data.result.deleted_activities} old activity logs`, 'success');
                addLogEntry(`- Deleted ${data.result.deleted_tokens} expired password tokens`, 'success');
                showToast('Maintenance tasks completed successfully', { type: 'success' });
            } else {
                addLogEntry(`Error: ${data.message}`, 'error');
                showToast('Maintenance failed', { type: 'error' });
            }
        })
        .catch(error => {
            addLogEntry(`Error: ${error.message}`, 'error');
            showToast('Maintenance failed', { type: 'error' });
        })
        .finally(() => {
            runMaintenanceBtn.disabled = false;
        });
    });

    // Save retention period
    saveRetentionBtn.addEventListener('click', function() {
        const days = parseInt(retentionDaysInput.value);
        if (isNaN(days) || days < 1) {
            showToast('Please enter a valid number of days', { type: 'error' });
            return;
        }

        addLogEntry(`Saving retention period: ${days} days`);

        fetch('/api/settings/update', createFetchOptions('POST', {
            key: 'activity_log_retention_days',
            value: days
        }))
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                addLogEntry(`Retention period updated to ${days} days`, 'success');
                showToast('Retention period updated', { type: 'success' });
            } else {
                addLogEntry(`Error: ${data.message}`, 'error');
                showToast('Failed to update retention period', { type: 'error' });
            }
        })
        .catch(error => {
            addLogEntry(`Error: ${error.message}`, 'error');
            showToast('Failed to update retention period', { type: 'error' });
        });
    });

    // No need for toggle state management with standard checkbox

    // Add click handler for the toggle container label
    const toggleContainer = document.getElementById('toggle-container');
    if (toggleContainer) {
        toggleContainer.addEventListener('click', function(e) {
            // Don't trigger if clicking on the checkbox itself
            if (e.target !== autoMaintenanceCheckbox) {
                autoMaintenanceCheckbox.checked = !autoMaintenanceCheckbox.checked;
                // Trigger the change event
                autoMaintenanceCheckbox.dispatchEvent(new Event('change'));
            }
        });
    }

    // Toggle automatic maintenance
    autoMaintenanceCheckbox.addEventListener('change', function() {
        const enabled = this.checked;

        fetch('/api/settings/update', createFetchOptions('POST', {
            key: 'enable_automatic_maintenance',
            value: enabled
        }))
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                addLogEntry(`Automatic maintenance ${enabled ? 'enabled' : 'disabled'}`, 'success');
                showToast(`Automatic maintenance ${enabled ? 'enabled' : 'disabled'}`, { type: 'success' });
            } else {
                addLogEntry(`Error: ${data.message}`, 'error');
                showToast('Failed to update setting', { type: 'error' });
                // Revert checkbox state
                this.checked = !enabled;
            }
        })
        .catch(error => {
            addLogEntry(`Error: ${error.message}`, 'error');
            showToast('Failed to update setting', { type: 'error' });
            // Revert checkbox state
            this.checked = !enabled;
        });
    });

    // Delete logs older than 30 days
    delete30DaysBtn.addEventListener('click', function() {
        if (!confirm('Delete all logs older than 30 days?')) return;

        addLogEntry('Deleting logs older than 30 days...');

        fetch('/api/activities/delete-older-than/30', createFetchOptions('POST'))
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                addLogEntry(`Deleted ${data.affected_count} logs older than 30 days`, 'success');
                showToast(`Deleted ${data.affected_count} logs`, { type: 'success' });
            } else {
                addLogEntry(`Error: ${data.message}`, 'error');
                showToast('Failed to delete logs', { type: 'error' });
            }
        })
        .catch(error => {
            addLogEntry(`Error: ${error.message}`, 'error');
            showToast('Failed to delete logs', { type: 'error' });
        });
    });

    // Delete logs older than 90 days
    delete90DaysBtn.addEventListener('click', function() {
        if (!confirm('Delete all logs older than 90 days?')) return;

        addLogEntry('Deleting logs older than 90 days...');

        fetch('/api/activities/delete-older-than/90', createFetchOptions('POST'))
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                addLogEntry(`Deleted ${data.affected_count} logs older than 90 days`, 'success');
                showToast(`Deleted ${data.affected_count} logs`, { type: 'success' });
            } else {
                addLogEntry(`Error: ${data.message}`, 'error');
                showToast('Failed to delete logs', { type: 'error' });
            }
        })
        .catch(error => {
            addLogEntry(`Error: ${error.message}`, 'error');
            showToast('Failed to delete logs', { type: 'error' });
        });
    });

    // Delete info logs
    deleteInfoBtn.addEventListener('click', function() {
        if (!confirm('Delete all info-level logs?')) return;

        addLogEntry('Deleting info-level logs...');

        fetch('/api/activities/delete-by-severity/info', createFetchOptions('POST'))
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                addLogEntry(`Deleted ${data.affected_count} info-level logs`, 'success');
                showToast(`Deleted ${data.affected_count} logs`, { type: 'success' });
            } else {
                addLogEntry(`Error: ${data.message}`, 'error');
                showToast('Failed to delete logs', { type: 'error' });
            }
        })
        .catch(error => {
            addLogEntry(`Error: ${error.message}`, 'error');
            showToast('Failed to delete logs', { type: 'error' });
        });
    });

    // Delete all logs
    deleteAllLogsBtn.addEventListener('click', function() {
        if (!confirm('WARNING: Delete ALL activity logs? This action cannot be undone.')) return;
        if (!confirm('Are you REALLY sure? This will delete ALL activity history.')) return;

        addLogEntry('Deleting ALL activity logs...');

        fetch('/api/activities/delete-all', createFetchOptions('POST', {
            confirmation: 'DELETE_ALL_LOGS'
        }))
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                addLogEntry(`Deleted ${data.affected_count} activity logs`, 'success');
                showToast(`Deleted ${data.affected_count} logs`, { type: 'success' });
            } else {
                addLogEntry(`Error: ${data.message}`, 'error');
                showToast('Failed to delete logs', { type: 'error' });
            }
        })
        .catch(error => {
            addLogEntry(`Error: ${error.message}`, 'error');
            showToast('Failed to delete logs', { type: 'error' });
        });
    });

    // Clean up tokens
    cleanupTokensBtn.addEventListener('click', function() {
        addLogEntry('Cleaning up expired password tokens...');

        fetch('/api/system/maintenance', createFetchOptions('POST'))
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                addLogEntry(`Deleted ${data.result.deleted_tokens} expired password tokens`, 'success');
                showToast(`Deleted ${data.result.deleted_tokens} tokens`, { type: 'success' });
            } else {
                addLogEntry(`Error: ${data.message}`, 'error');
                showToast('Failed to clean up tokens', { type: 'error' });
            }
        })
        .catch(error => {
            addLogEntry(`Error: ${error.message}`, 'error');
            showToast('Failed to clean up tokens', { type: 'error' });
        });
    });

    // Clear dashboard cache
    clearDashboardCacheBtn.addEventListener('click', function() {
        addLogEntry('Clearing dashboard cache...');

        fetch('/api/system/clear-cache', createFetchOptions('POST', {
            cache_type: 'dashboard'
        }))
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                addLogEntry('Dashboard cache cleared successfully', 'success');
                showToast('Dashboard cache cleared', { type: 'success' });
            } else {
                addLogEntry(`Error: ${data.message}`, 'error');
                showToast('Failed to clear cache', { type: 'error' });
            }
        })
        .catch(error => {
            addLogEntry(`Error: ${error.message}`, 'error');
            showToast('Failed to clear cache', { type: 'error' });
        });
    });

    // Clear business cache
    clearBusinessCacheBtn.addEventListener('click', function() {
        addLogEntry('Clearing business cache...');

        fetch('/api/system/clear-cache', createFetchOptions('POST', {
            cache_type: 'business'
        }))
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                addLogEntry('Business cache cleared successfully', 'success');
                showToast('Business cache cleared', { type: 'success' });
            } else {
                addLogEntry(`Error: ${data.message}`, 'error');
                showToast('Failed to clear cache', { type: 'error' });
            }
        })
        .catch(error => {
            addLogEntry(`Error: ${error.message}`, 'error');
            showToast('Failed to clear cache', { type: 'error' });
        });
    });

    // Clear employee cache
    clearEmployeeCacheBtn.addEventListener('click', function() {
        addLogEntry('Clearing employee cache...');

        fetch('/api/system/clear-cache', createFetchOptions('POST', {
            cache_type: 'employee'
        }))
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                addLogEntry('Employee cache cleared successfully', 'success');
                showToast('Employee cache cleared', { type: 'success' });
            } else {
                addLogEntry(`Error: ${data.message}`, 'error');
                showToast('Failed to clear cache', { type: 'error' });
            }
        })
        .catch(error => {
            addLogEntry(`Error: ${error.message}`, 'error');
            showToast('Failed to clear cache', { type: 'error' });
        });
    });

    // Clear all cache
    clearAllCacheBtn.addEventListener('click', function() {
        if (!confirm('Clear all application cache? This will force the application to reload all data from the database.')) return;

        addLogEntry('Clearing all application cache...');

        fetch('/api/system/clear-cache', createFetchOptions('POST', {
            cache_type: 'all'
        }))
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                addLogEntry('All application cache cleared successfully', 'success');
                showToast('All cache cleared', { type: 'success' });
            } else {
                addLogEntry(`Error: ${data.message}`, 'error');
                showToast('Failed to clear cache', { type: 'error' });
            }
        })
        .catch(error => {
            addLogEntry(`Error: ${error.message}`, 'error');
            showToast('Failed to clear cache', { type: 'error' });
        });
    });

    // Database health functions
    let profilerEnabled = false;

    function refreshDbHealth() {
        addLogEntry('Fetching database health information...');

        fetch('/api/system/db-health', createFetchOptions('GET'))
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                updateDbHealthUI(data);
                addLogEntry('Database health information updated', 'success');
            } else {
                addLogEntry(`Error: ${data.message}`, 'error');
                showToast('Failed to fetch database health', { type: 'error' });
            }
        })
        .catch(error => {
            addLogEntry(`Error: ${error.message}`, 'error');
            showToast('Failed to fetch database health', { type: 'error' });
        });
    }

    function updateDbHealthUI(data) {
        // Update pool status
        const poolStatus = data.pool_status;
        const poolStatusEl = document.getElementById('pool-status');
        if (poolStatus) {
            let poolHtml = '';

            // Add engine type if available
            if (poolStatus.engine_type) {
                poolHtml += `
                <div class="flex justify-between py-1.5 border-b border-border/60">
                    <span class="text-muted-foreground">Engine Type:</span>
                    <span class="font-mono font-medium">${poolStatus.engine_type}</span>
                </div>`;
            }

            poolHtml += `
                <div class="flex justify-between py-1.5 border-b border-border/60">
                    <span class="text-muted-foreground">Pool Size:</span>
                    <span class="font-mono font-medium">${poolStatus.pool_size || '-'}</span>
                </div>
                <div class="flex justify-between py-1.5 border-b border-border/60">
                    <span class="text-muted-foreground">Checked In:</span>
                    <span class="font-mono font-medium">${poolStatus.checkedin || '-'}</span>
                </div>
                <div class="flex justify-between py-1.5 border-b border-border/60">
                    <span class="text-muted-foreground">Checked Out:</span>
                    <span class="font-mono font-medium">${poolStatus.checkedout || '-'}</span>
                </div>
                <div class="flex justify-between py-1.5">
                    <span class="text-muted-foreground">Overflow:</span>
                    <span class="font-mono font-medium">${poolStatus.overflow || '-'}</span>
                </div>
            `;

            // Add SQLite info message if engine is SQLite
            if (poolStatus.engine_type && poolStatus.engine_type.toLowerCase().includes('sqlite')) {
                poolHtml += `
                <div class="mt-3 text-xs text-muted-foreground bg-primary/5 p-2 rounded-md border border-primary/10">
                    <p>SQLite uses a file-based database and doesn't maintain a traditional connection pool.</p>
                </div>`;
            }

            poolStatusEl.innerHTML = poolHtml;
        }

        // Update database statistics
        const stats = data.statistics;
        const dbStatsEl = document.getElementById('db-stats');
        if (stats) {
            const tableCounts = stats.table_counts || {};
            const totalTables = Object.keys(tableCounts).length;
            const totalRecords = Object.values(tableCounts).reduce((sum, count) => sum + count, 0);

            dbStatsEl.innerHTML = `
                <div class="flex justify-between py-1.5 border-b border-border/60">
                    <span class="text-muted-foreground">Database Size:</span>
                    <span class="font-mono font-medium">${stats.db_size_mb ? stats.db_size_mb + ' MB' : '-'}</span>
                </div>
                <div class="flex justify-between py-1.5 border-b border-border/60">
                    <span class="text-muted-foreground">Total Tables:</span>
                    <span class="font-mono font-medium">${totalTables}</span>
                </div>
                <div class="flex justify-between py-1.5 border-b border-border/60">
                    <span class="text-muted-foreground">Total Records:</span>
                    <span class="font-mono font-medium">${totalRecords}</span>
                </div>
                <div class="flex justify-between py-1.5">
                    <span class="text-muted-foreground">Last Refresh:</span>
                    <span class="font-mono font-medium">${new Date().toLocaleTimeString()}</span>
                </div>
            `;
        }

        // Update profiler stats
        const profilerStats = data.query_profiler;
        if (profilerStats && profilerStats.count !== undefined) {
            profilerEnabled = true;
            profilerStatusText.textContent = 'Stop Profiler';
            profilerSection.classList.remove('hidden');

            const profilerStatsEl = document.getElementById('profiler-stats');
            profilerStatsEl.innerHTML = `
                <div class="bg-card/50 p-3 rounded-md border border-border/30">
                    <div class="text-xs text-muted-foreground mb-1">Queries</div>
                    <div class="font-mono text-lg font-semibold">${profilerStats.count}</div>
                </div>
                <div class="bg-card/50 p-3 rounded-md border border-border/30">
                    <div class="text-xs text-muted-foreground mb-1">Avg Time</div>
                    <div class="font-mono text-lg font-semibold">${profilerStats.avg_time ? profilerStats.avg_time.toFixed(2) + ' ms' : '-'}</div>
                </div>
                <div class="bg-card/50 p-3 rounded-md border border-border/30">
                    <div class="text-xs text-muted-foreground mb-1">Max Time</div>
                    <div class="font-mono text-lg font-semibold">${profilerStats.max_time ? profilerStats.max_time.toFixed(2) + ' ms' : '-'}</div>
                </div>
                <div class="bg-card/50 p-3 rounded-md border border-border/30">
                    <div class="text-xs text-muted-foreground mb-1">Slow Queries</div>
                    <div class="font-mono text-lg font-semibold">${profilerStats.slow_queries}</div>
                </div>
            `;
        } else {
            profilerEnabled = false;
            profilerStatusText.textContent = 'Start Profiler';
        }
    }

    function toggleProfiler() {
        const newState = !profilerEnabled;
        const threshold = parseInt(thresholdInput.value) || 100;

        addLogEntry(`${newState ? 'Starting' : 'Stopping'} query profiler...`);

        fetch('/api/system/query-profiler', createFetchOptions('POST', {
            enabled: newState,
            threshold_ms: threshold
        }))
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                profilerEnabled = data.enabled;
                profilerStatusText.textContent = profilerEnabled ? 'Stop Profiler' : 'Start Profiler';

                if (profilerEnabled) {
                    profilerSection.classList.remove('hidden');
                } else {
                    // Don't hide the section if we have stats to show
                    if (!data.stats || data.stats.count === 0) {
                        profilerSection.classList.add('hidden');
                    }
                }

                addLogEntry(data.message, 'success');
                showToast(data.message, { type: 'success' });

                // Update stats if available
                if (data.stats && data.stats.count !== undefined) {
                    const profilerStatsEl = document.getElementById('profiler-stats');
                    profilerStatsEl.innerHTML = `
                        <div class="bg-card/50 p-3 rounded-md border border-border/30">
                            <div class="text-xs text-muted-foreground mb-1">Queries</div>
                            <div class="font-mono text-lg font-semibold">${data.stats.count}</div>
                        </div>
                        <div class="bg-card/50 p-3 rounded-md border border-border/30">
                            <div class="text-xs text-muted-foreground mb-1">Avg Time</div>
                            <div class="font-mono text-lg font-semibold">${data.stats.avg_time ? data.stats.avg_time.toFixed(2) + ' ms' : '-'}</div>
                        </div>
                        <div class="bg-card/50 p-3 rounded-md border border-border/30">
                            <div class="text-xs text-muted-foreground mb-1">Max Time</div>
                            <div class="font-mono text-lg font-semibold">${data.stats.max_time ? data.stats.max_time.toFixed(2) + ' ms' : '-'}</div>
                        </div>
                        <div class="bg-card/50 p-3 rounded-md border border-border/30">
                            <div class="text-xs text-muted-foreground mb-1">Slow Queries</div>
                            <div class="font-mono text-lg font-semibold">${data.stats.slow_queries}</div>
                        </div>
                    `;
                }
            } else {
                addLogEntry(`Error: ${data.message}`, 'error');
                showToast('Failed to toggle profiler', { type: 'error' });
            }
        })
        .catch(error => {
            addLogEntry(`Error: ${error.message}`, 'error');
            showToast('Failed to toggle profiler', { type: 'error' });
        });
    }

    // Event listeners for database health
    refreshDbHealthBtn.addEventListener('click', refreshDbHealth);
    toggleProfilerBtn.addEventListener('click', toggleProfiler);
    applyThresholdBtn.addEventListener('click', function() {
        if (profilerEnabled) {
            toggleProfiler(); // Turn off
            setTimeout(toggleProfiler, 500); // Turn back on with new threshold
        } else {
            // Just update the threshold for next time
            addLogEntry(`Updated threshold to ${thresholdInput.value}ms`);
        }
    });

    // Initial database health check
    refreshDbHealth();

    // Add initial log entry
    addLogEntry('Maintenance page loaded. Ready to run tasks.');
});

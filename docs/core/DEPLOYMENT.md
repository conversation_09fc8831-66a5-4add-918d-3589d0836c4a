# Deployment Guide

**Last Updated:** January 2025  
**Version:** 1.0.0

## Overview

This guide covers deployment strategies for the Matrix App, from development to production environments. The application is designed to be deployed on various platforms with different configurations based on your infrastructure needs.

## Prerequisites

### System Requirements

**Minimum Requirements:**
- CPU: 2 cores
- RAM: 2GB
- Storage: 10GB
- OS: Ubuntu 20.04+, CentOS 8+, or similar

**Recommended for Production:**
- CPU: 4+ cores
- RAM: 8GB+
- Storage: 50GB+ SSD
- OS: Ubuntu 22.04 LTS

### Software Dependencies

**Required:**
- Python 3.11+
- Node.js 16+
- PostgreSQL 12+ (production) or SQLite (development)
- Nginx (recommended for production)

**Optional:**
- Redis 6+ (for caching and rate limiting)
- SSL certificate (Let's Encrypt recommended)
- Docker (for containerized deployment)

## Environment Configuration

### Environment Variables

Create a `.env` file with the following variables:

```bash
# Application Configuration
FLASK_CONFIG=production
SECRET_KEY=your-super-secret-key-here
DEBUG=False

# Database Configuration
DATABASE_URL=postgresql://username:password@localhost:5432/matrix_app

# Email Configuration
MAIL_SERVER=smtp.gmail.com
MAIL_PORT=587
MAIL_USE_TLS=True
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password
MAIL_DEFAULT_SENDER=<EMAIL>

# Redis Configuration (Optional)
REDIS_URL=redis://localhost:6379/0

# Security Configuration
JWT_SECRET_KEY=your-jwt-secret-key
WTF_CSRF_SECRET_KEY=your-csrf-secret-key

# Rate Limiting
RATELIMIT_STORAGE_URL=redis://localhost:6379/1
RATELIMIT_DEFAULT=300 per day, 60 per hour, 10 per minute

# Application Settings
PAGINATION_PER_PAGE=20
PAGINATION_PER_PAGE_ADMIN=50
```

### Production Configuration

**Security Settings:**
```bash
# SSL/HTTPS
SESSION_COOKIE_SECURE=True
REMEMBER_COOKIE_SECURE=True
PREFERRED_URL_SCHEME=https

# CSRF Protection
WTF_CSRF_ENABLED=True
WTF_CSRF_TIME_LIMIT=3600

# Session Security
PERMANENT_SESSION_LIFETIME=604800  # 7 days in seconds
```

## Database Setup

### PostgreSQL Installation

**Ubuntu/Debian:**
```bash
sudo apt update
sudo apt install postgresql postgresql-contrib
sudo systemctl start postgresql
sudo systemctl enable postgresql
```

**CentOS/RHEL:**
```bash
sudo dnf install postgresql postgresql-server
sudo postgresql-setup --initdb
sudo systemctl start postgresql
sudo systemctl enable postgresql
```

### Database Configuration

```bash
# Switch to postgres user
sudo -u postgres psql

# Create database and user
CREATE DATABASE matrix_app;
CREATE USER matrix_user WITH PASSWORD 'secure_password';
GRANT ALL PRIVILEGES ON DATABASE matrix_app TO matrix_user;
\q
```

### Database Migration

```bash
# Set environment variables
export FLASK_APP=run.py
export FLASK_CONFIG=production

# Run migrations
flask db upgrade

# Initialize with admin user
python run.py init-db
```

## Application Deployment

### Method 1: Traditional Deployment

**1. Server Setup:**
```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install Python and dependencies
sudo apt install python3.11 python3.11-venv python3-pip nginx

# Create application user
sudo useradd -m -s /bin/bash matrix
sudo usermod -aG sudo matrix
```

**2. Application Installation:**
```bash
# Switch to application user
sudo su - matrix

# Clone repository
git clone <repository-url> /home/<USER>/matrix-app
cd /home/<USER>/matrix-app

# Create virtual environment
python3.11 -m venv venv
source venv/bin/activate

# Install dependencies
pip install -r requirements.txt
npm install

# Build frontend assets
npm run build
```

**3. Gunicorn Configuration:**

Create `/home/<USER>/matrix-app/gunicorn.conf.py`:
```python
bind = "127.0.0.1:8000"
workers = 4
worker_class = "sync"
worker_connections = 1000
max_requests = 1000
max_requests_jitter = 100
timeout = 30
keepalive = 2
preload_app = True
user = "matrix"
group = "matrix"
```

**4. Systemd Service:**

Create `/etc/systemd/system/matrix-app.service`:
```ini
[Unit]
Description=Matrix App
After=network.target

[Service]
User=matrix
Group=matrix
WorkingDirectory=/home/<USER>/matrix-app
Environment=PATH=/home/<USER>/matrix-app/venv/bin
ExecStart=/home/<USER>/matrix-app/venv/bin/gunicorn -c gunicorn.conf.py run:app
ExecReload=/bin/kill -s HUP $MAINPID
Restart=always

[Install]
WantedBy=multi-user.target
```

**5. Start Services:**
```bash
sudo systemctl daemon-reload
sudo systemctl start matrix-app
sudo systemctl enable matrix-app
```

### Method 2: Docker Deployment

**1. Dockerfile:**
```dockerfile
FROM python:3.11-slim

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Install Node.js
RUN curl -fsSL https://deb.nodesource.com/setup_18.x | bash - \
    && apt-get install -y nodejs

# Copy requirements and install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy package.json and install Node dependencies
COPY package*.json ./
RUN npm ci --only=production

# Copy application code
COPY . .

# Build frontend assets
RUN npm run build

# Create non-root user
RUN useradd -m -u 1000 matrix && chown -R matrix:matrix /app
USER matrix

EXPOSE 8000

CMD ["gunicorn", "--bind", "0.0.0.0:8000", "--workers", "4", "run:app"]
```

**2. Docker Compose:**
```yaml
version: '3.8'

services:
  app:
    build: .
    ports:
      - "8000:8000"
    environment:
      - FLASK_CONFIG=production
      - DATABASE_URL=************************************/matrix_app
      - REDIS_URL=redis://redis:6379/0
    depends_on:
      - db
      - redis
    volumes:
      - ./instance:/app/instance

  db:
    image: postgres:15
    environment:
      - POSTGRES_DB=matrix_app
      - POSTGRES_USER=matrix
      - POSTGRES_PASSWORD=password
    volumes:
      - postgres_data:/var/lib/postgresql/data

  redis:
    image: redis:7-alpine
    volumes:
      - redis_data:/data

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - app

volumes:
  postgres_data:
  redis_data:
```

## Web Server Configuration

### Nginx Configuration

Create `/etc/nginx/sites-available/matrix-app`:
```nginx
server {
    listen 80;
    server_name your-domain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com;

    # SSL Configuration
    ssl_certificate /etc/letsencrypt/live/your-domain.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/your-domain.com/privkey.pem;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
    ssl_prefer_server_ciphers off;

    # Security Headers
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=63072000; includeSubDomains; preload";

    # Static Files
    location /static/ {
        alias /home/<USER>/matrix-app/app/static/;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # Application
    location / {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_redirect off;
        
        # Timeouts
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }

    # File Upload Limits
    client_max_body_size 10M;
}
```

Enable the site:
```bash
sudo ln -s /etc/nginx/sites-available/matrix-app /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

## SSL Certificate Setup

### Let's Encrypt with Certbot

```bash
# Install Certbot
sudo apt install certbot python3-certbot-nginx

# Obtain certificate
sudo certbot --nginx -d your-domain.com

# Auto-renewal
sudo crontab -e
# Add: 0 12 * * * /usr/bin/certbot renew --quiet
```

## Monitoring and Logging

### Application Logging

Configure logging in `config.py`:
```python
import logging
from logging.handlers import RotatingFileHandler

class ProductionConfig(Config):
    @staticmethod
    def init_app(app):
        Config.init_app(app)
        
        # Log to file
        file_handler = RotatingFileHandler(
            'logs/matrix-app.log',
            maxBytes=10240000,
            backupCount=10
        )
        file_handler.setFormatter(logging.Formatter(
            '%(asctime)s %(levelname)s: %(message)s [in %(pathname)s:%(lineno)d]'
        ))
        file_handler.setLevel(logging.INFO)
        app.logger.addHandler(file_handler)
        app.logger.setLevel(logging.INFO)
```

### System Monitoring

**Log Rotation:**
```bash
# Create logrotate configuration
sudo tee /etc/logrotate.d/matrix-app << EOF
/home/<USER>/matrix-app/logs/*.log {
    daily
    missingok
    rotate 52
    compress
    delaycompress
    notifempty
    create 644 matrix matrix
    postrotate
        systemctl reload matrix-app
    endscript
}
EOF
```

**Health Check Script:**
```bash
#!/bin/bash
# /home/<USER>/health-check.sh

URL="https://your-domain.com/api/system/status"
RESPONSE=$(curl -s -o /dev/null -w "%{http_code}" $URL)

if [ $RESPONSE -eq 200 ]; then
    echo "$(date): Application is healthy"
else
    echo "$(date): Application health check failed (HTTP $RESPONSE)"
    systemctl restart matrix-app
fi
```

## Backup Strategy

### Database Backup

```bash
#!/bin/bash
# /home/<USER>/backup-db.sh

BACKUP_DIR="/home/<USER>/backups"
DATE=$(date +%Y%m%d_%H%M%S)
DB_NAME="matrix_app"

mkdir -p $BACKUP_DIR

# Create backup
pg_dump $DB_NAME > $BACKUP_DIR/matrix_app_$DATE.sql

# Compress backup
gzip $BACKUP_DIR/matrix_app_$DATE.sql

# Remove backups older than 30 days
find $BACKUP_DIR -name "*.sql.gz" -mtime +30 -delete

echo "Database backup completed: matrix_app_$DATE.sql.gz"
```

### Application Backup

```bash
#!/bin/bash
# /home/<USER>/backup-app.sh

BACKUP_DIR="/home/<USER>/backups"
DATE=$(date +%Y%m%d_%H%M%S)
APP_DIR="/home/<USER>/matrix-app"

mkdir -p $BACKUP_DIR

# Backup instance directory (contains uploaded files, logs)
tar -czf $BACKUP_DIR/instance_$DATE.tar.gz -C $APP_DIR instance/

# Backup configuration
cp $APP_DIR/.env $BACKUP_DIR/env_$DATE.backup

echo "Application backup completed"
```

## Performance Optimization

### Production Optimizations

**1. Enable Gzip Compression:**
```nginx
# In nginx configuration
gzip on;
gzip_vary on;
gzip_min_length 1024;
gzip_types text/plain text/css application/json application/javascript text/xml application/xml;
```

**2. Database Connection Pooling:**
```python
# In config.py
SQLALCHEMY_ENGINE_OPTIONS = {
    'pool_size': 20,
    'pool_recycle': 3600,
    'pool_pre_ping': True
}
```

**3. Redis Caching:**
```python
# Enable caching in production
CACHE_TYPE = 'RedisCache'
CACHE_REDIS_URL = 'redis://localhost:6379/0'
CACHE_DEFAULT_TIMEOUT = 300
```

## Troubleshooting

### Common Issues

**1. Permission Errors:**
```bash
# Fix file permissions
sudo chown -R matrix:matrix /home/<USER>/matrix-app
sudo chmod -R 755 /home/<USER>/matrix-app
```

**2. Database Connection Issues:**
```bash
# Check PostgreSQL status
sudo systemctl status postgresql

# Check database connectivity
sudo -u postgres psql -c "SELECT version();"
```

**3. Application Not Starting:**
```bash
# Check application logs
sudo journalctl -u matrix-app -f

# Check Gunicorn process
ps aux | grep gunicorn
```

**4. Nginx Issues:**
```bash
# Test nginx configuration
sudo nginx -t

# Check nginx logs
sudo tail -f /var/log/nginx/error.log
```

---

This deployment guide provides comprehensive instructions for deploying Matrix App in production environments with proper security, monitoring, and backup strategies.

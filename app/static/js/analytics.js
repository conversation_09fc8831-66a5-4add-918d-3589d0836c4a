/**
 * Analytics Dashboard JavaScript
 * Handles chart initialization, data loading, and UI interactions
 */

// Global variables for chart data
let activityDates = [];
let activityCounts = [];
let userCounts = [];
let formattedDates = [];
let trafficChart = null;

document.addEventListener('DOMContentLoaded', function() {
    initAnalytics();
});

/**
 * Initialize the analytics dashboard
 */
function initAnalytics() {
    // Initialize refresh button
    initRefreshButton();

    // Load data from backend
    loadChartData();

    // Initialize charts
    initTrafficChart();

    // Initialize chart period buttons
    initChartPeriodButtons();
}

/**
 * Load chart data from the backend
 */
function loadChartData() {
    // Get data from the backend
    try {
        // Check if the data is already parsed or needs to be parsed
        const activityDatesStr = document.getElementById('activity-dates-data').textContent;
        const activityCountsStr = document.getElementById('activity-counts-data').textContent;
        const userCountsStr = document.getElementById('user-counts-data').textContent;

        // Try to parse if it's a string, otherwise use as is
        activityDates = typeof activityDatesStr === 'string' ?
            (activityDatesStr.startsWith('[') ? JSON.parse(activityDatesStr) : activityDatesStr) :
            activityDatesStr;

        activityCounts = typeof activityCountsStr === 'string' ?
            (activityCountsStr.startsWith('[') ? JSON.parse(activityCountsStr) : activityCountsStr) :
            activityCountsStr;

        userCounts = typeof userCountsStr === 'string' ?
            (userCountsStr.startsWith('[') ? JSON.parse(userCountsStr) : userCountsStr) :
            userCountsStr;

        // Parse the data successfully
    } catch (e) {
        console.error('Error parsing activity data:', e);
        activityDates = [];
        activityCounts = [];
        userCounts = [];
    }

    // Format dates for display (just show day and month)
    if (activityDates && activityDates.length > 0) {
        formattedDates = activityDates.map(date => {
            try {
                const dateObj = new Date(date);
                return dateObj.getDate() + '/' + (dateObj.getMonth() + 1);
            } catch (e) {
                console.error(`Error formatting date: ${date}`, e);
                return 'Invalid';
            }
        });
    } else {
        console.warn('No activity dates available');
        formattedDates = [];
    }
}

// Registration data functionality has been removed

/**
 * Initialize refresh buttons and period dropdowns
 */
function initRefreshButton() {
    // Initialize main refresh button
    const refreshBtn = document.getElementById('refresh-analytics-btn');
    if (refreshBtn) {
        refreshBtn.addEventListener('click', function() {
            // Show loading state
            this.classList.add('opacity-50', 'pointer-events-none');

            // Find the SVG directly
            const svg = this.querySelector('svg');
            if (svg) {
                // Add spin animation directly to the SVG
                svg.classList.add('animate-spin');
            }

            // Actually refresh the page data
            setTimeout(() => {
                // Reload the page to get fresh data
                window.location.reload();
            }, 300);
        });
    }

    // Initialize user behavior refresh button
    const behaviorBtn = document.getElementById('refresh-behavior-btn');
    if (behaviorBtn) {
        behaviorBtn.addEventListener('click', function() {
            // Show loading state
            this.classList.add('opacity-50', 'pointer-events-none');

            // Find the SVG directly
            const svg = this.querySelector('svg');
            if (svg) {
                // Add spin animation directly to the SVG
                svg.classList.add('animate-spin');
            }

            // Actually refresh the page data
            setTimeout(() => {
                // Reload the page to get fresh data
                window.location.reload();
            }, 300);
        });
    }

    // Initialize all period dropdowns
    initPeriodDropdowns();
}

/**
 * Get period text based on period value
 */
function getPeriodText(period) {
    switch(period) {
        case '7':
            return 'Last 7 days';
        case '30':
            return 'Last 30 days';
        case '90':
            return 'Last 90 days';
        default:
            return 'Last 30 days';
    }
}

/**
 * Initialize period dropdowns
 */
function initPeriodDropdowns() {
    // Get the main period dropdown
    const mainPeriodDropdown = document.querySelector('.flex.items-center.mt-2 select');
    if (!mainPeriodDropdown) return;

    // Get URL parameters
    const urlParams = new URLSearchParams(window.location.search);
    const periodParam = urlParams.get('period');

    // Update the period display in the header
    const periodDisplay = document.getElementById('period-display');

    // Set dropdown value if specified in URL
    if (periodParam) {
        const hasOption = Array.from(mainPeriodDropdown.options).some(option => option.value === periodParam);
        if (hasOption) {
            mainPeriodDropdown.value = periodParam;

            // Update the period display text
            if (periodDisplay) {
                periodDisplay.textContent = getPeriodText(periodParam);
            }
        }
    }

    // Add event listener to the main period dropdown
    mainPeriodDropdown.addEventListener('change', function() {
        const period = this.value;
        const loadingOverlay = document.querySelector('.chart-loading-overlay');

        // Show loading state if available
        if (loadingOverlay) {
            loadingOverlay.classList.remove('hidden');
            loadingOverlay.classList.add('flex');
        }

        // Update the keyMetrics loading indicator
        const keyMetricsLoader = document.querySelector('.keyMetrics-loading');
        if (keyMetricsLoader) {
            keyMetricsLoader.classList.remove('hidden');
        }

        // Update the period display text
        if (periodDisplay) {
            periodDisplay.textContent = getPeriodText(period);
        }

        // Simulate loading data for the selected period
        setTimeout(() => {
            // Navigate to the updated URL with the new period
            window.location.href = `${window.location.pathname}?period=${period}`;
        }, 300);
    });
}

/**
 * Initialize the traffic chart
 */
function initTrafficChart() {
    const ctx = document.getElementById('trafficOverviewChart');
    if (!ctx) return;

    // Get the loading overlay
    const loadingOverlay = document.querySelector('.chart-loading-overlay');
    if (loadingOverlay) {
        loadingOverlay.classList.remove('hidden');
        loadingOverlay.classList.add('flex');
    }

    // Check if we have data
    if (!activityDates.length || !activityCounts.length || !userCounts.length) {
        console.warn('No activity data available for chart');

        // Hide loading overlay
        if (loadingOverlay) {
            loadingOverlay.classList.add('hidden');
            loadingOverlay.classList.remove('flex');
        }

        // Display a message in the chart area
        ctx.parentNode.innerHTML = `
            <div class="flex items-center justify-center h-full">
                <div class="text-center p-6">
                    <i data-lucide="bar-chart" class="w-10 h-10 text-gray-400 dark:text-gray-600 mx-auto mb-3"></i>
                    <p class="text-gray-500 dark:text-gray-400 mb-1 analytics-text">No activity data available</p>
                    <p class="text-xs text-gray-400 dark:text-gray-500 analytics-text">Activity data will appear here once users start using the system</p>
                </div>
            </div>
        `;

        // Initialize Lucide icons for the new elements
        if (window.lucide) {
            window.lucide.createIcons();
        }

        return;
    }

    const data = {
        labels: formattedDates,
        datasets: [{
            label: 'Active Users',
            data: userCounts,
            borderColor: '#3b82f6',
            backgroundColor: 'rgba(59, 130, 246, 0.1)',
            borderWidth: 2,
            tension: 0.4,
            fill: true
        }, {
            label: 'Activities',
            data: activityCounts,
            borderColor: '#10b981',
            backgroundColor: 'rgba(16, 185, 129, 0.0)',
            borderWidth: 2,
            tension: 0.4,
            fill: false
        }]
    };

    const config = {
        type: 'line',
        data: data,
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: true,
                    position: 'top',
                    labels: {
                        usePointStyle: true,
                        boxWidth: 8,
                        padding: 15,
                        color: '#4b5563',
                        font: {
                            size: 13,
                            family: "'Inter', sans-serif",
                            weight: '500'
                        },
                        generateLabels: function(chart) {
                            const originalLabels = Chart.defaults.plugins.legend.labels.generateLabels(chart);
                            originalLabels.forEach(label => {
                                label.text = ' ' + label.text + ' ';
                            });
                            return originalLabels;
                        }
                    }
                },
                tooltip: {
                    backgroundColor: 'rgba(255, 255, 255, 0.95)',
                    titleColor: '#1e293b',
                    bodyColor: '#475569',
                    borderColor: 'rgba(0, 0, 0, 0.1)',
                    borderWidth: 1,
                    padding: 10,
                    boxPadding: 5,
                    usePointStyle: true,
                    titleFont: {
                        size: 12,
                        family: "'Inter', sans-serif"
                    },
                    bodyFont: {
                        size: 11,
                        family: "'Inter', sans-serif"
                    },
                    callbacks: {
                        labelTextColor: function() {
                            return '#475569';
                        }
                    }
                }
            },
            scales: {
                x: {
                    grid: {
                        display: false
                    },
                    ticks: {
                        color: '#94a3b8',
                        font: {
                            size: window.innerWidth < 768 ? 9 : 10
                        },
                        maxRotation: 0,
                        autoSkipPadding: 20
                    }
                },
                y: {
                    beginAtZero: true,
                    grid: {
                        color: 'rgba(148, 163, 184, 0.1)'
                    },
                    ticks: {
                        color: '#94a3b8',
                        precision: 0,
                        font: {
                            size: window.innerWidth < 768 ? 9 : 10
                        }
                    }
                }
            },
            interaction: {
                intersect: false,
                mode: 'index'
            },
            elements: {
                point: {
                    radius: window.innerWidth < 768 ? 1 : 2,
                    hoverRadius: window.innerWidth < 768 ? 3 : 4
                }
            },
            animation: {
                duration: 1000,
                easing: 'easeOutQuart',
                onComplete: function() {
                    // Hide loading overlay when animation completes
                    if (loadingOverlay) {
                        loadingOverlay.classList.add('hidden');
                        loadingOverlay.classList.remove('flex');
                    }
                }
            }
        }
    };

    // Check if we're in dark mode and update chart styles
    if (document.documentElement.classList.contains('dark')) {
        // Update tooltip styles for dark mode
        config.options.plugins.tooltip.backgroundColor = 'rgba(30, 41, 59, 0.95)';
        config.options.plugins.tooltip.titleColor = '#f1f5f9';
        config.options.plugins.tooltip.bodyColor = '#cbd5e1';
        config.options.plugins.tooltip.borderColor = 'rgba(255, 255, 255, 0.1)';
        config.options.plugins.tooltip.callbacks.labelTextColor = function() {
            return '#cbd5e1';
        };

        // Update legend styles for dark mode
        config.options.plugins.legend.labels.color = '#d1d5db';
    }

    // Create the chart
    window.trafficChart = new Chart(ctx, config);

    // Handle window resize to make chart responsive
    window.addEventListener('resize', function() {
        if (window.trafficChart) {
            // Update font sizes based on screen width
            config.options.scales.x.ticks.font.size = window.innerWidth < 768 ? 9 : 10;
            config.options.scales.y.ticks.font.size = window.innerWidth < 768 ? 9 : 10;
            config.options.elements.point.radius = window.innerWidth < 768 ? 1 : 2;
            config.options.elements.point.hoverRadius = window.innerWidth < 768 ? 3 : 4;

            window.trafficChart.update();
        }
    });
}

/**
 * Initialize chart period buttons (daily, weekly, monthly)
 */
function initChartPeriodButtons() {
    const dailyBtn = document.getElementById('chartDailyBtn');
    const weeklyBtn = document.getElementById('chartWeeklyBtn');
    const monthlyBtn = document.getElementById('chartMonthlyBtn');

    if (!dailyBtn || !weeklyBtn || !monthlyBtn || !window.trafficChart) return;

    // We'll use the data from the backend for daily view (default)
    // For weekly and monthly, we'll aggregate the data

    // Check if we have data
    if (!activityDates.length || !activityCounts.length || !userCounts.length) {
        console.warn('No activity data available for chart periods');

        // Disable the period buttons if there's no data
        [dailyBtn, weeklyBtn, monthlyBtn].forEach(btn => {
            btn.classList.add('opacity-50', 'cursor-not-allowed');
            btn.disabled = true;
        });

        return;
    }

    // Daily data (default) - already loaded from backend
    const dailyData = {
        users: userCounts,
        activities: activityCounts
    };
    const dailyLabels = formattedDates;

    // Weekly data - aggregate daily data into weeks
    const weeklyData = {
        users: [],
        activities: []
    };
    const weeklyLabels = [];

    // Group data into weeks (assuming 30 days of data)
    // We'll create 4 weeks, with the most recent week first
    for (let i = 0; i < 4; i++) {
        // Calculate week boundaries (most recent week first)
        const weekEnd = userCounts.length;
        const weekStart = Math.max(0, weekEnd - (i + 1) * 7);
        const weekEndIndex = Math.max(0, weekEnd - i * 7);

        // Sum the data for this week
        let userSum = 0;
        let activitySum = 0;
        for (let j = weekStart; j < weekEndIndex; j++) {
            userSum += userCounts[j] || 0;
            activitySum += activityCounts[j] || 0;
        }

        // Add to the beginning of the arrays to maintain chronological order
        weeklyData.users.unshift(userSum);
        weeklyData.activities.unshift(activitySum);

        // Create a safe label for this week
        let weekLabel = `Week ${i+1}`;

        // Try to create a date-based label if possible
        try {
            if (activityDates && activityDates.length > 0) {
                // Make sure we have valid indices
                const validStartIndex = Math.min(weekStart, activityDates.length - 1);
                const validEndIndex = Math.min(weekEndIndex - 1, activityDates.length - 1);

                if (validStartIndex >= 0 && validEndIndex >= 0) {
                    const startDate = new Date(activityDates[validStartIndex]);
                    const endDate = new Date(activityDates[validEndIndex]);

                    // Check if dates are valid
                    if (!isNaN(startDate) && !isNaN(endDate)) {
                        const startFormatted = `${startDate.getDate()}/${startDate.getMonth() + 1}`;
                        const endFormatted = `${endDate.getDate()}/${endDate.getMonth() + 1}`;
                        weekLabel = `${startFormatted} - ${endFormatted}`;
                    }
                }
            }
        } catch (e) {
            console.warn('Error formatting week label:', e);
            // Keep the default week label
        }

        weeklyLabels.unshift(weekLabel);
    }

    // Monthly data - aggregate by actual months
    const monthlyData = {
        users: [],
        activities: []
    };
    const monthlyLabels = [];

    // Get the month names
    const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];

    try {
        // Group by month using a Map
        const monthlyMap = new Map();

        // Check if we have valid dates
        if (activityDates && activityDates.length > 0) {
            // Process all dates and aggregate by month
            for (let i = 0; i < activityDates.length; i++) {
                try {
                    const date = new Date(activityDates[i]);

                    // Skip invalid dates
                    if (isNaN(date)) continue;

                    const monthKey = `${date.getFullYear()}-${date.getMonth() + 1}`;

                    if (!monthlyMap.has(monthKey)) {
                        monthlyMap.set(monthKey, {
                            users: 0,
                            activities: 0,
                            month: date.getMonth(),
                            year: date.getFullYear()
                        });
                    }

                    const monthData = monthlyMap.get(monthKey);
                    monthData.users += userCounts[i] || 0;
                    monthData.activities += activityCounts[i] || 0;
                } catch (e) {
                    console.warn(`Error processing date at index ${i}:`, e);
                }
            }

            // Convert the map to arrays and sort by date
            const monthEntries = Array.from(monthlyMap.entries())
                .sort((a, b) => {
                    // Sort by year and month
                    if (a[1].year !== b[1].year) {
                        return a[1].year - b[1].year;
                    }
                    return a[1].month - b[1].month;
                });

            // Extract the data
            for (const [_, data] of monthEntries) {
                monthlyData.users.push(data.users);
                monthlyData.activities.push(data.activities);
                monthlyLabels.push(monthNames[data.month]);
            }
        }

        // If we don't have any monthly data, create default data for the current month
        if (monthlyLabels.length === 0) {
            const now = new Date();
            const currentMonth = now.getMonth();

            // Sum all user and activity counts for the current month
            let totalUsers = 0;
            let totalActivities = 0;
            for (let i = 0; i < userCounts.length; i++) {
                totalUsers += userCounts[i] || 0;
                totalActivities += activityCounts[i] || 0;
            }

            monthlyData.users.push(totalUsers);
            monthlyData.activities.push(totalActivities);
            monthlyLabels.push(monthNames[currentMonth]);

            console.log('Created default monthly data for current month:', {
                month: monthNames[currentMonth],
                users: totalUsers,
                activities: totalActivities
            });
        }
    } catch (e) {
        console.error('Error creating monthly data:', e);

        // Create fallback data
        const now = new Date();
        monthlyData.users.push(0);
        monthlyData.activities.push(0);
        monthlyLabels.push(monthNames[now.getMonth()]);
    }

    // Update chart function
    function updateChart(labels, data, activeBtn) {
        // Validate input data
        if (!labels || !labels.length || !data || !data.users || !data.activities) {
            console.error('Invalid data for chart update:', { labels, data });
            return;
        }

        console.log(`Updating chart with ${labels.length} labels:`, labels);
        console.log('User data:', data.users);
        console.log('Activity data:', data.activities);

        try {
            // Update chart data
            window.trafficChart.data.labels = labels;
            window.trafficChart.data.datasets[0].data = data.users;
            window.trafficChart.data.datasets[1].data = data.activities;
            window.trafficChart.update();

            // Update button states
            [dailyBtn, weeklyBtn, monthlyBtn].forEach(btn => {
                btn.classList.remove('bg-blue-50', 'dark:bg-blue-900/30', 'text-blue-600', 'dark:text-blue-400', 'shadow-sm');
                btn.classList.add('text-gray-600', 'dark:text-gray-400');
            });

            activeBtn.classList.add('bg-blue-50', 'dark:bg-blue-900/30', 'text-blue-600', 'dark:text-blue-400', 'shadow-sm');
            activeBtn.classList.remove('text-gray-600', 'dark:text-gray-400');
        } catch (e) {
            console.error('Error updating chart:', e);
        }
    }

    // Add event listeners
    dailyBtn.addEventListener('click', () => updateChart(dailyLabels, dailyData, dailyBtn));
    weeklyBtn.addEventListener('click', () => updateChart(weeklyLabels, weeklyData, weeklyBtn));
    monthlyBtn.addEventListener('click', () => updateChart(monthlyLabels, monthlyData, monthlyBtn));
}

{% macro modal(id, title="Modal Title", size="md") %}
{#
  Reusable Modal Component

  Parameters:
  - id: Required. Unique identifier for the modal
  - title: Optional. Title displayed in the modal header (default: "Modal Title")
  - size: Optional. Size of the modal: "sm", "md", "lg", "xl" (default: "md")

  Usage:
  {% from "components/modal.html" import modal %}

  {% call modal(id="my-modal", title="My Modal Title", size="lg") %}
    <!-- Modal content goes here -->
    <p>This is the modal content.</p>

    <!-- Optional footer content -->
    {% set footer %}
      <button type="button" class="btn btn-outline" onclick="closeModal('my-modal')">Cancel</button>
      <button type="button" class="btn btn-primary ml-2">Save</button>
    {% endset %}
  {% endcall %}
#}

{# Determine max-width based on size #}
{% set max_width = {
  "sm": "max-w-sm",
  "md": "max-w-md",
  "lg": "max-w-lg",
  "xl": "max-w-xl",
  "2xl": "max-w-2xl",
  "3xl": "max-w-3xl",
  "4xl": "max-w-4xl",
  "5xl": "max-w-5xl",
  "full": "max-w-full"
}.get(size, "max-w-md") %}

<!-- Modal Container -->
<div id="{{ id }}" class="hidden fixed inset-0 z-50 overflow-hidden" aria-labelledby="{{ id }}-title" role="dialog" aria-modal="true">
  <!-- Modal Backdrop -->
  <div class="fixed inset-0 bg-black/50 backdrop-blur-sm transition-opacity" id="{{ id }}-backdrop"></div>

  <!-- Modal Dialog -->
  <div class="flex min-h-full items-center justify-center p-4 text-center sm:p-0 relative z-50">
    <!-- Modal Content -->
    <div class="relative transform overflow-hidden rounded-lg bg-card text-card-foreground shadow-xl transition-all w-full {{ max_width }} opacity-0 translate-y-4" id="{{ id }}-content">

      <!-- Modal Header -->
      <div class="flex items-center justify-between p-4 border-b border-border">
        <h3 class="text-lg font-semibold" id="{{ id }}-title">{{ title }}</h3>
        <button type="button" class="flex h-8 w-8 items-center justify-center rounded-full hover:bg-muted/80 transition-colors focus:outline-none focus:ring-2 focus:ring-primary/30" onclick="closeModal('{{ id }}')" aria-label="Close">
          <i data-lucide="x" class="h-4 w-4"></i>
        </button>
      </div>

      <!-- Modal Body -->
      <div class="p-6 max-h-[70vh] overflow-y-auto">
        {{ caller() }}
      </div>

      <!-- Modal Footer (if provided) -->
      {% if footer is defined %}
      <div class="px-6 py-4 border-t border-border flex justify-end space-x-2">
        {{ footer }}
      </div>
      {% endif %}

    </div>
  </div>
</div>
{% endmacro %}

/**
 * Date Input Component
 * Enhances date inputs with better styling and interaction
 */
document.addEventListener('DOMContentLoaded', function() {
  // Initialize date inputs
  initializeDateInputs();
  
  // Also initialize date inputs when drawers are opened
  if (window.drawerManager) {
    const originalOpenForm = window.drawerManager.openForm;
    window.drawerManager.openForm = function() {
      const result = originalOpenForm.apply(this, arguments);
      
      // Initialize date inputs after drawer is opened
      setTimeout(function() {
        initializeDateInputs();
      }, 100);
      
      return result;
    };
  }
});

/**
 * Initialize all date inputs on the page
 */
function initializeDateInputs() {
  const dateInputs = document.querySelectorAll('input[type="date"]');
  
  dateInputs.forEach(function(input) {
    // Add click handler to the calendar icon
    const container = input.closest('.relative');
    if (container) {
      const icon = container.querySelector('[data-lucide="calendar"]');
      if (icon) {
        icon.addEventListener('click', function() {
          if (!input.disabled) {
            input.showPicker();
          }
        });
        
        // Make the icon clickable
        const iconContainer = icon.closest('.pointer-events-none');
        if (iconContainer) {
          iconContainer.classList.remove('pointer-events-none');
          iconContainer.classList.add('cursor-pointer');
        }
      }
    }
    
    // Format the date when it changes
    input.addEventListener('change', function() {
      if (input.value) {
        const date = new Date(input.value);
        const formattedDate = formatDate(date);
        
        // Set a data attribute with the formatted date for display
        input.setAttribute('data-formatted-date', formattedDate);
      } else {
        input.removeAttribute('data-formatted-date');
      }
    });
    
    // Trigger change event to format existing dates
    if (input.value) {
      const event = new Event('change');
      input.dispatchEvent(event);
    }
  });
}

/**
 * Format a date as DD/MM/YYYY
 * @param {Date} date - The date to format
 * @returns {string} - The formatted date
 */
function formatDate(date) {
  const day = String(date.getDate()).padStart(2, '0');
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const year = date.getFullYear();
  
  return `${day}/${month}/${year}`;
}

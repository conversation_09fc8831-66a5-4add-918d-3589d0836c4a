/**
 * Toast Notifications Module
 *
 * This module provides functions for showing toast notifications.
 */

import { createElement } from './dom.js';
import { createIcon, initIcons } from '../dist/icons.bundle.js';

// Store active toast for single toast management
let _activeToast = null;

/**
 * Initialize the toast container if it doesn't exist
 * @returns {HTMLElement} - The toast container element
 */
function initToastContainer() {
  let container = document.getElementById('toast-container');

  if (!container) {
    container = createElement('div', document.body, {
      attributes: {
        id: 'toast-container',
        'aria-live': 'polite',
        'aria-atomic': 'true'
      },
      styles: {
        position: 'fixed',
        bottom: '1rem',
        right: '1rem',
        zIndex: '9999',
        display: 'flex',
        flexDirection: 'column',
        gap: '0.5rem',
        maxWidth: 'calc(100% - 2rem)',
        width: '25rem'
      }
    });
  }

  return container;
}

/**
 * Show a toast notification
 * @param {string} message - The message to display
 * @param {Object} options - Options for the toast
 * @param {string} options.type - Type of toast: 'success', 'error', 'warning', 'info'
 * @param {number} options.duration - Duration in milliseconds to show the toast
 * @param {boolean} options.dismissible - Whether the toast can be dismissed manually
 * @returns {HTMLElement} - The toast element
 */
function showToast(message, options = {}) {
  const {
    type = 'info',
    duration = 3000,
    dismissible = true
  } = options;

  // Ensure toast container exists
  initToastContainer();

  // Close any existing toast first
  if (_activeToast) {
    closeToast(_activeToast);
    // Small delay to allow the closing animation to start
    // This prevents visual glitches when replacing toasts
    return setTimeout(() => {
      _createAndShowToast(message, type, duration, dismissible);
    }, 100);
  }

  return _createAndShowToast(message, type, duration, dismissible);
}

/**
 * Internal function to create and show a toast
 * @private
 */
function _createAndShowToast(message, type, duration, dismissible) {
  const toastContainer = document.getElementById('toast-container');

  // Get appropriate icon based on type
  let iconName;
  switch (type) {
    case 'success':
      iconName = 'check-circle';
      break;
    case 'error':
      iconName = 'alert-circle';
      break;
    case 'warning':
      iconName = 'alert-triangle';
      break;
    default:
      iconName = 'info';
  }

  // Clear container before adding new toast (single toast at a time)
  toastContainer.innerHTML = '';

  // Create toast element with proper DOM elements instead of innerHTML
  const toast = document.createElement('div');
  toast.className = `toast toast-${type}`;
  toast.setAttribute('role', 'alert');
  toast.setAttribute('data-state', 'open');

  const iconWrapper = document.createElement('div');
  iconWrapper.className = 'toast-icon';
  const icon = document.createElement('i');
  icon.setAttribute('data-lucide', iconName);
  iconWrapper.appendChild(icon);

  const contentDiv = document.createElement('div');
  contentDiv.className = 'toast-content';
  contentDiv.textContent = message;

  // Add elements to toast
  toast.appendChild(iconWrapper);
  toast.appendChild(contentDiv);

  // Add close button if dismissible
  if (dismissible) {
    const closeBtn = document.createElement('button');
    closeBtn.className = 'toast-close';
    closeBtn.setAttribute('aria-label', 'Close');

    const closeIcon = document.createElement('i');
    closeIcon.setAttribute('data-lucide', 'x');
    closeIcon.className = 'h-4 w-4';

    closeBtn.appendChild(closeIcon);
    toast.appendChild(closeBtn);

    // Add close functionality
    closeBtn.addEventListener('click', (e) => {
      e.stopPropagation(); // Prevent event bubbling
      closeToast(toast);
    });
  }

  // Append to container
  toastContainer.appendChild(toast);

  // Initialize icons in the toast
  initIcons(toast);

  // Store as the active toast
  _activeToast = toast;

  // Auto close after duration
  const timeoutId = setTimeout(() => {
    closeToast(toast);
  }, duration);

  // Store timeout ID on the toast element for potential early cancellation
  toast._timeoutId = timeoutId;

  return toast;
}

/**
 * Close a toast with animation
 * @param {HTMLElement} toast - The toast element to close
 */
function closeToast(toast) {
  // Prevent multiple close attempts
  if (!toast || toast.getAttribute('data-state') === 'closing') return;

  // Clear any existing auto-close timeout
  if (toast._timeoutId) {
    clearTimeout(toast._timeoutId);
    toast._timeoutId = null;
  }

  // Set closing state
  toast.setAttribute('data-state', 'closing');

  // Prevent any interaction during closing animation
  toast.style.pointerEvents = 'none';

  // Remove after animation completes
  // Use the animationend event to ensure the animation completes before removing
  const handleAnimationEnd = () => {
    if (toast && toast.parentNode) {
      toast.parentNode.removeChild(toast);
    }

    // Clear active toast reference if this is the active toast
    if (_activeToast === toast) {
      _activeToast = null;
    }

    // Remove the event listener
    toast.removeEventListener('animationend', handleAnimationEnd);
  };

  // Listen for the animation to end
  toast.addEventListener('animationend', handleAnimationEnd);

  // Fallback timeout in case the animation event doesn't fire
  setTimeout(() => {
    handleAnimationEnd();
  }, 400);
}

// Export public API
export {
  initToastContainer,
  showToast,
  closeToast
};

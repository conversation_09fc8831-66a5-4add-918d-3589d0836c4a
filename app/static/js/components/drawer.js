// No longer using module imports

/**
 * Drawer Component for displaying forms and content in a sliding panel
 */
class DrawerComponent {
  constructor(options = {}) {
    this.position = options.position || 'right';
    this.width = options.width || '30rem';
    this.onClose = options.onClose || (() => {});

    // Get or create container
    this.container = document.getElementById('drawer-container');
    if (!this.container) {
      this.container = document.createElement('div');
      this.container.id = 'drawer-container';
      document.body.appendChild(this.container);
    }

    this.drawer = null;
    this.overlay = null;
  }

  /**
   * Open the drawer with the provided content
   * @param {string} content - HTML content to display in the drawer
   * @returns {DrawerComponent} - The drawer instance for chaining
   */
  open(content) {
    // Create overlay
    this.overlay = document.createElement('div');
    this.overlay.className = 'drawer-overlay';
    this.overlay.style.position = 'fixed';
    this.overlay.style.inset = '0';
    this.overlay.style.backgroundColor = 'rgba(0, 0, 0, 0.5)';
    this.overlay.style.backdropFilter = 'blur(4px)';
    this.overlay.style.zIndex = '9998';
    this.overlay.style.opacity = '0';
    this.overlay.style.transition = 'opacity 0.3s ease-in-out';

    // Create drawer
    this.drawer = document.createElement('div');
    this.drawer.className = `drawer-content drawer-${this.position}`;

    // Apply styles based on theme
    const isDarkMode = document.documentElement.classList.contains('dark');
    this.drawer.style.backgroundColor = isDarkMode ? '#111827' : '#ffffff';
    this.drawer.style.color = isDarkMode ? '#fff' : '#111827';
    this.drawer.style.borderColor = isDarkMode ? '#1f2937' : '#e5e7eb';

    // Position styles
    this.drawer.style.position = 'fixed';
    this.drawer.style.zIndex = '9999';
    this.drawer.style.padding = '1.5rem';
    this.drawer.style.boxShadow = isDarkMode ?
      '0 0 15px rgba(0, 0, 0, 0.5)' :
      '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)';
    this.drawer.style.overflow = 'auto';
    this.drawer.style.transition = 'transform 0.3s ease-in-out';

    // Position-specific styles
    if (this.position === 'right') {
      this.drawer.style.top = '0';
      this.drawer.style.right = '0';
      this.drawer.style.bottom = '0';
      this.drawer.style.width = this.width;
      this.drawer.style.maxWidth = '100vw';
      this.drawer.style.transform = 'translateX(100%)';
      this.drawer.style.borderLeft = `1px solid ${this.drawer.style.borderColor}`;
    } else if (this.position === 'left') {
      this.drawer.style.top = '0';
      this.drawer.style.left = '0';
      this.drawer.style.bottom = '0';
      this.drawer.style.width = this.width;
      this.drawer.style.maxWidth = '100vw';
      this.drawer.style.transform = 'translateX(-100%)';
      this.drawer.style.borderRight = `1px solid ${this.drawer.style.borderColor}`;
    } else if (this.position === 'top') {
      this.drawer.style.top = '0';
      this.drawer.style.left = '0';
      this.drawer.style.right = '0';
      this.drawer.style.maxHeight = '80vh';
      this.drawer.style.transform = 'translateY(-100%)';
      this.drawer.style.borderBottom = `1px solid ${this.drawer.style.borderColor}`;
    } else if (this.position === 'bottom') {
      this.drawer.style.bottom = '0';
      this.drawer.style.left = '0';
      this.drawer.style.right = '0';
      this.drawer.style.maxHeight = '80vh';
      this.drawer.style.transform = 'translateY(100%)';
      this.drawer.style.borderTop = `1px solid ${this.drawer.style.borderColor}`;
    }

    // Create a wrapper for the content
    const contentWrapper = document.createElement('div');
    contentWrapper.className = 'drawer-content-container';
    contentWrapper.innerHTML = content;

    // Create close button
    const closeButton = document.createElement('button');
    closeButton.type = 'button';
    closeButton.className = 'absolute right-4 top-4 rounded-sm opacity-70 hover:opacity-100 focus:outline-none';
    closeButton.style.position = 'absolute';
    closeButton.style.right = '1rem';
    closeButton.style.top = '1rem';
    closeButton.style.cursor = 'pointer';
    closeButton.style.zIndex = '9999';
    closeButton.style.width = '24px';
    closeButton.style.height = '24px';
    closeButton.style.backgroundColor = 'transparent';
    closeButton.style.border = 'none';
    closeButton.innerHTML = `
      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
        <line x1="18" y1="6" x2="6" y2="18"></line>
        <line x1="6" y1="6" x2="18" y2="18"></line>
      </svg>
    `;

    // Add the content and close button to the drawer
    this.drawer.appendChild(closeButton);
    this.drawer.appendChild(contentWrapper);

    // Add to container
    this.container.appendChild(this.overlay);
    this.container.appendChild(this.drawer);

    // Initialize icons if available
    if (typeof lucide !== 'undefined' && lucide.createIcons) {
      lucide.createIcons({ root: this.drawer });
    }

    // Trigger animation after a small delay
    requestAnimationFrame(() => {
      requestAnimationFrame(() => {
        // Apply the open transform
        if (this.position === 'right' || this.position === 'left') {
          this.drawer.style.transform = 'translateX(0)';
        } else {
          this.drawer.style.transform = 'translateY(0)';
        }

        // Fade in the overlay
        this.overlay.style.opacity = '1';
      });
    });

    // Prevent body scrolling
    document.body.style.overflow = 'hidden';

    // Add event listeners
    const self = this; // Store reference to this for event handlers

    // Close on overlay click
    this.overlay.addEventListener('click', function() {
      self.close();
    });

    // Close on close button click
    closeButton.addEventListener('click', function(e) {
      e.preventDefault();
      self.close();
    });

    // Add event listeners to any elements with drawer-close class
    const closeElements = contentWrapper.querySelectorAll('.drawer-close');
    closeElements.forEach(function(element) {
      element.addEventListener('click', function(e) {
        e.preventDefault();
        self.close();
      });
    });

    // Initialize form validation
    this.initFormValidation(contentWrapper);

    return this;
  }

  /**
   * Close the drawer
   */
  close() {
    if (!this.drawer || !this.overlay) return;

    // Animation
    if (this.position === 'right') {
      this.drawer.style.transform = 'translateX(100%)';
    } else if (this.position === 'left') {
      this.drawer.style.transform = 'translateX(-100%)';
    } else if (this.position === 'top') {
      this.drawer.style.transform = 'translateY(-100%)';
    } else if (this.position === 'bottom') {
      this.drawer.style.transform = 'translateY(100%)';
    }

    this.overlay.style.opacity = '0';

    // Remove after animation
    const self = this;
    setTimeout(function() {
      if (self.container.contains(self.overlay)) {
        self.container.removeChild(self.overlay);
      }
      if (self.container.contains(self.drawer)) {
        self.container.removeChild(self.drawer);
      }

      // Restore scrolling
      document.body.style.overflow = '';

      // Call onClose callback
      if (typeof self.onClose === 'function') {
        self.onClose();
      }

      self.drawer = null;
      self.overlay = null;
    }, 300);
  }

  /**
   * Initialize form validation
   * @private
   */
  initFormValidation(contentWrapper) {
    if (!contentWrapper) return;

    const form = contentWrapper.querySelector('form');
    if (!form) return;

    form.addEventListener('submit', function(e) {
      // Basic form validation
      const requiredFields = form.querySelectorAll('[required]');
      let isValid = true;

      requiredFields.forEach(function(field) {
        if (!field.value.trim()) {
          isValid = false;
          // Add error styling
          field.classList.add('border-destructive');

          // Add or update error message
          let errorMsg = field.parentNode.querySelector('.error-message');
          if (!errorMsg) {
            errorMsg = document.createElement('p');
            errorMsg.className = 'text-destructive text-xs mt-1 error-message';
            field.parentNode.appendChild(errorMsg);
          }
          errorMsg.textContent = 'This field is required';
        } else {
          // Remove error styling
          field.classList.remove('border-destructive');
          const errorMsg = field.parentNode.querySelector('.error-message');
          if (errorMsg) errorMsg.remove();
        }
      });

      if (!isValid) {
        e.preventDefault();
      }
    });
  }
}

/**
 * DrawerManager for managing multiple drawers
 */
class DrawerManager {
  constructor() {
    this.activeDrawers = [];
    this.drawerStack = [];
    this.formTypes = {}; // Form URL mappings
  }

  /**
   * Register a form type with its URLs
   * @param {string} formType - The type of form (e.g., 'user', 'business_unit', 'attendance_type')
   * @param {Object} config - Configuration for this form type
   * @param {string} config.createUrl - URL for creating a new entity
   * @param {string} config.editUrl - URL for editing an existing entity (use {id} as placeholder)
   * @param {string} config.position - Position of the drawer (right, left, top, bottom)
   * @param {string} config.size - Size of the drawer (sm, md, lg, xl)
   */
  registerFormType(formType, config) {
    this.formTypes[formType] = config;
  }

  /**
   * Open a drawer with the specified form type
   * @param {string} formType - The type of form to open (e.g., 'user', 'business_unit')
   * @param {string|number} entityId - Optional ID of the entity to edit
   * @param {Object} options - Options for the drawer
   * @returns {Promise<DrawerComponent>} - Promise resolving to the drawer instance
   */
  openForm(formType, entityId = null, options = {}) {
    // Check if we have a registered form type
    if (this.formTypes[formType]) {
      const config = this.formTypes[formType];
      let url;

      // Determine the URL based on whether this is a create or edit operation
      if (entityId) {
        url = config.editUrl.replace('{id}', entityId);
      } else {
        url = config.createUrl;
      }

      // Apply drawer configuration from the registered form type
      options = Object.assign({
        position: config.position || 'right',
        size: config.size || 'md'
      }, options);

      // Build query parameters if provided
      if (options.queryParams) {
        const queryParams = new URLSearchParams();
        Object.entries(options.queryParams).forEach(([key, value]) => {
          queryParams.append(key, value);
        });

        const queryString = queryParams.toString();
        if (queryString) {
          url += (url.includes('?') ? '&' : '?') + queryString;
        }
      }

      return this.fetchAndOpenForm(url, options);
    }

    // Fallback to the previous behavior if the form type is not registered
    // Try to determine the correct URL based on the current page
    let url;

    // If we're on an admin page, try the admin URL first
    if (window.location.pathname.startsWith('/admin')) {
      url = `/admin/forms/${formType}`;
    } else {
      // Otherwise use the regular forms URL
      url = `/forms/${formType}`;
    }

    // Build query parameters
    const queryParams = new URLSearchParams();

    // Add entity ID as a query parameter instead of in the URL path
    if (entityId) {
      queryParams.append('id', entityId);
    }

    // Add additional query parameters from options
    if (options.queryParams) {
      Object.entries(options.queryParams).forEach(([key, value]) => {
        queryParams.append(key, value);
      });
    }

    // Add query parameters to URL if there are any
    const queryString = queryParams.toString();
    if (queryString) {
      url += `?${queryString}`;
    }

    return fetch(url)
      .then(response => {
        if (!response.ok) {
          throw new Error(`HTTP error! Status: ${response.status}`);
        }
        return response.text().then(text => {
          try {
            return JSON.parse(text);
          } catch (e) {
            throw new Error('Invalid JSON response from server');
          }
        });
      })
      .then(data => {
        if (!data.success) {
          throw new Error(data.error || 'Failed to load form');
        }

        const drawer = new DrawerComponent(options);
        drawer.open(data.html);

        this.activeDrawers.push(drawer);
        this.drawerStack.push(drawer);

        return drawer;
      })
      .catch(error => {
        if (typeof showToast === 'function') {
          showToast(error.message || 'Failed to load form', { type: 'error' });
        }
        return null;
      });
  }

  /**
   * Helper method to fetch a form from a URL and open it in a drawer
   * @param {string} url - The URL to fetch the form from
   * @param {Object} options - Drawer options
   * @returns {Promise<DrawerComponent>} - Promise resolving to the drawer instance
   * @private
   */
  fetchAndOpenForm(url, options = {}) {
    return fetch(url)
      .then(response => {
        if (!response.ok) {
          throw new Error(`HTTP error! Status: ${response.status}`);
        }
        return response.text().then(text => {
          try {
            return JSON.parse(text);
          } catch (e) {
            // If it's not JSON, it might be direct HTML
            return { success: true, html: text };
          }
        });
      })
      .then(data => {
        if (!data.success) {
          throw new Error(data.error || 'Failed to load form');
        }

        const drawer = new DrawerComponent(options);
        drawer.open(data.html);

        this.activeDrawers.push(drawer);
        this.drawerStack.push(drawer);

        return drawer;
      })
      .catch(error => {
        if (typeof showToast === 'function') {
          showToast(error.message || 'Failed to load form', { type: 'error' });
        }
        return null;
      });
  }

  /**
   * Close all open drawers
   */
  closeAll() {
    this.activeDrawers.forEach(drawer => drawer.close());
    this.activeDrawers = [];
    this.drawerStack = [];
  }

  /**
   * Close the most recently opened drawer
   */
  closeLast() {
    if (this.drawerStack.length > 0) {
      const drawer = this.drawerStack.pop();
      drawer.close();

      // Remove from active drawers
      const index = this.activeDrawers.indexOf(drawer);
      if (index > -1) {
        this.activeDrawers.splice(index, 1);
      }
    }
  }

  /**
   * Open a drawer directly with HTML content
   * @param {string} html - HTML content to display in the drawer
   * @param {Object} options - Options for the drawer
   * @returns {DrawerComponent} - The created drawer component
   */
  openHtml(html, options = {}) {
    const drawer = new DrawerComponent(options);
    drawer.open(html);

    this.activeDrawers.push(drawer);
    this.drawerStack.push(drawer);

    return drawer;
  }
}

window.DrawerComponent = DrawerComponent;
window.drawerManager = new DrawerManager();

drawerManager.testConnection = function() {
  // Try to determine the correct URL based on the current page
  let url;

  // If we're on an admin page, try the admin URL first
  if (window.location.pathname.startsWith('/admin')) {
    url = '/admin/forms/test';
  } else {
    // Otherwise use the regular forms URL
    url = '/forms/test';
  }

  fetch(url)
    .then(response => {
      if (!response.ok) {
        throw new Error(`HTTP error! Status: ${response.status}`);
      }
      return response.text().then(text => {
        try {
          return JSON.parse(text);
        } catch (e) {
          throw new Error('Invalid JSON response from server');
        }
      });
    })
    .then(() => {
      if (typeof showToast === 'function') {
        showToast('Forms API connection successful', { type: 'success' });
      }
    })
    .catch(error => {
      if (typeof showToast === 'function') {
        showToast(`Forms API connection failed: ${error.message}`, { type: 'error' });
      }
    });
};

// Initialize drawer triggers when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
  document.querySelectorAll('[data-drawer-trigger]').forEach(trigger => {
    trigger.addEventListener('click', function(e) {
      e.preventDefault();

      const formType = this.dataset.drawerTrigger;
      const entityId = this.dataset.entityId;
      const position = this.dataset.drawerPosition || 'right';
      const width = this.dataset.drawerWidth || '30rem';

      window.drawerManager.openForm(formType, entityId, {
        position: position,
        width: width
      });
    });
  });
});

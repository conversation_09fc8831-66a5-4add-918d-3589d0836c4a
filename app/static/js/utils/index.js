/**
 * Utilities Module - Main Entry Point
 *
 * This module imports and re-exports all utility functions from the utils package.
 */

// Import from modular structure
import { handleFlashMessages } from './dom.js';
import { showToast, closeToast, initToastContainer } from './toast.js';
import { openDrawer } from './drawer.js';
import { showAlertDialog } from './dialog.js';

// Import optimization utilities
import { loadScript, loadScripts, loadScriptOnDemand, preloadScript } from './script-loader.js';
import { loadStylesheet, preloadImage, preloadImages, loadFont, loadOptimizedAssets } from './asset-loader.js';
import {
  batchDOMOperations,
  throttleDOMUpdates,
  getElement,
  getElements,
  clearElementCache,
  createElements,
  delegateEvent
} from './dom-optimizer.js';

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
  // Initialize flash messages
  handleFlashMessages();
});

// Export all functions
export {
  // DOM utilities
  handleFlashMessages,

  // Toast notifications
  showToast,
  closeToast,
  initToastContainer,

  // Drawer component
  openDrawer,

  // Dialog component
  showAlertDialog,

  // Script loading utilities
  loadScript,
  loadScripts,
  loadScriptOnDemand,
  preloadScript,

  // Asset loading utilities
  loadStylesheet,
  preloadImage,
  preloadImages,
  loadFont,
  loadOptimizedAssets,

  // DOM optimization utilities
  batchDOMOperations,
  throttleDOMUpdates,
  getElement,
  getElements,
  clearElementCache,
  createElements,
  delegateEvent
};

// Make these functions globally available for backward compatibility
window.handleFlashMessages = handleFlashMessages;
window.showToast = showToast;
window.closeToast = closeToast;
window.openDrawer = openDrawer;
window.showAlertDialog = showAlertDialog;

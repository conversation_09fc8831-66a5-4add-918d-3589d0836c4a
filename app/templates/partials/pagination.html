{% if pagination %}
<div class="px-6 py-3 flex items-center justify-between border-t border-border">
  <div class="text-sm text-muted-foreground">
    Showing <span class="font-medium">{{ pagination.start_index }}</span> to <span class="font-medium">{{ pagination.end_index }}</span> of <span class="font-medium">{{ pagination.total_items }}</span> results
  </div>
  <div class="flex space-x-2">
    <a href="{{ url_for(request.endpoint, page=pagination.prev_page, **request.view_args) }}" class="btn btn-outline btn-sm {% if not pagination.has_prev %}opacity-50 pointer-events-none{% endif %}">
      <i data-lucide="chevron-left" class="w-4 h-4 mr-1"></i>
      Previous
    </a>
    <a href="{{ url_for(request.endpoint, page=pagination.next_page, **request.view_args) }}" class="btn btn-outline btn-sm {% if not pagination.has_next %}opacity-50 pointer-events-none{% endif %}">
      Next
      <i data-lucide="chevron-right" class="w-4 h-4 ml-1"></i>
    </a>
  </div>
</div>
{% endif %}

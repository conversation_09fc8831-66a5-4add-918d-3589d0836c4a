{% macro department_activity(department_data) %}
<div class="analytics-card bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 shadow-lg overflow-hidden transition-all duration-200 h-full flex flex-col">
    <div class="flex flex-col sm:flex-row sm:items-center justify-between p-5 md:p-6 border-b border-gray-200 dark:border-gray-700 bg-gray-50/80 dark:bg-gray-800/80 analytics-card-header">
        <div>
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white flex items-center analytics-text">
                <i data-lucide="briefcase" class="w-5 h-5 text-blue-600 dark:text-blue-400 mr-2"></i>
                <span>Department Activity</span>
            </h3>
            <p class="text-sm text-gray-600 dark:text-gray-400 mt-1 analytics-text">User activity by department</p>
        </div>
        <!-- No period filter for department section -->
        <div class="flex items-center space-x-2 mt-3 sm:mt-0">
            <!-- Empty space to maintain layout -->
        </div>
    </div>
    <div class="p-5 md:p-6 flex-1 overflow-y-auto analytics-card-content">
        <!-- Department Summary -->
        <div class="flex items-center justify-between mb-4 pb-2 border-b border-gray-100 dark:border-gray-700">
            <div class="text-xs text-gray-600 dark:text-gray-400 analytics-text">Department</div>
            <div class="text-xs text-gray-600 dark:text-gray-400 analytics-text">Activity</div>
        </div>

        <div class="space-y-4 overflow-y-auto" style="max-height: 300px;">
        {% if department_data %}
            {% for dept in department_data %}
            <div class="bg-white dark:bg-gray-700/50 border border-gray-200 dark:border-gray-700 rounded-lg p-4 shadow-sm">
                <div class="flex justify-between items-center mb-2">
                    <div class="flex items-center">
                        <div class="w-2 h-2 rounded-full {% if loop.index == 1 %}bg-blue-500 dark:bg-blue-400{% elif loop.index == 2 %}bg-green-500 dark:bg-green-400{% elif loop.index == 3 %}bg-purple-500 dark:bg-purple-400{% elif loop.index == 4 %}bg-orange-500 dark:bg-orange-400{% else %}bg-pink-500 dark:bg-pink-400{% endif %} mr-2"></div>
                        <span class="text-sm font-medium text-gray-800 dark:text-gray-200 analytics-text">{{ dept.name }}</span>
                    </div>
                    <span class="text-sm font-semibold text-gray-900 dark:text-white analytics-text">{{ dept.percentage }}</span>
                </div>
                <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2.5 mb-2 overflow-hidden shadow-inner">
                    <div class="{% if loop.index == 1 %}bg-blue-500 dark:bg-blue-400{% elif loop.index == 2 %}bg-green-500 dark:bg-green-400{% elif loop.index == 3 %}bg-purple-500 dark:bg-purple-400{% elif loop.index == 4 %}bg-orange-500 dark:bg-orange-400{% else %}bg-pink-500 dark:bg-pink-400{% endif %} h-2.5 rounded-full analytics-progress transition-all duration-500 ease-out" style="width: {{ dept.percentage }}"></div>
                </div>
                <div class="flex justify-between">
                    <span class="text-xs text-gray-500 dark:text-gray-400 analytics-text flex items-center"><i data-lucide="users" class="w-3 h-3 mr-1"></i> {{ dept.users }} users</span>
                    <span class="text-xs text-gray-500 dark:text-gray-400 analytics-text flex items-center"><i data-lucide="activity" class="w-3 h-3 mr-1"></i> {{ dept.activity }} activities</span>
                </div>
            </div>
            {% endfor %}
        {% else %}
            <div class="flex items-center justify-center h-64 bg-gray-50 dark:bg-gray-800/50 rounded-lg border border-gray-200 dark:border-gray-700">
                <div class="text-center p-6">
                    <div class="w-12 h-12 rounded-full bg-blue-100 dark:bg-blue-900/30 flex items-center justify-center mx-auto mb-4">
                        <i data-lucide="briefcase" class="w-6 h-6 text-blue-600 dark:text-blue-400"></i>
                    </div>
                    <h4 class="text-gray-900 dark:text-gray-100 font-medium mb-2 analytics-text">No Department Data</h4>
                    <p class="text-gray-500 dark:text-gray-400 mb-1 analytics-text">No department activity data available</p>
                    <p class="text-xs text-gray-400 dark:text-gray-500 analytics-text">Department data will appear here once users start using the system</p>
                </div>
            </div>
        {% endif %}
        </div>
    </div>
</div>
{% endmacro %}

/**
 * Global access to icon functions
 * This script exposes the icon functions globally for non-module scripts
 * Optimized to use SVG sprite for critical icons
 */

import { createIcon, initIcons, initIconsInContainer } from '../dist/icons.bundle.js';
import { loadIconSprite } from './icon-utils.js';

// Expose functions globally
window.iconUtils = {
  createIcon,
  initIcons,
  initIconsInContainer
};

// Load the sprite as early as possible
loadIconSprite().catch(error => {
  console.warn('Failed to preload icon sprite:', error);
});

// Initialize icons on page load
document.addEventListener('DOMContentLoaded', () => {
  // Only initialize once
  if (!window.iconsInitialized) {
    initIcons();
    window.iconsInitialized = true;
  }
});

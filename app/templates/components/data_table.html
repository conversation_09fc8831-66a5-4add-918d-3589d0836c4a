{% macro data_table(headers, items, empty_icon="file-x", empty_title="No items found", empty_description="No data available at this time", empty_button_text=None, empty_button_icon=None, empty_button_action=None) %}
<div class="overflow-x-auto">
  <table class="w-full">
    <thead>
      <tr class="border-b border-border bg-muted/40">
        {% for header in headers %}
        <th class="px-4 py-3 text-left text-sm font-medium text-muted-foreground {% if header.align == 'right' %}text-right{% endif %}">{{ header.label }}</th>
        {% endfor %}
      </tr>
    </thead>
    <tbody>
      {% if items and items|length > 0 %}
        {% for item in items %}
          <tr class="border-b border-border hover:bg-muted/30">
            {{ caller(item) }}
          </tr>
        {% endfor %}
      {% else %}
        <tr>
          <td colspan="{{ headers|length }}" class="px-4 py-8 text-center text-muted-foreground">
            <div class="flex flex-col items-center justify-center space-y-3">
              <i data-lucide="{{ empty_icon }}" class="w-12 h-12 text-muted-foreground/50"></i>
              <div class="text-center">
                <p class="text-lg font-medium">{{ empty_title }}</p>
                <p class="text-sm text-muted-foreground">{{ empty_description }}</p>
              </div>
              {% if empty_button_text %}
              <button onclick="{{ empty_button_action }}" class="btn btn-primary btn-sm mt-2">
                {% if empty_button_icon %}
                <i data-lucide="{{ empty_button_icon }}" class="w-4 h-4 mr-2"></i>
                {% endif %}
                <span>{{ empty_button_text }}</span>
              </button>
              {% endif %}
            </div>
          </td>
        </tr>
      {% endif %}
    </tbody>
  </table>
</div>
{% endmacro %}

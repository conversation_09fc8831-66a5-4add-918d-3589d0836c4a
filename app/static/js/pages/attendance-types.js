/**
 * Attendance Types Page JavaScript
 *
 * This file contains functions specific to the attendance types management page.
 */

/**
 * Open the form to add a new attendance type
 */
function openAddAttendanceTypeForm() {
  const url = document.getElementById('add-attendance-type-url').getAttribute('data-url');
  if (!url) {
    console.error('Add attendance type URL not found');
    if (typeof showToast === 'function') {
      showToast('Error loading form: URL not found', { type: 'error' });
    }
    return;
  }

  fetch(url)
    .then(response => {
      if (!response.ok) {
        throw new Error(`HTTP error! Status: ${response.status}`);
      }
      return response.text();
    })
    .then(html => {
      // Use the new openHtml method of the DrawerManager
      const drawer = window.drawerManager.openHtml(html, {
        position: 'right',
        size: 'md'
      });
      return drawer;
    })
    .catch(error => {
      console.error('Error opening add attendance type form:', error);
      if (typeof showToast === 'function') {
        showToast(`Error loading form: ${error.message}`, { type: 'error' });
      }
    });
}

/**
 * Open the form to edit an existing attendance type
 * @param {number} typeId - ID of the attendance type to edit
 */
function openEditAttendanceTypeForm(typeId) {
  const baseUrl = document.getElementById('edit-attendance-type-base-url').getAttribute('data-url');
  if (!baseUrl) {
    console.error('Edit attendance type base URL not found');
    if (typeof showToast === 'function') {
      showToast('Error loading form: URL not found', { type: 'error' });
    }
    return;
  }  const url = baseUrl.replace('0', typeId);

  fetch(url)
    .then(response => {
      if (!response.ok) {
        throw new Error(`HTTP error! Status: ${response.status}`);
      }
      return response.text();
    })
    .then(html => {
      // Use the new openHtml method of the DrawerManager
      const drawer = window.drawerManager.openHtml(html, {
        position: 'right',
        size: 'md'
      });
      return drawer;
    })
    .catch(error => {
      console.error('Error opening edit attendance type form:', error);
      if (typeof showToast === 'function') {
        showToast(`Error loading form: ${error.message}`, { type: 'error' });
      }
    });
}

/**
 * Confirm deletion of an attendance type
 * @param {number} typeId - ID of the attendance type to delete
 * @param {string} typeName - Name of the attendance type
 */
function confirmDeleteAttendanceType(typeId, typeName) {
  showAlertDialog({
    title: 'Delete Attendance Type',
    description: `Are you sure you want to delete the attendance type "${typeName}"? This action cannot be undone.`,
    variant: 'destructive',
    onConfirm: () => {
      const baseUrl = document.getElementById('delete-attendance-type-base-url').getAttribute('data-url');
      if (!baseUrl) {
        console.error('Delete attendance type base URL not found');
        if (typeof showToast === 'function') {
          showToast('Error deleting attendance type: URL not found', { type: 'error' });
        }
        return;
      }

      const form = document.createElement('form');
      form.method = 'POST';
      form.action = baseUrl.replace('0', typeId);

      const csrfTokenInput = document.createElement('input');
      csrfTokenInput.type = 'hidden';
      csrfTokenInput.name = 'csrf_token';
      csrfTokenInput.value = document.getElementById('csrf-token').getAttribute('data-token');
      form.appendChild(csrfTokenInput);

      document.body.appendChild(form);
      form.submit();
    }
  });
}

// Export functions for use in other modules
window.openAddAttendanceTypeForm = openAddAttendanceTypeForm;
window.openEditAttendanceTypeForm = openEditAttendanceTypeForm;
window.confirmDeleteAttendanceType = confirmDeleteAttendanceType;

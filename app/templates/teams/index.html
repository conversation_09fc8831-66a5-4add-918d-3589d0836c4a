{% extends "base.html" %}

{% from "components/page_header.html" import page_header %}
{% from "components/table_header.html" import table_header %}
{% from "components/simple_table.html" import simple_table %}
{% from "components/action_buttons.html" import action_buttons %}

{% block title %}Teams{% endblock %}

{% block header %}Teams{% endblock %}

{% block content %}
<div class="space-y-6">
  {{ page_header(
    title="Teams",
    button_text="Add Team",
    button_icon="plus",
    button_action="drawerManager.openForm('team')"
  ) }}

  <div class="card">
    {{ table_header(
      title="Team List",
      count=pagination.total,
      count_label="teams"
    ) }}

    {% call simple_table(
      headers=[
        {'label': 'Name'},
        {'label': 'Short Name'},
        {'label': 'Members'},
        {'label': 'Groups'},
        {'label': 'Status'},
        {'label': 'Actions', 'align': 'right'}
      ],
      items=teams,
      empty_icon="users",
      empty_title="No teams found",
      empty_description="Create a new team to get started",
      empty_button_text="Add Team",
      empty_button_icon="plus",
      empty_button_action="drawerManager.openForm('team')"
    ) %}
      {% for team in teams %}
      <tr class="border-b border-border hover:bg-muted/30">
        <td class="px-4 py-3 text-sm">
          <a href="{{ url_for('teams.view', slug=team.slug) }}" class="font-medium text-primary hover:underline">
            {{ team.name }}
          </a>
        </td>
        <td class="px-4 py-3 text-sm">{{ team.short_name or '-' }}</td>
        <td class="px-4 py-3 text-sm">{{ team.members|length }}</td>
        <td class="px-4 py-3 text-sm">{{ team.groups.count() }}</td>
        <td class="px-4 py-3 text-sm">
          {% if team.is_active %}
          <span class="badge badge-success">Active</span>
          {% else %}
          <span class="badge badge-secondary">Inactive</span>
          {% endif %}
        </td>
        <td class="px-4 py-3 text-sm text-right">
          {{ action_buttons([
            {'type': 'link', 'url': url_for('teams.view', slug=team.slug), 'icon': 'eye', 'title': 'View'},
            {'type': 'button', 'action': 'drawerManager.openForm(\'team\', ' ~ team.id ~ ')', 'icon': 'edit', 'title': 'Edit'},
            {'type': 'button', 'action': 'confirmDelete(' ~ team.id ~ ', \'' ~ team.name ~ '\')', 'icon': 'trash-2', 'variant': 'text-destructive', 'title': 'Delete'}
          ]) }}
        </td>
      </tr>
      {% endfor %}
    {% endcall %}

    {% include "partials/pagination.html" %}
  </div>
</div>
{% endblock %}

{% block scripts %}
<script>
  function confirmDelete(teamId, teamName) {
    showAlertDialog({
      title: 'Delete Team',
      description: `Are you sure you want to delete the team "${teamName}"? This action cannot be undone.`,
      variant: 'destructive',
      onConfirm: () => {
        // Submit form to delete endpoint
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = "{{ url_for('teams.delete', team_id=0) }}".replace('0', teamId);

        const csrfToken = document.createElement('input');
        csrfToken.type = 'hidden';
        csrfToken.name = 'csrf_token';
        csrfToken.value = '{{ csrf_token() }}';

        form.appendChild(csrfToken);
        document.body.appendChild(form);
        form.submit();
      }
    });
  }

  // Show success toast if redirected from a successful action
  document.addEventListener('DOMContentLoaded', function() {
    // Process icons if needed
    if (window.processIcons) {
      window.processIcons();
    }

    // We're now using flash messages converted to toasts via handleFlashMessages() in utils.js
    // This happens automatically on page load and provides a consistent notification experience

    // Clean URL parameters if they exist (for backward compatibility)
    const urlParams = new URLSearchParams(window.location.search);
    if (urlParams.has('status')) {
      const newUrl = window.location.pathname;
      window.history.replaceState({}, document.title, newUrl);
    }
  });
</script>
{% endblock %}

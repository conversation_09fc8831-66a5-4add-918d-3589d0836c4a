{% extends "layouts/base.html" %}
{% from "partials/forms/base_form.html" import form_group %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="max-w-2xl mx-auto space-y-6">
  <!-- <PERSON> Header -->
  <div class="text-center">
    <h1 class="text-2xl font-bold text-gray-900 dark:text-white">{{ title }}</h1>
    <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
      Edit your attendance request for {{ record.date.strftime('%B %d, %Y') }}
    </p>
  </div>

  <!-- Form Card -->
  <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
    <form method="POST" id="attendanceEditForm" class="space-y-6">
      {{ form.hidden_tag() }}

      {# Holiday Warning Alert (existing or updated) #}
      {% if holiday_warning and holiday_warning.is_holiday %}
      <div class="bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800 rounded-lg p-4">
        <div class="flex items-start">
          <div class="flex-shrink-0">
            <svg class="h-5 w-5 text-amber-400" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
            </svg>
          </div>
          <div class="ml-3">
            <h3 class="text-sm font-medium text-amber-800 dark:text-amber-200">
              Holiday Date Selected
            </h3>
            <div class="mt-2 text-sm text-amber-700 dark:text-amber-300">
              <p>The selected date is <strong>{{ holiday_warning.holiday_name }}</strong> ({{ holiday_warning.region_code }}).</p>
              {% if holiday_warning.holiday_description %}
              <p class="mt-1">{{ holiday_warning.holiday_description }}</p>
              {% endif %}
              <p class="mt-2 font-medium">Working on holidays may require special approval.</p>
            </div>
          </div>
        </div>
      </div>
      {% endif %}

      {# Dynamic Holiday Warning (will be populated by JavaScript) #}
      <div id="holidayWarning" class="hidden bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800 rounded-lg p-4">
        <div class="flex items-start">
          <div class="flex-shrink-0">
            <svg class="h-5 w-5 text-amber-400" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
            </svg>
          </div>
          <div class="ml-3">
            <h3 class="text-sm font-medium text-amber-800 dark:text-amber-200">
              Holiday Date Selected
            </h3>
            <div class="mt-2 text-sm text-amber-700 dark:text-amber-300" id="holidayWarningContent">
              <!-- Holiday warning content will be populated by JavaScript -->
            </div>
          </div>
        </div>
      </div>

      <!-- Current Record Info -->
      <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
        <h3 class="text-sm font-medium text-gray-900 dark:text-white mb-2">Current Request</h3>
        <div class="grid grid-cols-2 gap-4 text-sm">
          <div>
            <span class="text-gray-500 dark:text-gray-400">Date:</span>
            <span class="ml-2 text-gray-900 dark:text-white">{{ record.date.strftime('%Y-%m-%d') }}</span>
          </div>
          <div>
            <span class="text-gray-500 dark:text-gray-400">Type:</span>
            <span class="ml-2 text-gray-900 dark:text-white">{{ record.attendance_type.name }}</span>
          </div>
          <div>
            <span class="text-gray-500 dark:text-gray-400">Status:</span>
            <span class="ml-2 inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200">
              {{ record.status.title() }}
            </span>
          </div>
          <div>
            <span class="text-gray-500 dark:text-gray-400">Submitted:</span>
            <span class="ml-2 text-gray-900 dark:text-white">{{ record.created_at.strftime('%m/%d/%Y') }}</span>
          </div>
        </div>
      </div>

      <!-- Date Selection -->
      <div>
        {{ form_group(
          label = form.date.label.text,
          name = form.date.name,
          type = "date",
          value = form.date.data.strftime('%Y-%m-%d') if form.date.data else "",
          required = form.date.flags.required
        ) }}
        {% if form.date.errors %}
          <div class="text-xs text-destructive mt-1">
            <ul>
              {% for error in form.date.errors %}<li>{{ error }}</li>{% endfor %}
            </ul>
          </div>
        {% endif %}
      </div>

      <!-- Attendance Type Selection -->
      <div>
        {{ form_group(
          label = form.attendance_type_id.label.text,
          name = form.attendance_type_id.name,
          type = "select",
          value = form.attendance_type_id.data if form.attendance_type_id.data else "",
          required = form.attendance_type_id.flags.required,
          options = form.attendance_type_id.choices
        ) }}
        {% if form.attendance_type_id.errors %}
          <div class="text-xs text-destructive mt-1">
            <ul>
              {% for error in form.attendance_type_id.errors %}<li>{{ error }}</li>{% endfor %}
            </ul>
          </div>
        {% endif %}
      </div>

      <!-- Time Fields (for partial day types) -->
      <div class="grid grid-cols-2 gap-4" id="timeFields" style="display: none;">
        <div>
          {{ form_group(
            label = form.start_time.label.text,
            name = form.start_time.name,
            type = "time",
            value = form.start_time.data.strftime('%H:%M') if form.start_time.data else "",
            description = form.start_time.description
          ) }}
          {% if form.start_time.errors %}
            <div class="text-xs text-destructive mt-1">
              <ul>
                {% for error in form.start_time.errors %}<li>{{ error }}</li>{% endfor %}
              </ul>
            </div>
          {% endif %}
        </div>

        <div>
          {{ form_group(
            label = form.end_time.label.text,
            name = form.end_time.name,
            type = "time",
            value = form.end_time.data.strftime('%H:%M') if form.end_time.data else "",
            description = form.end_time.description
          ) }}
          {% if form.end_time.errors %}
            <div class="text-xs text-destructive mt-1">
              <ul>
                {% for error in form.end_time.errors %}<li>{{ error }}</li>{% endfor %}
              </ul>
            </div>
          {% endif %}
        </div>
      </div>

      <!-- Duration Field (for partial day types) -->
      <div id="durationField" style="display: none;">
        {{ form_group(
          label = form.duration_hours.label.text,
          name = form.duration_hours.name,
          type = "number",
          value = form.duration_hours.data if form.duration_hours.data else "",
          description = form.duration_hours.description,
          step = "0.5",
          min = "0.5",
          max = "24"
        ) }}
        {% if form.duration_hours.errors %}
          <div class="text-xs text-destructive mt-1">
            <ul>
              {% for error in form.duration_hours.errors %}<li>{{ error }}</li>{% endfor %}
            </ul>
          </div>
        {% endif %}
      </div>

      <!-- Notes Field -->
      <div>
        {{ form_group(
          label = form.notes.label.text,
          name = form.notes.name,
          type = "textarea",
          value = form.notes.data if form.notes.data else "",
          placeholder = form.notes.render_kw.placeholder
        ) }}
        {% if form.notes.errors %}
          <div class="text-xs text-destructive mt-1">
            <ul>
              {% for error in form.notes.errors %}<li>{{ error }}</li>{% endfor %}
            </ul>
          </div>
        {% endif %}
      </div>

      <!-- Form Actions -->
      <div class="flex justify-end space-x-3 pt-6 border-t border-gray-200 dark:border-gray-700">
        <a href="{{ url_for('attendance.my_attendance') }}" class="btn btn-outline btn-md">
          Cancel
        </a>
        <button type="submit" class="btn btn-primary btn-md" id="submitBtn">
          <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
          </svg>
          Update Request
        </button>
      </div>
    </form>
  </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
  const form = document.getElementById('attendanceEditForm');
  const attendanceTypeSelect = document.getElementById('attendance_type_id');
  const timeFields = document.getElementById('timeFields');
  const durationField = document.getElementById('durationField');
  const dateField = document.getElementById('date');
  const holidayWarning = document.getElementById('holidayWarning');
  const holidayWarningContent = document.getElementById('holidayWarningContent');
  const submitBtn = document.getElementById('submitBtn');
  
  // Show/hide time and duration fields based on attendance type
  function toggleFields() {
    const selectedOption = attendanceTypeSelect.options[attendanceTypeSelect.selectedIndex];
    if (selectedOption && selectedOption.dataset.isFullDay === 'false') {
      timeFields.style.display = 'grid';
      durationField.style.display = 'block';
    } else {
      timeFields.style.display = 'none';
      durationField.style.display = 'none';
    }
  }
  
  // Check for holidays when date changes
  function checkHoliday() {
    if (dateField.value) {
      fetch(`/api/holidays/check?date=${dateField.value}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        }
      })
      .then(response => response.json())
      .then(data => {
        if (data.success && data.is_holiday) {
          holidayWarningContent.innerHTML = `
            <p>The selected date is <strong>${data.holiday_name}</strong> (${data.region_code}).</p>
            ${data.holiday_description ? `<p class="mt-1">${data.holiday_description}</p>` : ''}
            <p class="mt-2 font-medium">Working on holidays may require special approval.</p>
          `;
          holidayWarning.classList.remove('hidden');
        } else {
          holidayWarning.classList.add('hidden');
        }
      })
      .catch(error => {
        console.log('Holiday check failed:', error);
        holidayWarning.classList.add('hidden');
      });
    } else {
      holidayWarning.classList.add('hidden');
    }
  }
  
  // Event listeners
  attendanceTypeSelect.addEventListener('change', toggleFields);
  dateField.addEventListener('change', checkHoliday);
  
  // Initial calls
  toggleFields();
  checkHoliday();
  
  // Form submission
  form.addEventListener('submit', function(e) {
    e.preventDefault();
    
    submitBtn.disabled = true;
    submitBtn.innerHTML = `
      <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
      </svg>
      Updating...
    `;
    
    const formData = new FormData(form);
    
    fetch(form.action, {
      method: 'POST',
      body: formData,
      headers: {
        'X-CSRFToken': document.querySelector('input[name="csrf_token"]').value
      }
    })
    .then(response => response.json())
    .then(data => {
      if (data.success) {
        showToast(data.message, 'success');
        if (data.redirect_url) {
          window.location.href = data.redirect_url;
        } else {
          window.location.href = '{{ url_for("attendance.my_attendance") }}';
        }
      } else {
        showToast(data.message, 'error');
        if (data.errors) {
          Object.keys(data.errors).forEach(field => {
            console.log(`Error in ${field}:`, data.errors[field]);
          });
        }
      }
    })
    .catch(error => {
      showToast('An error occurred while updating the request.', 'error');
      console.error('Error:', error);
    })
    .finally(() => {
      submitBtn.disabled = false;
      submitBtn.innerHTML = `
        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
        </svg>
        Update Request
      `;
    });
  });
});
</script>
{% endblock %}

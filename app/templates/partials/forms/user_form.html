{% from "partials/forms/base_form.html" import form_group, form_actions, form_header %}

<form action="{{ action_url }}" method="POST" class="space-y-4">
  <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">

  {{ form_header(
    title = "Edit User" if entity else "Add New User",
    description = "Update user information" if entity else "Create a new user account"
  ) }}

  <div class="grid grid-cols-1 gap-4">
    {{ form_group(
      label = "Full Name",
      name = "name",
      value = entity.name if entity else "",
      required = true,
      placeholder = "<PERSON>"
    ) }}

    {{ form_group(
      label = "Email",
      name = "email",
      type = "email",
      value = entity.email if entity else "",
      required = true,
      placeholder = "<EMAIL>"
    ) }}

    {{ form_group(
      label = "Password" if not entity else "New Password",
      name = "password" if not entity else "new_password",
      type = "password",
      required = not entity,
      hint = "Leave blank to keep current password" if entity else ""
    ) }}

    {{ form_group(
      label = "Role",
      name = "role",
      type = "select",
      value = entity.role if entity else "",
      required = true,
      options = [
        ("User", "User"),
        ("Manager", "Manager"),
        ("Admin", "Admin")
      ]
    ) }}

    {% if entity %}
      {{ form_group(
        label = "Active",
        name = "is_active",
        type = "checkbox",
        value = entity.is_active
      ) }}
    {% endif %}
  </div>

  {{ form_actions(
    submit_label = "Update User" if entity else "Add User"
  ) }}
</form>

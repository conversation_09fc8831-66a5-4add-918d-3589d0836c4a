{% macro info_card(label, icon, content, content_type="text", height="120px") %}
<div class="rounded-lg border border-border overflow-hidden h-[{{ height }}] flex flex-col">
  <div class="bg-muted/30 px-4 py-2 border-b border-border">
    <div class="flex items-center">
      <i data-lucide="{{ icon }}" class="w-4 h-4 mr-2 text-muted-foreground"></i>
      <label class="text-sm font-medium">{{ label }}</label>
    </div>
  </div>
  <div class="p-4 font-medium flex-1">
    {{ content|safe }}
  </div>
</div>
{% endmacro %}

{% macro date_content(date, format="%B %d, %Y", time_format="%I:%M %p", fallback="Unknown") %}
  {% if date %}
    <div>
      <p>{{ date.strftime(format) }}</p>
      <p class="text-xs text-muted-foreground">{{ date.strftime(time_format) }}</p>
    </div>
  {% else %}
    <span class="text-muted-foreground">{{ fallback }}</span>
  {% endif %}
{% endmacro %}

{% macro status_content(is_active, active_text="Active", inactive_text="Inactive") %}
  {% if is_active %}
    <span>{{ active_text }}</span>
  {% else %}
    <span>{{ inactive_text }}</span>
  {% endif %}
{% endmacro %}

{% macro manager_content(manager, role_field="role", fallback="Not assigned") %}
  {% if manager %}
    <div>
      <p>{{ manager.name }}</p>
      <p class="text-xs text-muted-foreground">{{ manager[role_field] }}</p>
    </div>
  {% else %}
    <span class="text-muted-foreground">{{ fallback }}</span>
  {% endif %}
{% endmacro %}

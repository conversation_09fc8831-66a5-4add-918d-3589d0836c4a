/**
 * Activity Log Management
 * Handles the log management modal functionality
 */

// Initialize event listeners when the document is loaded
document.addEventListener('DOMContentLoaded', function() {
  // Add event listener for the modal being shown
  document.addEventListener('modal:shown', function(event) {
    if (event.detail && event.detail.id === 'log-management') {
      // Initialize the visibility when the modal is shown
      setTimeout(function() {
        // Get the action select and trigger its change event
        const actionSelect = document.querySelector('#log-management-modal #log-action');
        if (actionSelect) {
          handleActionChange(actionSelect.value);
        }
      }, 100);
    }
  });
});

/**
 * Handle action select change
 * @param {string} action - The selected action value
 */
function handleActionChange(action) {

  // Use direct DOM manipulation with setTimeout to ensure the DOM is ready
  setTimeout(function() {
    // Get all option sections using document.getElementById for more reliable selection
    const daysOption = document.getElementById('days-option');
    const categoryOption = document.getElementById('category-option');
    const severityOption = document.getElementById('severity-option');
    const confirmationOption = document.getElementById('confirmation-option');



    // Hide all options first using style.display for more direct control
    if (daysOption) daysOption.style.display = 'none';
    if (categoryOption) categoryOption.style.display = 'none';
    if (severityOption) severityOption.style.display = 'none';
    if (confirmationOption) confirmationOption.style.display = 'none';

    // Show the appropriate option based on the selected action
    if (action === 'delete_older_than' && daysOption) {
      daysOption.style.display = 'block';
    } else if (action === 'delete_by_category' && categoryOption) {
      categoryOption.style.display = 'block';
    } else if (action === 'delete_by_severity' && severityOption) {
      severityOption.style.display = 'block';
    } else if (action === 'delete_all' && confirmationOption) {
      confirmationOption.style.display = 'block';
    }

    // Update button appearance if needed
    const submitBtn = document.getElementById('log-management-submit-btn');
    if (submitBtn) {
      if (action === 'delete_all') {
        submitBtn.className = 'inline-flex items-center justify-center rounded-md text-sm font-medium bg-red-600 hover:bg-red-700 text-white transition-all duration-200 focus:outline-none disabled:pointer-events-none disabled:opacity-50 h-10 px-4 py-2 shadow-sm hover:shadow min-w-[100px]';
        const textSpan = submitBtn.querySelector('span');
        if (textSpan) {
          textSpan.textContent = 'Delete All';
        }
      } else {
        submitBtn.className = 'inline-flex items-center justify-center rounded-md text-sm font-medium bg-amber-600 hover:bg-amber-700 text-white transition-all duration-200 focus:outline-none disabled:pointer-events-none disabled:opacity-50 h-10 px-4 py-2 shadow-sm hover:shadow min-w-[100px]';
        const textSpan = submitBtn.querySelector('span');
        if (textSpan) {
          textSpan.textContent = 'Delete';
        }
      }
    }
  }, 50); // Small delay to ensure DOM is ready
}

/**
 * Open the log management modal
 */
function openLogManagementModal() {
  // Get the template content
  const template = document.getElementById('log-management-modal-template');
  const content = template.content.cloneNode(true);

  // Show modal
  const modalInstance = showModal({
    id: 'log-management',
    content: content.querySelector('div').outerHTML,
    size: 'md'
  });
}

/**
 * Close the log management modal
 * This function is kept for backward compatibility
 * The modal component now handles closing automatically
 */
function closeLogManagementModal() {
  // Find any open modals and close them
  const modalElement = document.querySelector('.modal-dialog');
  if (modalElement) {
    const closeButton = modalElement.querySelector('[data-modal-close]');
    if (closeButton) {
      closeButton.click();

      // Reset form after closing
      setTimeout(() => {
        resetLogManagementForm();
      }, 300);
    }
  }
}

/**
 * Reset the log management form to its initial state
 */
function resetLogManagementForm() {

  // Reset form selections using direct DOM manipulation
  const actionSelect = document.getElementById('log-action');
  const daysSelect = document.getElementById('days-select');
  const categorySelect = document.getElementById('category-select');
  const severitySelect = document.getElementById('severity-select');
  const confirmationInput = document.getElementById('confirmation-input');

  if (actionSelect) actionSelect.value = 'delete_older_than';
  if (daysSelect) daysSelect.value = '30';
  if (categorySelect) categorySelect.value = '';
  if (severitySelect) severitySelect.value = '';
  if (confirmationInput) confirmationInput.value = '';

  // Reset visibility of option sections
  const daysOption = document.getElementById('days-option');
  const categoryOption = document.getElementById('category-option');
  const severityOption = document.getElementById('severity-option');
  const confirmationOption = document.getElementById('confirmation-option');

  if (daysOption) daysOption.style.display = 'block';
  if (categoryOption) categoryOption.style.display = 'none';
  if (severityOption) severityOption.style.display = 'none';
  if (confirmationOption) confirmationOption.style.display = 'none';

  // Reset progress and result sections
  const progressSection = document.getElementById('log-management-progress');
  const resultSection = document.getElementById('log-management-result');
  const formSection = document.getElementById('log-management-form');

  if (progressSection) progressSection.style.display = 'none';
  if (resultSection) resultSection.style.display = 'none';
  if (formSection) formSection.style.display = 'block';


}

/**
 * Update the visibility of form sections based on the selected action
 * This function is kept for backward compatibility
 */
function updateLogActionVisibility() {
  // Get the modal element
  const modalElement = document.getElementById('log-management-modal');
  if (!modalElement) return;

  // Find the action select within the modal
  const actionSelect = modalElement.querySelector('#log-action');
  if (!actionSelect) return;

  // Call handleActionChange with the current value
  handleActionChange(actionSelect.value);
}

/**
 * Validate the log management form before submission
 */
function validateLogForm() {

  // Use direct DOM manipulation for more reliable validation
  const actionSelect = document.getElementById('log-action');
  if (!actionSelect) {
    return false;
  }

  const action = actionSelect.value;

  // Reset any previous error states
  const allInputs = document.querySelectorAll('select, input');
  allInputs.forEach(input => {
    input.style.borderColor = '';
    input.style.boxShadow = '';
  });

  // Validate based on the action
  if (action === 'delete_by_category') {
    const categorySelect = document.getElementById('category-select');
    if (categorySelect && !categorySelect.value) {
      categorySelect.style.borderColor = 'rgb(239, 68, 68)';
      categorySelect.style.boxShadow = '0 0 0 1px rgb(239, 68, 68)';
      showToast('Please select a category to proceed with deletion', { type: 'error' });
      categorySelect.focus();
      return false;
    }
  } else if (action === 'delete_by_severity') {
    const severitySelect = document.getElementById('severity-select');
    if (severitySelect && !severitySelect.value) {
      severitySelect.style.borderColor = 'rgb(239, 68, 68)';
      severitySelect.style.boxShadow = '0 0 0 1px rgb(239, 68, 68)';
      showToast('Please select a severity level to proceed with deletion', { type: 'error' });
      severitySelect.focus();
      return false;
    }
  } else if (action === 'delete_all') {
    const confirmationInput = document.getElementById('confirmation-input');
    if (confirmationInput) {
      const confirmation = confirmationInput.value;

      // Validate confirmation
      if (confirmation !== 'DELETE_ALL_LOGS') {
        confirmationInput.style.borderColor = 'rgb(239, 68, 68)';
        confirmationInput.style.boxShadow = '0 0 0 1px rgb(239, 68, 68)';
        showToast('Please type DELETE_ALL_LOGS exactly as shown to confirm deletion of all logs', { type: 'error' });
        confirmationInput.focus();
        return false;
      }
    }
  }

  // Show confirmation toast
  if (action === 'delete_all') {
    showToast('Deleting all logs. This action cannot be undone.', { type: 'warning' });
  } else {
    showToast('Deleting selected logs. This action cannot be undone.', { type: 'warning' });
  }

  // Show progress
  const formSection = document.getElementById('log-management-form');
  const progressSection = document.getElementById('log-management-progress');

  if (formSection) formSection.style.display = 'none';
  if (progressSection) progressSection.style.display = 'block';

  return true;
}

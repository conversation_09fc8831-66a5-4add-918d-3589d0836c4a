/**
 * Analytics Dashboard CSS
 * Styles for the analytics dashboard components
 */

/* Card styles */
.analytics-card {
    @apply bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 shadow-md overflow-hidden transition-all duration-200;
}

.analytics-card:hover {
    @apply shadow-lg;
    transform: translateY(-2px);
}

.analytics-stat-card {
    @apply bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 shadow-md p-5 md:p-6 transition-all duration-200;
}

.analytics-stat-card:hover {
    @apply shadow-lg;
    transform: translateY(-2px);
}

/* Text transitions for theme switching */
.analytics-text {
    transition: color 0.3s ease;
}

/* Progress bar animations */
.analytics-progress {
    transition: width 0.5s ease-out, background-color 0.3s ease;
}

/* Chart loading overlay */
.chart-loading-overlay.flex {
    display: flex !important;
}

/* Responsive adjustments */
@media (max-width: 640px) {
    .analytics-grid-sm {
        grid-template-columns: repeat(1, minmax(0, 1fr)) !important;
    }
}

@media (max-width: 768px) {
    .analytics-card-content {
        padding: 1rem !important;
    }
}

/* Chart tooltip styles */
.chart-tooltip {
    @apply bg-white/95 dark:bg-gray-800/95 border border-gray-200 dark:border-gray-700 rounded-md shadow-lg p-2;
}

/* Skeleton loading animation */
@keyframes pulse {
    0%, 100% {
        opacity: 0.5;
    }
    50% {
        opacity: 0.8;
    }
}

.skeleton-loading {
    animation: pulse 1.5s ease-in-out infinite;
    @apply bg-gray-200 dark:bg-gray-700;
}

/* Chart legend styling */
.chart-legend {
    @apply flex flex-wrap justify-center gap-4 mt-3;
}

.chart-legend-item {
    @apply flex items-center text-sm text-gray-600 dark:text-gray-400;
}

.chart-legend-color {
    @apply w-3 h-3 rounded-full mr-2;
}

/* Registration bars animation */
.registration-bar {
    transition: height 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Refresh button animation */
.refresh-icon {
    transition: transform 0.5s ease;
}

.refresh-icon.animate-spin {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

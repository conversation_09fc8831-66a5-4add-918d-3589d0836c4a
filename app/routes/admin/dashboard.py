"""
Admin dashboard routes.
"""

from flask import render_template
from flask_login import login_required, current_user
import json
from datetime import datetime

from app import db
from app.models import User, BusinessUnit, BusinessSegment, Activity, EmployeeDetail, Team, PH_TZ
from app.utils.decorators import admin_required, log_activity
from app.routes.admin import admin_bp


@admin_bp.route('/')
@login_required
@admin_required
@log_activity('Viewed admin dashboard', category=Activity.CATEGORY_ADMIN, method=Activity.METHOD_READ, skip_ajax=True)
def dashboard():
    """Admin dashboard view with key metrics and charts."""
    # Get some stats for the dashboard
    user_count = User.query.count()
    business_unit_count = BusinessUnit.query.count()
    business_segment_count = BusinessSegment.query.count()
    team_count = Team.query.count()

    # Get recent important activities (warnings and errors)
    recent_activities = Activity.query.filter(
        Activity.severity.in_([Activity.SEVERITY_WARNING, Activity.SEVERITY_ERROR])
    ).order_by(Activity.created_at.desc()).limit(5).all()

    # Get user distribution by business unit
    user_distribution = db.session.query(
        BusinessUnit.name,
        db.func.count(EmployeeDetail.id)
    ).outerjoin(EmployeeDetail, BusinessUnit.id == EmployeeDetail.business_unit_id)\
     .group_by(BusinessUnit.name)\
     .all()

    # Format for chart
    unit_labels = [unit[0] for unit in user_distribution] if user_distribution else []
    unit_data = [unit[1] for unit in user_distribution] if user_distribution else []

    # Get managers for forms
    managers = User.query.filter(User.role.in_(['Admin', 'Manager'])).all()
    business_units = BusinessUnit.query.all()
    business_segments = BusinessSegment.query.all()

    # Get users without employee details for the form
    users_without_details = User.query.outerjoin(EmployeeDetail, User.id == EmployeeDetail.user_id).filter(EmployeeDetail.id == None).all()

    # Get current time for the dashboard
    now = datetime.now(PH_TZ)

    return render_template('admin/dashboard.html',
                          active_page='dashboard',
                          user_count=user_count,
                          business_unit_count=business_unit_count,
                          business_segment_count=business_segment_count,
                          team_count=team_count,
                          recent_activities=recent_activities,
                          unit_labels=json.dumps(unit_labels),
                          unit_data=json.dumps(unit_data),
                          managers=managers,
                          business_units=business_units,
                          business_segments=business_segments,
                          users=users_without_details,
                          now=now)

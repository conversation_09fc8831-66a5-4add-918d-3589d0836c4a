{% extends "base.html" %}

{% from "components/page_header.html" import page_header %}
{% from "components/button.html" import button, button_group, icon_button %}
{% from "components/action_buttons.html" import action_buttons %}
{% from "partials/forms/base_form.html" import form_group %}

{# Custom select macro for calendar controls with onchange support #}
{% macro calendar_select(label, name, options, selected_value, onchange=None, min_width="auto") %}
<div class="flex items-center space-x-2">
  <label for="{{ name }}" class="text-sm font-medium whitespace-nowrap">{{ label }}:</label>
  <div class="relative">
    <select id="{{ name }}" name="{{ name }}"
            class="flex h-10 w-auto min-w-[{{ min_width }}] rounded-md border border-input bg-background pl-3 pr-10 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 appearance-none"
            {% if onchange %}onchange="{{ onchange }}"{% endif %}>
      {% for option_value, option_label in options %}
        <option value="{{ option_value }}" {% if selected_value == option_value %}selected{% endif %}>{{ option_label }}</option>
      {% endfor %}
    </select>
    <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
      <i data-lucide="chevron-down" class="h-4 w-4 text-muted-foreground"></i>
    </div>
  </div>
</div>
{% endmacro %}

{% block title %}Holiday Calendar{% endblock %}

{% block header %}Holiday Calendar{% endblock %}

{% block content %}
<!-- Hidden fields for JavaScript -->
<input type="hidden" id="calendar-api-url" data-url="{{ url_for('api.get_holiday_calendar', region_code='PH', year=2025) }}">
<input type="hidden" id="quick-add-url" data-url="{{ url_for('admin.create_holiday') }}">
<input type="hidden" id="csrf-token" data-token="{{ csrf_token() }}">

<div class="space-y-6">
  {{ page_header(
    title="Holiday Calendar",
    button_text="Add Holiday",
    button_icon="plus",
    button_action="openAddHolidayForm()",
    description="View and manage holidays in calendar format."
  ) }}

  <!-- Filter Controls -->
  <div class="card">
    <div class="card-content p-6">
      <div class="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4 sm:gap-6 px-2">
        <!-- View Controls -->
        <div class="flex flex-wrap items-center gap-4">
          <!-- Region Selector -->
          {% set region_options = [] %}
          {% for region in available_regions %}
            {% if region == 'US' %}
              {% set _ = region_options.append((region, '🇺🇸 United States')) %}
            {% elif region == 'PH' %}
              {% set _ = region_options.append((region, '🇵🇭 Philippines')) %}
            {% elif region == 'GLOBAL' %}
              {% set _ = region_options.append((region, '🌍 Global')) %}
            {% else %}
              {% set _ = region_options.append((region, region)) %}
            {% endif %}
          {% endfor %}
          {{ calendar_select("Region", "regionSelect", region_options, current_region, "updateCalendar()", "140px") }}

          <!-- Year Selector -->
          {% set year_options = [] %}
          {% for year in range(current_year - 2, current_year + 3) %}
            {% set _ = year_options.append((year, year)) %}
          {% endfor %}
          {{ calendar_select("Year", "yearSelect", year_options, current_year, "updateCalendar()", "80px") }}

          <!-- Month Selector -->
          {% set month_options = [
            (0, 'All Months'),
            (1, 'January'), (2, 'February'), (3, 'March'), (4, 'April'),
            (5, 'May'), (6, 'June'), (7, 'July'), (8, 'August'),
            (9, 'September'), (10, 'October'), (11, 'November'), (12, 'December')
          ] %}
          {{ calendar_select("Month", "monthSelect", month_options, current_month, "updateCalendar()", "120px") }}
        </div>

        <!-- Action Buttons -->
        <div class="flex flex-wrap gap-2 ml-auto">
          {{ button("List View", variant="outline", href=url_for('admin.holidays'), icon="list") }}
          {{ icon_button("refresh-cw", variant="outline", onclick="refreshCalendar()", title="Refresh Calendar") }}
        </div>
      </div>
    </div>
  </div>

  <!-- Calendar Display -->
  <div class="card">
    <div class="card-header">
      <div class="flex items-center justify-between">
        <div>
          <h3 id="calendarTitle" class="card-title">
            {{ calendar_data.year }}
            {% if calendar_data.month %}
            - {{ ['', 'January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'][calendar_data.month] }}
            {% endif %}
            ({{ calendar_data.region_code }})
          </h3>
          <div class="text-sm text-muted-foreground">
            {{ calendar_data.total_holidays }} holidays found
          </div>
        </div>

        <!-- Navigation Controls -->
        <div class="flex items-center">
          {% set nav_buttons = [
            {"text": "Previous", "variant": "outline", "size": "sm", "onclick": "navigateCalendar('prev')", "icon": "chevron-left"},
            {"text": "This Month", "variant": "outline", "size": "sm", "onclick": "navigateCalendar('current')", "icon": "calendar"},
            {"text": "Next", "variant": "outline", "size": "sm", "onclick": "navigateCalendar('next')", "icon": "chevron-right", "icon_position": "right"}
          ] %}
          {{ button_group(nav_buttons) }}
        </div>
      </div>
    </div>
    <div class="card-content">
      <div id="calendarContainer">
        <!-- Calendar will be rendered here by JavaScript -->
        <div class="text-center py-8">
          <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
          <p class="text-muted-foreground mt-2">Loading calendar...</p>
        </div>
      </div>
    </div>
  </div>

  <!-- Holiday Summary -->
  <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
    <!-- Upcoming Holidays -->
    <div class="card">
      <div class="card-header">
        <h3 class="card-title">Upcoming Holidays</h3>
      </div>
      <div class="card-content">
        <div id="upcomingHolidays" class="space-y-3">
          <!-- Will be populated by JavaScript -->
        </div>
      </div>
    </div>

    <!-- Holiday Statistics -->
    <div class="card">
      <div class="card-header">
        <h3 class="card-title">Statistics</h3>
      </div>
      <div class="card-content">
        <div id="holidayStats" class="space-y-3">
          <!-- Will be populated by JavaScript -->
        </div>
      </div>
    </div>

    <!-- Legend -->
    <div class="card">
      <div class="card-header">
        <h3 class="card-title">Legend</h3>
      </div>
      <div class="card-content">
        <div class="space-y-2">
          <div class="flex items-center space-x-2">
            <div class="w-4 h-4 bg-blue-500 rounded"></div>
            <span class="text-sm">🇺🇸 US Holidays</span>
          </div>
          <div class="flex items-center space-x-2">
            <div class="w-4 h-4 bg-green-500 rounded"></div>
            <span class="text-sm">🇵🇭 PH Holidays</span>
          </div>
          <div class="flex items-center space-x-2">
            <div class="w-4 h-4 bg-gray-500 rounded"></div>
            <span class="text-sm">🌍 Global Holidays</span>
          </div>
          <div class="flex items-center space-x-2">
            <div class="w-4 h-4 bg-purple-500 rounded"></div>
            <span class="text-sm">📅 Today</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>


{% endblock %}

{% block scripts %}
{{ super() }}
<script src="{{ url_for('static', filename='js/pages/holiday-calendar.js') }}"></script>
{% endblock %}

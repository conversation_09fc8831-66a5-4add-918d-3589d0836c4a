"""
API routes for employee data.
"""

from flask import jsonify, current_app
from flask_login import login_required

from app import cache, db
from app.models import EmployeeDetail
from app.routes.api import api_bp
from app.routes.api.utils import handle_api_error


@api_bp.route('/employees')
@login_required
@handle_api_error
@cache.cached(timeout=60, key_prefix='employees')
def get_employees():
    """Get all employees with details."""
    current_app.logger.info("Fetching employees (may be cached)")
    # Get all employee details with eager loading to avoid N+1 queries
    employees = EmployeeDetail.query.options(
        db.joinedload('user'),
        db.joinedload('business_unit'),
        db.joinedload('business_segment'),
        db.joinedload('teams')
    ).all()

    # Format the response
    employees_data = [{
        'id': emp.id,
        'user_id': emp.user_id,
        'name': emp.user.name,
        'email': emp.user.email,
        'position': emp.job_title,  # Updated from position to job_title
        'employee_number': emp.employee_number,
        'phone': emp.phone,
        'business_unit': emp.business_unit.name if emp.business_unit else None,
        'business_segment': emp.business_segment.name if emp.business_segment else None,
        'hire_date': emp.hire_date.isoformat() if emp.hire_date else None,
        'emp_status': emp.emp_status
    } for emp in employees]

    return jsonify(employees_data)

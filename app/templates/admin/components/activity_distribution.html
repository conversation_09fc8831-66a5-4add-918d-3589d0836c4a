{% macro activity_distribution(activity_labels, activity_data) %}
<div class="analytics-card bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 shadow-lg overflow-hidden transition-all duration-200">
    <div class="flex flex-col sm:flex-row sm:items-center justify-between p-5 md:p-6 border-b border-gray-200 dark:border-gray-700 bg-gray-50/80 dark:bg-gray-800/80 analytics-card-header">
        <div>
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white flex items-center analytics-text">
                <i data-lucide="database" class="w-5 h-5 text-blue-600 dark:text-blue-400 mr-2"></i>
                <span>Activity by Entity Type</span>
            </h3>
            <p class="text-sm text-gray-600 dark:text-gray-400 mt-1 analytics-text">Entity distribution and top activities</p>
        </div>
        <div class="flex items-center space-x-2 mt-3 sm:mt-0">
            <button id="refresh-activity-btn" class="px-3 py-1.5 text-sm rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500 flex items-center shadow-sm transition-colors" aria-label="Refresh Activity Data">
                <i data-lucide="refresh-cw" class="w-3.5 h-3.5 mr-1.5"></i>
                <span>Refresh</span>
            </button>
        </div>
    </div>
    <div class="p-5 md:p-6 analytics-card-content">
        <div class="flex flex-col space-y-6">
            <div class="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                <div class="flex flex-col h-full">
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 h-full">
                        <!-- Left: Chart visualization -->
                        <div class="md:col-span-1 flex flex-col">
                            <div class="flex items-center justify-between mb-3">
                                <h5 class="text-xs font-medium text-gray-600 dark:text-gray-400">Entity Distribution</h5>
                                <span class="text-xs text-gray-500 dark:text-gray-400">By activity count</span>
                            </div>
                            <div class="flex-grow flex items-center justify-center">
                                <div class="w-[220px] h-[220px] max-w-full max-h-full">
                                    <canvas id="activityDistributionChart"></canvas>
                                </div>
                            </div>
                            <!-- Legend with percentages -->
                            <div class="flex flex-col gap-y-1.5 mt-3">
                                {% for i in range(4) %}
                                <div class="flex items-center justify-between py-1 px-2 rounded {% if i == 0 %}bg-blue-50/50 dark:bg-blue-900/20{% elif i == 1 %}bg-green-50/50 dark:bg-green-900/20{% elif i == 2 %}bg-purple-50/50 dark:bg-purple-900/20{% else %}bg-orange-50/50 dark:bg-orange-900/20{% endif %}">
                                    <div class="flex items-center flex-grow mr-2">
                                        <div class="w-2.5 h-2.5 rounded-full flex-shrink-0 {% if i == 0 %}bg-blue-500 dark:bg-blue-400{% elif i == 1 %}bg-green-500 dark:bg-green-400{% elif i == 2 %}bg-purple-500 dark:bg-purple-400{% else %}bg-orange-500 dark:bg-orange-400{% endif %} mr-2"></div>
                                        <span class="text-xs font-medium {% if i == 0 %}text-blue-700 dark:text-blue-300{% elif i == 1 %}text-green-700 dark:text-green-300{% elif i == 2 %}text-purple-700 dark:text-purple-300{% else %}text-orange-700 dark:text-orange-300{% endif %} truncate analytics-text legend-label-{{ i }}" id="entity-label-{{ i }}">
                                            Loading...
                                        </span>
                                    </div>
                                    <span class="text-xs font-medium flex-shrink-0 {% if i == 0 %}text-blue-700 dark:text-blue-300{% elif i == 1 %}text-green-700 dark:text-green-300{% elif i == 2 %}text-purple-700 dark:text-purple-300{% else %}text-orange-700 dark:text-orange-300{% endif %} legend-percent-{{ i }}">--%</span>
                                </div>
                                {% endfor %}
                            </div>
                        </div>

                        <!-- Right: Activity metrics with enhanced visualization -->
                        <div class="md:col-span-2 flex flex-col">
                            <div class="flex items-center justify-between mb-3">
                                <h5 class="text-xs font-medium text-gray-600 dark:text-gray-400">Top Activities</h5>
                            </div>
                            <div class="space-y-2 overflow-y-auto flex-grow" id="top-activities-container">
                                <!-- This container will be populated by JavaScript -->
                                <div class="flex items-center justify-center h-full bg-gray-100 dark:bg-gray-800/50 rounded-lg border border-gray-200 dark:border-gray-700">
                                    <div class="text-center p-6">
                                        <div class="w-10 h-10 rounded-full bg-blue-100 dark:bg-blue-900/30 flex items-center justify-center mx-auto mb-3">
                                            <i data-lucide="list" class="w-5 h-5 text-blue-600 dark:text-blue-400"></i>
                                        </div>
                                        <h4 class="text-gray-900 dark:text-gray-100 font-medium mb-1 analytics-text">Loading Activities</h4>
                                        <p class="text-xs text-gray-500 dark:text-gray-400 analytics-text">Activity data will appear here shortly</p>
                                        <div class="mt-3 w-20 h-1 bg-gray-200 dark:bg-gray-700 rounded-full mx-auto overflow-hidden">
                                            <div class="h-full bg-blue-500 dark:bg-blue-400 rounded-full animate-pulse" style="width: 70%"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- JavaScript to populate the top activities with enhanced visualization -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    let activityChart = null;
    let chartType = 'doughnut';

    // Function to populate top activities with enhanced visualization
    function populateTopActivities() {
        try {
            // Get the data from the hidden elements
            const labelsStr = document.getElementById('activity-labels-data').textContent;
            const dataStr = document.getElementById('activity-data-data').textContent;

            // Parse the data
            const labels = JSON.parse(labelsStr);
            const data = JSON.parse(dataStr);

            // Get the container
            const container = document.getElementById('top-activities-container');

            // Clear the container
            container.innerHTML = '';

            // Check if we have data
            if (!labels || !labels.length || !data || !data.length) {
                container.innerHTML = `
                    <div class="flex items-center justify-center h-64 bg-gray-50 dark:bg-gray-800/50 rounded-lg border border-gray-200 dark:border-gray-700">
                        <div class="text-center p-6">
                            <div class="w-12 h-12 rounded-full bg-blue-100 dark:bg-blue-900/30 flex items-center justify-center mx-auto mb-4">
                                <i data-lucide="info" class="w-6 h-6 text-blue-600 dark:text-blue-400"></i>
                            </div>
                            <h4 class="text-gray-900 dark:text-gray-100 font-medium mb-2 analytics-text">No Activity Data</h4>
                            <p class="text-gray-500 dark:text-gray-400 mb-1 analytics-text">No activity distribution data available</p>
                            <p class="text-xs text-gray-400 dark:text-gray-500 analytics-text">Activity data will appear here once users start using the system</p>
                        </div>
                    </div>
                `;

                // Initialize Lucide icons for the new elements
                if (window.lucide) {
                    window.lucide.createIcons();
                }

                return;
            }

            // Get the maximum value for percentage calculation
            const maxValue = Math.max(...data);
            const activityTotalValue = data.reduce((sum, val) => sum + val, 0);

            // Create the top activities with enhanced visualization
            const maxActivities = Math.min(5, labels.length);
            for (let i = 0; i < maxActivities; i++) {
                // Use the pre-calculated total value
                const percentage = (data[i] / activityTotalValue * 100).toFixed(1);
                const percentageOfMax = (data[i] / maxValue * 100).toFixed(0);

                // Get color based on index
                const colors = {
                    bg: i === 0 ? 'bg-blue-100 dark:bg-blue-900/30' :
                         i === 1 ? 'bg-green-100 dark:bg-green-900/30' :
                         i === 2 ? 'bg-purple-100 dark:bg-purple-900/30' :
                         i === 3 ? 'bg-orange-100 dark:bg-orange-900/30' :
                         'bg-pink-100 dark:bg-pink-900/30',
                    dot: i === 0 ? 'bg-blue-500 dark:bg-blue-400' :
                          i === 1 ? 'bg-green-500 dark:bg-green-400' :
                          i === 2 ? 'bg-purple-500 dark:bg-purple-400' :
                          i === 3 ? 'bg-orange-500 dark:bg-orange-400' :
                          'bg-pink-500 dark:bg-pink-400',
                    text: i === 0 ? 'text-blue-700 dark:text-blue-300' :
                           i === 1 ? 'text-green-700 dark:text-green-300' :
                           i === 2 ? 'text-purple-700 dark:text-purple-300' :
                           i === 3 ? 'text-orange-700 dark:text-orange-300' :
                           'text-pink-700 dark:text-pink-300',
                    bar: i === 0 ? 'bg-blue-500 dark:bg-blue-400' :
                          i === 1 ? 'bg-green-500 dark:bg-green-400' :
                          i === 2 ? 'bg-purple-500 dark:bg-purple-400' :
                          i === 3 ? 'bg-orange-500 dark:bg-orange-400' :
                          'bg-pink-500 dark:bg-pink-400',
                    rank: i === 0 ? 'text-blue-500 dark:text-blue-400' :
                           i === 1 ? 'text-green-500 dark:text-green-400' :
                           i === 2 ? 'text-purple-500 dark:text-purple-400' :
                           i === 3 ? 'text-orange-500 dark:text-orange-400' :
                           'text-pink-500 dark:text-pink-400'
                };

                const activityItem = document.createElement('div');
                activityItem.innerHTML = `
                    <div class="bg-white dark:bg-gray-700/50 border border-gray-200 dark:border-gray-700 rounded-lg p-2.5 shadow-sm hover:shadow-md transition-all duration-200">
                        <div class="flex justify-between items-center mb-1.5">
                            <div class="flex items-center">
                                <div class="flex-shrink-0 w-2 h-2 rounded-full ${colors.dot} mr-1.5"></div>
                                <span class="text-sm font-medium text-gray-800 dark:text-gray-200 analytics-text truncate max-w-[150px]">${labels[i]}</span>
                            </div>
                            <div class="flex items-center">
                                <span class="text-xs font-semibold ${colors.text} px-1.5 py-0.5 rounded ${colors.bg}">${percentage}%</span>
                            </div>
                        </div>
                        <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-1.5 mb-1.5 overflow-hidden shadow-inner">
                            <div class="${colors.bar} h-1.5 rounded-full analytics-progress transition-all duration-500 ease-out" style="width: ${percentageOfMax}%"></div>
                        </div>
                        <div class="flex justify-between items-center text-xs">
                            <span class="text-gray-500 dark:text-gray-400 analytics-text">${data[i]} activities</span>
                            <span class="${colors.rank} font-medium analytics-text">Rank #${i+1}</span>
                        </div>
                    </div>
                `;

                container.appendChild(activityItem.firstElementChild);
            }

            // Initialize Lucide icons for the new elements
            if (window.lucide) {
                window.lucide.createIcons();
            }

        } catch (error) {
            console.error('Error populating top activities:', error);
        }
    }

    // Function to initialize the chart with enhanced visualization
    function initActivityChart() {
        try {
            // Get the entity data for the chart
            const entityLabelsStr = document.getElementById('entity-labels-data').textContent;
            const entityDataStr = document.getElementById('entity-data-data').textContent;

            // Get the activity data for the list
            const labelsStr = document.getElementById('activity-labels-data').textContent;
            const dataStr = document.getElementById('activity-data-data').textContent;

            // Parse the data
            const entityLabels = JSON.parse(entityLabelsStr);
            const entityData = JSON.parse(entityDataStr);
            const labels = JSON.parse(labelsStr);
            const data = JSON.parse(dataStr);

            // Check if we have entity data for the chart
            if (!entityLabels || !entityLabels.length || !entityData || !entityData.length) {
                console.warn('No entity data available for chart');
                return;
            }

            // Get the canvas context
            const ctx = document.getElementById('activityDistributionChart').getContext('2d');

            // Define chart colors with improved palette for better visualization
            const isDarkMode = document.documentElement.classList.contains('dark');

            // Define chart colors - more vibrant and distinct
            const chartColors = [
                'rgba(59, 130, 246, 0.85)', // blue
                'rgba(16, 185, 129, 0.85)', // green
                'rgba(139, 92, 246, 0.85)', // purple
                'rgba(249, 115, 22, 0.85)', // orange
                'rgba(236, 72, 153, 0.85)'  // pink
            ];

            // Define chart border colors (slightly darker)
            const chartBorderColors = [
                'rgba(37, 99, 235, 1)', // blue
                'rgba(5, 150, 105, 1)', // green
                'rgba(124, 58, 237, 1)', // purple
                'rgba(234, 88, 12, 1)', // orange
                'rgba(219, 39, 119, 1)'  // pink
            ];

            // Update the legend with entity data
            const totalValue = entityData.reduce((sum, val) => sum + val, 0);
            for (let i = 0; i < 4; i++) {
                const legendLabel = document.getElementById(`entity-label-${i}`);
                const legendPercent = document.querySelector(`.legend-percent-${i}`);
                const legendItem = legendLabel?.closest('.flex.items-center.justify-between');

                if (i < entityLabels.length) {
                    if (legendLabel) {
                        // Format entity type names to be more readable
                        let label = entityLabels[i];
                        if (label === 'BusinessUnit') label = 'Business Unit';
                        if (label === 'BusinessSegment') label = 'Business Segment';
                        if (label === 'EmployeeDetail') label = 'Employee';
                        legendLabel.textContent = label;
                    }

                    if (legendPercent) {
                        const percentage = (entityData[i] / totalValue * 100).toFixed(1);
                        legendPercent.textContent = `${percentage}%`;
                    }

                    if (legendItem) {
                        legendItem.style.display = 'flex';
                    }
                } else {
                    // Hide unused legend items
                    if (legendItem) {
                        legendItem.style.display = 'none';
                    }
                }
            }

            // Prepare the entity data for the chart
            const chartData = {
                labels: entityLabels.slice(0, 5), // Only show top 5
                datasets: [{
                    data: entityData.slice(0, 5), // Only show top 5
                    backgroundColor: chartColors,
                    borderColor: chartBorderColors,
                    borderWidth: 1,
                    borderRadius: 0,
                    hoverOffset: 12,
                    hoverBorderWidth: 2
                }]
            };

            // Chart configuration with improved visuals for dashboard best practices
            const config = {
                type: chartType,
                data: chartData,
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    cutout: '55%',
                    layout: {
                        padding: 5
                    },
                    plugins: {
                        legend: {
                            display: false
                        },
                        tooltip: {
                            backgroundColor: isDarkMode ? 'rgba(30, 41, 59, 0.95)' : 'rgba(255, 255, 255, 0.95)',
                            titleColor: isDarkMode ? '#e2e8f0' : '#1e293b',
                            bodyColor: isDarkMode ? '#e2e8f0' : '#1e293b',
                            borderColor: isDarkMode ? 'rgba(51, 65, 85, 0.5)' : 'rgba(203, 213, 225, 0.5)',
                            borderWidth: 1,
                            cornerRadius: 6,
                            padding: 8,
                            boxPadding: 4,
                            usePointStyle: true,
                            callbacks: {
                                label: function(context) {
                                    let label = context.label || '';
                                    // Format entity type names for better readability
                                    if (label === 'BusinessUnit') label = 'Business Unit';
                                    if (label === 'BusinessSegment') label = 'Business Segment';
                                    if (label === 'EmployeeDetail') label = 'Employee';

                                    const value = context.raw || 0;
                                    const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                    const percentage = Math.round((value / total) * 100);
                                    return `${label}: ${percentage}%`;
                                }
                            }
                        }
                    },
                    animation: {
                        animateScale: true,
                        animateRotate: true,
                        duration: 800,
                        easing: 'easeOutCubic'
                    },
                    elements: {
                        arc: {
                            borderWidth: 1
                        }
                    }
                }
            };

            // No bar chart option anymore

            // Create the chart
            if (activityChart) {
                activityChart.destroy();
            }

            // Double-check if there's still a chart instance with this ID
            const existingChart = Chart.getChart('activityDistributionChart');
            if (existingChart) {
                existingChart.destroy();
                console.log('Destroyed existing chart before creating new one');
            }

            activityChart = new Chart(ctx, config);

        } catch (error) {
            console.error('Error initializing activity chart:', error);
        }
    }

    // We're only using donut chart now
    function updateChartType() {
        chartType = 'doughnut';
        initActivityChart();
    }

    // No chart type buttons needed anymore

    // Add event listener to the refresh button
    const refreshBtn = document.getElementById('refresh-activity-btn');
    if (refreshBtn) {
        refreshBtn.addEventListener('click', function() {
            // Show loading state
            this.disabled = true;
            this.classList.add('opacity-70');

            // Find the icon and add spin animation
            const icon = this.querySelector('i[data-lucide="refresh-cw"]');
            if (icon) {
                icon.classList.add('animate-spin');
            }

            // Simulate refreshing the data
            setTimeout(() => {
                // Reload the page to get fresh data
                window.location.reload();
            }, 500);
        });
    }

    // Initialize the chart and populate activities when the page loads
    window.initActivityDistributionChart = function() {
        // Check if there's already a chart instance with this ID in Chart.instances
        const existingChart = Chart.getChart('activityDistributionChart');
        if (existingChart) {
            // Destroy the existing chart before creating a new one
            existingChart.destroy();
            console.log('Destroyed existing activity distribution chart');
        }

        initActivityChart();
        populateTopActivities();
        return true;
    };

    // Call the initialization function only once
    if (typeof window._activityDistributionInitialized === 'undefined') {
        window._activityDistributionInitialized = true;
        window.initActivityDistributionChart();
    }
});
</script>
{% endmacro %}

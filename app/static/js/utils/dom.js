/**
 * DOM Utilities Module
 *
 * This module provides utility functions for DOM manipulation.
 */

/**
 * Handle flash messages by adding animations and auto-hiding
 */
function handleFlashMessages() {
  // Auto-hide flash messages after 5 seconds
  const flashMessages = document.querySelectorAll('.flash-message');
  if (flashMessages.length) {
    flashMessages.forEach((message) => {
      // Add animation styles
      message.style.transition = 'all 0.3s ease';

      // Auto-hide after 5 seconds
      setTimeout(() => {
        message.style.opacity = '0';
        message.style.transform = 'translateY(-10px)';

        // Remove after animation completes
        setTimeout(() => {
          message.remove();
        }, 300);
      }, 5000);
    });
  }
}

/**
 * Get all focusable elements within a container
 * @param {HTMLElement} container - The container element
 * @returns {NodeList} - List of focusable elements
 */
function getFocusableElements(container) {
  return container.querySelectorAll(
    'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
  );
}

/**
 * Trap focus within a container (for modals, dialogs, etc.)
 * @param {HTMLElement} container - The container to trap focus within
 * @param {Function} onEscape - Optional callback when Escape key is pressed
 * @returns {Function} - Function to remove the event listener
 */
function trapFocus(container, onEscape) {
  const focusableElements = getFocusableElements(container);
  if (focusableElements.length === 0) return () => {};

  const firstElement = focusableElements[0];
  const lastElement = focusableElements[focusableElements.length - 1];

  // Focus the first element
  setTimeout(() => {
    firstElement.focus();
  }, 50);

  // Handle keydown events
  function handleKeyDown(e) {
    if (e.key === 'Escape' && typeof onEscape === 'function') {
      onEscape();
      return;
    }

    if (e.key === 'Tab') {
      // If shift+tab on first element, move to last element
      if (e.shiftKey && document.activeElement === firstElement) {
        e.preventDefault();
        lastElement.focus();
      } 
      // If tab on last element, move to first element
      else if (!e.shiftKey && document.activeElement === lastElement) {
        e.preventDefault();
        firstElement.focus();
      }
    }
  }

  container.addEventListener('keydown', handleKeyDown);

  // Return function to remove the event listener
  return () => {
    container.removeEventListener('keydown', handleKeyDown);
  };
}

/**
 * Create and append an element to a parent
 * @param {string} tag - Tag name of the element to create
 * @param {HTMLElement} parent - Parent element to append to
 * @param {Object} options - Options for the element
 * @param {string} options.className - Class name(s) to add
 * @param {Object} options.attributes - Attributes to set
 * @param {Object} options.styles - Styles to apply
 * @param {string} options.innerHTML - Inner HTML content
 * @param {string} options.textContent - Text content
 * @returns {HTMLElement} - The created element
 */
function createElement(tag, parent, options = {}) {
  const element = document.createElement(tag);

  // Add class name
  if (options.className) {
    element.className = options.className;
  }

  // Set attributes
  if (options.attributes) {
    Object.entries(options.attributes).forEach(([key, value]) => {
      element.setAttribute(key, value);
    });
  }

  // Apply styles
  if (options.styles) {
    Object.entries(options.styles).forEach(([key, value]) => {
      element.style[key] = value;
    });
  }

  // Set inner HTML or text content
  if (options.innerHTML) {
    element.innerHTML = options.innerHTML;
  } else if (options.textContent) {
    element.textContent = options.textContent;
  }

  // Append to parent if provided
  if (parent) {
    parent.appendChild(element);
  }

  return element;
}

// Export public API
export {
  handleFlashMessages,
  getFocusableElements,
  trapFocus,
  createElement
};

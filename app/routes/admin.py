"""
Admin routes module - imports all admin routes from the admin package.
This file is kept for backward compatibility.
"""

# Import the admin Blueprint from the admin package
from app.routes.admin import admin_bp

# Import all admin routes from the admin package
from app.routes.admin.dashboard import *
from app.routes.admin.users import *
from app.routes.admin.employee_details import *
from app.routes.admin.business import *
from app.routes.admin.settings import *
from app.routes.admin.analytics import *
from app.routes.admin.activities import *
from app.routes.admin.testing import *
from app.routes.admin.system import *

# Define __all__ to explicitly specify what is exported
__all__ = ['admin_bp']

{% macro button(text, variant="primary", size="md", icon=None, icon_position="left", type="button", onclick=None, href=None, disabled=false, loading=false, class_extra="", title=None, id=None) %}
{#
  Reusable Button Component

  Parameters:
  - text: Button text content
  - variant: Button style variant ("primary", "secondary", "outline", "ghost", "destructive")
  - size: Button size ("sm", "md", "lg")
  - icon: Lucide icon name (optional)
  - icon_position: Icon position ("left", "right")
  - type: Button type ("button", "submit", "reset") - only for <button> tags
  - onclick: JavaScript onclick handler - creates <button> tag
  - href: URL for link - creates <a> tag
  - disabled: Whether button is disabled
  - loading: Whether button shows loading state
  - class_extra: Additional CSS classes
  - title: Tooltip text
  - id: Element ID

  Usage Examples:

  Primary button:
  {{ button("Save", variant="primary", icon="save") }}

  Link button:
  {{ button("View Details", variant="outline", href="/details", icon="external-link") }}

  Loading button:
  {{ button("Processing...", variant="primary", loading=true) }}

  Custom onclick:
  {{ button("Delete", variant="destructive", icon="trash-2", onclick="confirmDelete()") }}
#}

{# Define base classes for all button variants #}
{% set base_classes = "inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50" %}

{# Define variant-specific classes #}
{% set variant_classes = {
  "primary": "bg-primary text-primary-foreground shadow hover:bg-primary/90",
  "secondary": "bg-secondary text-secondary-foreground hover:bg-secondary/80",
  "outline": "border border-input bg-background hover:bg-accent hover:text-accent-foreground",
  "ghost": "hover:bg-accent hover:text-accent-foreground",
  "destructive": "bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90"
} %}

{# Define size-specific classes #}
{% set size_classes = {
  "sm": "h-8 px-3 text-xs",
  "md": "h-9 px-4 py-2",
  "lg": "h-10 px-6 py-2",
  "icon-sm": "h-8 w-8",
  "icon-md": "h-9 w-9",
  "icon-lg": "h-10 w-10"
} %}

{# Build the complete class string #}
{% set button_classes = [
  base_classes,
  variant_classes.get(variant, variant_classes.primary),
  size_classes.get(size, size_classes.md),
  class_extra
] | join(" ") %}

{# Determine if this should be a link or button #}
{% if href %}
  {# Render as <a> tag #}
  <a href="{{ href }}"
     class="{{ button_classes }}"
     {% if title %}title="{{ title }}"{% endif %}
     {% if id %}id="{{ id }}"{% endif %}
     {% if disabled %}aria-disabled="true" tabindex="-1"{% endif %}>

    {# Loading state #}
    {% if loading %}
      <svg class="animate-spin h-4 w-4 mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
      </svg>
      <span>{{ text }}</span>
    {% else %}
      {# Icon on left #}
      {% if icon and icon_position == "left" %}
        <i data-lucide="{{ icon }}" class="h-4 w-4 mr-2"></i>
      {% endif %}

      <span>{{ text }}</span>

      {# Icon on right #}
      {% if icon and icon_position == "right" %}
        <i data-lucide="{{ icon }}" class="h-4 w-4 ml-2"></i>
      {% endif %}
    {% endif %}
  </a>

{% else %}
  {# Render as <button> tag #}
  <button type="{{ type }}"
          class="{{ button_classes }}"
          {% if onclick %}onclick="{{ onclick }}"{% endif %}
          {% if title %}title="{{ title }}"{% endif %}
          {% if id %}id="{{ id }}"{% endif %}
          {% if disabled or loading %}disabled{% endif %}>

    {# Loading state #}
    {% if loading %}
      <svg class="animate-spin h-4 w-4 mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
      </svg>
      <span>{{ text }}</span>
    {% else %}
      {# Icon on left #}
      {% if icon and icon_position == "left" %}
        <i data-lucide="{{ icon }}" class="h-4 w-4 mr-2"></i>
      {% endif %}

      <span>{{ text }}</span>

      {# Icon on right #}
      {% if icon and icon_position == "right" %}
        <i data-lucide="{{ icon }}" class="h-4 w-4 ml-2"></i>
      {% endif %}
    {% endif %}
  </button>
{% endif %}
{% endmacro %}


{% macro button_group(buttons, gap="2") %}
{#
  Button Group Component

  Parameters:
  - buttons: List of button configurations
  - gap: Gap between buttons ("1", "2", "3", "4")

  Usage:
  {% set filter_buttons = [
    {"text": "Reset", "variant": "outline", "href": url_for('admin.holidays'), "icon": "rotate-ccw"},
    {"text": "Apply Filters", "variant": "primary", "type": "submit", "icon": "filter"}
  ] %}
  {{ button_group(filter_buttons) }}
#}
<div class="flex flex-wrap gap-{{ gap }}">
  {% for btn in buttons %}
    {{ button(
      text=btn.text,
      variant=btn.get('variant', 'primary'),
      size=btn.get('size', 'md'),
      icon=btn.get('icon'),
      icon_position=btn.get('icon_position', 'left'),
      type=btn.get('type', 'button'),
      onclick=btn.get('onclick'),
      href=btn.get('href'),
      disabled=btn.get('disabled', false),
      loading=btn.get('loading', false),
      class_extra=btn.get('class_extra', ''),
      title=btn.get('title'),
      id=btn.get('id')
    ) }}
  {% endfor %}
</div>
{% endmacro %}


{% macro icon_button(icon, variant="ghost", size="md", onclick=None, href=None, disabled=false, title=None, id=None, class_extra="") %}
{#
  Icon-only Button Component

  Parameters:
  - icon: Lucide icon name
  - variant: Button style variant
  - size: Button size (uses icon-specific sizing)
  - onclick: JavaScript onclick handler
  - href: URL for link
  - disabled: Whether button is disabled
  - title: Tooltip text (recommended for accessibility)
  - id: Element ID
  - class_extra: Additional CSS classes

  Usage:
  {{ icon_button("edit", title="Edit item", onclick="editItem(1)") }}
  {{ icon_button("external-link", href="/details", title="View details") }}
#}

{# Define base classes for icon buttons #}
{% set base_classes = "inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50" %}

{# Define variant-specific classes #}
{% set variant_classes = {
  "primary": "bg-primary text-primary-foreground shadow hover:bg-primary/90",
  "secondary": "bg-secondary text-secondary-foreground hover:bg-secondary/80",
  "outline": "border border-input bg-background hover:bg-accent hover:text-accent-foreground",
  "ghost": "hover:bg-accent hover:text-accent-foreground",
  "destructive": "bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90"
} %}

{# Define size-specific classes for icon buttons #}
{% set size_classes = {
  "sm": "h-8 w-8",
  "md": "h-9 w-9",
  "lg": "h-10 w-10"
} %}

{# Build the complete class string #}
{% set button_classes = [
  base_classes,
  variant_classes.get(variant, variant_classes.ghost),
  size_classes.get(size, size_classes.md),
  class_extra
] | join(" ") %}

{# Determine if this should be a link or button #}
{% if href %}
  {# Render as <a> tag #}
  <a href="{{ href }}"
     class="{{ button_classes }}"
     {% if title %}title="{{ title }}"{% endif %}
     {% if id %}id="{{ id }}"{% endif %}
     {% if disabled %}aria-disabled="true" tabindex="-1"{% endif %}>
    <i data-lucide="{{ icon }}" class="h-4 w-4"></i>
  </a>
{% else %}
  {# Render as <button> tag #}
  <button type="button"
          class="{{ button_classes }}"
          {% if onclick %}onclick="{{ onclick }}"{% endif %}
          {% if title %}title="{{ title }}"{% endif %}
          {% if id %}id="{{ id }}"{% endif %}
          {% if disabled %}disabled{% endif %}>
    <i data-lucide="{{ icon }}" class="h-4 w-4"></i>
  </button>
{% endif %}
{% endmacro %}

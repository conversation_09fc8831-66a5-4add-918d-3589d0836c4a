#!/usr/bin/env python3
"""
Modern Test Runner for Matrix App
Displays test progress and results in a visually appealing way.
"""
import unittest
import sys
import time
import os
from rich.console import Console
from rich.panel import Panel
from rich.progress import Progress, SpinnerColumn, TextColumn, BarColumn, TaskProgressColumn
from rich.table import Table
from rich.text import Text
from rich.live import Live
from rich.box import ROUNDED
import importlib.util

# Initialize Rich console
console = Console()

# Test result counters
passed_tests = []
failed_tests = []
skipped_tests = []
errored_tests = []

# Define colors for test outcomes
SUCCESS_COLOR = "green"
FAIL_COLOR = "red"
SKIP_COLOR = "yellow"
ERROR_COLOR = "magenta"
INFO_COLOR = "cyan"
HIGHLIGHT_COLOR = "bright_white"


class ModernTestResult(unittest.TestResult):
    """Custom TestResult class that tracks timing and test outcomes."""

    def __init__(self, verbosity=1):
        super().__init__()
        self.verbosity = verbosity
        self.tests_run = 0
        self.test_start_time = 0
        self.test_times = {}

    def startTest(self, test):
        super().startTest(test)
        self.tests_run += 1
        self.test_start_time = time.time()
        test_name = self._get_test_name(test)
        task_id = progress.add_task(f"[white]{test_name}", total=1)
        test.task_id = task_id

    def stopTest(self, test):
        super().stopTest(test)
        elapsed = time.time() - self.test_start_time
        test_name = self._get_test_name(test)
        self.test_times[test_name] = elapsed
        progress.update(test.task_id, completed=1)

    def addSuccess(self, test):
        super().addSuccess(test)
        test_name = self._get_test_name(test)
        passed_tests.append((test_name, self.test_times.get(test_name, 0)))
        progress.update(test.task_id, description=f"[{SUCCESS_COLOR}]✓ {test_name}")

    def addFailure(self, test, err):
        super().addFailure(test, err)
        test_name = self._get_test_name(test)
        error_message = self._format_error(err)
        failed_tests.append((test_name, error_message, self.test_times.get(test_name, 0)))
        progress.update(test.task_id, description=f"[{FAIL_COLOR}]✗ {test_name}")

    def addError(self, test, err):
        super().addError(test, err)
        test_name = self._get_test_name(test)
        error_message = self._format_error(err)
        errored_tests.append((test_name, error_message, self.test_times.get(test_name, 0)))
        progress.update(test.task_id, description=f"[{ERROR_COLOR}]! {test_name}")

    def addSkip(self, test, reason):
        super().addSkip(test, reason)
        test_name = self._get_test_name(test)
        skipped_tests.append((test_name, reason, self.test_times.get(test_name, 0)))
        progress.update(test.task_id, description=f"[{SKIP_COLOR}]- {test_name}")

    def _get_test_name(self, test):
        return f"{test.__class__.__name__}.{test._testMethodName}"

    def _format_error(self, err):
        exctype, value, _ = err
        return f"{exctype.__name__}: {value}"


def display_summary():
    """Display a summary of test results."""
    console.print()
    table = Table(show_header=True, header_style="bold", box=ROUNDED, title="Test Results Summary")

    table.add_column("Status", style="bold")
    table.add_column("Count", justify="right")
    table.add_column("Time", justify="right")

    total_time = sum(t for _, t in passed_tests) + \
                 sum(t for _, _, t in failed_tests) + \
                 sum(t for _, _, t in errored_tests) + \
                 sum(t for _, _, t in skipped_tests)

    table.add_row(f"[{SUCCESS_COLOR}]PASSED", str(len(passed_tests)), f"{sum(t for _, t in passed_tests):.2f}s")
    table.add_row(f"[{FAIL_COLOR}]FAILED", str(len(failed_tests)), f"{sum(t for _, _, t in failed_tests):.2f}s")
    table.add_row(f"[{ERROR_COLOR}]ERRORS", str(len(errored_tests)), f"{sum(t for _, _, t in errored_tests):.2f}s")
    table.add_row(f"[{SKIP_COLOR}]SKIPPED", str(len(skipped_tests)), f"{sum(t for _, _, t in skipped_tests):.2f}s")
    table.add_row("[bold]TOTAL", str(len(passed_tests) + len(failed_tests) + len(errored_tests) + len(skipped_tests)), f"{total_time:.2f}s")

    console.print(table)


def display_failed_tests():
    """Display details of failed tests."""
    if failed_tests or errored_tests:
        console.print()
        console.print("[bold red]Failed Tests[/bold red]:", style="bold")
        for i, (test_name, error_msg, _) in enumerate(failed_tests + errored_tests):
            console.print(Panel(
                f"[bold]{test_name}[/bold]\n[dim]{error_msg}[/dim]",
                title=f"Test #{i+1}",
                title_align="left",
                border_style=FAIL_COLOR if i < len(failed_tests) else ERROR_COLOR
            ))


def get_all_test_modules(test_dir='tests'):
    """Discover all test modules in the specified directory."""
    test_modules = []
    for file in os.listdir(test_dir):
        if file.startswith('test_') and file.endswith('.py'):
            module_path = os.path.join(test_dir, file)
            module_name = os.path.splitext(file)[0]
            spec = importlib.util.spec_from_file_location(module_name, module_path)
            module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(module)
            test_modules.append(module)
    return test_modules


def run_specific_tests(test_files=None):
    """Run specific test files or discover and run all tests."""
    if test_files:
        # Run specific test files
        loader = unittest.TestLoader()
        suite = unittest.TestSuite()

        for file in test_files:
            # Handle both with and without .py extension
            if not file.endswith('.py'):
                file = f"{file}.py"

            full_path = os.path.join('tests', file)
            if not os.path.exists(full_path):
                console.print(f"[{FAIL_COLOR}]Test file not found: {full_path}[/{FAIL_COLOR}]")
                continue

            module_name = os.path.splitext(file)[0]
            spec = importlib.util.spec_from_file_location(module_name, full_path)
            module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(module)

            for name in dir(module):
                obj = getattr(module, name)
                if isinstance(obj, type) and issubclass(obj, unittest.TestCase) and obj is not unittest.TestCase:
                    suite.addTests(loader.loadTestsFromTestCase(obj))
    else:
        # Discover and run all tests
        loader = unittest.TestLoader()
        suite = loader.discover('tests', pattern='test_*.py')

    with Live(progress, refresh_per_second=10):
        result = ModernTestResult()
        start_time = time.time()
        suite.run(result)
        run_time = time.time() - start_time

    # Display test outcomes
    display_summary()
    display_failed_tests()

    # Final summary line
    all_passed = (len(failed_tests) + len(errored_tests)) == 0
    status = f"[{SUCCESS_COLOR}]PASSED" if all_passed else f"[{FAIL_COLOR}]FAILED"
    console.print(f"\nTest suite {status}: {len(passed_tests)} passed, {len(failed_tests)} failed, "
                  f"{len(errored_tests)} errors, {len(skipped_tests)} skipped[/] in {run_time:.2f} seconds")

    return 0 if all_passed else 1


if __name__ == '__main__':
    # Create the progress display
    progress = Progress(
        SpinnerColumn(),
        TextColumn("[progress.description]{task.description}"),
        BarColumn(bar_width=40),
        TaskProgressColumn(),
        refresh_per_second=10,
    )

    # Show welcome message
    console.print()
    console.print(Panel.fit(
        Text("Matrix App Test Runner", style=f"bold {INFO_COLOR}"),
        subtitle=Text("Running Tests", style=f"italic {INFO_COLOR}"),
        border_style=INFO_COLOR
    ))
    console.print()

    # Check if specific test files are provided as arguments
    test_files = sys.argv[1:] if len(sys.argv) > 1 else None

    # Run tests
    exit_code = run_specific_tests(test_files)

    sys.exit(exit_code)

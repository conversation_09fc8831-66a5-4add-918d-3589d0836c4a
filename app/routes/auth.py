"""
Authentication routes module - imports all auth routes from the auth package.
This file is kept for backward compatibility.
"""

# Import the auth Blueprint from the auth package
from app.routes.auth import auth_bp, redirect_authenticated_user

# Import all auth routes from the auth package
from app.routes.auth.login import *
from app.routes.auth.register import *
from app.routes.auth.password import *
from app.routes.auth.profile import *

# Define __all__ to explicitly specify what is exported
__all__ = ['auth_bp', 'redirect_authenticated_user']

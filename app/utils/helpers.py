"""
Helper functions for the application.
"""
import re
import unicodedata
from typing import Optional


def generate_slug(text: str) -> str:
    """
    Generate a slug from the given text.
    
    Args:
        text: The text to convert to a slug
        
    Returns:
        A slug version of the text (lowercase, spaces replaced with hyphens, etc.)
    """
    # Normalize unicode characters
    text = unicodedata.normalize('NFKD', text)
    
    # Convert to lowercase
    text = text.lower()
    
    # Replace spaces with hyphens
    text = re.sub(r'[\s]+', '-', text)
    
    # Remove all non-word characters (except hyphens)
    text = re.sub(r'[^\w\-]', '', text)
    
    # Replace multiple hyphens with a single hyphen
    text = re.sub(r'[\-]+', '-', text)
    
    # Remove leading/trailing hyphens
    text = text.strip('-')
    
    return text


def get_status_badge_class(status: str) -> str:
    """
    Get the appropriate badge class for a status.
    
    Args:
        status: The status string
        
    Returns:
        A CSS class for the badge
    """
    status_map = {
        'active': 'badge-success',
        'terminated': 'badge-destructive',
        'leave_of_absence': 'badge-warning',
    }
    
    return status_map.get(status, 'badge-secondary')


def get_status_display(status: str) -> str:
    """
    Get a human-readable display for a status.
    
    Args:
        status: The status string
        
    Returns:
        A human-readable status string
    """
    status_map = {
        'active': 'Active',
        'terminated': 'Terminated',
        'leave_of_absence': 'Leave of Absence',
    }
    
    return status_map.get(status, status.replace('_', ' ').title())

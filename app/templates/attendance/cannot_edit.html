{% extends "layouts/base.html" %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="max-w-2xl mx-auto space-y-6">
  <!-- Page Header -->
  <div class="text-center">
    <h1 class="text-2xl font-bold text-gray-900 dark:text-white">{{ title }}</h1>
  </div>

  <!-- Message Card -->
  <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
    <div class="text-center">
      <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
      </svg>
      <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-white">Cannot Edit This Record</h3>
      <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
        This attendance record cannot be edited because it has already been processed.
      </p>

      <!-- Record Details -->
      <div class="mt-6 bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
        <h4 class="text-sm font-medium text-gray-900 dark:text-white mb-3">Record Details</h4>
        <div class="grid grid-cols-2 gap-4 text-sm">
          <div class="text-left">
            <span class="text-gray-500 dark:text-gray-400">Date:</span>
            <span class="ml-2 text-gray-900 dark:text-white">{{ record.date.strftime('%B %d, %Y') }}</span>
          </div>
          <div class="text-left">
            <span class="text-gray-500 dark:text-gray-400">Type:</span>
            <span class="ml-2 text-gray-900 dark:text-white">{{ record.attendance_type.name }}</span>
          </div>
          <div class="text-left">
            <span class="text-gray-500 dark:text-gray-400">Status:</span>
            <span class="ml-2">
              {% if record.status == 'Approved' %}
                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                  Approved
                </span>
              {% elif record.status == 'Rejected' %}
                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200">
                  Rejected
                </span>
              {% elif record.status == 'Auto-Approved' %}
                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                  Auto Approved
                </span>
              {% else %}
                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200">
                  {{ record.status.title() }}
                </span>
              {% endif %}
            </span>
          </div>
          <div class="text-left">
            <span class="text-gray-500 dark:text-gray-400">Submitted:</span>
            <span class="ml-2 text-gray-900 dark:text-white">{{ record.created_at.strftime('%m/%d/%Y at %I:%M %p') }}</span>
          </div>
          {% if record.approved_at %}
          <div class="text-left">
            <span class="text-gray-500 dark:text-gray-400">Processed:</span>
            <span class="ml-2 text-gray-900 dark:text-white">{{ record.approved_at.strftime('%m/%d/%Y at %I:%M %p') }}</span>
          </div>
          {% endif %}
          {% if record.approved_by %}
          <div class="text-left">
            <span class="text-gray-500 dark:text-gray-400">Processed by:</span>
            <span class="ml-2 text-gray-900 dark:text-white">{{ record.approved_by.name }}</span>
          </div>
          {% endif %}
        </div>

        {% if record.duration_hours %}
        <div class="mt-3 text-left">
          <span class="text-gray-500 dark:text-gray-400">Duration:</span>
          <span class="ml-2 text-gray-900 dark:text-white">{{ record.duration_hours }} hours</span>
          {% if record.start_time and record.end_time %}
          <span class="ml-2 text-gray-500 dark:text-gray-400">
            ({{ record.start_time.strftime('%H:%M') }} - {{ record.end_time.strftime('%H:%M') }})
          </span>
          {% endif %}
        </div>
        {% endif %}

        {% if record.notes %}
        <div class="mt-3 text-left">
          <span class="text-gray-500 dark:text-gray-400">Notes:</span>
          <div class="mt-1 text-gray-900 dark:text-white text-sm">{{ record.notes }}</div>
        </div>
        {% endif %}

        {% if record.rejection_reason %}
        <div class="mt-3 text-left">
          <span class="text-gray-500 dark:text-gray-400">Rejection Reason:</span>
          <div class="mt-1 text-red-600 dark:text-red-400 text-sm">{{ record.rejection_reason }}</div>
        </div>
        {% endif %}
      </div>

      <!-- Explanation -->
      <div class="mt-6 text-left">
        <h4 class="text-sm font-medium text-gray-900 dark:text-white mb-2">Why can't I edit this record?</h4>
        <ul class="text-sm text-gray-500 dark:text-gray-400 space-y-1">
          {% if record.status == 'approved' %}
          <li>• This record has been approved and is now part of your official attendance history</li>
          <li>• Approved records cannot be modified to maintain data integrity</li>
          {% elif record.status == 'rejected' %}
          <li>• This record has been rejected and cannot be modified</li>
          <li>• You can submit a new attendance request for the same date if needed</li>
          {% elif record.status == 'auto_approved' %}
          <li>• This record was automatically approved by the system</li>
          <li>• Auto-approved records cannot be modified to maintain consistency</li>
          {% else %}
          <li>• Only pending attendance requests can be edited</li>
          <li>• Once processed, records become part of your permanent attendance history</li>
          {% endif %}
        </ul>
      </div>

      <!-- Actions -->
      <div class="mt-6 flex flex-col sm:flex-row gap-3 justify-center">
        <a href="{{ url_for('attendance.my_attendance') }}" class="btn btn-primary btn-md">
          <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
          </svg>
          Back to My Attendance
        </a>
        {% if record.status == 'rejected' %}
        <a href="{{ url_for('attendance.request_attendance') }}" class="btn btn-outline btn-md">
          <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
          </svg>
          Submit New Request
        </a>
        {% endif %}
      </div>

      <!-- Contact Info -->
      <div class="mt-6 text-xs text-gray-500 dark:text-gray-400">
        <p>
          If you believe this is an error or need to make changes to a processed record,
          please contact your manager or HR department.
        </p>
      </div>
    </div>
  </div>
</div>
{% endblock %}

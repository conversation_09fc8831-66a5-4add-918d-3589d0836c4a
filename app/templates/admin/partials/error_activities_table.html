<table class="w-full">
  <thead>
    <tr class="border-b border-border">
      <th class="px-4 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">User</th>
      <th class="px-4 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">Action</th>
      <th class="px-4 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">Category</th>
      <th class="px-4 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">Details</th>
      <th class="px-4 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">Date & Time</th>
      <th class="px-4 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">Actions</th>
    </tr>
  </thead>
  <tbody>
    {% if error_activities %}
      {% for activity in error_activities %}
        <tr class="border-b border-border hover:bg-muted/30 transition-colors duration-150 bg-destructive/5">
          <!-- User -->
          <td class="px-4 py-3">
            <div class="flex items-center">
              <span class="text-sm">{{ activity.user.name }}</span>
            </div>
          </td>

          <!-- Action -->
          <td class="px-4 py-3">
            <div class="text-sm text-destructive font-medium">{{ activity.action }}</div>
            {% if activity.method %}
              <div class="mt-1">
                <span class="text-xs px-1.5 py-0.5 rounded-sm inline-flex items-center bg-red-100 text-red-700 dark:bg-red-900/30 dark:text-red-400">
                  {% if activity.method == 'create' %}<i data-lucide="plus" class="h-3 w-3 mr-1"></i>
                  {% elif activity.method == 'read' %}<i data-lucide="eye" class="h-3 w-3 mr-1"></i>
                  {% elif activity.method == 'update' %}<i data-lucide="edit" class="h-3 w-3 mr-1"></i>
                  {% elif activity.method == 'delete' %}<i data-lucide="trash" class="h-3 w-3 mr-1"></i>
                  {% else %}<i data-lucide="activity" class="h-3 w-3 mr-1"></i>{% endif %}
                  {{ activity.method_display or activity.method.title() }}
                </span>
              </div>
            {% endif %}
          </td>

          <!-- Category -->
          <td class="px-4 py-3">
            <span class="text-xs px-2 py-1 rounded-full bg-muted inline-flex items-center">
              {% if activity.category == 'auth' %}<i data-lucide="key" class="h-3.5 w-3.5 mr-1"></i>
              {% elif activity.category == 'admin' %}<i data-lucide="shield" class="h-3.5 w-3.5 mr-1"></i>
              {% elif activity.category == 'user' %}<i data-lucide="user" class="h-3.5 w-3.5 mr-1"></i>
              {% elif activity.category == 'system' %}<i data-lucide="settings" class="h-3.5 w-3.5 mr-1"></i>
              {% elif activity.category == 'data' %}<i data-lucide="database" class="h-3.5 w-3.5 mr-1"></i>
              {% else %}<i data-lucide="tag" class="h-3.5 w-3.5 mr-1"></i>{% endif %}
              {{ activity.category_display or activity.category.title() }}
            </span>
          </td>

          <!-- Details -->
          <td class="px-4 py-3">
            <div class="max-w-xs truncate text-sm" title="{{ activity.details or '-' }}">
              {{ activity.details or '-' }}
            </div>
          </td>

          <!-- Date & Time -->
          <td class="px-4 py-3">
            <div class="flex flex-col">
              <span class="text-sm">{{ activity.created_at.strftime('%Y-%m-%d') }}</span>
              <span class="text-xs text-muted-foreground">{{ activity.created_at.strftime('%H:%M:%S') }}</span>
            </div>
          </td>

          <!-- Actions -->
          <td class="px-4 py-3">
            <div class="flex space-x-1">
              <button type="button"
                      class="btn btn-icon btn-sm"
                      onclick="showActivityDetails({{ activity.id }})"
                      title="View Details">
                <i data-lucide="eye" class="h-4 w-4"></i>
              </button>
            </div>
          </td>
        </tr>
      {% endfor %}
    {% else %}
      <tr>
        <td colspan="6" class="px-4 py-8 text-center">
          <div class="flex flex-col items-center justify-center">
            <div class="rounded-full bg-destructive/10 p-3 mb-3">
              <i data-lucide="alert-circle" class="h-6 w-6 text-destructive"></i>
            </div>
            <h3 class="text-lg font-medium mb-1">No error activities found</h3>
            <p class="text-muted-foreground text-sm">System is running smoothly with no recorded errors</p>
          </div>
        </td>
      </tr>
    {% endif %}
  </tbody>
</table>

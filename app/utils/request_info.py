import socket
from flask import request

def get_client_info():
    """Get detailed client information from request."""
    client_info = {
        'ip_address': request.remote_addr,
        'user_agent': str(request.user_agent),
        'hostname': None
    }

    # Try to get hostname from IP (may not always work due to DNS restrictions)
    try:
        if client_info['ip_address'] and client_info['ip_address'] not in ('127.0.0.1', 'localhost'):
            hostname = socket.gethostbyaddr(client_info['ip_address'])[0]
            client_info['hostname'] = hostname
    except (socket.herror, socket.gaierror):
        # Can't resolve hostname, which is common
        pass

    # Add any HTTP headers that might help identify the client
    if 'X-Forwarded-For' in request.headers:
        client_info['forwarded_for'] = request.headers.get('X-Forwarded-For')

    if 'X-Real-IP' in request.headers:
        client_info['real_ip'] = request.headers.get('X-Real-IP')

    return client_info

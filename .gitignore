# Node modules
node_modules/

# Compiled CSS
app/static/css/main.css

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
*.egg-info/
.installed.cfg
*.egg

# Virtual Environment
venv/
ENV/

# IDE files
.idea/
.vscode/
*.swp
*.swo

# OS specific
.DS_Store
Thumbs.db

# Flask
instance/
.webassets-cache
*.db
migrations/
*.pyc

# Environment variables and secrets
.env
*.env

# Logs and database files
logs/
*.log
*.sqlite3
*.db
*.sqlite

# Cache files
*.cache
*.egg-info/
*.mo
*.pot

# Dependencies (if using pip freeze)
requirements.txt
Pipfile
Pipfile.lock

/**
 * System Health JavaScript
 * Handles fetching and displaying system health information
 */

document.addEventListener('DOMContentLoaded', function() {
    // Initialize system health
    initSystemHealth();
});

/**
 * Initialize system health monitoring
 */
function initSystemHealth() {
    // Get the system health value element
    const systemHealthElement = document.querySelector('.system-health-value');
    if (!systemHealthElement) return;
    
    // Fetch system health data from the API
    fetchSystemHealth()
        .then(data => {
            if (data && data.success) {
                updateSystemHealthDisplay(data.health, systemHealthElement);
            } else {
                console.error('Failed to fetch system health data');
                systemHealthElement.textContent = 'Unknown';
            }
        })
        .catch(error => {
            console.error('Error fetching system health:', error);
            systemHealthElement.textContent = 'Unknown';
        });
}

/**
 * Fetch system health data from the API
 */
async function fetchSystemHealth() {
    try {
        const response = await fetch('/api/system/health');
        if (!response.ok) {
            throw new Error(`HTTP error! Status: ${response.status}`);
        }
        return await response.json();
    } catch (error) {
        console.error('Error fetching system health:', error);
        return { success: false };
    }
}

/**
 * Update the system health display
 */
function updateSystemHealthDisplay(healthData, element) {
    if (!healthData || !element) return;
    
    // Update the health status text
    element.textContent = healthData.status;
    
    // Apply appropriate color based on status
    const statusColors = {
        'Excellent': 'text-emerald-600 dark:text-emerald-400',
        'Good': 'text-blue-600 dark:text-blue-400',
        'Fair': 'text-amber-600 dark:text-amber-400',
        'Poor': 'text-red-600 dark:text-red-400'
    };
    
    // Remove any existing color classes
    element.className = element.className
        .split(' ')
        .filter(cls => !cls.startsWith('text-'))
        .join(' ');
    
    // Add the appropriate color class
    const colorClass = statusColors[healthData.status] || 'text-gray-900 dark:text-white';
    element.classList.add(...colorClass.split(' '));
    
    // Update the parent card icon color if status is not excellent
    if (healthData.status !== 'Excellent') {
        const iconContainer = element.closest('.analytics-card').querySelector('.rounded-full');
        if (iconContainer) {
            // Update background color
            iconContainer.className = iconContainer.className
                .replace(/bg-emerald-100/g, '')
                .replace(/text-emerald-600/g, '');
            
            if (healthData.status === 'Good') {
                iconContainer.classList.add('bg-blue-100', 'text-blue-600');
                iconContainer.classList.remove('dark:bg-blue-900/20');
                iconContainer.classList.add('dark:bg-blue-900/20');
            } else if (healthData.status === 'Fair') {
                iconContainer.classList.add('bg-amber-100', 'text-amber-600');
                iconContainer.classList.remove('dark:bg-blue-900/20');
                iconContainer.classList.add('dark:bg-amber-900/20');
            } else if (healthData.status === 'Poor') {
                iconContainer.classList.add('bg-red-100', 'text-red-600');
                iconContainer.classList.remove('dark:bg-blue-900/20');
                iconContainer.classList.add('dark:bg-red-900/20');
            }
        }
    }
}

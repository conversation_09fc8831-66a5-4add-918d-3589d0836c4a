{% macro activity_details_modal() %}
<!-- Activity Details Modal - Pure Tailwind Implementation -->
<div id="activity-details-modal" class="hidden fixed inset-0 w-full h-full z-[9999] overflow-y-auto overflow-x-hidden" aria-modal="true" role="dialog">
  <!-- Modal Backdrop -->
  <div class="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm transition-opacity duration-300" id="activity-modal-backdrop"></div>
  
  <!-- Modal Dialog -->
  <div class="relative flex min-h-full items-center justify-center p-4">
    <!-- Modal Content -->
    <div class="relative max-w-2xl w-full transform overflow-hidden rounded-lg bg-card text-card-foreground border border-border shadow-xl transition-all duration-300 translate-y-4 opacity-0" id="activity-modal-content">
      
      <!-- Modal Header -->
      <div id="activity-details-header" class="p-4 border-b border-border bg-card">
        <!-- Header content will be dynamically populated -->
      </div>
      
      <!-- Modal Body -->
      <div id="activity-details-content" class="p-6 max-h-[70vh] overflow-y-auto">
        <div class="flex justify-center items-center py-16">
          <div class="animate-spin rounded-full h-10 w-10 border-2 border-t-transparent border-primary"></div>
        </div>
      </div>
      
      <!-- Modal Footer -->
      <div class="px-6 py-4 border-t border-border flex justify-end">
        <button type="button" class="inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-9 px-4 py-2" onclick="closeActivityModal()">
          <i data-lucide="x" class="h-4 w-4 mr-2"></i>
          Close
        </button>
      </div>
    </div>
  </div>
</div>
{% endmacro %}

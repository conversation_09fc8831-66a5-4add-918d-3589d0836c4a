# Matrix App Database Schema

## Overview

The Matrix App uses a relational database with several interconnected tables to manage users, organizational structure, teams, and system activities. This document provides a detailed explanation of the database schema and the relationships between entities.

## Core Entities

### User
The central entity that represents system users with authentication and basic profile information.
- Primary authentication entity with roles (Admin, Manager, User)
- Stores credentials and basic user information
- Linked to employee details for extended information
- Tracks user activity and system access

### EmployeeDetail
Extended information about users who are employees in the organization.
- Contains personal and professional details
- Links to business units and segments
- Can be assigned to multiple teams
- Stores employment status and job information

### BusinessUnit
Represents major divisions within the organization.
- Top-level organizational structure
- Has a manager (linked to User)
- Contains multiple business segments
- Groups employees by organizational division

### BusinessSegment
Represents subdivisions within business units.
- Second-level organizational structure
- Belongs to a specific business unit
- Has a manager (linked to User)
- Further categorizes employees within business units

### Team
Represents functional teams that can span across business units and segments.
- Cross-organizational grouping of employees
- Can contain multiple team groups
- Members are linked through the team_members association table
- Allows flexible employee grouping beyond the organizational hierarchy

## Supporting Entities

### TeamGroup
Subgroups within teams for more granular organization.
- Belongs to a specific team
- Provides additional organizational structure within teams

### Activity
Comprehensive audit logging for user actions in the system.
- Tracks all significant user actions
- Records details including IP address, user agent, and URL
- Stores before/after values for data changes
- Categorizes activities by type and severity

### Message
Internal messaging system between users.
- Supports communication between system users
- Tracks read status and message history
- Links sender and recipient users

### PasswordReset
Manages password reset tokens and their lifecycle.
- Handles secure password reset workflow
- Tracks token creation, expiration, and usage
- Ensures one-time use of reset tokens

### Setting
System-wide configuration settings.
- Stores application configuration
- Supports different value types (string, integer, boolean, etc.)
- Controls feature flags and system behavior
- Distinguishes between public and private settings

## Key Relationships

1. **User and EmployeeDetail (1:1)**
   - Each user can have one employee detail record
   - Employee details are directly linked to a specific user

2. **BusinessUnit and BusinessSegment (1:N)**
   - A business unit contains multiple business segments
   - Each business segment belongs to exactly one business unit

3. **User and BusinessUnit/BusinessSegment (1:N)**
   - A user can manage multiple business units or segments
   - Each business unit or segment has at most one manager

4. **EmployeeDetail and BusinessUnit/BusinessSegment (N:1)**
   - An employee belongs to one business unit and optionally one business segment
   - Business units and segments can have multiple employees

5. **Team and EmployeeDetail (M:N)**
   - An employee can belong to multiple teams
   - A team can have multiple employees
   - Relationship is managed through the team_members association table

6. **Team and TeamGroup (1:N)**
   - A team can have multiple team groups
   - Each team group belongs to exactly one team

7. **User and Activity (1:N)**
   - A user generates multiple activity records
   - Each activity is associated with exactly one user

8. **User and Message (1:N as sender and recipient)**
   - A user can send multiple messages
   - A user can receive multiple messages
   - Each message has exactly one sender and one recipient

## Database Indexes

The schema includes several strategic indexes to optimize query performance:

1. **User table**
   - Index on email (unique) for fast login lookups

2. **Activity table**
   - Composite index on user_id and created_at for user activity history
   - Indexes on entity_type and entity_id for entity-specific activity logs
   - Indexes on category and severity for filtered activity views

3. **Business tables**
   - Unique indexes on business unit and segment codes
   - Indexes on manager_id for manager-specific queries

4. **Team tables**
   - Unique index on team slug for URL-friendly access
   - Primary key indexes on the team_members association table

5. **Message table**
   - Composite index on recipient_id, is_read, and created_at for inbox views

## Data Integrity

The schema enforces referential integrity through foreign key constraints:

1. **Cascading deletes** for certain relationships:
   - User deletion cascades to EmployeeDetail, Activity, and sent Messages
   - Team deletion cascades to TeamGroup

2. **Non-cascading relationships** to prevent accidental data loss:
   - Business unit and segment relationships to employees
   - Received messages (to preserve message history)

3. **Soft deletion** for important entities:
   - Users have a deleted_at field for soft deletion
   - Business units and segments use is_active flag rather than deletion

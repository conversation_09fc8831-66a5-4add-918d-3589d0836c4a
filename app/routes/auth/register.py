"""
Authentication registration routes.
"""

from flask import render_template, redirect, url_for, flash, request, current_app
from flask_login import current_user

from app import db, limiter
from app.models import User, Activity, WeakPasswordError
from app.utils.request_info import get_client_info
from app.utils.settings import get_setting
from app.routes.auth import auth_bp, redirect_authenticated_user


@auth_bp.route('/register', methods=['GET', 'POST'])
@limiter.limit("5 per hour", error_message="Too many registration attempts. Please try again later.")
def register():
    """Handle user registration."""
    # Redirect if already logged in
    redirect_result = redirect_authenticated_user()
    if redirect_result:
        return redirect_result

    # Check if registration is allowed
    allow_registration = get_setting('allow_registration', True)

    if not allow_registration:
        flash('Registration is currently disabled. Please contact an administrator.', 'error')
        return redirect(url_for('auth.login'))

    if request.method == 'POST':
        name = request.form.get('name')
        email = request.form.get('email')
        password = request.form.get('password')
        confirm_password = request.form.get('confirm_password')

        # Validate input
        if not name or not email or not password:
            flash('All fields are required', 'error')
            return render_template('auth/register.html')

        if password != confirm_password:
            flash('Passwords do not match', 'error')
            return render_template('auth/register.html')

        # Check if user already exists
        existing_user = User.get_by_email(email)
        if existing_user:
            flash('Email already registered', 'error')
            return render_template('auth/register.html')

        try:
            # Get registration settings
            require_email_verification = get_setting('require_email_verification', True)
            require_admin_approval = get_setting('require_admin_approval', False)
            default_role = get_setting('default_user_role', 'User')

            # Create new user
            new_user = User(
                name=name,
                email=email,
                role=default_role
            )

            # Set initial active state based on verification requirements
            if require_email_verification or require_admin_approval:
                new_user.is_active = False

            new_user.set_password(password)

            db.session.add(new_user)
            db.session.commit()

            # Log activity
            client_info = get_client_info()
            Activity.log(
                user_id=new_user.id,
                action='User registered',
                entity_type='User',
                entity_id=new_user.id,
                ip_address=client_info['ip_address'],
                user_agent=client_info['user_agent'],
                details=f"Hostname: {client_info['hostname'] or 'Unknown'}",
                category=Activity.CATEGORY_AUTH,
                method=Activity.METHOD_CREATE,
                severity=Activity.SEVERITY_INFO
            )

            # Handle email verification if required
            if require_email_verification:
                # TODO: Implement email verification
                # For now, we'll just show a message
                flash('Your account has been created. Please check your email to verify your account.', 'success')
                return redirect(url_for('auth.login'))
            # Handle admin approval if required
            elif require_admin_approval:
                flash('Your account has been created and is pending admin approval.', 'success')
                return redirect(url_for('auth.login'))
            # Otherwise, account is active immediately
            else:
                flash('Registration successful! You can now log in.', 'success')
                return redirect(url_for('auth.login'))

        except WeakPasswordError as e:
            flash(str(e), 'error')
            return render_template('auth/register.html')
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"Registration error: {str(e)}")
            flash('An error occurred during registration. Please try again.', 'error')
            return render_template('auth/register.html')

    return render_template('auth/register.html')

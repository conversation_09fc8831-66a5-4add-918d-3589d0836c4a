/**
 * Debugging utilities for the attendance types page
 */

document.addEventListener('DOMContentLoaded', function() {
  // Create debug console
  createDebugConsole();

  // Add a debug button to the page
  const debugButton = document.createElement('button');
  debugButton.textContent = 'Debug Attendance Forms';
  debugButton.className = 'btn btn-sm btn-outline-secondary ms-2';
  debugButton.addEventListener('click', debugAttendanceTypes);

  // Find the page header actions area and append our button
  const pageHeader = document.querySelector('.page-header-actions');
  if (pageHeader) {
    pageHeader.appendChild(debugButton);
  } else {
    // If we can't find the page header, add it to the body
    document.body.appendChild(debugButton);
  }

  // Log initial page load - use setTimeout to ensure console is fully created
  setTimeout(() => {
    logToDebugConsole('Page loaded. Ready for debugging.');
  }, 100);
});

/**
 * Create a debug console for showing log messages
 */
function createDebugConsole() {
  // Create console container
  const container = document.createElement('div');
  container.id = 'debug-console';
  container.style.cssText = 'position: fixed; bottom: 10px; right: 10px; width: 400px; height: 200px; ' +
    'background: rgba(0, 0, 0, 0.8); color: #00ff00; font-family: monospace; font-size: 12px; ' +
    'padding: 10px; overflow: auto; z-index: 9999; display: none; border-radius: 5px;';

  // Create console header
  const header = document.createElement('div');
  header.style.cssText = 'display: flex; justify-content: space-between; margin-bottom: 5px; border-bottom: 1px solid #444; padding-bottom: 5px;';

  const title = document.createElement('div');
  title.textContent = 'Debug Console';
  header.appendChild(title);

  const buttons = document.createElement('div');

  const clearBtn = document.createElement('button');
  clearBtn.textContent = 'Clear';
  clearBtn.style.cssText = 'background: #333; color: #fff; border: none; margin-right: 5px; border-radius: 3px; padding: 2px 5px;';
  clearBtn.addEventListener('click', () => {
    const content = document.getElementById('debug-console-content');
    if (content) content.innerHTML = '';
  });

  const closeBtn = document.createElement('button');
  closeBtn.textContent = 'Close';
  closeBtn.style.cssText = 'background: #333; color: #fff; border: none; border-radius: 3px; padding: 2px 5px;';
  closeBtn.addEventListener('click', () => {
    const debugConsole = document.getElementById('debug-console');
    if (debugConsole) debugConsole.style.display = 'none';
  });

  buttons.appendChild(clearBtn);
  buttons.appendChild(closeBtn);
  header.appendChild(buttons);

  container.appendChild(header);

  // Create console content area
  const content = document.createElement('div');
  content.id = 'debug-console-content';
  content.style.cssText = 'height: calc(100% - 30px); overflow: auto;';
  container.appendChild(content);

  document.body.appendChild(container);

  // Create toggle button
  const toggleBtn = document.createElement('button');
  toggleBtn.textContent = 'Debug';
  toggleBtn.style.cssText = 'position: fixed; bottom: 10px; right: 10px; background: #007bff; ' +
    'color: white; border: none; border-radius: 5px; padding: 5px 10px; z-index: 9998;';
  toggleBtn.addEventListener('click', () => {
    const debugConsole = document.getElementById('debug-console');
    if (debugConsole) {
      debugConsole.style.display = debugConsole.style.display === 'none' ? 'block' : 'none';
    }
  });

  document.body.appendChild(toggleBtn);
}

/**
 * Log a message to the debug console
 * @param {string} message - Message to log
 * @param {string} type - Message type ('log', 'error', 'warning', 'success')
 */
function logToDebugConsole(message, type = 'log') {
  const content = document.getElementById('debug-console-content');
  if (!content) return;

  // Log to browser console as well
  console.log(message);

  const entry = document.createElement('div');
  const timestamp = new Date().toLocaleTimeString();

  let color = '#00ff00'; // Default green for log
  if (type === 'error') color = '#ff3333';
  if (type === 'warning') color = '#ffcc00';
  if (type === 'success') color = '#00cc66';

  entry.style.color = color;
  entry.innerHTML = `[${timestamp}] ${message}`;
  content.appendChild(entry);
  content.scrollTop = content.scrollHeight;

  // Show console if it was hidden
  const debugConsole = document.getElementById('debug-console');
  if (debugConsole && debugConsole.style.display === 'none') {
    debugConsole.style.display = 'block';
  }
}

/**
 * Debug function to test form loading
 */
function debugAttendanceTypes() {
  logToDebugConsole('==== DEBUGGING ATTENDANCE TYPES ====', 'log');

  // Check URLs in hidden fields
  const addUrl = document.getElementById('add-attendance-type-url')?.getAttribute('data-url');
  const editBaseUrl = document.getElementById('edit-attendance-type-base-url')?.getAttribute('data-url');
  const deleteBaseUrl = document.getElementById('delete-attendance-type-base-url')?.getAttribute('data-url');

  logToDebugConsole(`Add URL: ${addUrl}`, 'log');
  logToDebugConsole(`Edit Base URL: ${editBaseUrl}`, 'log');
  logToDebugConsole(`Delete Base URL: ${deleteBaseUrl}`, 'log');

  // Check if drawerManager is available
  logToDebugConsole(`drawerManager available: ${!!window.drawerManager}`, 'log');

  // Test direct fetch of the add form
  if (addUrl) {
    logToDebugConsole('Testing direct fetch of add form URL...', 'log');

    fetch(addUrl)
      .then(response => {
        logToDebugConsole(`Add form fetch response status: ${response.status}`, response.ok ? 'success' : 'error');
        if (!response.ok) {
          throw new Error(`HTTP error! Status: ${response.status}`);
        }
        return response.text();
      })
      .then(html => {
        logToDebugConsole(`Add form HTML received, length: ${html.length}`, 'success');
        logToDebugConsole(`First 100 chars: ${html.substring(0, 100).replace(/</g, '&lt;').replace(/>/g, '&gt;')}`, 'log');

        // Test opening the drawer with this HTML
        logToDebugConsole('Attempting to open drawer with HTML...', 'log');
        try {
          // Use the new openHtml method of the DrawerManager
          const drawer = window.drawerManager.openHtml(html, {
            position: 'right',
            size: 'md'
          });
          logToDebugConsole('Drawer opened successfully!', 'success');
        } catch (error) {
          logToDebugConsole(`Error opening drawer: ${error.message}`, 'error');
        }

        // Show a toast with the result
        if (typeof showToast === 'function') {
          showToast('Add form fetch successful', { type: 'success' });
        }
      })
      .catch(error => {
        logToDebugConsole(`Error fetching add form: ${error.message}`, 'error');
        if (typeof showToast === 'function') {
          showToast(`Error fetching add form: ${error.message}`, { type: 'error' });
        }
      });
  }
}

// Add to window object
window.debugAttendanceTypes = debugAttendanceTypes;
window.logToDebugConsole = logToDebugConsole;

// Debug form submissions
const originalSubmitDrawerForm = window.submitDrawerForm;
window.submitDrawerForm = function(formId) {
  logToDebugConsole(`Debug: Submitting form ${formId}`, 'log');
  const form = document.getElementById(formId);
  if (form) {
    const formData = new FormData(form);
    const formDataObj = {};

    logToDebugConsole(`Form action: ${form.getAttribute('action')}`, 'log');
    logToDebugConsole(`Form method: ${form.getAttribute('method')}`, 'log');

    // Log form fields
    for (const [key, value] of formData.entries()) {
      formDataObj[key] = value;
      logToDebugConsole(`Form field: ${key} = ${value}`, 'log');
    }
  }

  return originalSubmitDrawerForm.apply(this, arguments)
    .then(result => {
      logToDebugConsole(`Form submission successful: ${JSON.stringify(result)}`, 'success');
      return result;
    })
    .catch(error => {
      logToDebugConsole(`Form submission error: ${error.message}`, 'error');
      if (error.cause) {
        if (error.cause.clientValidation) {
          logToDebugConsole(`Client-side validation failed: ${error.cause.message}`, 'error');
        } else if (error.cause.errors) {
          logToDebugConsole(`Validation errors: ${JSON.stringify(error.cause.errors)}`, 'error');
        }
      }
      // Don't rethrow the error here, just log it and return a rejected promise
      return Promise.reject(error);
    });
};

// Override the standard attendance type functions to add logging
const originalOpenAddAttendanceTypeForm = window.openAddAttendanceTypeForm;
window.openAddAttendanceTypeForm = function() {
  logToDebugConsole('openAddAttendanceTypeForm called', 'log');
  return originalOpenAddAttendanceTypeForm?.apply(this, arguments);
};

const originalOpenEditAttendanceTypeForm = window.openEditAttendanceTypeForm;
window.openEditAttendanceTypeForm = function(typeId) {
  logToDebugConsole(`openEditAttendanceTypeForm called with typeId: ${typeId}`, 'log');
  return originalOpenEditAttendanceTypeForm?.apply(this, arguments);
};

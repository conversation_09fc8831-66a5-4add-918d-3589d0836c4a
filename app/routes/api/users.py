"""
API routes for user management.
"""

from flask import jsonify, request
from flask_login import login_required, current_user

from app import db
from app.models import User, EmployeeDetail, Activity
from app.utils.decorators import admin_required, log_activity
from app.routes.api import api_bp
from app.routes.api.utils import handle_api_error
from app.routes.api.activities import APIError


@api_bp.route('/users', methods=['GET'])
@login_required
@handle_api_error
@log_activity('Retrieved user list via API')
def get_users():
    """Get all users with optional filtering."""
    # Get query parameters for filtering
    role = request.args.get('role')
    is_active = request.args.get('is_active')

    # Start with base query - use eager loading for employee_detail and related business entities
    query = User.query.options(
        db.joinedload(User.employee_detail).joinedload(EmployeeDetail.business_unit),
        db.joinedload(User.employee_detail).joinedload(EmployeeDetail.business_segment)
    )

    # Apply filters if provided with validation
    if role:
        # Validate role against allowed values
        allowed_roles = ['Admin', 'Manager', 'User']
        if role in allowed_roles:
            query = query.filter(User.role == role)

    if is_active is not None:
        # Validate is_active parameter
        if isinstance(is_active, str) and is_active.lower() in ['true', 'false']:
            is_active_bool = is_active.lower() == 'true'
            query = query.filter_by(_is_active=is_active_bool)

    users = query.all()
    result = []

    for user in users:
        user_data = {
            'id': user.id,
            'name': user.name,
            'email': user.email,
            'role': user.role,
            'is_active': user.is_active,
            'created_at': user.created_at.isoformat()
        }

        # Add employee details if they exist - now using the preloaded relationship
        if user.employee_detail:
            user_data.update({
                'position': user.employee_detail.job_title,  # Updated from position to job_title
                'business_unit': user.employee_detail.business_unit.name if user.employee_detail.business_unit else None,
                'business_segment': user.employee_detail.business_segment.name if user.employee_detail.business_segment else None,
            })

        result.append(user_data)

    return jsonify({
        'success': True,
        'count': len(result),
        'users': result
    })


@api_bp.route('/users/<int:user_id>', methods=['GET'])
@login_required
@handle_api_error
@log_activity('Retrieved user details via API')
def get_user(user_id):
    """Get details for a specific user."""
    # Use eager loading for employee_detail and related business entities
    user = User.query.options(
        db.joinedload(User.employee_detail).joinedload(EmployeeDetail.business_unit),
        db.joinedload(User.employee_detail).joinedload(EmployeeDetail.business_segment)
    ).get_or_404(user_id)

    data = {
        'id': user.id,
        'name': user.name,
        'email': user.email,
        'role': user.role,
        'is_active': user.is_active,
        'created_at': user.created_at.isoformat(),
        'last_login': user.last_login.isoformat() if user.last_login else None
    }

    # Add employee details if they exist - now using the preloaded relationship
    if user.employee_detail:
        data.update({
            'position': user.employee_detail.job_title,  # Updated from position to job_title
            'phone': user.employee_detail.phone,
            'business_unit_id': user.employee_detail.business_unit_id,
            'business_segment_id': user.employee_detail.business_segment_id,
            'business_unit': user.employee_detail.business_unit.name if user.employee_detail.business_unit else None,
            'business_segment': user.employee_detail.business_segment.name if user.employee_detail.business_segment else None,
            'hire_date': user.employee_detail.hire_date.isoformat() if user.employee_detail.hire_date else None
        })

    return jsonify({
        'success': True,
        'user': data
    })


@api_bp.route('/users/<int:user_id>/toggle-status', methods=['POST'])
@login_required
@admin_required
@handle_api_error
@log_activity('Toggled user status via API')
def toggle_user_status(user_id):
    """Toggle a user's active status."""
    user = User.query.get_or_404(user_id)

    # Prevent deactivating yourself
    if user.id == current_user.id:
        raise APIError('You cannot deactivate your own account', status_code=400)

    user.is_active = not user.is_active
    db.session.commit()

    # Log activity using the Activity.log class method
    Activity.log(
        user_id=current_user.id,
        action=f"{'Activated' if user.is_active else 'Deactivated'} user",
        entity_type='User',
        entity_id=user.id,
        details=f"{'Activated' if user.is_active else 'Deactivated'} user {user.name} ({user.email})"
    )

    return jsonify({
        'success': True,
        'is_active': user.is_active,
        'message': f"User {'activated' if user.is_active else 'deactivated'} successfully"
    })

from flask import Flask
from flask_sqlalchemy import SQLAlchemy
from flask_migrate import Migrate
from flask_login import <PERSON>gin<PERSON>ana<PERSON>
from flask_wtf.csrf import CSRFProtect
from flask_mail import Mail
from flask_limiter import Limiter
from flask_limiter.util import get_remote_address
from flask_caching import Cache
import os
from datetime import timedelta

# Initialize extensions
db = SQLAlchemy()
migrate = Migrate()
login_manager = LoginManager()
csrf = CSRFProtect()
mail = Mail()
cache = Cache(config={'CACHE_TYPE': 'SimpleCache', 'CACHE_DEFAULT_TIMEOUT': 300})

limiter = Limiter(
    key_func=get_remote_address,
    default_limits=["10000 per day", "2000 per hour"],  # More relaxed limits for development
    storage_uri="memory://",  # For production, use Redis: "redis://localhost:6379/0"
    strategy="fixed-window-elastic-expiry",  # Better for production
    swallow_errors=True  # Don't fail if rate limiter has issues
)

def create_app(config_name='default'):
    """Application factory function."""
    from config import config

    app = Flask(__name__)

    # Load configuration
    app.config.from_object(config[config_name])

    # Initialize extensions
    db.init_app(app)
    migrate.init_app(app, db)
    login_manager.init_app(app)
    csrf.init_app(app)
    mail.init_app(app)
    limiter.init_app(app)
    cache.init_app(app)

    # Configure login manager
    login_manager.login_view = 'auth.login'  # type: ignore
    login_manager.login_message = 'Please log in to access this page.'
    login_manager.login_message_category = 'error'
    login_manager.session_protection = 'strong'

    # Ensure the CSS output directory exists
    if app.static_folder:
        os.makedirs(os.path.join(app.static_folder, 'css'), exist_ok=True)

    # Register blueprints
    from app.routes.admin import admin_bp
    from app.routes.main import main_bp
    from app.routes.auth import auth_bp
    from app.routes.api import api_bp
    from app.routes.forms import forms_bp, admin_forms_bp
    from app.routes.teams import teams_bp
    from app.routes.admin.attendance_admin import admin_attendance_bp # Import admin attendance blueprint
    from app.routes.attendance import attendance_bp # Import user attendance blueprint

    app.register_blueprint(main_bp)
    app.register_blueprint(auth_bp)
    app.register_blueprint(admin_bp)
    app.register_blueprint(api_bp)
    app.register_blueprint(teams_bp)
    app.register_blueprint(admin_attendance_bp) # Register admin attendance blueprint
    app.register_blueprint(attendance_bp) # Register user attendance blueprint
    # Register forms blueprints
    app.register_blueprint(forms_bp)
    app.register_blueprint(admin_forms_bp)

    # Apply specific rate limits to sensitive routes
    # We'll use decorators in the route files instead

    # User loader callback
    from app.models.user import User

    @login_manager.user_loader
    def load_user(user_id):
        return User.query.get(int(user_id))

    # Don't automatically create tables, use migrations instead
    # with app.app_context():
    #     db.create_all()

    # Apply default settings or use values from database if the table exists
    with app.app_context():
        try:
            # Use safe version of settings that won't fail during migrations
            from app.utils.settings import get_setting_safe

            # Apply session lifetime from settings, default to 7 days if not found
            session_lifetime_days = get_setting_safe('session_lifetime', 7)
            app.config['PERMANENT_SESSION_LIFETIME'] = timedelta(days=int(session_lifetime_days))
            app.logger.info(f"Session lifetime set to {session_lifetime_days} days")

            # Only run maintenance if settings table exists and maintenance is enabled
            if get_setting_safe('enable_automatic_maintenance', False):
                from app.utils.scheduled_tasks import run_maintenance_tasks
                result = run_maintenance_tasks()
                app.logger.info(f"Automatic maintenance completed: {result}")
        except Exception as e:
            # Just use defaults if any error occurs
            app.logger.warning(f"Using default settings: {str(e)}")
            app.config['PERMANENT_SESSION_LIFETIME'] = timedelta(days=7)

    # Add context processor to make settings available to all templates
    @app.context_processor
    def inject_settings():
        from app.utils.settings import get_all_settings

        # Helper function to convert hex color to RGB
        def hex_to_rgb(hex_color):
            hex_color = hex_color.lstrip('#')
            if len(hex_color) == 6:
                return tuple(int(hex_color[i:i+2], 16) for i in (0, 2, 4))
            return (2, 132, 199)  # Default blue

        # Helper function to convert hex color to HSL
        def hex_to_hsl(hex_color):
            # First convert to RGB
            hex_color = hex_color.lstrip('#')
            if len(hex_color) != 6:
                hex_color = '0284c7'  # Default blue

            r, g, b = tuple(int(hex_color[i:i+2], 16) / 255.0 for i in (0, 2, 4))

            # Calculate HSL
            max_val = max(r, g, b)
            min_val = min(r, g, b)
            l = (max_val + min_val) / 2

            if max_val == min_val:
                h = s = 0  # achromatic
            else:
                d = max_val - min_val
                s = d / (2 - max_val - min_val) if l > 0.5 else d / (max_val + min_val)

                if max_val == r:
                    h = (g - b) / d + (6 if g < b else 0)
                elif max_val == g:
                    h = (b - r) / d + 2
                else:  # max_val == b
                    h = (r - g) / d + 4

                h /= 6

            # Convert to degrees and percentage
            h = round(h * 360)
            s = round(s * 100)
            l = round(l * 100)

            return f"{h}deg {s}% {l}%"

        return {
            'app_settings': get_all_settings(include_private=False),
            'hex_to_rgb': hex_to_rgb,
            'hex_to_hsl': hex_to_hsl
        }

    return app

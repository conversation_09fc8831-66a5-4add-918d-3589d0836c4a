from functools import wraps
from flask import flash, redirect, url_for, request, current_app
from flask_login import current_user
from app.models import Activity
from app import db
from app.utils.request_info import get_client_info
import time
from datetime import datetime

def admin_required(f):
    """
    Decorator to restrict access to admin users only.
    Redirects to dashboard with an error message for non-admin users.
    """
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not current_user.is_authenticated:
            flash('Please log in to access this page.', 'error')
            return redirect(url_for('auth.login', next=request.url))

        if not current_user.is_admin:
            flash('You do not have permission to access this page.', 'error')
            return redirect(url_for('main.user_dashboard'))
        return f(*args, **kwargs)
    return decorated_function

def manager_required(f):
    """
    Decorator to restrict access to admin and manager users only.
    Redirects to dashboard with an error message for non-manager users.
    """
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not current_user.is_authenticated:
            flash('Please log in to access this page.', 'error')
            return redirect(url_for('auth.login', next=request.url))

        if not current_user.is_admin and not current_user.is_manager:
            flash('You do not have permission to access this page.', 'error')
            return redirect(url_for('main.user_dashboard'))
        return f(*args, **kwargs)
    return decorated_function

def log_activity(action_description, entity_type=None, category=None, severity=None, method=None, include_request_details=True, track_changes=False, skip_ajax=False):
    """
    Decorator that logs user activity to the database.

    Args:
        action_description: Description of the action being performed
        entity_type: Type of entity being acted upon (User, BusinessUnit, etc.)
        category: Category of the activity (auth, user, admin, system, data)
        severity: Severity level (info, warning, error)
        method: CRUD method (create, read, update, delete)
        include_request_details: Whether to include request details in the log
        track_changes: Whether to track old and new values for CRUD operations
    """
    # Create a standardized action description
    standardized_action = action_description
    action_lower = action_description.lower()

    # Standardize CRUD operation descriptions and determine method
    actual_method = method
    if 'create' in action_lower and entity_type:
        standardized_action = f"Created {entity_type}"
        if not actual_method:
            actual_method = Activity.METHOD_CREATE
    elif 'update' in action_lower and entity_type:
        standardized_action = f"Updated {entity_type}"
        if not actual_method:
            actual_method = Activity.METHOD_UPDATE
    elif 'delete' in action_lower and entity_type:
        standardized_action = f"Deleted {entity_type}"
        if not actual_method:
            actual_method = Activity.METHOD_DELETE
    elif 'view' in action_lower and entity_type:
        standardized_action = f"Viewed {entity_type}"
        if not actual_method:
            actual_method = Activity.METHOD_READ
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            start_time = time.time()

            try:
                # For update operations with change tracking, get the entity before changes
                old_values = None
                entity_obj = None

                # Check if this is an update operation that should track changes
                is_update_operation = 'update' in action_lower
                if track_changes and entity_type and is_update_operation:
                    # Get the entity object before changes
                    entity_id_param = None
                    entity_type_lower = entity_type.lower()

                    # Common pattern: entity_type_id (e.g., user_id, team_id)
                    entity_id_param_candidates = [
                        f"{entity_type_lower}_id",
                        'id',
                        'detail_id' if entity_type == 'EmployeeDetail' else None,
                        'unit_id' if entity_type == 'BusinessUnit' else None,
                        'segment_id' if entity_type == 'BusinessSegment' else None,
                        'team_id' if entity_type == 'Team' else None,
                        'group_id' if entity_type == 'TeamGroup' else None,
                        'user_id' if entity_type == 'User' else None,
                    ]

                    # Find the first valid parameter
                    for param in entity_id_param_candidates:
                        if param and param in kwargs:
                            entity_id_param = param
                            break

                    if entity_id_param:
                        entity_id = kwargs[entity_id_param]
                        # Import models dynamically to avoid circular imports
                        from app.models import User, BusinessUnit, BusinessSegment, EmployeeDetail, Team, TeamGroup

                        # Map entity types to model classes
                        entity_map = {
                            'User': User,
                            'BusinessUnit': BusinessUnit,
                            'BusinessSegment': BusinessSegment,
                            'EmployeeDetail': EmployeeDetail,
                            'Team': Team,
                            'TeamGroup': TeamGroup
                        }

                        # Get the entity object
                        if entity_type in entity_map:
                            entity_obj = entity_map[entity_type].query.get(entity_id)
                            if entity_obj and hasattr(entity_obj, 'to_dict'):
                                old_values = entity_obj.to_dict()

                # Execute the view function
                result = f(*args, **kwargs)

                # Get new values after the function execution
                new_values = None
                changed_fields = None
                if track_changes and entity_obj and old_values:
                    # Refresh the entity object to get updated values
                    db.session.refresh(entity_obj)
                    if hasattr(entity_obj, 'to_dict'):
                        new_values_full = entity_obj.to_dict()

                        # Compare old and new values to find only changed fields
                        changed_fields = {}
                        for key, new_value in new_values_full.items():
                            if key in old_values and old_values[key] != new_value:
                                changed_fields[key] = {
                                    'old': old_values[key],
                                    'new': new_value
                                }

                        # If we have changed fields, create focused old/new values dicts
                        if changed_fields:
                            old_values = {k: v['old'] for k, v in changed_fields.items()}
                            new_values = {k: v['new'] for k, v in changed_fields.items()}
                        else:
                            # No changes detected
                            old_values = None
                            new_values = None

                # Check if we should skip AJAX requests
                is_ajax = skip_ajax and request.args.get('ajax', False, type=bool)

                # Log the activity only if user is authenticated and not an AJAX request (if skip_ajax is True)
                if current_user.is_authenticated and not is_ajax:
                    # Calculate execution time
                    execution_time = time.time() - start_time

                    # Get client information
                    client_info = get_client_info()

                    # Determine entity_id if possible from kwargs
                    entity_id = None
                    if entity_type:
                        # Common pattern: entity_type_id (e.g., user_id, team_id)
                        entity_type_lower = entity_type.lower()
                        entity_id_param = f"{entity_type_lower}_id"

                        if entity_id_param in kwargs:
                            entity_id = kwargs[entity_id_param]
                        # Handle special cases and variations
                        elif entity_type == 'User' and 'user_id' in kwargs:
                            entity_id = kwargs['user_id']
                        elif entity_type == 'BusinessUnit' and 'unit_id' in kwargs:
                            entity_id = kwargs['unit_id']
                        elif entity_type == 'BusinessSegment' and 'segment_id' in kwargs:
                            entity_id = kwargs['segment_id']
                        elif entity_type == 'EmployeeDetail' and 'detail_id' in kwargs:
                            entity_id = kwargs['detail_id']
                        elif entity_type == 'Message' and 'message_id' in kwargs:
                            entity_id = kwargs['message_id']
                        elif entity_type == 'Team' and 'team_id' in kwargs:
                            entity_id = kwargs['team_id']
                        elif entity_type == 'TeamGroup' and 'group_id' in kwargs:
                            entity_id = kwargs['group_id']
                        elif entity_type == 'Activity' and 'activity_id' in kwargs:
                            entity_id = kwargs['activity_id']
                        elif entity_type == 'Setting' and 'setting_id' in kwargs:
                            entity_id = kwargs['setting_id']

                        # Try to extract from form data for POST/PUT requests
                        if entity_id is None and request.method in ['POST', 'PUT', 'PATCH']:
                            if request.form.get('id'):
                                entity_id = request.form.get('id')
                            elif request.form.get(entity_id_param):
                                entity_id = request.form.get(entity_id_param)

                    # Determine severity if not provided
                    actual_severity = severity
                    if not actual_severity:
                        # Try to infer severity from action description
                        # We already have action_lower from the outer scope
                        if any(word in action_lower for word in ['error', 'fail', 'invalid', 'denied', 'reject']):
                            actual_severity = Activity.SEVERITY_ERROR
                        elif any(word in action_lower for word in ['warn', 'caution', 'alert', 'attempt']):
                            actual_severity = Activity.SEVERITY_WARNING
                        else:
                            actual_severity = Activity.SEVERITY_INFO

                    # Determine category if not provided
                    actual_category = category
                    if not actual_category:
                        # Try to infer category from route function name or blueprint
                        route_name = f.__name__
                        if 'auth' in route_name or request.blueprint == 'auth':
                            actual_category = Activity.CATEGORY_AUTH
                        elif 'admin' in route_name or request.blueprint == 'admin':
                            actual_category = Activity.CATEGORY_ADMIN
                        elif 'user' in route_name or request.blueprint == 'main':
                            actual_category = Activity.CATEGORY_USER
                        else:
                            actual_category = Activity.CATEGORY_SYSTEM

                    # Build detailed information
                    details_parts = []

                    # Add execution time
                    details_parts.append(f"Execution time: {execution_time:.2f}s")

                    # We no longer include URL and Method in details since they're separate fields now

                    # Add form data for POST/PUT requests if include_request_details is True
                    if include_request_details and request.method in ['POST', 'PUT', 'PATCH'] and request.form:
                        # Special handling for activity log management
                        if 'manage_activities' in f.__name__:
                            action_type = request.form.get('action')
                            if action_type == 'delete_older_than':
                                days = request.form.get('days', '30')
                                details_parts.append(f"Action: Deleted logs older than {days} days")
                            elif action_type == 'delete_by_category':
                                category_value = request.form.get('category', '')
                                details_parts.append(f"Action: Deleted logs with category '{category_value}'")
                            elif action_type == 'delete_by_severity':
                                severity_value = request.form.get('severity', '')
                                details_parts.append(f"Action: Deleted logs with severity '{severity_value}'")
                            elif action_type == 'delete_all':
                                details_parts.append("Action: Deleted all activity logs")
                        else:
                            # Filter out sensitive information like passwords
                            safe_form = {k: '******' if 'password' in k.lower() else v
                                        for k, v in request.form.items()}
                            details_parts.append(f"Form data: {safe_form}")

                    # Add query parameters if present and include_request_details is True
                    if include_request_details and request.args:
                        details_parts.append(f"Query params: {dict(request.args)}")

                    # Join all parts with separator
                    details = ' | '.join(details_parts)

                    # Create activity log with enhanced information
                    try:
                        Activity.log(
                            user_id=current_user.id,
                            action=standardized_action,
                            entity_type=entity_type,
                            entity_id=entity_id,
                            ip_address=client_info['ip_address'],
                            user_agent=client_info.get('user_agent'),
                            url=request.url,  # Capture the full URL
                            details=details,
                            severity=actual_severity,
                            category=actual_category,
                            method=actual_method,
                            old_values=old_values if track_changes else None,
                            new_values=new_values if track_changes else None
                        )
                    except Exception as e:
                        current_app.logger.error(f"Failed to log activity: {str(e)}")

                return result

            except Exception as e:
                current_app.logger.error(f"Error in {f.__name__}: {str(e)}")

                # Log error activity if possible
                if current_user.is_authenticated:
                    try:
                        client_info = get_client_info()

                        # Make sure variables are defined
                        local_entity_id = None
                        local_action_lower = None

                        # Check if variables exist in the local scope
                        if 'entity_id' in locals():
                            local_entity_id = entity_id
                        if 'action_lower' in locals():
                            local_action_lower = action_lower

                        # Build detailed error information
                        error_details_parts = [
                            f"Error Type: {type(e).__name__}",
                            f"Error Message: {str(e)}"
                            # We no longer include URL and Method in details since they're separate fields now
                        ]

                        # Add traceback information if available
                        import traceback
                        tb = traceback.format_exc()
                        if tb and tb != 'NoneType: None\n':
                            # Format traceback for better readability
                            formatted_tb = '\n'.join(['    ' + line for line in tb.split('\n')])
                            error_details_parts.append(f"Traceback:\n{formatted_tb}")

                        # Add form data for POST/PUT requests (with sensitive info filtered)
                        if include_request_details and request.method in ['POST', 'PUT', 'PATCH'] and request.form:
                            safe_form = {k: '******' if 'password' in k.lower() else v
                                        for k, v in request.form.items()}
                            error_details_parts.append(f"Form data: {safe_form}")

                        # Join all parts with separator
                        error_details = ' | '.join(error_details_parts)

                        # Use Activity.log method for consistency and to handle old/new values columns
                        Activity.log(
                            user_id=current_user.id,
                            action=f"Error ({type(e).__name__}) in {standardized_action}",
                            entity_type=entity_type,  # Include entity type for errors too
                            entity_id=local_entity_id,  # Use the local variable we defined
                            details=error_details,
                            ip_address=client_info['ip_address'],
                            user_agent=client_info.get('user_agent'),
                            url=request.url,  # Capture the full URL
                            severity=Activity.SEVERITY_ERROR,
                            category=category or Activity.CATEGORY_SYSTEM,
                            method=actual_method if 'actual_method' in locals() else None,
                            # Include old_values if we have them (for errors during updates)
                            old_values='old_values' in locals() and old_values if track_changes else None,
                            # Don't include new_values for errors as they may be incomplete/invalid
                            new_values=None
                        )
                    except Exception as log_error:
                        db.session.rollback()
                        current_app.logger.error(f"Failed to log error activity: {str(log_error)}")

                # Re-raise the exception to be handled by the caller
                raise

        return decorated_function
    return decorator

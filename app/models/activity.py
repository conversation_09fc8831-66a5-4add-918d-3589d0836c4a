"""
Activity model for tracking user actions.
"""

from datetime import datetime
import json
from typing import Dict, Any, Optional, List

from app import db
from app.models import PH_TZ


class Activity(db.Model):
    """Activity log model for tracking user actions."""
    __tablename__ = 'activities'

    # Severity levels
    SEVERITY_INFO = 'info'
    SEVERITY_WARNING = 'warning'
    SEVERITY_ERROR = 'error'

    SEVERITY_CHOICES = [
        (SEVERITY_INFO, 'Info'),
        (SEVERITY_WARNING, 'Warning'),
        (SEVERITY_ERROR, 'Error')
    ]

    # Activity categories
    CATEGORY_AUTH = 'auth'
    CATEGORY_USER = 'user'
    CATEGORY_ADMIN = 'admin'
    CATEGORY_SYSTEM = 'system'
    CATEGORY_DATA = 'data'

    CATEGORY_CHOICES = [
        (CATEGORY_AUTH, 'Authentication'),
        (CATEGORY_USER, 'User Activity'),
        (CATEGORY_ADMIN, 'Administration'),
        (CATEGORY_SYSTEM, 'System'),
        (CATEGORY_DATA, 'Data Management')
    ]

    # CRUD Methods
    METHOD_CREATE = 'create'
    METHOD_READ = 'read'
    METHOD_UPDATE = 'update'
    METHOD_DELETE = 'delete'

    METHOD_CHOICES = [
        (METHOD_CREATE, 'Create'),
        (METHOD_READ, 'Read'),
        (METHOD_UPDATE, 'Update'),
        (METHOD_DELETE, 'Delete')
    ]

    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id', name='fk_activity_user_id_cascade', ondelete='CASCADE'), nullable=False)
    action = db.Column(db.String(255), nullable=False)
    entity_type = db.Column(db.String(50), nullable=True)  # User, BusinessUnit, etc.
    entity_id = db.Column(db.Integer, nullable=True)
    details = db.Column(db.Text, nullable=True)
    ip_address = db.Column(db.String(50), nullable=True)
    user_agent = db.Column(db.String(255), nullable=True)
    url = db.Column(db.String(500), nullable=True)  # URL where the activity occurred
    severity = db.Column(db.String(20), default=SEVERITY_INFO)
    category = db.Column(db.String(20), nullable=True)
    method = db.Column(db.String(20), nullable=True)  # CRUD method: create, read, update, delete
    old_values = db.Column(db.Text, nullable=True)  # JSON string of old values
    new_values = db.Column(db.Text, nullable=True)  # JSON string of new values
    created_at = db.Column(db.DateTime, default=lambda: datetime.now(PH_TZ), index=True)

    __table_args__ = (
        db.Index('idx_activity_user_created', user_id, created_at),
        db.Index('idx_activity_entity', entity_type, entity_id),
        db.Index('idx_activity_category', category),
        db.Index('idx_activity_severity', severity),
    )

    def __repr__(self) -> str:
        return f'<Activity {self.action}>'

    @classmethod
    def log(cls, user_id: int, action: str, entity_type: Optional[str] = None,
            entity_id: Optional[int] = None, details: Optional[str] = None,
            ip_address: Optional[str] = None, user_agent: Optional[str] = None, url: Optional[str] = None,
            severity: Optional[str] = None, category: Optional[str] = None, method: Optional[str] = None,
            old_values: Optional[Dict[str, Any]] = None, new_values: Optional[Dict[str, Any]] = None) -> 'Activity':
        """Create and save a new activity log entry."""
        # Determine severity if not provided
        actual_severity = severity
        if not actual_severity:
            # Try to infer severity from action description
            action_lower = action.lower()
            if any(word in action_lower for word in ['error', 'fail', 'invalid', 'denied', 'reject', 'exception']):
                actual_severity = cls.SEVERITY_ERROR
            elif any(word in action_lower for word in ['warn', 'caution', 'alert', 'attempt', 'suspicious']):
                actual_severity = cls.SEVERITY_WARNING
            else:
                actual_severity = cls.SEVERITY_INFO

        # Determine category if not provided
        actual_category = category
        if not actual_category:
            # Try to infer category from action and entity type
            action_lower = action.lower()

            # Authentication related activities
            if any(word in action_lower for word in ['login', 'logout', 'password', 'auth', 'credential', 'session']):
                actual_category = cls.CATEGORY_AUTH
            # Admin activities
            elif any(word in action_lower for word in ['admin', 'configuration', 'setting', 'system']):
                actual_category = cls.CATEGORY_ADMIN
            # Data management activities
            elif any(word in action_lower for word in ['create', 'update', 'delete', 'edit', 'import', 'export']):
                actual_category = cls.CATEGORY_DATA
            # User activities
            elif any(word in action_lower for word in ['view', 'access', 'profile', 'dashboard']):
                actual_category = cls.CATEGORY_USER
            # Default to system
            else:
                actual_category = cls.CATEGORY_SYSTEM

        # Determine method if not provided
        actual_method = method
        if not actual_method:
            # Try to infer method from action description
            action_lower = action.lower()
            if 'create' in action_lower or 'add' in action_lower or 'new' in action_lower:
                actual_method = cls.METHOD_CREATE
            elif 'update' in action_lower or 'edit' in action_lower or 'modify' in action_lower or 'change' in action_lower:
                actual_method = cls.METHOD_UPDATE
            elif 'delete' in action_lower or 'remove' in action_lower:
                actual_method = cls.METHOD_DELETE
            elif 'view' in action_lower or 'read' in action_lower or 'get' in action_lower or 'list' in action_lower or 'search' in action_lower:
                actual_method = cls.METHOD_READ

        # Convert old and new values to JSON strings if provided
        old_values_json = json.dumps(old_values) if old_values else None
        new_values_json = json.dumps(new_values) if new_values else None

        # Create activity with basic fields
        activity_data = {
            'user_id': user_id,
            'action': action,
            'entity_type': entity_type,
            'entity_id': entity_id,
            'details': details,
            'ip_address': ip_address,
            'user_agent': user_agent,
            'url': url,
            'severity': actual_severity,
            'category': actual_category,
            'method': actual_method
        }

        # Try to add old_values and new_values if the columns exist
        try:
            activity_data['old_values'] = old_values_json
            activity_data['new_values'] = new_values_json
            activity = cls(**activity_data)
        except Exception as e:
            # If the columns don't exist yet, create without them
            if 'no column named' in str(e).lower() and ('old_values' in str(e).lower() or 'new_values' in str(e).lower()):
                # Remove the problematic fields
                if 'old_values' in activity_data:
                    del activity_data['old_values']
                if 'new_values' in activity_data:
                    del activity_data['new_values']
                activity = cls(**activity_data)

                # If old/new values were provided, add them to details instead
                if old_values or new_values:
                    additional_details = []
                    if old_values:
                        additional_details.append(f"Old values: {json.dumps(old_values)}")
                    if new_values:
                        additional_details.append(f"New values: {json.dumps(new_values)}")

                    if additional_details and activity.details:
                        activity.details += " | " + " | ".join(additional_details)
                    elif additional_details:
                        activity.details = " | ".join(additional_details)
            else:
                # Re-raise if it's a different error
                raise
        db.session.add(activity)
        db.session.commit()
        return activity

    def get_entity_url(self) -> Optional[str]:
        """Get URL to the related entity if applicable."""
        from flask import url_for

        if not self.entity_type or not self.entity_id:
            return None

        try:
            if self.entity_type == 'User':
                return url_for('admin.get_user', user_id=self.entity_id)
            elif self.entity_type == 'BusinessUnit':
                return url_for('admin.get_business_unit', unit_id=self.entity_id)
            elif self.entity_type == 'BusinessSegment':
                return url_for('admin.get_business_segment', segment_id=self.entity_id)
            elif self.entity_type == 'EmployeeDetail':
                return url_for('admin.get_employee_detail', detail_id=self.entity_id)
            elif self.entity_type == 'Message':
                # Assuming there's a message view route
                return None
        except:
            return None

        return None

    def get_severity_badge_class(self) -> str:
        """Get the appropriate badge class for the severity level."""
        severity_map = {
            self.SEVERITY_INFO: 'badge-info',
            self.SEVERITY_WARNING: 'badge-warning',
            self.SEVERITY_ERROR: 'badge-destructive',
        }
        return severity_map.get(self.severity, 'badge-secondary')

    def get_category_display(self) -> str:
        """Get a human-readable display for the category."""
        category_map = dict(self.CATEGORY_CHOICES)
        return category_map.get(self.category, self.category.title() if self.category else 'Uncategorized')

    def get_method_display(self) -> str:
        """Get a human-readable display for the method."""
        method_map = dict(self.METHOD_CHOICES)
        return method_map.get(self.method, self.method.title() if self.method else 'Unknown')

    def to_dict(self) -> Dict[str, Any]:
        """Serialize activity to dictionary."""
        # Parse old and new values from JSON if they exist
        old_values = None
        new_values = None
        has_changes = False

        # Try to access old_values and new_values attributes
        try:
            if hasattr(self, 'old_values') and self.old_values:
                old_values = json.loads(self.old_values)
            if hasattr(self, 'new_values') and self.new_values:
                new_values = json.loads(self.new_values)
            has_changes = bool(old_values and new_values)
        except Exception:
            # If there's an error (e.g., columns don't exist), just continue without them
            pass

        # Check if old/new values are in details as a fallback
        if not has_changes and self.details:
            # Try to extract from details
            if 'Old values:' in self.details and 'New values:' in self.details:
                try:
                    # Extract old values
                    old_start = self.details.find('Old values:') + len('Old values:')
                    old_end = self.details.find('New values:') if 'New values:' in self.details else len(self.details)
                    old_json = self.details[old_start:old_end].strip()
                    if old_json.endswith('|'):
                        old_json = old_json[:-1].strip()
                    old_values = json.loads(old_json)

                    # Extract new values
                    new_start = self.details.find('New values:') + len('New values:')
                    new_json = self.details[new_start:].strip()
                    if '|' in new_json:
                        new_json = new_json.split('|')[0].strip()
                    new_values = json.loads(new_json)

                    has_changes = bool(old_values and new_values)
                except Exception:
                    # If parsing fails, just continue without them
                    pass

        return {
            'id': self.id,
            'user_id': self.user_id,
            'user_name': self.user.name if self.user else 'Unknown',
            'action': self.action,
            'entity_type': self.entity_type,
            'entity_id': self.entity_id,
            'details': self.details,
            'ip_address': self.ip_address,
            'user_agent': self.user_agent,
            'url': self.url,
            'severity': self.severity,
            'category': self.category,
            'method': self.method,
            'old_values': old_values,
            'new_values': new_values,
            'created_at': self.created_at.isoformat(),
            'entity_url': self.get_entity_url(),
            'severity_badge_class': self.get_severity_badge_class(),
            'category_display': self.get_category_display(),
            'method_display': self.get_method_display(),
            'has_changes': has_changes
        }

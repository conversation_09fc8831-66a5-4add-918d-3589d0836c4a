import unittest
from datetime import datetime, date
from flask_testing import TestCase

from app import create_app, db
from app.models import User, EmployeeDetail, BusinessUnit, BusinessSegment


class TestBusinessUnits(TestCase):
    """Test cases for BusinessUnit model and related functionality"""

    def create_app(self):
        """Create and configure a Flask app for testing"""
        app = create_app()
        app.config.update(
            TESTING=True,
            SQLALCHEMY_DATABASE_URI='sqlite:///:memory:',
            WTF_CSRF_ENABLED=False,
            SERVER_NAME='localhost.localdomain',
            SECRET_KEY='test_secret_key'
        )
        with app.app_context():
            db.create_all()
        return app

    def setUp(self):
        """Set up test environment before each test"""
        db.session.remove()
        db.drop_all()
        db.create_all()

        # Create test users
        self.admin_user = User(
            name='Admin User',
            email='<EMAIL>',
            role='Admin'
        )
        self.admin_user.set_password('Admin@123')

        self.manager_user = User(
            name='Manager User',
            email='<EMAIL>',
            role='Manager'
        )
        self.manager_user.set_password('Manager@123')

        self.regular_user = User(
            name='Regular User',
            email='<EMAIL>',
            role='User'
        )
        self.regular_user.set_password('User@123')

        db.session.add_all([self.admin_user, self.manager_user, self.regular_user])
        db.session.commit()

    def tearDown(self):
        """Clean up test environment after each test"""
        db.session.remove()
        db.drop_all()

    def test_business_unit_creation(self):
        """Test basic business unit creation and retrieval"""
        # Create a business unit
        business_unit = BusinessUnit(
            name='Engineering',
            code='ENG',
            description='Engineering Department',
            manager_id=self.manager_user.id,
            is_active=True
        )
        db.session.add(business_unit)
        db.session.commit()

        # Retrieve the business unit and check attributes
        saved_unit = BusinessUnit.query.filter_by(code='ENG').first()
        self.assertIsNotNone(saved_unit)
        self.assertEqual(saved_unit.name, 'Engineering')
        self.assertEqual(saved_unit.description, 'Engineering Department')
        self.assertTrue(saved_unit.is_active)
        self.assertEqual(saved_unit.manager_id, self.manager_user.id)

        # Test relationship with manager
        self.assertEqual(saved_unit.manager.name, 'Manager User')

    def test_business_unit_code_uniqueness(self):
        """Test business unit code uniqueness constraint"""
        # Create first business unit
        business_unit1 = BusinessUnit(
            name='Engineering',
            code='ENG',
            description='Engineering Department'
        )
        db.session.add(business_unit1)
        db.session.commit()

        # Try to create another business unit with the same code
        business_unit2 = BusinessUnit(
            name='Another Engineering',
            code='ENG',  # Same code
            description='Another Engineering Department'
        )
        db.session.add(business_unit2)

        # This should raise an IntegrityError due to unique constraint
        from sqlalchemy.exc import IntegrityError
        with self.assertRaises(IntegrityError):
            db.session.commit()

        # Rollback the failed transaction
        db.session.rollback()

    def test_get_active_segments(self):
        """Test retrieving active segments from a business unit"""
        # Create a business unit
        business_unit = BusinessUnit(
            name='Engineering',
            code='ENG',
            description='Engineering Department',
            manager_id=self.manager_user.id,
            is_active=True
        )
        db.session.add(business_unit)
        db.session.commit()

        # Create active and inactive segments
        active_segment1 = BusinessSegment(
            name='Software Development',
            code='SD',
            description='Software Development Team',
            business_unit_id=business_unit.id,
            is_active=True
        )

        active_segment2 = BusinessSegment(
            name='Quality Assurance',
            code='QA',
            description='Quality Assurance Team',
            business_unit_id=business_unit.id,
            is_active=True
        )

        inactive_segment = BusinessSegment(
            name='Legacy Systems',
            code='LS',
            description='Legacy Systems Team',
            business_unit_id=business_unit.id,
            is_active=False
        )

        db.session.add_all([active_segment1, active_segment2, inactive_segment])
        db.session.commit()

        # Test get_active_segments method
        active_segments = business_unit.get_active_segments()
        self.assertEqual(len(active_segments), 2)

        # Verify only active segments are returned
        segment_names = [segment.name for segment in active_segments]
        self.assertIn('Software Development', segment_names)
        self.assertIn('Quality Assurance', segment_names)
        self.assertNotIn('Legacy Systems', segment_names)

    def test_get_employee_count(self):
        """Test getting employee count for a business unit"""
        # Create a business unit
        business_unit = BusinessUnit(
            name='Engineering',
            code='ENG',
            description='Engineering Department'
        )
        db.session.add(business_unit)
        db.session.commit()

        # Initially should have 0 employees
        self.assertEqual(business_unit.get_employee_count(), 0)

        # Create employee details with references to the business unit
        employee_detail1 = EmployeeDetail(
            user_id=self.regular_user.id,
            employee_number='EMP001',
            position='Software Engineer',
            business_unit_id=business_unit.id
        )

        employee_detail2 = EmployeeDetail(
            user_id=self.manager_user.id,
            employee_number='EMP002',
            position='Engineering Manager',
            business_unit_id=business_unit.id
        )

        db.session.add_all([employee_detail1, employee_detail2])
        db.session.commit()

        # Now should have 2 employees
        self.assertEqual(business_unit.get_employee_count(), 2)

    def test_to_dict_method(self):
        """Test business unit serialization to dictionary"""
        # Create a business unit with a manager
        business_unit = BusinessUnit(
            name='Engineering',
            code='ENG',
            description='Engineering Department',
            manager_id=self.manager_user.id,
            is_active=True
        )
        db.session.add(business_unit)
        db.session.commit()

        # Create a segment for this business unit
        segment = BusinessSegment(
            name='Software Development',
            code='SD',
            description='Software Development Team',
            business_unit_id=business_unit.id,
            is_active=True
        )
        db.session.add(segment)
        db.session.commit()

        # Test basic serialization (without segments)
        unit_dict = business_unit.to_dict(include_segments=False)
        self.assertEqual(unit_dict['name'], 'Engineering')
        self.assertEqual(unit_dict['code'], 'ENG')
        self.assertEqual(unit_dict['description'], 'Engineering Department')
        self.assertTrue(unit_dict['is_active'])
        self.assertEqual(unit_dict['manager_id'], self.manager_user.id)
        self.assertEqual(unit_dict['manager_name'], 'Manager User')
        self.assertEqual(unit_dict['employee_count'], 0)
        self.assertNotIn('segments', unit_dict)

        # Test serialization with segments
        unit_dict_with_segments = business_unit.to_dict(include_segments=True)
        self.assertIn('segments', unit_dict_with_segments)
        self.assertEqual(len(unit_dict_with_segments['segments']), 1)
        self.assertEqual(unit_dict_with_segments['segments'][0]['name'], 'Software Development')
        self.assertEqual(unit_dict_with_segments['segments'][0]['code'], 'SD')

    def test_cascade_delete_segments(self):
        """Test cascading delete of segments when a business unit is deleted"""
        # Create a business unit
        business_unit = BusinessUnit(
            name='Engineering',
            code='ENG',
            description='Engineering Department'
        )
        db.session.add(business_unit)
        db.session.commit()

        # Create segments for this business unit
        segment1 = BusinessSegment(
            name='Software Development',
            code='SD',
            description='Software Development Team',
            business_unit_id=business_unit.id
        )

        segment2 = BusinessSegment(
            name='Quality Assurance',
            code='QA',
            description='Quality Assurance Team',
            business_unit_id=business_unit.id
        )

        db.session.add_all([segment1, segment2])
        db.session.commit()

        # Verify segments exist
        segments = BusinessSegment.query.filter_by(business_unit_id=business_unit.id).all()
        self.assertEqual(len(segments), 2)

        # Delete the business unit
        db.session.delete(business_unit)
        db.session.commit()

        # Verify segments are also deleted (CASCADE)
        segments = BusinessSegment.query.filter_by(business_unit_id=business_unit.id).all()
        self.assertEqual(len(segments), 0)

    def test_admin_business_unit_route_access(self):
        """Test admin access to business unit routes"""
        with self.client:
            # Login as admin
            self.client.post(
                '/auth/login',
                data={
                    'email': '<EMAIL>',
                    'password': 'Admin@123'
                }
            )

            # Admin should have access to business units page
            response = self.client.get('/admin/business-units', follow_redirects=True)
            self.assertEqual(response.status_code, 200)
            self.assertIn(b'Business Units', response.data)

    def test_business_unit_manager_assignment(self):
        """Test assigning a manager to a business unit"""
        # Create a business unit without a manager
        business_unit = BusinessUnit(
            name='Engineering',
            code='ENG',
            description='Engineering Department',
            is_active=True
        )
        db.session.add(business_unit)
        db.session.commit()

        # Verify no manager is assigned
        self.assertIsNone(business_unit.manager_id)

        # Assign a manager
        business_unit.manager_id = self.manager_user.id
        db.session.commit()

        # Verify manager is assigned
        updated_unit = BusinessUnit.query.get(business_unit.id)
        self.assertEqual(updated_unit.manager_id, self.manager_user.id)
        self.assertEqual(updated_unit.manager.name, 'Manager User')

        # Verify from the user side
        self.assertIn(business_unit, self.manager_user.managed_units)


if __name__ == '__main__':
    unittest.main()

/**
 * DOM Optimizer Utility
 * 
 * This module provides utilities for optimizing DOM operations.
 */

// Use a WeakMap to store element references without memory leaks
const elementCache = new WeakMap();

/**
 * Batch DOM operations for better performance
 * 
 * @param {Function} callback - Function containing DOM operations to batch
 */
function batchDOMOperations(callback) {
  // Use requestAnimationFrame to batch DOM operations
  requestAnimationFrame(() => {
    // Create a document fragment for batch operations
    const fragment = document.createDocumentFragment();
    
    // Execute callback with fragment
    callback(fragment);
    
    // Apply changes in a single reflow/repaint
    document.body.appendChild(fragment);
  });
}

/**
 * Throttle DOM updates to prevent excessive reflows
 * 
 * @param {Function} callback - Function to throttle
 * @param {number} delay - Throttle delay in milliseconds
 * @returns {Function} - Throttled function
 */
function throttleDOMUpdates(callback, delay = 100) {
  let lastCall = 0;
  let timeout = null;
  
  return function(...args) {
    const now = Date.now();
    const remaining = delay - (now - lastCall);
    
    if (remaining <= 0) {
      // Execute immediately
      lastCall = now;
      callback.apply(this, args);
    } else if (!timeout) {
      // Schedule execution
      timeout = setTimeout(() => {
        lastCall = Date.now();
        timeout = null;
        callback.apply(this, args);
      }, remaining);
    }
  };
}

/**
 * Efficiently query and cache DOM elements
 * 
 * @param {string} selector - CSS selector
 * @param {Element} context - Context element (default: document)
 * @returns {Element} - Found element
 */
function getElement(selector, context = document) {
  // Use context as cache key
  if (!elementCache.has(context)) {
    elementCache.set(context, new Map());
  }
  
  const cache = elementCache.get(context);
  
  // Return from cache if available
  if (cache.has(selector)) {
    return cache.get(selector);
  }
  
  // Query and cache element
  const element = context.querySelector(selector);
  if (element) {
    cache.set(selector, element);
  }
  
  return element;
}

/**
 * Efficiently query and cache multiple DOM elements
 * 
 * @param {string} selector - CSS selector
 * @param {Element} context - Context element (default: document)
 * @returns {Array<Element>} - Array of found elements
 */
function getElements(selector, context = document) {
  // Use a different cache key for multiple elements
  const cacheKey = `__multiple__${selector}`;
  
  // Use context as cache key
  if (!elementCache.has(context)) {
    elementCache.set(context, new Map());
  }
  
  const cache = elementCache.get(context);
  
  // Return from cache if available
  if (cache.has(cacheKey)) {
    return cache.get(cacheKey);
  }
  
  // Query and cache elements
  const elements = Array.from(context.querySelectorAll(selector));
  cache.set(cacheKey, elements);
  
  return elements;
}

/**
 * Clear the element cache for a specific context
 * 
 * @param {Element} context - Context to clear cache for (default: all contexts)
 */
function clearElementCache(context = null) {
  if (context) {
    elementCache.delete(context);
  } else {
    // Clear all caches (less efficient)
    elementCache.clear();
  }
}

/**
 * Create and append multiple elements efficiently
 * 
 * @param {Array<Object>} elements - Array of element configs
 * @param {Element} parent - Parent element to append to
 */
function createElements(elements, parent) {
  // Use document fragment for batch operations
  const fragment = document.createDocumentFragment();
  
  // Create all elements
  elements.forEach(config => {
    const element = document.createElement(config.tag);
    
    // Set attributes
    if (config.attributes) {
      Object.entries(config.attributes).forEach(([key, value]) => {
        element.setAttribute(key, value);
      });
    }
    
    // Set properties
    if (config.properties) {
      Object.entries(config.properties).forEach(([key, value]) => {
        element[key] = value;
      });
    }
    
    // Set styles
    if (config.styles) {
      Object.entries(config.styles).forEach(([key, value]) => {
        element.style[key] = value;
      });
    }
    
    // Set content
    if (config.textContent) {
      element.textContent = config.textContent;
    } else if (config.innerHTML) {
      element.innerHTML = config.innerHTML;
    }
    
    // Add event listeners
    if (config.events) {
      Object.entries(config.events).forEach(([event, handler]) => {
        element.addEventListener(event, handler);
      });
    }
    
    // Append to fragment
    fragment.appendChild(element);
  });
  
  // Append fragment to parent in a single operation
  parent.appendChild(fragment);
}

/**
 * Optimize event delegation for better performance
 * 
 * @param {Element} container - Container element
 * @param {string} selector - CSS selector for target elements
 * @param {string} eventType - Event type to listen for
 * @param {Function} handler - Event handler
 * @returns {Function} - Function to remove the event listener
 */
function delegateEvent(container, selector, eventType, handler) {
  const listener = (event) => {
    // Find matching target
    const target = event.target.closest(selector);
    
    // Call handler if target matches
    if (target && container.contains(target)) {
      handler.call(target, event, target);
    }
  };
  
  // Add event listener to container
  container.addEventListener(eventType, listener);
  
  // Return function to remove listener
  return () => {
    container.removeEventListener(eventType, listener);
  };
}

// Export public API
export {
  batchDOMOperations,
  throttleDOMUpdates,
  getElement,
  getElements,
  clearElementCache,
  createElements,
  delegateEvent
};

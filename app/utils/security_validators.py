"""
Security validation utilities for API input sanitization and validation.
"""

import re
from typing import Any, List, Optional
from flask import request
from app.routes.api.activities import APIError


class SecurityValidator:
    """Utility class for common security validations."""

    # Common validation patterns
    ALPHANUMERIC_PATTERN = re.compile(r'^[a-zA-Z0-9_-]+$')
    EMAIL_PATTERN = re.compile(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$')

    # Allowed values for common fields
    ALLOWED_ROLES = ['Admin', 'Manager', 'User']
    ALLOWED_ENTITY_TYPES = ['User', 'BusinessUnit', 'BusinessSegment', 'EmployeeDetail', 'Team', 'Setting']
    ALLOWED_BOOLEAN_STRINGS = ['true', 'false']

    @staticmethod
    def validate_positive_integer(value: Any, field_name: str = 'field') -> Optional[int]:
        """
        Validate that a value is a positive integer.

        Args:
            value: The value to validate
            field_name: Name of the field for error messages

        Returns:
            The validated integer or None if invalid

        Raises:
            APIError: If value is not a positive integer
        """
        if value is None:
            return None

        try:
            int_value = int(value)
            if int_value <= 0:
                raise APIError(f'{field_name} must be a positive integer', status_code=400)
            return int_value
        except (ValueError, TypeError):
            raise APIError(f'{field_name} must be a valid integer', status_code=400)

    @staticmethod
    def validate_string_length(value: Any, max_length: int, field_name: str = 'field') -> Optional[str]:
        """
        Validate string length and type.

        Args:
            value: The value to validate
            max_length: Maximum allowed length
            field_name: Name of the field for error messages

        Returns:
            The validated string or None if None provided

        Raises:
            APIError: If value is not a string or exceeds max length
        """
        if value is None:
            return None

        if not isinstance(value, str):
            raise APIError(f'{field_name} must be a string', status_code=400)

        if len(value) > max_length:
            raise APIError(f'{field_name} too long (max {max_length} characters)', status_code=400)

        return value.strip()

    @staticmethod
    def validate_alphanumeric_key(value: Any, max_length: int = 100, field_name: str = 'key') -> Optional[str]:
        """
        Validate that a key contains only alphanumeric characters, underscores, and dashes.

        Args:
            value: The value to validate
            max_length: Maximum allowed length
            field_name: Name of the field for error messages

        Returns:
            The validated key or None if None provided

        Raises:
            APIError: If key format is invalid
        """
        if value is None:
            return None

        if not isinstance(value, str):
            raise APIError(f'{field_name} must be a string', status_code=400)

        if len(value) > max_length:
            raise APIError(f'{field_name} too long (max {max_length} characters)', status_code=400)

        if not SecurityValidator.ALPHANUMERIC_PATTERN.match(value):
            raise APIError(f'Invalid {field_name} format. Only alphanumeric, underscore, and dash allowed', status_code=400)

        return value

    @staticmethod
    def validate_enum_value(value: Any, allowed_values: List[str], field_name: str = 'field') -> Optional[str]:
        """
        Validate that a value is in the allowed list.

        Args:
            value: The value to validate
            allowed_values: List of allowed values
            field_name: Name of the field for error messages

        Returns:
            The validated value or None if None provided

        Raises:
            APIError: If value is not in allowed list
        """
        if value is None:
            return None

        if not isinstance(value, str):
            raise APIError(f'{field_name} must be a string', status_code=400)

        if value not in allowed_values:
            raise APIError(f'Invalid {field_name}. Allowed values: {", ".join(allowed_values)}', status_code=400)

        return value

    @staticmethod
    def validate_boolean_string(value: Any, field_name: str = 'field') -> Optional[bool]:
        """
        Validate and convert a string boolean value.

        Args:
            value: The value to validate ('true' or 'false')
            field_name: Name of the field for error messages

        Returns:
            The boolean value or None if None provided

        Raises:
            APIError: If value is not a valid boolean string
        """
        if value is None:
            return None

        if not isinstance(value, str):
            raise APIError(f'{field_name} must be a string', status_code=400)

        if value.lower() not in SecurityValidator.ALLOWED_BOOLEAN_STRINGS:
            raise APIError(f'{field_name} must be "true" or "false"', status_code=400)

        return value.lower() == 'true'

    @staticmethod
    def validate_email(value: Any, field_name: str = 'email') -> Optional[str]:
        """
        Validate email format.

        Args:
            value: The email to validate
            field_name: Name of the field for error messages

        Returns:
            The validated email or None if None provided

        Raises:
            APIError: If email format is invalid
        """
        if value is None:
            return None

        if not isinstance(value, str):
            raise APIError(f'{field_name} must be a string', status_code=400)

        if not SecurityValidator.EMAIL_PATTERN.match(value):
            raise APIError(f'Invalid {field_name} format', status_code=400)

        return value.lower().strip()

    @staticmethod
    def sanitize_search_term(value: Any, max_length: int = 100) -> Optional[str]:
        """
        Sanitize a search term by removing potentially dangerous characters.

        Args:
            value: The search term to sanitize
            max_length: Maximum allowed length

        Returns:
            The sanitized search term or None if None provided
        """
        if value is None:
            return None

        if not isinstance(value, str):
            return None

        # Remove potentially dangerous characters
        sanitized = re.sub(r'[<>"\';\\]', '', str(value))

        # Limit length
        if len(sanitized) > max_length:
            sanitized = sanitized[:max_length]

        return sanitized.strip() if sanitized.strip() else None


class APIParameterValidator:
    """Helper class for validating common API parameters."""

    @staticmethod
    def validate_pagination_params(page: Any = None, per_page: Any = None) -> tuple[int, int]:
        """
        Validate pagination parameters.

        Args:
            page: Page number
            per_page: Items per page

        Returns:
            Tuple of (page, per_page) with validated values
        """
        # Validate page
        if page is None:
            page = 1
        else:
            page = SecurityValidator.validate_positive_integer(page, 'page')
            if page is None:
                page = 1

        # Validate per_page
        if per_page is None:
            per_page = 20
        else:
            per_page = SecurityValidator.validate_positive_integer(per_page, 'per_page')
            if per_page is None:
                per_page = 20
            elif per_page > 100:  # Limit to prevent abuse
                per_page = 100

        return page, per_page

    @staticmethod
    def validate_filter_params(
        role: Any = None,
        is_active: Any = None,
        entity_type: Any = None,
        entity_id: Any = None,
        business_unit_id: Any = None,
        business_segment_id: Any = None
    ) -> dict:
        """
        Validate common filter parameters.

        Returns:
            Dictionary of validated filter parameters
        """
        filters = {}

        if role is not None:
            filters['role'] = SecurityValidator.validate_enum_value(
                role, SecurityValidator.ALLOWED_ROLES, 'role'
            )

        if is_active is not None:
            filters['is_active'] = SecurityValidator.validate_boolean_string(is_active, 'is_active')

        if entity_type is not None:
            filters['entity_type'] = SecurityValidator.validate_enum_value(
                entity_type, SecurityValidator.ALLOWED_ENTITY_TYPES, 'entity_type'
            )

        if entity_id is not None:
            filters['entity_id'] = SecurityValidator.validate_positive_integer(entity_id, 'entity_id')

        if business_unit_id is not None:
            filters['business_unit_id'] = SecurityValidator.validate_positive_integer(
                business_unit_id, 'business_unit_id'
            )

        if business_segment_id is not None:
            filters['business_segment_id'] = SecurityValidator.validate_positive_integer(
                business_segment_id, 'business_segment_id'
            )

        # Remove None values
        return {k: v for k, v in filters.items() if v is not None}


def validate_json_input(required_fields: Optional[List[str]] = None, optional_fields: Optional[List[str]] = None) -> dict:
    """
    Validate JSON input from request.

    Args:
        required_fields: List of required field names
        optional_fields: List of optional field names

    Returns:
        Validated JSON data

    Raises:
        APIError: If validation fails
    """
    if required_fields is None:
        required_fields = []
    if optional_fields is None:
        optional_fields = []

    try:
        data = request.get_json(force=True)
    except Exception:
        raise APIError('Invalid JSON data', status_code=400)

    if not data:
        raise APIError('No data provided', status_code=400)

    # Check required fields
    for field in required_fields:
        if field not in data:
            raise APIError(f'Required field missing: {field}', status_code=400)

    # Check for unexpected fields
    allowed_fields = set(required_fields + optional_fields)
    provided_fields = set(data.keys())
    unexpected_fields = provided_fields - allowed_fields

    if unexpected_fields:
        raise APIError(f'Unexpected fields: {", ".join(unexpected_fields)}', status_code=400)

    return data

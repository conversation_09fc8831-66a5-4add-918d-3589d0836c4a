/**
 * Toast Notification Styles
 * Inspired by shadcn/ui toast component
 */

/* Toast viewport - container for all toasts */
.toast-container {
  position: fixed;
  bottom: 1.5rem;
  right: 1.5rem;
  z-index: 9999;
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  max-width: calc(100% - 3rem);
  width: 380px; /* Optimal width for toast notifications */
  pointer-events: none; /* Allow clicking through the container */
}

/* Base toast styles */
.toast {
  display: flex;
  align-items: flex-start; /* Align to top for better title/description layout */
  pointer-events: auto; /* Make the toast itself clickable */
  background-color: white;
  color: #111827;
  border-radius: 0.75rem; /* Slightly more rounded corners */
  padding: 1rem 1.25rem; /* More consistent padding */
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  border: 1px solid rgba(0, 0, 0, 0.05);
  overflow: hidden;
  position: relative;
  width: 100%;

  /* Entrance animation */
  animation: toast-enter 0.2s cubic-bezier(0.16, 1, 0.3, 1);
}

/* Dark mode toast */
.dark .toast {
  background-color: #1e1e2e; /* Darker background for dark mode */
  color: #e2e8f0;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Toast closing state */
.toast[data-state="closing"] {
  animation: toast-exit 0.3s cubic-bezier(0.16, 1, 0.3, 1) forwards;
  pointer-events: none; /* Prevent interaction during exit animation */
}

/* Toast animations */
@keyframes toast-enter {
  from {
    transform: translateY(1rem);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes toast-exit {
  0% {
    transform: translateY(0);
    opacity: 1;
  }
  100% {
    transform: translateY(-1rem);
    opacity: 0;
  }
}

/* Toast icon */
.toast-icon {
  flex-shrink: 0;
  width: 1.5rem;
  height: 1.5rem;
  margin-right: 1rem;
  margin-top: 0.125rem; /* Align with title */
}

/* Toast content */
.toast-content {
  flex-grow: 1;
  margin-right: 1.5rem; /* Space for close button */
  display: flex;
  flex-direction: column;
}

/* Toast title */
.toast-title {
  font-size: 0.9375rem;
  font-weight: 600;
  margin-bottom: 0.25rem;
  line-height: 1.3;
}

/* Toast description */
.toast-description {
  font-size: 0.875rem;
  line-height: 1.4;
  font-weight: 400;
  opacity: 0.9;
}

/* Toast close button */
.toast-close {
  position: absolute;
  top: 0.75rem;
  right: 0.75rem;
  width: 1.5rem;
  height: 1.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 0.375rem;
  background: transparent;
  border: none;
  cursor: pointer;
  opacity: 0.7;
  transition: all 150ms cubic-bezier(0.4, 0, 0.2, 1);
  padding: 0;
  color: currentColor;
  z-index: 10; /* Ensure it's above other elements */
}

.toast-close:hover {
  opacity: 1;
  background-color: rgba(0, 0, 0, 0.05);
}

.toast-close:focus-visible {
  outline: 2px solid currentColor;
  outline-offset: 1px;
}

.dark .toast-close:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.toast-close:active {
  transform: scale(0.95);
}

/* Toast progress bar */
.toast-progress {
  position: absolute;
  bottom: 0;
  left: 0;
  height: 3px;
  background-color: rgba(0, 0, 0, 0.1);
  transition: width linear;
}

.dark .toast-progress {
  background-color: rgba(255, 255, 255, 0.2);
}

/* Toast variants */

/* Success toast */
.toast-success {
  border-left: 4px solid #10b981;
}

.toast-success::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  background-color: #10b981;
  border-top-left-radius: 0.5rem;
  border-bottom-left-radius: 0.5rem;
}

.toast-success .toast-progress {
  background-color: rgba(16, 185, 129, 0.3);
}

/* Error toast */
.toast-error {
  border-left: 4px solid #ef4444;
}

.toast-error::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  background-color: #ef4444;
  border-top-left-radius: 0.5rem;
  border-bottom-left-radius: 0.5rem;
}

.toast-error .toast-progress {
  background-color: rgba(239, 68, 68, 0.3);
}

/* Warning toast */
.toast-warning {
  border-left: 4px solid #f59e0b;
}

.toast-warning::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  background-color: #f59e0b;
  border-top-left-radius: 0.5rem;
  border-bottom-left-radius: 0.5rem;
}

.toast-warning .toast-progress {
  background-color: rgba(245, 158, 11, 0.3);
}

/* Info toast */
.toast-info {
  border-left: 4px solid #3b82f6;
}

.toast-info::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  background-color: #3b82f6;
  border-top-left-radius: 0.5rem;
  border-bottom-left-radius: 0.5rem;
}

.toast-info .toast-progress {
  background-color: rgba(59, 130, 246, 0.3);
}

/* Toast icons colors */
.toast-success .toast-icon {
  color: #10b981;
}

.toast-error .toast-icon {
  color: #ef4444;
}

.toast-warning .toast-icon {
  color: #f59e0b;
}

.toast-info .toast-icon {
  color: #3b82f6;
}

"""
Admin settings management routes.
"""

from flask import render_template, redirect, url_for, request, current_app
from flask_login import login_required, current_user
from datetime import datetime, timedelta

from app import db
from app.models import Activity, Setting
from app.utils.decorators import admin_required, log_activity
from app.utils.settings import get_setting, set_setting, get_all_settings, initialize_default_settings
from app.routes.admin import admin_bp


@admin_bp.route('/settings')
@login_required
@admin_required
@log_activity('Viewed settings', entity_type='Setting', category=Activity.CATEGORY_ADMIN, method=Activity.METHOD_READ)
def settings():
    """Display and manage application settings."""
    # Initialize default settings if needed
    initialize_default_settings()

    # Get all settings with a fresh query - don't close the session
    # as it will detach the current_user
    db.session.expire_all()

    # Get all settings with a completely fresh query
    all_settings = db.session.query(Setting).all()

    # Create a simple dictionary for direct access in templates
    settings_dict = {setting.key: setting.value for setting in all_settings}

    # Group settings by category based on key prefix
    grouped_settings = {}
    for setting in all_settings:
        # Extract category from key (e.g., 'email_from_name' -> 'email')
        category = setting.key.split('_')[0] if '_' in setting.key else 'general'

        if category not in grouped_settings:
            grouped_settings[category] = []

        grouped_settings[category].append(setting)

    # Log the settings being loaded with their values for debugging
    current_app.logger.debug(f"Loaded {len(all_settings)} settings grouped into {len(grouped_settings)} categories")
    for category, settings_list in grouped_settings.items():
        for setting in settings_list:
            current_app.logger.debug(f"Setting: {setting.key} = {setting.value}")

    # Add a timestamp to prevent browser caching
    timestamp = int(datetime.now().timestamp())

    # Get the current user's role to avoid detached instance error
    is_admin = current_user.role == 'Admin'

    return render_template('admin/settings.html',
                          active_page='settings',
                          grouped_settings=grouped_settings,
                          all_settings=all_settings,
                          settings=settings_dict,
                          is_admin=is_admin,
                          cache_buster=timestamp)


@admin_bp.route('/settings/update', methods=['POST'])
@login_required
@admin_required
@log_activity('Updated settings', entity_type='Setting', category=Activity.CATEGORY_ADMIN, method=Activity.METHOD_UPDATE)
def update_settings():
    """Update application settings."""
    # Get the active tab to redirect back to it
    active_tab = request.form.get('active_tab', 'general')

    # Refresh the session without closing it to avoid detaching current_user
    db.session.expire_all()

    # Process form data
    updated_settings = []
    updated_values = {}

    for key, value in request.form.items():
        # Skip CSRF token and active_tab
        if key in ['csrf_token', 'active_tab']:
            continue

        # Handle checkboxes (they are only included in form data when checked)
        if key.startswith('checkbox_'):
            actual_key = key[9:]  # Remove 'checkbox_' prefix
            # If we have a checkbox_ field but no corresponding value field,
            # it means the checkbox was unchecked
            if actual_key not in request.form:
                # Directly update the setting in the database
                setting = db.session.query(Setting).filter_by(key=actual_key).first()
                if setting:
                    setting.value = 'false'
                    updated_values[actual_key] = 'false'
                    updated_settings.append(actual_key)
            continue

        # Special handling for checkbox values that are present (checked)
        if key in ['maintenance_mode', 'password_requires_uppercase', 'password_requires_number', 'password_requires_special', 'allow_registration', 'require_email_verification', 'require_admin_approval']:
            # For checkboxes, we want to store 'on' when checked
            setting = db.session.query(Setting).filter_by(key=key).first()
            if setting:
                setting.value = 'on'
                updated_values[key] = 'on'
                updated_settings.append(key)
            continue

        # Special handling for primary color - ensure hex_color is also updated
        if key == 'primary_color':
            # Update the primary color
            setting = db.session.query(Setting).filter_by(key=key).first()
            if setting:
                setting.value = value
                updated_values[key] = value
                updated_settings.append(key)

                # Also update the hex value if it exists
                hex_setting = db.session.query(Setting).filter_by(key='primary_color_hex').first()
                if hex_setting:
                    hex_setting.value = value
                    updated_values['primary_color_hex'] = value
                    updated_settings.append('primary_color_hex')
            continue

        # Skip primary_color_hex as it's handled with primary_color
        if key == 'primary_color_hex':
            continue

        # For regular fields, directly update the setting in the database
        setting = db.session.query(Setting).filter_by(key=key).first()
        if setting:
            setting.value = value
            updated_values[key] = value
            updated_settings.append(key)
        else:
            # If setting doesn't exist, create it
            new_setting = Setting(key=key, value=value, type=Setting.TYPE_STRING)
            db.session.add(new_setting)
            updated_values[key] = value
            updated_settings.append(key)

    # Commit all changes at once
    db.session.commit()

    # Refresh the session without closing it
    db.session.expire_all()

    # Apply session lifetime if it was updated
    if 'session_lifetime' in updated_settings:
        try:
            session_lifetime_days = int(updated_values['session_lifetime'])
            current_app.config['PERMANENT_SESSION_LIFETIME'] = timedelta(days=session_lifetime_days)
            current_app.logger.info(f"Session lifetime updated to {session_lifetime_days} days")
        except (ValueError, KeyError) as e:
            current_app.logger.error(f"Error updating session lifetime: {str(e)}")

    # Log the settings that were updated with their new values
    current_app.logger.info(f"Settings updated by {current_user.email}:")
    for key, value in updated_values.items():
        current_app.logger.info(f"  - {key} = {value}")

    # Success message will be shown via toast notification using URL parameters

    # Add a timestamp to prevent browser caching
    timestamp = int(datetime.now().timestamp())
    return redirect(url_for('admin.settings', _t=timestamp, status='success', action='updated') + f'#{active_tab}')

import os
import sys
from flask import Flask
from flask_sqlalchemy import SQLAlchemy
from flask_migrate import Migrate

# Add the app directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '.')))

# Import the Activity model
from app.models import Activity, db, User

# Create a simple Flask app
app = Flask(__name__)

# Use the same database as the main application
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:////Users/<USER>/Documents/dev/client-engineering/matrix-app/instance/dev.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

# Initialize the database
db.init_app(app)
migrate = Migrate(app, db)

# Test the Activity model
with app.app_context():
    # Get all activities
    activities = Activity.query.all()
    print(f"Found {len(activities)} activities")

    # Check if the method field exists
    for activity in activities[:5]:
        print(f"Activity ID: {activity.id}")
        print(f"Action: {activity.action}")
        print(f"Method: {getattr(activity, 'method', 'Not available')}")
        print(f"Category: {activity.category}")
        print(f"Severity: {activity.severity}")
        print(f"Entity Type: {activity.entity_type}")
        print(f"Entity ID: {activity.entity_id}")
        print(f"Created At: {activity.created_at}")
        print("-" * 50)

    # Test creating a new activity with method
    if len(activities) > 0:
        user = User.query.first()
        if user:
            print("Creating a new activity with method...")
            new_activity = Activity(
                user_id=user.id,
                action="Test activity with method",
                method=Activity.METHOD_CREATE,
                entity_type="Test",
                entity_id=1,
                category=Activity.CATEGORY_DATA,
                severity=Activity.SEVERITY_INFO
            )
            db.session.add(new_activity)
            db.session.commit()
            print(f"Created activity with ID: {new_activity.id}")
            print(f"Method: {new_activity.method}")
            print(f"Method Display: {new_activity.get_method_display()}")
        else:
            print("No users found to create test activity")
    else:
        print("No activities found in the database")

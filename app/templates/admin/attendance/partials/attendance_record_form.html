{# This form partial is loaded into a drawer by client-side JavaScript #}
{# The `action_url` and `form_title` are passed from the route #}
{% from "partials/forms/base_form.html" import form_group, form_actions, form_header %}

<form method="POST" action="{{ action_url }}" id="attendanceRecordDrawerForm" class="space-y-4">
  {{ form.hidden_tag() }}

  {{ form_header(
    title = form_title,
    description = "Manage employee attendance records and track work schedules"
  ) }}

  {# Holiday Warning Alert #}
  {% if holiday_warning and holiday_warning.is_holiday %}
  <div class="bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800 rounded-lg p-4 mb-4">
    <div class="flex items-start">
      <div class="flex-shrink-0">
        <svg class="h-5 w-5 text-amber-400" viewBox="0 0 20 20" fill="currentColor">
          <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
        </svg>
      </div>
      <div class="ml-3">
        <h3 class="text-sm font-medium text-amber-800 dark:text-amber-200">
          Holiday Date Selected
        </h3>
        <div class="mt-2 text-sm text-amber-700 dark:text-amber-300">
          <p>The selected date is <strong>{{ holiday_warning.holiday_name }}</strong> ({{ holiday_warning.region_code }}).</p>
          {% if holiday_warning.holiday_description %}
          <p class="mt-1">{{ holiday_warning.holiday_description }}</p>
          {% endif %}
          <p class="mt-2 font-medium">Working on holidays may require special approval.</p>
        </div>
      </div>
    </div>
  </div>
  {% endif %}

  <div class="grid grid-cols-1 gap-4">
    {# Employee Selection #}
    {{ form_group(
      label = form.employee_detail_id.label.text,
      name = form.employee_detail_id.name,
      type = "select",
      value = form.employee_detail_id.data if form.employee_detail_id.data else "",
      required = form.employee_detail_id.flags.required,
      options = form.employee_detail_id.choices
    ) }}
    {% if form.employee_detail_id.errors %}
      <div class="text-xs text-destructive mt-1">
        <ul>
          {% for error in form.employee_detail_id.errors %}<li>{{ error }}</li>{% endfor %}
        </ul>
      </div>
    {% endif %}

    {# Date Selection #}
    {{ form_group(
      label = form.date.label.text,
      name = form.date.name,
      type = "date",
      value = form.date.data if form.date.data else "",
      required = form.date.flags.required
    ) }}
    {% if form.date.errors %}
      <div class="text-xs text-destructive mt-1">
        <ul>
          {% for error in form.date.errors %}<li>{{ error }}</li>{% endfor %}
        </ul>
      </div>
    {% endif %}

    {# Attendance Type Selection #}
    {{ form_group(
      label = form.attendance_type_id.label.text,
      name = form.attendance_type_id.name,
      type = "select",
      value = form.attendance_type_id.data if form.attendance_type_id.data else "",
      required = form.attendance_type_id.flags.required,
      options = form.attendance_type_id.choices
    ) }}
    {% if form.attendance_type_id.errors %}
      <div class="text-xs text-destructive mt-1">
        <ul>
          {% for error in form.attendance_type_id.errors %}<li>{{ error }}</li>{% endfor %}
        </ul>
      </div>
    {% endif %}

    {# Time Fields (for partial day types) #}
    <div class="grid grid-cols-2 gap-4" id="timeFields" style="display: none;">
      <div>
        {{ form_group(
          label = form.start_time.label.text,
          name = form.start_time.name,
          type = "time",
          value = form.start_time.data if form.start_time.data else ""
        ) }}
        {% if form.start_time.description %}
          <p class="text-sm text-muted-foreground mt-1">{{ form.start_time.description }}</p>
        {% endif %}
        {% if form.start_time.errors %}
          <div class="text-xs text-destructive mt-1">
            <ul>
              {% for error in form.start_time.errors %}<li>{{ error }}</li>{% endfor %}
            </ul>
          </div>
        {% endif %}
      </div>

      <div>
        {{ form_group(
          label = form.end_time.label.text,
          name = form.end_time.name,
          type = "time",
          value = form.end_time.data if form.end_time.data else ""
        ) }}
        {% if form.end_time.description %}
          <p class="text-sm text-muted-foreground mt-1">{{ form.end_time.description }}</p>
        {% endif %}
        {% if form.end_time.errors %}
          <div class="text-xs text-destructive mt-1">
            <ul>
              {% for error in form.end_time.errors %}<li>{{ error }}</li>{% endfor %}
            </ul>
          </div>
        {% endif %}
      </div>
    </div>

    {# Duration Field (for partial day types) #}
    <div id="durationField" style="display: none;">
      <div class="form-group">
        <label for="duration_hours" class="form-label">
          {{ form.duration_hours.label.text }}
          {% if form.duration_hours.flags.required %}<span class="text-destructive">*</span>{% endif %}
        </label>
        <input type="number" id="duration_hours" name="duration_hours"
               value="{{ form.duration_hours.data if form.duration_hours.data else '' }}"
               step="0.5" min="0.5" max="24"
               class="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50">
        {% if form.duration_hours.description %}
          <p class="text-sm text-muted-foreground mt-1">{{ form.duration_hours.description }}</p>
        {% endif %}
      </div>
      {% if form.duration_hours.errors %}
        <div class="text-xs text-destructive mt-1">
          <ul>
            {% for error in form.duration_hours.errors %}<li>{{ error }}</li>{% endfor %}
          </ul>
        </div>
      {% endif %}
    </div>

    {# Status Selection (Admin only) #}
    {% if current_user.is_admin %}
    {{ form_group(
      label = form.status.label.text,
      name = form.status.name,
      type = "select",
      value = form.status.data if form.status.data else "",
      required = form.status.flags.required,
      options = form.status.choices
    ) }}
    {% if form.status.errors %}
      <div class="text-xs text-destructive mt-1">
        <ul>
          {% for error in form.status.errors %}<li>{{ error }}</li>{% endfor %}
        </ul>
      </div>
    {% endif %}
    {% endif %}

    {# Rejection Reason (Admin only, for rejected records) #}
    {% if current_user.is_admin and form.status.data == 'Rejected' %}
    {{ form_group(
      label = form.rejection_reason.label.text,
      name = form.rejection_reason.name,
      type = "textarea",
      value = form.rejection_reason.data if form.rejection_reason.data else "",
      placeholder = form.rejection_reason.render_kw.placeholder if form.rejection_reason.render_kw and form.rejection_reason.render_kw.placeholder else ""
    ) }}
    {% if form.rejection_reason.errors %}
      <div class="text-xs text-destructive mt-1">
        <ul>
          {% for error in form.rejection_reason.errors %}<li>{{ error }}</li>{% endfor %}
        </ul>
      </div>
    {% endif %}
    {% endif %}

    {# Notes Field #}
    {{ form_group(
      label = form.notes.label.text,
      name = form.notes.name,
      type = "textarea",
      value = form.notes.data if form.notes.data else "",
      placeholder = form.notes.render_kw.placeholder if form.notes.render_kw and form.notes.render_kw.placeholder else ""
    ) }}
    {% if form.notes.errors %}
      <div class="text-xs text-destructive mt-1">
        <ul>
          {% for error in form.notes.errors %}<li>{{ error }}</li>{% endfor %}
        </ul>
      </div>
    {% endif %}
  </div>

  <div class="flex justify-end space-x-2 pt-4">
    <button type="button" class="btn btn-outline btn-md drawer-close">Cancel</button>
    <button type="button" onclick="submitDrawerForm('attendanceRecordDrawerForm')" class="btn btn-primary btn-md">
      {{ form.submit.label.text }}
    </button>
  </div>
</form>

<script>
// Show/hide time and duration fields based on attendance type
document.addEventListener('DOMContentLoaded', function() {
  const attendanceTypeSelect = document.getElementById('attendance_type_id');
  const timeFields = document.getElementById('timeFields');
  const durationField = document.getElementById('durationField');

  function toggleFields() {
    const selectedOption = attendanceTypeSelect.options[attendanceTypeSelect.selectedIndex];
    if (selectedOption && selectedOption.dataset.isFullDay === 'false') {
      timeFields.style.display = 'block';
      durationField.style.display = 'block';
    } else {
      timeFields.style.display = 'none';
      durationField.style.display = 'none';
    }
  }

  attendanceTypeSelect.addEventListener('change', toggleFields);
  toggleFields(); // Initial call

  // Holiday checking on date/employee change
  const dateField = document.getElementById('date');
  const employeeField = document.getElementById('employee_detail_id');

  function checkHoliday() {
    if (dateField.value && employeeField.value) {
      // This will be enhanced with AJAX call to check holidays
      console.log('Checking holiday for:', dateField.value, 'Employee:', employeeField.value);
    }
  }

  dateField.addEventListener('change', checkHoliday);
  employeeField.addEventListener('change', checkHoliday);
});
</script>

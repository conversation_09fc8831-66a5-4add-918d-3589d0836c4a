{% macro entity_activity_log(entity_type, entity_id, limit=5, show_title=True, show_all_link=True, methods=None, exclude_methods=None) %}
<div class="entity-activity-log bg-card dark:bg-slate-900 rounded-lg shadow-sm dark:border-slate-800" data-entity-type="{{ entity_type }}" data-entity-id="{{ entity_id }}" data-limit="{{ limit }}" {% if methods %}data-methods="{{ methods }}"{% endif %} {% if exclude_methods %}data-exclude-methods="{{ exclude_methods }}"{% endif %}>
    {% if show_title %}
    <div class="p-5 border-b border-border dark:border-slate-800">
        <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-3">
            <div>
                <h2 class="text-lg font-semibold flex items-center">
                    <i data-lucide="history" class="w-5 h-5 mr-2 text-primary"></i>
                    Activity Timeline
                </h2>
                <p class="text-sm text-muted-foreground dark:text-slate-400 mt-1">Recent changes to this {{ entity_type | lower }}</p>
            </div>
            <div class="flex space-x-2 mt-3 sm:mt-0">
            <div class="relative">
                <select id="activity-method-select" class="appearance-none bg-background dark:bg-slate-800 border border-border dark:border-slate-700 rounded-md py-1 pl-3 pr-8 text-sm font-medium text-foreground dark:text-slate-200 focus:outline-none focus:ring-1 focus:ring-primary">
                    <option value="create,update" {% if not methods or methods == 'create,update' %}selected{% endif %}>Create & Update</option>
                    <option value="all" {% if methods == 'all' %}selected{% endif %}>All activities</option>
                    <option value="create" {% if methods == 'create' %}selected{% endif %}>Create only</option>
                    <option value="update" {% if methods == 'update' %}selected{% endif %}>Update only</option>
                    {% if not exclude_methods or 'read' not in exclude_methods %}
                    <option value="read" {% if methods == 'read' %}selected{% endif %}>View only</option>
                    {% endif %}
                </select>
                <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-muted-foreground dark:text-slate-400">
                    <i data-lucide="filter" class="h-4 w-4"></i>
                </div>
            </div>
            <div class="relative">
                <select id="activity-limit-select" class="appearance-none bg-background dark:bg-slate-800 border border-border dark:border-slate-700 rounded-md py-1 pl-3 pr-8 text-sm font-medium text-foreground dark:text-slate-200 focus:outline-none focus:ring-1 focus:ring-primary">
                    <option value="5" {% if limit == 5 %}selected{% endif %}>Last 5 changes</option>
                    <option value="10" {% if limit == 10 %}selected{% endif %}>Last 10 changes</option>
                    <option value="20" {% if limit == 20 %}selected{% endif %}>Last 20 changes</option>
                    <option value="all" {% if limit == 'all' %}selected{% endif %}>All changes</option>
                </select>
                <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-muted-foreground dark:text-slate-400">
                    <i data-lucide="chevron-down" class="h-4 w-4"></i>
                </div>
            </div>
            {% if show_all_link %}
            <a href="{{ url_for('admin.activities', entity_type=entity_type, entity_id=entity_id) }}" class="inline-flex items-center justify-center rounded-md bg-background dark:bg-slate-800 px-3 py-1 text-sm font-medium text-foreground dark:text-slate-200 border border-border dark:border-slate-700 hover:bg-muted/50 dark:hover:bg-slate-700/50 transition-colors">
                <i data-lucide="external-link" class="w-4 h-4 mr-2"></i>
                <span>View All</span>
            </a>
            {% endif %}
            </div>
        </div>
    </div>
    {% endif %}

    <div class="p-0">
        <div class="entity-activity-loading flex items-center justify-center py-8">
            <div class="animate-spin h-6 w-6 border-2 border-primary/50 border-t-primary rounded-full"></div>
            <span class="ml-3 text-muted-foreground dark:text-slate-400">Loading activity history...</span>
        </div>

        <div class="entity-activity-empty hidden p-5">
            <div class="flex flex-col items-center justify-center py-12 space-y-4 bg-muted/10 rounded-lg border border-dashed border-border">
                <div class="w-16 h-16 rounded-full bg-muted/30 flex items-center justify-center">
                    <i data-lucide="history" class="w-8 h-8 text-muted-foreground/70"></i>
                </div>
                <div class="text-center max-w-md">
                    <p class="text-lg font-medium">No activities found</p>
                    <p class="text-sm text-muted-foreground mt-1">There are no activity records for this {{ entity_type | lower }} yet.</p>
                </div>
            </div>
        </div>

        <div class="entity-activity-error hidden py-8 text-center">
            <div class="text-muted-foreground dark:text-slate-400">Unable to load activity history</div>
        </div>

        <div class="entity-activity-list hidden max-h-[500px] overflow-y-auto">
            <!-- Activity items will be inserted here by JavaScript -->
        </div>
    </div>
</div>

<!-- Activity item template -->
<template id="entity-activity-item-template">
    <div class="border-b border-border dark:border-slate-800 last:border-0">
        <div class="relative pl-20 py-3 pr-4">
            <!-- Timeline vertical line -->
            <div class="absolute left-8 top-0 bottom-0 w-px bg-border dark:bg-slate-700"></div>

            <!-- User avatar -->
            <div class="absolute left-3 top-3 w-10 h-10">
                <div class="w-10 h-10 rounded-full bg-slate-700 dark:bg-slate-700 text-white dark:text-white flex items-center justify-center text-base font-semibold"></div>
            </div>

            <!-- Activity content -->
            <div>
                <!-- User name and action -->
                <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-1">
                    <div class="font-medium text-foreground dark:text-slate-200 activity-user-name"></div>
                    <div class="activity-method-badge inline-flex items-center px-2.5 py-0.5 rounded-md text-xs font-medium"></div>
                </div>

                <!-- Activity description -->
                <div class="text-foreground dark:text-slate-300 mb-1 activity-action"></div>

                <!-- Activity details (file, time) -->
                <div class="flex items-center text-xs text-muted-foreground dark:text-slate-400 space-x-4">
                    <div class="activity-entity-type hidden items-center">
                        <i data-lucide="file-text" class="w-3.5 h-3.5 mr-1 opacity-70"></i>
                        <span></span>
                    </div>
                    <div class="flex items-center">
                        <i data-lucide="clock" class="w-3.5 h-3.5 mr-1 opacity-70"></i>
                        <span class="activity-time-ago"></span>
                    </div>
                    <div class="activity-severity hidden items-center">
                        <i data-lucide="alert-circle" class="w-3.5 h-3.5 mr-1 opacity-70"></i>
                        <span class="activity-severity-badge"></span>
                    </div>
                </div>

                <!-- Action buttons -->
                <div class="activity-actions hidden mt-3 space-x-2">
                    <button class="activity-action-btn view-details-btn inline-flex items-center px-2.5 py-1 rounded text-xs font-medium bg-primary/10 dark:bg-primary/20 text-primary dark:text-primary-foreground hover:bg-primary/20 dark:hover:bg-primary/30 transition-colors">
                        <i data-lucide="eye" class="w-3.5 h-3.5 mr-1.5"></i>
                        <span>View Changes</span>
                    </button>
                    <button class="activity-action-btn view-entity-btn inline-flex items-center px-2.5 py-1 rounded text-xs font-medium bg-muted dark:bg-slate-700 text-muted-foreground dark:text-slate-300 hover:bg-muted/80 dark:hover:bg-slate-600 transition-colors">
                        <i data-lucide="external-link" class="w-3.5 h-3.5 mr-1.5"></i>
                        <span>View Entity</span>
                    </button>
                </div>

                <!-- Changes section (initially hidden) -->
                <div class="activity-changes hidden mt-3 bg-muted/30 dark:bg-slate-800/50 rounded-md p-3 border border-border dark:border-slate-700">
                    <div class="text-sm font-medium mb-3 flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="w-5 h-5 rounded-full bg-primary/10 dark:bg-primary/20 flex items-center justify-center mr-1.5">
                                <i data-lucide="git-compare" class="w-3 h-3 text-primary dark:text-primary-foreground"></i>
                            </div>
                            <span class="text-foreground dark:text-slate-200">Changes Made</span>
                        </div>
                        <button class="text-xs text-muted-foreground dark:text-slate-400 hover:text-primary dark:hover:text-primary-foreground transition-colors flex items-center close-changes-btn rounded-md px-2 py-1 hover:bg-muted dark:hover:bg-slate-700">
                            <i data-lucide="x" class="w-3.5 h-3.5 mr-1"></i>
                            <span>Close</span>
                        </button>
                    </div>
                    <div class="activity-changes-content space-y-3">
                        <!-- Changes will be inserted here -->
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<!-- Change item template -->
<template id="entity-activity-change-template">
    <div class="bg-background dark:bg-slate-800 rounded-md border border-border dark:border-slate-700 overflow-hidden">
        <div class="font-medium change-field flex items-center px-3 py-2 bg-muted/30 dark:bg-slate-700/30 border-b border-border dark:border-slate-700">
            <i data-lucide="file-text" class="w-3.5 h-3.5 mr-1.5 text-primary dark:text-primary-foreground"></i>
            <span class="text-foreground dark:text-slate-200"></span>
        </div>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-0 md:divide-x divide-border dark:divide-slate-700">
            <div class="change-old p-3">
                <div class="flex items-center mb-1.5">
                    <i data-lucide="minus-circle" class="w-3.5 h-3.5 mr-1.5 text-red-500 dark:text-red-400"></i>
                    <span class="text-xs font-medium text-muted-foreground dark:text-slate-400">Previous Value</span>
                </div>
                <div class="text-sm text-foreground dark:text-slate-300 pl-5 break-words"></div>
            </div>
            <div class="change-new p-3 bg-muted/10 dark:bg-slate-700/20">
                <div class="flex items-center mb-1.5">
                    <i data-lucide="plus-circle" class="w-3.5 h-3.5 mr-1.5 text-green-500 dark:text-green-400"></i>
                    <span class="text-xs font-medium text-muted-foreground dark:text-slate-400">New Value</span>
                </div>
                <div class="text-sm text-foreground dark:text-slate-300 pl-5 break-words"></div>
            </div>
        </div>
    </div>
</template>

<!-- Empty state template -->
<template id="entity-activity-empty-template">
    <div class="p-5">
        <div class="flex flex-col items-center justify-center py-12 space-y-4 bg-muted/10 rounded-lg border border-dashed border-border">
            <div class="w-16 h-16 rounded-full bg-muted/30 flex items-center justify-center">
                <i data-lucide="history" class="w-8 h-8 text-muted-foreground/70"></i>
            </div>
            <div class="text-center max-w-md">
                <p class="text-lg font-medium">No activities found</p>
                <p class="text-sm text-muted-foreground mt-1">There are no activity records for this entity yet.</p>
            </div>
        </div>
    </div>
</template>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize all entity activity logs on the page
    document.querySelectorAll('.entity-activity-log').forEach(function(container) {
        initEntityActivityLog(container);
    });
});

function initEntityActivityLog(container) {
    const entityType = container.dataset.entityType;
    const entityId = container.dataset.entityId;
    const limit = container.dataset.limit || 5;
    const methods = container.dataset.methods || 'create,update';
    const excludeMethods = container.dataset.excludeMethods || '';

    if (!entityType || !entityId) {
        console.error('Entity activity log is missing required data attributes');
        return;
    }

    // Set up method dropdown if it exists
    const methodSelect = container.querySelector('#activity-method-select');
    if (methodSelect) {
        // Set initial value based on data-methods attribute
        // If the selected method is excluded, default to 'create,update'
        if (methods === 'read' && excludeMethods && excludeMethods.includes('read')) {
            methods = 'create,update';
            container.dataset.methods = methods;
        }

        // Set the dropdown value
        methodSelect.value = methods;

        // If the selected option doesn't exist (e.g., it was excluded), default to 'create,update'
        if (Array.from(methodSelect.options).every(option => option.value !== methods)) {
            methodSelect.value = 'create,update';
            container.dataset.methods = 'create,update';
        }

        // Add event listener
        methodSelect.addEventListener('change', function() {
            const newMethods = this.value;
            container.dataset.methods = newMethods;
            fetchEntityActivities(entityType, entityId, limit, container, newMethods, excludeMethods);
        });
    }

    // Set up limit dropdown if it exists
    const limitSelect = container.querySelector('#activity-limit-select');
    if (limitSelect) {
        // Set initial value
        limitSelect.value = limit;

        // Add event listener
        limitSelect.addEventListener('change', function() {
            const newLimit = this.value;
            container.dataset.limit = newLimit;
            fetchEntityActivities(entityType, entityId, newLimit, container, methods, excludeMethods);
        });
    }

    // Fetch activities for this entity
    fetchEntityActivities(entityType, entityId, limit, container, methods, excludeMethods);
}

function fetchEntityActivities(entityType, entityId, limit, container, methods = 'all', excludeMethods = '') {
    // Show loading state
    const loadingEl = container.querySelector('.entity-activity-loading');
    const emptyEl = container.querySelector('.entity-activity-empty');
    const errorEl = container.querySelector('.entity-activity-error');
    const listEl = container.querySelector('.entity-activity-list');

    loadingEl.classList.remove('hidden');
    emptyEl.classList.add('hidden');
    errorEl.classList.add('hidden');
    listEl.classList.add('hidden');

    // Determine if this is an admin view
    const isAdminView = document.body.classList.contains('admin-view') ||
                        window.location.pathname.includes('/admin/');

    // Build API URL with parameters
    let apiUrl = `/api/activities?entity_type=${encodeURIComponent(entityType)}&entity_id=${encodeURIComponent(entityId)}`;

    // Add limit parameter (if limit is 'all', use 0 to get all activities)
    if (limit === 'all') {
        apiUrl += '&limit=0';
    } else {
        apiUrl += `&limit=${limit}`;
    }

    // Add method parameter if we're filtering by method
    // The API supports a 'method' parameter that filters server-side
    if (methods !== 'all') {
        // If we have multiple methods (e.g., 'create,update'), we'll handle that client-side
        // since the API only supports a single method parameter
        if (!methods.includes(',')) {
            apiUrl += `&method=${methods}`;
        } else if (methods === 'create,update') {
            // Special case for create,update - exclude read activities
            apiUrl += '&exclude_method=read'; // Exclude read activities
        }
    }

    // Add exclude_method parameter if provided
    if (excludeMethods && excludeMethods !== '') {
        // If we already have an exclude_method parameter, don't add it again
        if (!apiUrl.includes('exclude_method=')) {
            apiUrl += `&exclude_method=${excludeMethods}`;
        }
    }

    // Add admin parameters
    if (isAdminView) {
        apiUrl += '&show_all=true';
    }

    // Store the methods filter for client-side filtering of multiple methods
    container.dataset.methodsFilter = methods;
    // Store the original limit
    container.dataset.originalLimit = limit;
    // Store the exclude methods for client-side filtering
    container.dataset.excludeMethods = excludeMethods;

    console.log('Fetching activities from:', apiUrl);

    fetch(apiUrl)
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            loadingEl.classList.add('hidden');

            // Debug: Log the API response
            console.log('Activity API response:', data);

            // Log all methods in the response for debugging
            if (data.activities && data.activities.length > 0) {
                console.log('Methods in API response:');
                data.activities.forEach(activity => {
                    console.log(`Activity ID: ${activity.id}, Method: ${activity.method}, Type: ${typeof activity.method}`);
                });
            }

            if (data.success && data.activities && data.activities.length > 0) {
                // Debug: Log the activities
                console.log('Activities found:', data.activities.length);
                renderEntityActivities(data.activities, listEl);
                listEl.classList.remove('hidden');
            } else {
                console.log('No activities found or empty response');
                emptyEl.classList.remove('hidden');
            }
        })
        .catch(error => {
            console.error('Error fetching entity activities:', error);
            loadingEl.classList.add('hidden');
            errorEl.classList.remove('hidden');
        });
}

function renderEntityActivities(activities, container) {
    // Clear existing activities
    container.innerHTML = '';

    console.log('Processing activities:', activities.length, 'activities');

    // Get the methods filter from the container's data attribute
    const activityLog = container.closest('.entity-activity-log');
    const methodsFilter = activityLog.dataset.methodsFilter || 'all';
    const originalLimit = activityLog.dataset.originalLimit || 5;
    const excludeMethodsFilter = activityLog.dataset.excludeMethods || '';
    console.log('Filtering by methods:', methodsFilter, 'with original limit:', originalLimit);
    console.log('Excluding methods:', excludeMethodsFilter);

    // Filter activities based on the selected methods
    let filteredActivities = activities;

    // First, apply exclude methods filter if provided
    if (excludeMethodsFilter && excludeMethodsFilter !== '') {
        const methodsToExclude = excludeMethodsFilter.split(',');
        console.log('Excluding methods:', methodsToExclude);

        filteredActivities = filteredActivities.filter(activity => {
            // Try multiple properties where the method might be stored
            const activityMethod = (activity.method || '').toString().toLowerCase();
            const activityMethodDisplay = (activity.method_display || '').toString().toLowerCase();
            const activityAction = (activity.action || '').toString().toLowerCase();

            // Check if this activity has a method we want to exclude
            const shouldExclude = methodsToExclude.some(method => {
                const methodLower = method.toLowerCase();

                // Check if method matches any of the excluded methods
                if (methodLower === 'read') {
                    return activityMethod === 'read' ||
                           activityAction.includes('view') ||
                           activityAction.includes('viewed');
                }

                return activityMethod === methodLower ||
                       activityMethodDisplay.includes(methodLower) ||
                       activityAction.includes(methodLower);
            });

            // Keep the activity if it should NOT be excluded
            return !shouldExclude;
        });

        console.log('After excluding methods:', filteredActivities.length, 'activities remaining');
    }

    // Then apply the methods filter
    if (methodsFilter !== 'all') {
        const allowedMethods = methodsFilter.split(',');
        filteredActivities = filteredActivities.filter(activity => {
            // Check if the method is in our allowed list
            // Also handle case differences (e.g., 'Create' vs 'create')
            console.log('Checking activity:', activity);
            console.log('Activity method:', activity.method, 'type:', typeof activity.method);
            console.log('Activity method_display:', activity.method_display);

            // Try multiple properties where the method might be stored
            const activityMethod = activity.method || '';
            const activityMethodDisplay = activity.method_display || '';
            const activityAction = activity.action || '';

            // Check if this is a 'read' activity based on action text or method
            const isReadActivity =
                activityMethod.toString().toLowerCase() === 'read' ||
                activityAction.toString().toLowerCase().includes('view') ||
                activityAction.toString().toLowerCase().includes('viewed');

            // If we're filtering for 'create,update' and this is a read activity, exclude it
            if (methodsFilter === 'create,update' && isReadActivity) {
                console.log('Excluding read activity:', activity);
                return false;
            }

            const isAllowed = allowedMethods.some(method => {
                console.log('Comparing with allowed method:', method);

                // Check multiple ways the method might be represented
                const methodMatches = activityMethod.toString().toLowerCase() === method.toLowerCase();
                const displayMatches = activityMethodDisplay.toString().toLowerCase().includes(method.toLowerCase());

                // Also check if the action text contains keywords like 'updated' or 'created'
                const actionMatches =
                    (method === 'update' && activityAction.toLowerCase().includes('update')) ||
                    (method === 'create' && activityAction.toLowerCase().includes('create')) ||
                    (method === 'read' && (activityAction.toLowerCase().includes('view') || activityAction.toLowerCase().includes('viewed')));

                const matches = methodMatches || displayMatches || actionMatches;
                console.log('Matches method?', methodMatches, 'Matches display?', displayMatches, 'Matches action?', actionMatches);
                return matches;
            });

            return isAllowed;
        });
        console.log('Filtered to', filteredActivities.length, 'activities with methods:', allowedMethods);
    }

    // If we have no activities after filtering but we have activities before filtering,
    // fall back to showing all activities EXCEPT for create,update filter
    if (filteredActivities.length === 0 && activities.length > 0) {
        // For create,update filter, we want to be strict and not show read activities
        if (methodsFilter === 'create,update') {
            console.log('No create/update activities found, keeping empty state');
            // Keep filteredActivities empty
        } else {
            console.log('No activities after filtering, falling back to all activities');
            filteredActivities = activities;
        }
    }

    // Apply the original limit to the filtered activities
    if (originalLimit !== 'all' && filteredActivities.length > parseInt(originalLimit)) {
        filteredActivities = filteredActivities.slice(0, parseInt(originalLimit));
        console.log('Limited to', filteredActivities.length, 'activities after filtering');
    }

    // Log methods of activities
    const methods = activities.map(a => a.method);
    console.log('Activity methods:', methods);

    // Check if we have any activities after filtering
    if (filteredActivities.length === 0) {
        // Use the empty state template
        const emptyTemplate = document.getElementById('entity-activity-empty-template');
        const emptyEl = emptyTemplate.content.cloneNode(true);

        // Update the empty state message to reflect the filter
        if (methodsFilter !== 'all') {
            const emptyTitle = emptyEl.querySelector('p.text-lg');
            const emptyDesc = emptyEl.querySelector('p.text-sm');

            // Set appropriate message based on filter
            if (emptyTitle) {
                if (methodsFilter === 'create,update') {
                    emptyTitle.textContent = 'No create or update activities';
                } else {
                    emptyTitle.textContent = 'No matching activities';
                }
            }

            // Set description
            if (emptyDesc) {
                if (methodsFilter === 'create,update') {
                    emptyDesc.textContent = 'No create or update activities found for this entity';
                } else if (methodsFilter === 'read') {
                    emptyDesc.textContent = 'No view activities found for this entity';
                } else {
                    emptyDesc.textContent = `No ${methodsFilter} activities found for this entity`;
                }
            }
        }

        container.appendChild(emptyEl);
    } else {
        // Group activities by date
        const groupedActivities = groupActivitiesByDate(filteredActivities);

        // Render activities by date groups
        Object.keys(groupedActivities).forEach(dateGroup => {
            // Add date header
            const dateHeader = document.createElement('div');
            dateHeader.className = 'py-2 px-4 text-sm font-medium text-foreground dark:text-slate-300 bg-muted/30 dark:bg-slate-800/50 border-y border-border dark:border-slate-700';
            dateHeader.textContent = dateGroup;
            container.appendChild(dateHeader);

            // Render activities for this date
            groupedActivities[dateGroup].forEach(activity => {
                const activityEl = createActivityElement(activity);
                container.appendChild(activityEl);
            });
        });
    }

    // Initialize Lucide icons
    if (window.lucide) {
        lucide.createIcons();
    }
}

function groupActivitiesByDate(activities) {
    const groups = {};

    activities.forEach(activity => {
        const date = new Date(activity.created_at);
        const today = new Date();
        const yesterday = new Date(today);
        yesterday.setDate(yesterday.getDate() - 1);

        let dateGroup;

        // Check if the activity date is today
        if (date.toDateString() === today.toDateString()) {
            dateGroup = 'Today';
        }
        // Check if the activity date is yesterday
        else if (date.toDateString() === yesterday.toDateString()) {
            dateGroup = 'Yesterday';
        }
        // Otherwise use the formatted date
        else {
            dateGroup = formatDateForGroup(date);
        }

        // Create the group if it doesn't exist
        if (!groups[dateGroup]) {
            groups[dateGroup] = [];
        }

        // Add the activity to the group
        groups[dateGroup].push(activity);
    });

    return groups;
}

function formatDateForGroup(date) {
    const options = { month: 'long', day: 'numeric', year: 'numeric' };
    return date.toLocaleDateString('en-US', options);
}

function createActivityElement(activity) {
    // Clone the template
    const template = document.getElementById('entity-activity-item-template');
    const activityEl = template.content.cloneNode(true);

    // Set user avatar
    const avatarEl = activityEl.querySelector('.w-10.h-10.rounded-full');
    avatarEl.textContent = activity.user_name.charAt(0).toUpperCase();

    // Set user name
    const nameEl = activityEl.querySelector('.activity-user-name');
    nameEl.textContent = activity.user_name;

    // Set time ago
    const timeAgoEl = activityEl.querySelector('.activity-time-ago');
    timeAgoEl.textContent = formatTimeAgo(new Date(activity.created_at));
    timeAgoEl.title = new Date(activity.created_at).toLocaleString();

    // Set action text with better formatting
    const actionEl = activityEl.querySelector('.activity-action');

    // Set action text (use the original action text if available)
    let actionText = activity.action || '';

    // For 'read' activities, make the action text more descriptive if needed
    if (activity.method === 'read' && (!actionText || actionText.includes('Viewed'))) {
        const entityName = activity.entity_type || 'item';
        actionText = `Viewed ${entityName}`;
    }

    actionEl.textContent = actionText;

    // Show entity type if available
    const activityEntityType = activity.entity_type;
    if (activityEntityType) {
        const entityTypeEl = activityEl.querySelector('.activity-entity-type');
        entityTypeEl.classList.remove('hidden');
        entityTypeEl.classList.add('flex');
        entityTypeEl.querySelector('span').textContent = activityEntityType;
    }

    // Set severity badge if available
    if (activity.severity && activity.severity !== 'info') {
        const severityContainer = activityEl.querySelector('.activity-severity');
        severityContainer.classList.remove('hidden');
        severityContainer.classList.add('flex');

        const severityBadgeEl = activityEl.querySelector('.activity-severity-badge');
        severityBadgeEl.textContent = activity.severity;

        // Apply appropriate styling based on severity
        if (activity.severity === 'error') {
            severityBadgeEl.classList.add('text-red-500', 'dark:text-red-400');
        } else if (activity.severity === 'warning') {
            severityBadgeEl.classList.add('text-amber-500', 'dark:text-amber-400');
        }
    }

    // Set method badge
    const methodBadgeEl = activityEl.querySelector('.activity-method-badge');
    if (activity.method) {
        // Format method name for display
        let methodDisplay = activity.method_display || activity.method;
        methodDisplay = methodDisplay.charAt(0).toUpperCase() + methodDisplay.slice(1);

        // Add method-specific styling
        if (activity.method === 'create') {
            methodBadgeEl.classList.add('bg-primary/10', 'text-primary', 'dark:bg-primary/20', 'dark:text-primary-foreground');
            methodDisplay = 'Create';
        } else if (activity.method === 'update') {
            methodBadgeEl.classList.add('bg-amber-100', 'text-amber-700', 'dark:bg-amber-900/30', 'dark:text-amber-400');
            methodDisplay = 'Update';
        } else if (activity.method === 'delete') {
            methodBadgeEl.classList.add('bg-red-100', 'text-red-700', 'dark:bg-red-900/30', 'dark:text-red-400');
            methodDisplay = 'Delete';
        } else if (activity.method === 'read') {
            methodBadgeEl.classList.add('bg-blue-100', 'text-blue-700', 'dark:bg-blue-900/30', 'dark:text-blue-400');
            methodDisplay = 'Viewed';
        }

        methodBadgeEl.textContent = methodDisplay;
    } else {
        methodBadgeEl.classList.add('hidden');
    }

    // Handle changes section
    const changesEl = activityEl.querySelector('.activity-changes');
    const changesContentEl = activityEl.querySelector('.activity-changes-content');
    const closeChangesBtn = activityEl.querySelector('.close-changes-btn');

    // Show changes if available
    if (activity.has_changes && activity.old_values && activity.new_values) {
        // Initially hide changes section
        changesEl.classList.add('hidden');

        // Render each change
        for (const field in activity.new_values) {
            if (activity.old_values.hasOwnProperty(field)) {
                const changeEl = createChangeElement(
                    field,
                    activity.old_values[field],
                    activity.new_values[field]
                );
                changesContentEl.appendChild(changeEl);
            }
        }

        // Add alternating background colors to change items
        const changeItems = changesContentEl.querySelectorAll('.activity-change-item');
        changeItems.forEach((item, index) => {
            if (index % 2 === 0) {
                item.classList.add('bg-muted/30');
            } else {
                item.classList.add('bg-muted/10');
            }
        });

        // Set up close button for changes section
        closeChangesBtn.addEventListener('click', () => {
            changesEl.classList.add('hidden');
            // Update view details button text
            const viewDetailsBtn = activityEl.querySelector('.view-details-btn');
            if (viewDetailsBtn) {
                viewDetailsBtn.querySelector('span').textContent = 'View Changes';
                viewDetailsBtn.querySelector('[data-lucide]').setAttribute('data-lucide', 'eye');
                // Re-initialize Lucide icons
                if (window.lucide) {
                    lucide.createIcons();
                }
            }
        });
    }

    // Show action buttons if applicable
    const actionsEl = activityEl.querySelector('.activity-actions');
    const viewDetailsBtn = activityEl.querySelector('.view-details-btn');
    const viewEntityBtn = activityEl.querySelector('.view-entity-btn');

    // Only show action buttons if we have entity data or changes
    if (activity.has_changes || activity.entity_url) {
        actionsEl.classList.remove('hidden');

        // Set up view details button
        if (activity.has_changes) {
            viewDetailsBtn.addEventListener('click', () => {
                // Toggle changes visibility
                if (changesEl.classList.contains('hidden')) {
                    changesEl.classList.remove('hidden');
                    viewDetailsBtn.querySelector('span').textContent = 'Hide Changes';
                    viewDetailsBtn.querySelector('[data-lucide]').setAttribute('data-lucide', 'eye-off');
                } else {
                    changesEl.classList.add('hidden');
                    viewDetailsBtn.querySelector('span').textContent = 'View Changes';
                    viewDetailsBtn.querySelector('[data-lucide]').setAttribute('data-lucide', 'eye');
                }

                // Re-initialize Lucide icons
                if (window.lucide) {
                    lucide.createIcons();
                }
            });
        } else {
            viewDetailsBtn.classList.add('hidden');
        }

        // Set up view entity button
        if (activity.entity_url) {
            viewEntityBtn.addEventListener('click', () => {
                window.location.href = activity.entity_url;
            });
        } else {
            viewEntityBtn.classList.add('hidden');
        }
    }

    return activityEl;
}

function createChangeElement(field, oldValue, newValue) {
    // Clone the template
    const template = document.getElementById('entity-activity-change-template');
    const changeEl = template.content.cloneNode(true).querySelector('div');

    // Format field name
    const fieldName = field.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());

    // Set field name
    changeEl.querySelector('.change-field span').textContent = fieldName;

    // Set old value
    const oldValueEl = changeEl.querySelector('.change-old div:last-child');
    oldValueEl.textContent = formatValue(oldValue);

    // Set tooltip for long values
    if (String(formatValue(oldValue)).length > 50) {
        oldValueEl.title = formatValue(oldValue);
    }

    // Set new value
    const newValueEl = changeEl.querySelector('.change-new div:last-child');
    newValueEl.textContent = formatValue(newValue);

    // Set tooltip for long values
    if (String(formatValue(newValue)).length > 50) {
        newValueEl.title = formatValue(newValue);
    }

    // Highlight changes
    if (oldValue !== newValue) {
        if (newValue === null || newValue === '' || newValue === false) {
            // Value was removed
            newValueEl.classList.add('text-red-500', 'dark:text-red-400');
            changeEl.querySelector('.change-new').classList.add('bg-red-50/20', 'dark:bg-red-900/10');
        } else if (oldValue === null || oldValue === '' || oldValue === false) {
            // Value was added
            newValueEl.classList.add('text-green-500', 'dark:text-green-400');
            changeEl.querySelector('.change-new').classList.add('bg-green-50/20', 'dark:bg-green-900/10');
        } else {
            // Value was changed
            newValueEl.classList.add('text-amber-500', 'dark:text-amber-400');
            changeEl.querySelector('.change-new').classList.add('bg-amber-50/20', 'dark:bg-amber-900/10');
        }
    }

    return changeEl;
}

function formatValue(value) {
    if (value === null || value === undefined) {
        return 'None';
    } else if (value === '') {
        return 'Empty';
    } else if (typeof value === 'boolean') {
        return value ? 'Yes' : 'No';
    } else {
        return String(value);
    }
}

function formatTimeAgo(date) {
    const now = new Date();
    const diffMs = now - date;
    const diffSec = Math.floor(diffMs / 1000);
    const diffMin = Math.floor(diffSec / 60);
    const diffHour = Math.floor(diffMin / 60);
    const diffDay = Math.floor(diffHour / 24);

    if (diffSec < 60) {
        return 'just now';
    } else if (diffMin < 60) {
        return `${diffMin} minute${diffMin > 1 ? 's' : ''} ago`;
    } else if (diffHour < 24) {
        return `${diffHour} hour${diffHour > 1 ? 's' : ''} ago`;
    } else if (diffDay < 30) {
        return `${diffDay} day${diffDay > 1 ? 's' : ''} ago`;
    } else {
        return date.toLocaleDateString();
    }
}


</script>
{% endmacro %}

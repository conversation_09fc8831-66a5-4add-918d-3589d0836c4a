/**
 * Lucide Icons Initialization - Legacy Support
 * This script provides backward compatibility with the new optimized icon system
 */

// Helper function for error suppression
function withSuppressedLucideErrors(callback) {
  if (typeof window.withSuppressedErrors === 'function') {
    return window.withSuppressedErrors(callback);
  } else {
    // Fallback error suppression
    const originalConsoleError = console.error;
    console.error = function(msg) {
      if (typeof msg === 'string' && msg.includes('icon name was not found in the provided icons object')) {
        return;
      }
      originalConsoleError.apply(console, arguments);
    };

    try {
      return callback();
    } finally {
      console.error = originalConsoleError;
    }
  }
}

// No automatic initialization - this is now handled by the optimized icon system

/**
 * Create a Lucide icon element - Legacy support
 * @param {string} name - The name of the icon
 * @param {Object} options - Options for the icon
 * @returns {HTMLElement} - The icon element
 */
function createLucideIcon(name, options = {}) {
  // Use the global icon utils if available
  if (window.iconUtils && window.iconUtils.createIcon) {
    return window.iconUtils.createIcon(name, options);
  }

  // Fallback to the old method
  const {
    class: className = '',
    size = 24,
    color = 'currentColor',
    stroke = 2
  } = options;

  // Create the icon element
  const iconElement = document.createElement('i');
  iconElement.setAttribute('data-lucide', name);

  if (className) {
    iconElement.className = className;
  }

  if (size !== 24) {
    iconElement.style.width = `${size}px`;
    iconElement.style.height = `${size}px`;
  }

  if (color !== 'currentColor') {
    iconElement.style.color = color;
  }

  if (stroke !== 2) {
    iconElement.setAttribute('stroke-width', stroke);
  }

  // Initialize the icon with error handling
  if (typeof lucide !== 'undefined' && lucide.createIcons) {
    withSuppressedLucideErrors(() => {
      lucide.createIcons({
        elements: [iconElement]
      });
    });
  }

  return iconElement;
}

/**
 * Replace an element with a Lucide icon - Legacy support
 * @param {HTMLElement} element - The element to replace
 * @param {string} name - The name of the icon
 * @param {Object} options - Options for the icon
 * @returns {HTMLElement} - The icon element
 */
function replaceLucideIcon(element, name, options = {}) {
  const icon = createLucideIcon(name, options);
  element.parentNode.replaceChild(icon, element);
  return icon;
}

/**
 * Initialize Lucide icons in a specific container - Legacy support
 * @param {HTMLElement} container - The container element
 */
function initLucideIcons(container = document) {
  // Use the global icon utils if available
  if (window.iconUtils && window.iconUtils.initIconsInContainer) {
    window.iconUtils.initIconsInContainer(container);
    return;
  }

  // Fallback to the old method
  if (typeof lucide !== 'undefined' && lucide.createIcons) {
    withSuppressedLucideErrors(() => {
      lucide.createIcons({
        root: container
      });
    });
  }
}

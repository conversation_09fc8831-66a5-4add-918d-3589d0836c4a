/** @type {import('tailwindcss').Config} */
module.exports = {
    darkMode: "class",
    content: ["./app/templates/**/*.html", "./app/static/**/*.js", "*.{js,ts,jsx,tsx,mdx}"],
    theme: {
      container: {
        center: true,
        padding: "2rem",
        screens: {
          "2xl": "1400px",
        },
      },
      extend: {
        colors: {
          border: "hsl(var(--border))",
          input: "hsl(var(--input))",
          ring: "hsl(var(--ring))",
          background: "hsl(var(--background))",
          foreground: "hsl(var(--foreground))",
          primary: {
            DEFAULT: "hsl(var(--primary))",
            foreground: "hsl(var(--primary-foreground))",
          },
          secondary: {
            DEFAULT: "hsl(var(--secondary))",
            foreground: "hsl(var(--secondary-foreground))",
          },
          destructive: {
            DEFAULT: "hsl(var(--destructive))",
            foreground: "hsl(var(--destructive-foreground))",
          },
          muted: {
            DEFAULT: "hsl(var(--muted))",
            foreground: "hsl(var(--muted-foreground))",
          },
          accent: {
            DEFAULT: "hsl(var(--accent))",
            foreground: "hsl(var(--accent-foreground))",
          },
          popover: {
            DEFAULT: "hsl(var(--popover))",
            foreground: "hsl(var(--popover-foreground))",
          },
          card: {
            DEFAULT: "hsl(var(--card))",
            foreground: "hsl(var(--card-foreground))",
          },
          sidebar: {
            background: "hsl(var(--sidebar-background))",
            foreground: "hsl(var(--sidebar-foreground))",
            muted: "hsl(var(--sidebar-muted))",
            "muted-foreground": "hsl(var(--sidebar-muted-foreground))",
            accent: "hsl(var(--sidebar-accent))",
            "accent-foreground": "hsl(var(--sidebar-accent-foreground))",
            border: "hsl(var(--sidebar-border))",
            ring: "hsl(var(--sidebar-ring))",
          },
        },
        borderRadius: {
          lg: "var(--radius)",
          md: "calc(var(--radius) - 2px)",
          sm: "calc(var(--radius) - 4px)",
        },
        keyframes: {
          "accordion-down": {
            from: { height: 0 },
            to: { height: "var(--radix-accordion-content-height)" },
          },
          "accordion-up": {
            from: { height: "var(--radix-accordion-content-height)" },
            to: { height: 0 },
          },
          "fade-in": {
            from: { opacity: 0 },
            to: { opacity: 1 },
          },
          "fade-out": {
            from: { opacity: 1 },
            to: { opacity: 0 },
          },
          "slide-in-right": {
            from: { transform: "translateX(100%)" },
            to: { transform: "translateX(0)" },
          },
          "slide-out-right": {
            from: { transform: "translateX(0)" },
            to: { transform: "translateX(100%)" },
          },
          "pulse-subtle": {
            "0%, 100%": { opacity: 1 },
            "50%": { opacity: 0.8 },
          },
        },
        animation: {
          "accordion-down": "accordion-down 0.2s ease-out",
          "accordion-up": "accordion-up 0.2s ease-out",
          "fade-in": "fade-in 0.3s ease-out",
          "fade-out": "fade-out 0.3s ease-out",
          "slide-in-right": "slide-in-right 0.3s ease-out",
          "slide-out-right": "slide-out-right 0.3s ease-out",
          "pulse-subtle": "pulse-subtle 2s ease-in-out infinite",
        },
        boxShadow: {
          soft: "0 2px 10px rgba(0, 0, 0, 0.05)",
          "soft-md": "0 4px 20px rgba(0, 0, 0, 0.08)",
          "soft-lg": "0 10px 30px rgba(0, 0, 0, 0.1)",
        },
      },
    },
    plugins: [require("tailwindcss-animate")],
  }

/**
 * <PERSON>ton styles with dynamic color handling
 * These styles are difficult to implement with <PERSON><PERSON><PERSON> alone due to the dynamic color values
 */

/* Base button styles */
.btn-primary, .page-header-button {
  background-color: var(--button-bg) !important;
  color: var(--button-text) !important;
  border-color: var(--button-bg) !important;
  transition: background-color 0.2s, opacity 0.2s;
}

/* Specific styles for page header buttons */
.flex.justify-between.items-center > button.btn-primary,
button[onclick*="drawerManager.openForm('user')"] {
  background-color: var(--button-bg) !important;
  color: var(--button-text) !important;
  border-color: var(--button-bg) !important;
}

/* Hover effects for neutral colors */
.neutral-primary .btn-primary:hover, 
.neutral-primary .page-header-button:hover, 
.neutral-primary .flex.justify-between.items-center > button.btn-primary:hover,
.neutral-primary button[onclick*="drawerManager.openForm('user')"]:hover {
  background-color: var(--button-hover-bg) !important;
  border-color: var(--button-hover-bg) !important;
}

/* Hover effects for non-neutral colors */
:not(.neutral-primary) .btn-primary:hover, 
:not(.neutral-primary) .page-header-button:hover, 
:not(.neutral-primary) .flex.justify-between.items-center > button.btn-primary:hover,
:not(.neutral-primary) button[onclick*="drawerManager.openForm('user')"]:hover {
  opacity: var(--button-hover-opacity) !important;
}

/* Text primary color */
.text-primary {
  color: var(--primary-hex);
}

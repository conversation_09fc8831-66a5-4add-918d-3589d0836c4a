"""
User model and related functionality.
"""

from flask_login import UserMixin
from werkzeug.security import generate_password_hash, check_password_hash
from datetime import datetime
import re
from typing import List, Optional, Dict, Any, Union

# Import from app package
from app import db
from app.models import PH_TZ


class WeakPasswordError(ValueError):
    """Raised when a password doesn't meet strength requirements."""
    pass


class User(UserMixin, db.Model):
    """User model for authentication and profile information."""
    __tablename__ = 'users'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False, index=True)
    password_hash = db.Column(db.String(255))
    role = db.Column(db.String(20), default='User')  # Admin, Manager, User
    _is_active = db.Column('is_active', db.<PERSON>, default=True)
    created_at = db.Column(db.DateTime, default=lambda: datetime.now(PH_TZ))
    updated_at = db.Column(db.DateTime, default=lambda: datetime.now(PH_TZ), onupdate=lambda: datetime.now(PH_TZ))
    last_login = db.Column(db.DateTime, nullable=True)
    last_password_date = db.Column(db.DateTime, default=lambda: datetime.now(PH_TZ), nullable=True)
    bio = db.Column(db.Text, nullable=True)
    timezone = db.Column(db.String(50), default='Asia/Manila', nullable=True)
    avatar = db.Column(db.String(255), nullable=True)
    deleted_at = db.Column(db.DateTime, nullable=True)

    # Relationships
    employee_detail = db.relationship('EmployeeDetail',
                                  foreign_keys='EmployeeDetail.user_id',
                                  uselist=False,
                                  cascade='all, delete-orphan',
                                  primaryjoin="User.id == EmployeeDetail.user_id",
                                  back_populates="user",
                                  lazy='joined')
    managed_employees = db.relationship('EmployeeDetail',
                                      foreign_keys='EmployeeDetail.direct_manager_user_id',
                                      primaryjoin="User.id == EmployeeDetail.direct_manager_user_id",
                                      lazy='dynamic')
    activities = db.relationship('Activity', backref='user', lazy='dynamic',
                                cascade='all, delete-orphan')
    managed_units = db.relationship('BusinessUnit', foreign_keys='BusinessUnit.manager_id',
                                  backref='manager', lazy='dynamic')
    managed_segments = db.relationship('BusinessSegment', foreign_keys='BusinessSegment.manager_id',
                                     backref='manager', lazy='dynamic')

    def __repr__(self) -> str:
        return f'<User {self.name}>'

    def set_password(self, password: str) -> None:
        """Hash and set the user password."""
        from app.utils.settings import get_setting

        # Get password requirements from settings
        min_length = get_setting('min_password_length', 8)
        requires_uppercase = get_setting('password_requires_uppercase', True)
        requires_number = get_setting('password_requires_number', True)
        requires_special = get_setting('password_requires_special', False)

        # Validate password strength
        if not self._is_password_strong(password):
            # Build a dynamic error message based on actual requirements
            error_msg = f"Password must be at least {min_length} characters and contain at least one letter"
            requirements = []

            if requires_uppercase:
                requirements.append("one uppercase letter")
            if requires_number:
                requirements.append("one number")
            if requires_special:
                requirements.append("one special character")

            if requirements:
                error_msg += " and " + ", ".join(requirements)

            raise WeakPasswordError(error_msg)

        self.password_hash = generate_password_hash(password)
        self.last_password_date = datetime.now(PH_TZ)

    def check_password(self, password: str) -> bool:
        """Verify the user password."""
        if not self.password_hash:
            return False
        return check_password_hash(self.password_hash, password)

    def get_days_since_password_change(self) -> int:
        """Get the number of days since the last password change."""
        if not self.last_password_date:
            return 0

        # Make sure we're comparing timezone-aware datetimes
        if self.last_password_date.tzinfo is None:
            # If last_password_date is naive, make it aware using PH_TZ
            aware_last_password_date = PH_TZ.localize(self.last_password_date)
        else:
            aware_last_password_date = self.last_password_date

        return (datetime.now(PH_TZ) - aware_last_password_date).days

    def get_password_status(self) -> str:
        """Get the status of the password based on its age."""
        days = self.get_days_since_password_change()
        if days > 90:  # More than 3 months
            return 'expired'
        elif days > 60:  # More than 2 months
            return 'warning'
        return 'good'

    @classmethod
    def _is_password_strong(cls, password: str) -> bool:
        """Check if password meets strength requirements based on system settings."""
        from app.utils.settings import get_setting

        # Get password requirements from settings
        min_length = get_setting('min_password_length', 8)
        requires_uppercase = get_setting('password_requires_uppercase', True)
        requires_number = get_setting('password_requires_number', True)
        requires_special = get_setting('password_requires_special', False)

        # Check minimum length
        if len(password) < min_length:
            return False

        # Always require at least one letter (lowercase or uppercase)
        has_letter = re.search(r'[a-zA-Z]', password) is not None
        if not has_letter:
            return False

        # Check for uppercase if required
        if requires_uppercase:
            has_uppercase = re.search(r'[A-Z]', password) is not None
            if not has_uppercase:
                return False

        # Check for number if required
        if requires_number:
            has_number = re.search(r'\d', password) is not None
            if not has_number:
                return False

        # Check for special character if required
        if requires_special:
            has_special = re.search(r'[!@#$%^&*(),.?":{}|<>]', password) is not None
            if not has_special:
                return False

        return True

    @property
    def is_admin(self) -> bool:
        """Check if user has admin role."""
        return self.role == 'Admin'

    @property
    def is_manager(self) -> bool:
        """Check if user has manager role."""
        return self.role == 'Manager'

    @property
    def is_active(self) -> bool:
        """Override UserMixin.is_active to check for soft deletion."""
        return self._is_active and not self.is_deleted

    @is_active.setter
    def is_active(self, value: bool) -> None:
        """Setter for is_active property."""
        self._is_active = value

    @property
    def is_deleted(self) -> bool:
        """Check if user has been soft-deleted."""
        return self.deleted_at is not None

    def soft_delete(self) -> None:
        """Soft delete the user instead of permanent deletion."""
        self.deleted_at = datetime.now(PH_TZ)
        self.is_active = False
        db.session.commit()

    @property
    def employee_details(self) -> 'EmployeeDetailsProxy':
        """Access employee details with proper defaults to avoid None errors."""
        if not hasattr(self, '_employee_details_proxy'):
            self._employee_details_proxy = EmployeeDetailsProxy(self.employee_detail)
        else:
            # Update the proxy with the current employee_detail reference
            self._employee_details_proxy._update_reference(self.employee_detail)
        return self._employee_details_proxy

    def get_recent_activities(self, limit: int = 10) -> List['Activity']:
        """Get the user's most recent activities."""
        return self.activities.order_by(Activity.created_at.desc()).limit(limit).all()

    def to_dict(self, include_details: bool = True) -> Dict[str, Any]:
        """Serialize user object to dictionary."""
        data = {
            'id': self.id,
            'name': self.name,
            'email': self.email,
            'role': self.role,
            'is_active': self.is_active,
            'last_login': self.last_login.isoformat() if self.last_login else None,
            'created_at': self.created_at.isoformat(),
        }

        if include_details and self.employee_detail:
            data['employee_details'] = {
                'position': self.employee_details.position,
                'phone': self.employee_details.phone,
                'business_unit': self.employee_details.business_unit.name if self.employee_details.business_unit else None,
                'business_segment': self.employee_details.business_segment.name if self.employee_details.business_segment else None,
            }

        return data

    @classmethod
    def get_by_email(cls, email: str) -> Optional['User']:
        """Get a user by email with efficient query."""
        return cls.query.filter_by(email=email).first()

    @classmethod
    def get_active_users_by_unit(cls, business_unit_id: int) -> List['User']:
        """Get all active users in a business unit with optimized query."""
        from app.models.employee import EmployeeDetail
        return cls.query.join(EmployeeDetail, User.id == EmployeeDetail.user_id).filter(
            EmployeeDetail.business_unit_id == business_unit_id,
            User._is_active == True,
            User.deleted_at == None
        ).all()


class EmployeeDetailsProxy:
    """A proxy class for safely accessing employee details even when they don't exist."""

    def __init__(self, employee_detail: Optional['EmployeeDetail']):
        self._employee_detail = employee_detail
        # Force cache refresh on initialization
        self._refresh_cache()

    def _refresh_cache(self):
        """Refresh cached property values"""
        # This ensures we always have the latest values
        if hasattr(self, '_cache'):
            delattr(self, '_cache')

    def _update_reference(self, employee_detail: Optional['EmployeeDetail']):
        """Update the reference to the employee_detail object."""
        self._employee_detail = employee_detail
        self._refresh_cache()

    @property
    def exists(self) -> bool:
        """Check if employee details exist for this user."""
        return self._employee_detail is not None

    @property
    def job_title(self) -> str:
        """Get employee job title with fallback."""
        return self._employee_detail.job_title if self._employee_detail else ''

    @property
    def position(self) -> str:
        """Get employee position with fallback (alias for job_title for backward compatibility)."""
        return self.job_title

    @property
    def employee_number(self) -> str:
        """Get employee number with fallback."""
        return self._employee_detail.employee_number if self._employee_detail else ''

    @property
    def phone(self) -> str:
        """Get employee phone with fallback."""
        return self._employee_detail.phone if self._employee_detail else ''

    @property
    def bio(self) -> str:
        """Get employee bio with fallback."""
        if self._employee_detail and self._employee_detail.user:
            return self._employee_detail.user.bio or ''
        return ''

    @property
    def avatar(self) -> str:
        """Get employee avatar with fallback."""
        if self._employee_detail and self._employee_detail.user:
            return self._employee_detail.user.avatar or ''
        return ''

    @property
    def business_unit_id(self) -> Optional[int]:
        """Get employee business unit ID with fallback."""
        return self._employee_detail.business_unit_id if self._employee_detail else None

    @property
    def business_segment_id(self) -> Optional[int]:
        """Get employee business segment ID with fallback."""
        return self._employee_detail.business_segment_id if self._employee_detail else None

    @property
    def hire_date(self) -> Optional[datetime]:
        """Get employee hire date with fallback."""
        return self._employee_detail.hire_date if self._employee_detail else None

    @property
    def business_unit(self) -> Optional['BusinessUnit']:
        """Get employee business unit with fallback."""
        return self._employee_detail.business_unit if self._employee_detail else None

    @property
    def business_segment(self) -> Optional['BusinessSegment']:
        """Get employee business segment with fallback."""
        return self._employee_detail.business_segment if self._employee_detail else None

    @property
    def teams(self) -> List['Team']:
        """Get employee teams with fallback."""
        return self._employee_detail.teams if self._employee_detail else []

    @property
    def first_name(self) -> str:
        """Get employee first name with fallback."""
        return self._employee_detail.first_name if self._employee_detail else ''

    @property
    def middle_name(self) -> str:
        """Get employee middle name with fallback."""
        return self._employee_detail.middle_name if self._employee_detail else ''

    @property
    def last_name(self) -> str:
        """Get employee last name with fallback."""
        return self._employee_detail.last_name if self._employee_detail else ''

    @property
    def legal_name(self) -> str:
        """Get employee legal name with fallback."""
        return self._employee_detail.legal_name if self._employee_detail else ''

    @property
    def emp_type(self) -> str:
        """Get employee type with fallback."""
        return self._employee_detail.emp_type if self._employee_detail else ''

    @property
    def enterprise_id(self) -> str:
        """Get employee enterprise ID with fallback."""
        return self._employee_detail.enterprise_id if self._employee_detail else ''

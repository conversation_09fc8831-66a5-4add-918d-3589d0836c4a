"""
Utility functions for managing application settings.
"""
from app.models import Setting, db
from typing import Any, Dict, List, Optional, Union # Ensure Optional is here
import json
from sqlalchemy.exc import OperationalError # Import OperationalError

def get_setting(key: str, default: Any = None) -> Any:
    """
    Get a setting value by key.

    Args:
        key: The setting key
        default: Default value if setting doesn't exist

    Returns:
        The setting value converted to its appropriate type
    """
    # Use a fresh query to avoid caching issues
    db.session.expire_all()
    setting = db.session.query(Setting).filter_by(key=key).first()
    if not setting:
        return default
    return setting.typed_value

def get_setting_safe(key: str, default: Any = None) -> Any:
    """
    Safely get a setting value by key, handles case where settings table doesn't exist yet.
    Use this version during app initialization and migrations.

    Args:
        key: The setting key
        default: Default value if setting doesn't exist or table doesn't exist

    Returns:
        The setting value converted to its appropriate type, or default if any error
    """
    try:
        # First check if the table exists
        try:
            db.session.execute(db.text('SELECT 1 FROM settings LIMIT 1'))
        except Exception:
            # Table doesn't exist, return default
            return default

        # If we get here, try to get the setting normally
        return get_setting(key, default)
    except Exception:
        # Any other error, return default
        return default

def set_setting(key: str, value: Any, type_name: Optional[str] = None, description: Optional[str] = None, is_public: bool = False) -> Setting:
    """
    Set a setting value.

    Args:
        key: The setting key
        value: The setting value
        type_name: The setting type (string, integer, float, boolean, json)
        description: Optional description of the setting
        is_public: Whether this setting is accessible to non-admin users

    Returns:
        The Setting object
    """
    # Convert value to string based on type
    if value is None:
        str_value = None
    elif isinstance(value, bool):
        str_value = 'true' if value else 'false'
        type_name = type_name or Setting.TYPE_BOOLEAN
    elif isinstance(value, int):
        str_value = str(value)
        type_name = type_name or Setting.TYPE_INTEGER
    elif isinstance(value, float):
        str_value = str(value)
        type_name = type_name or Setting.TYPE_FLOAT
    elif isinstance(value, (dict, list)):
        str_value = json.dumps(value)
        type_name = type_name or Setting.TYPE_JSON
    else:
        str_value = str(value)
        type_name = type_name or Setting.TYPE_STRING

    # Get or create setting - use a fresh query to avoid caching issues
    db.session.expire_all()
    setting = db.session.query(Setting).filter_by(key=key).first()

    if setting:
        setting.value = str_value
        if type_name:
            setting.type = type_name
        if description:
            setting.description = description
        if is_public is not None:
            setting.is_public = is_public
    else:
        setting = Setting()
        setting.key = key
        setting.value = str_value
        setting.type = type_name or Setting.TYPE_STRING
        setting.description = description
        setting.is_public = is_public
        db.session.add(setting)

    # Flush changes to the database but don't commit yet
    # This allows the caller to commit multiple settings at once
    db.session.flush()
    return setting

def get_all_settings(include_private: bool = False) -> Dict[str, Any]:
    """
    Get all settings as a dictionary.

    Args:
        include_private: Whether to include private settings

    Returns:
        Dictionary of setting keys and values
    """
    # Use a fresh query to avoid caching issues
    db.session.expire_all()

    query = db.session.query(Setting)
    if not include_private:
        query = query.filter_by(is_public=True)

    settings = {}
    for setting in query.all():
        settings[setting.key] = setting.typed_value

    return settings

def delete_setting(key: str) -> bool:
    """
    Delete a setting.

    Args:
        key: The setting key

    Returns:
        True if setting was deleted, False otherwise
    """
    # Use a fresh query to avoid caching issues
    db.session.expire_all()
    setting = db.session.query(Setting).filter_by(key=key).first()
    if not setting:
        return False

    db.session.delete(setting)
    db.session.commit()
    return True

def initialize_default_settings():
    """Initialize default settings if they don't exist."""
    defaults = [
        # General settings
        {
            'key': 'site_name',
            'value': 'Matrix App',
            'type': Setting.TYPE_STRING,
            'description': 'The name of the site',
            'is_public': True
        },
        {
            'key': 'admin_email',
            'value': '<EMAIL>',
            'type': Setting.TYPE_STRING,
            'description': 'The email address of the administrator',
            'is_public': False
        },
        {
            'key': 'site_description',
            'value': 'A powerful admin dashboard for managing your application.',
            'type': Setting.TYPE_STRING,
            'description': 'A brief description of the site',
            'is_public': True
        },

        # Security settings
        {
            'key': 'min_password_length',
            'value': '8',
            'type': Setting.TYPE_INTEGER,
            'description': 'Minimum password length',
            'is_public': True
        },
        {
            'key': 'max_login_attempts',
            'value': '5',
            'type': Setting.TYPE_INTEGER,
            'description': 'Maximum number of login attempts before account lockout',
            'is_public': False
        },
        {
            'key': 'password_requires_uppercase',
            'value': 'true',
            'type': Setting.TYPE_BOOLEAN,
            'description': 'Whether passwords require at least one uppercase letter',
            'is_public': True
        },
        {
            'key': 'password_requires_number',
            'value': 'true',
            'type': Setting.TYPE_BOOLEAN,
            'description': 'Whether passwords require at least one number',
            'is_public': True
        },
        {
            'key': 'password_requires_special',
            'value': 'false',
            'type': Setting.TYPE_BOOLEAN,
            'description': 'Whether passwords require at least one special character',
            'is_public': True
        },
        {
            'key': 'session_lifetime',
            'value': '7',
            'type': Setting.TYPE_INTEGER,
            'description': 'Number of days before users are required to log in again',
            'is_public': False
        },

        # User registration settings
        {
            'key': 'allow_registration',
            'value': 'true',
            'type': Setting.TYPE_BOOLEAN,
            'description': 'Whether to allow new user registrations',
            'is_public': True
        },
        {
            'key': 'require_email_verification',
            'value': 'true',
            'type': Setting.TYPE_BOOLEAN,
            'description': 'Whether to require email verification for new accounts',
            'is_public': True
        },
        {
            'key': 'require_admin_approval',
            'value': 'false',
            'type': Setting.TYPE_BOOLEAN,
            'description': 'Whether new accounts require admin approval',
            'is_public': True
        },
        {
            'key': 'default_user_role',
            'value': 'User',
            'type': Setting.TYPE_STRING,
            'description': 'Default role assigned to new users',
            'is_public': False
        },
        {
            'key': 'account_activation_expiry',
            'value': '24',
            'type': Setting.TYPE_INTEGER,
            'description': 'Hours before account activation links expire',
            'is_public': False
        },

        # UI settings
        {
            'key': 'default_theme',
            'value': 'light',
            'type': Setting.TYPE_STRING,
            'description': 'Default theme (light or dark)',
            'is_public': True
        },
        {
            'key': 'maintenance_mode',
            'value': 'false',
            'type': Setting.TYPE_BOOLEAN,
            'description': 'Whether the site is in maintenance mode',
            'is_public': True
        },
        {
            'key': 'primary_color',
            'value': '#0284c7',
            'type': Setting.TYPE_STRING,
            'description': 'Primary brand color (hex format)',
            'is_public': True
        },

        # System maintenance settings
        {
            'key': 'activity_log_retention_days',
            'value': '30',
            'type': Setting.TYPE_INTEGER,
            'description': 'Number of days to keep activity logs before automatic cleanup',
            'is_public': False
        },
        {
            'key': 'enable_automatic_maintenance',
            'value': 'true',
            'type': Setting.TYPE_BOOLEAN,
            'description': 'Whether to automatically run maintenance tasks on application startup',
            'is_public': False
        }
    ]

    # Make sure we have a fresh session
    db.session.expire_all()

    try:
        # First check if the settings table exists before trying to query it
        try:
            db.session.execute(db.text('SELECT 1 FROM settings LIMIT 1'))
        except Exception as table_error:
            print(f"Settings table does not exist yet, skipping initialization: {table_error}")
            db.session.rollback()
            return

        # Rest of your function...
        for setting_data in defaults:
            # Only create if it doesn't exist - use a fresh query
            existing_setting = db.session.query(Setting).filter_by(key=setting_data['key']).first()
            if not existing_setting:
                set_setting(
                    key=setting_data['key'],
                    value=setting_data['value'],
                    type_name=setting_data['type'],
                    description=setting_data['description'],
                    is_public=setting_data['is_public']
                )

        # Commit all changes
        db.session.commit()
    except Exception as e:
        db.session.rollback()
        print(f"Error initializing settings: {e}")
        # Re-raise if it's not a "no such table" error
        if not ('no such table' in str(e).lower() and 'settings' in str(e).lower()):
            raise

    try:
        for setting_data in defaults:
            # Only create if it doesn't exist - use a fresh query
            existing_setting = db.session.query(Setting).filter_by(key=setting_data['key']).first()
            if not existing_setting:
                set_setting(
                    key=setting_data['key'],
                    value=setting_data['value'],
                    type_name=setting_data['type'],
                    description=setting_data['description'],
                    is_public=setting_data['is_public']
                )

        # Commit all changes
        db.session.commit()
    except OperationalError as e:
        # This can happen if the database/tables haven't been created yet (e.g., during initial migration)
        # In such cases, we can't initialize settings, so we log and skip.
        # Check if the error message indicates "no such table" or similar for settings.
        if 'no such table' in str(e).lower() and 'settings' in str(e).lower():
            db.session.rollback() # Rollback any potential partial transaction
            print(f"Skipping settings initialization: Settings table does not exist yet. Error: {e}")
            # Optionally, log this to your app logger if available here
            # current_app.logger.warning("Skipping settings initialization: Settings table does not exist yet.")
        else:
            # If it's a different OperationalError, re-raise it
            db.session.rollback()
            raise

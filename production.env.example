# Production Environment Configuration Example
# Rename to production.env and fill in the values for production deployment

# Flask Configuration
FLASK_APP=run.py
FLASK_ENV=production
SECRET_KEY=your-very-secure-secret-key-here

# Database Configuration
# For SQLite (not recommended for production)
# DATABASE_URL=sqlite:///prod.db

# For PostgreSQL (recommended for production)
DATABASE_URL=postgresql://username:password@localhost:5432/matrix_app

# Redis Configuration (for caching and rate limiting)
REDIS_URL=redis://localhost:6379/0

# Mail Configuration
MAIL_SERVER=smtp.example.com
MAIL_PORT=587
MAIL_USE_TLS=true
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-email-password
MAIL_DEFAULT_SENDER=<EMAIL>

# Security Settings
JWT_SECRET_KEY=your-very-secure-jwt-secret-key-here

# Logging Configuration
LOG_LEVEL=ERROR
LOG_TO_STDOUT=false
LOG_FILE=/var/log/matrix_app/app.log

# Performance Settings
WORKERS=4
THREADS=2

# SSL/HTTPS Settings (if using a reverse proxy like Nginx)
PREFERRED_URL_SCHEME=https
SESSION_COOKIE_SECURE=true
REMEMBER_COOKIE_SECURE=true

# Rate Limiting
RATELIMIT_STORAGE_URL=redis://localhost:6379/0
RATELIMIT_STRATEGY=moving-window
RATELIMIT_DEFAULT=300 per day, 60 per hour, 10 per minute
RATELIMIT_KEY_PREFIX=matrix_app_prod_

# Caching
CACHE_TYPE=RedisCache
CACHE_REDIS_URL=redis://localhost:6379/1
CACHE_DEFAULT_TIMEOUT=300

# Application Settings
MAINTENANCE_MODE=off
ENABLE_REGISTRATION=false

# Pagination Configuration
PAGINATION_PER_PAGE=15
PAGINATION_PER_PAGE_ADMIN=25
PAGINATION_MAX_PER_PAGE=100

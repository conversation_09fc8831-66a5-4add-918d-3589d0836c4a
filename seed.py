#!/usr/bin/env python
"""
Seed script to initialize database with required data.
Run this after migrations to set up the database with initial data.
"""
import click
import os
import sys
import argparse
from app import create_app, db
from app.utils.settings import initialize_default_settings
from dotenv import load_dotenv

# Import all seeders from the seeders package
from seeders import (
    seed_users,
    seed_business_units,
    seed_business_segments,
    seed_teams,
    seed_settings,
    seed_attendance_types,
    seed_holidays,
    clear_database
)

# Load environment variables from .env file
load_dotenv()

# Create app with proper configuration
config_name = os.environ.get('FLASK_CONFIG', 'development')
app = create_app(config_name)

@click.group()
def cli():
    """Database seeding commands."""
    pass

@cli.command('settings')
def cmd_seed_settings():
    """Initialize default settings in the database."""
    with app.app_context():
        try:
            click.echo("Seeding default settings...")
            # Call the settings seeder
            seed_settings()
            click.echo("Default settings seeded successfully!")
        except Exception as e:
            click.echo(f"Error seeding settings: {e}")
            raise e

@cli.command('users')
def cmd_seed_users():
    """Seed user accounts in the database."""
    with app.app_context():
        try:
            click.echo("Seeding users...")
            seed_users()
            click.echo("Users seeded successfully!")
        except Exception as e:
            click.echo(f"Error seeding users: {e}")
            raise e

@cli.command('business')
def cmd_seed_business():
    """Seed business units and segments in the database."""
    with app.app_context():
        try:
            click.echo("Seeding business units...")
            seed_business_units()
            click.echo("Business units seeded successfully!")

            click.echo("Seeding business segments...")
            seed_business_segments()
            click.echo("Business segments seeded successfully!")
        except Exception as e:
            click.echo(f"Error seeding business data: {e}")
            raise e

@cli.command('teams')
def cmd_seed_teams():
    """Seed teams in the database."""
    with app.app_context():
        try:
            click.echo("Seeding teams...")
            seed_teams()
            click.echo("Teams seeded successfully!")
        except Exception as e:
            click.echo(f"Error seeding teams: {e}")
            raise e

@cli.command('attendance')
def cmd_seed_attendance():
    """Seed attendance types in the database."""
    with app.app_context():
        try:
            click.echo("Seeding attendance types...")
            seed_attendance_types()
            click.echo("Attendance types seeded successfully!")
        except Exception as e:
            click.echo(f"Error seeding attendance types: {e}")
            raise e

@cli.command('holidays')
def cmd_seed_holidays():
    """Seed holidays in the database."""
    with app.app_context():
        try:
            click.echo("Seeding holidays...")
            result = seed_holidays()
            click.echo(f"Holidays seeded successfully! Created: {result.get('created', 0)}, Updated: {result.get('updated', 0)}, Skipped: {result.get('skipped', 0)}")
        except Exception as e:
            click.echo(f"Error seeding holidays: {e}")
            raise e

@cli.command('all')
@click.option('--clear', '-c', is_flag=True, help='Clear the database before seeding')
@click.option('--yes', '-y', is_flag=True, help='Skip confirmation prompts')
def cmd_seed_all(clear, yes):
    """Run all seeders in the correct order."""
    with app.app_context():
        try:
            click.echo("Starting database seeding process...")

            # Clear database if requested
            if clear:
                click.echo("\n=== Clearing Database ===")
                if not clear_database(confirm=not yes):
                    click.echo("Database seeding aborted.")
                    return

            # Seed settings first
            click.echo("\n=== Seeding Settings ===")
            seed_settings()

            # Seed users
            click.echo("\n=== Seeding Users ===")
            seed_users()

            # Seed business units and segments
            click.echo("\n=== Seeding Business Units ===")
            seed_business_units()

            click.echo("\n=== Seeding Business Segments ===")
            seed_business_segments()

            # Seed teams
            click.echo("\n=== Seeding Teams ===")
            seed_teams()

            # Seed attendance types
            click.echo("\n=== Seeding Attendance Types ===")
            seed_attendance_types()

            # Seed holidays
            click.echo("\n=== Seeding Holidays ===")
            result = seed_holidays()
            click.echo(f"Created: {result.get('created', 0)}, Updated: {result.get('updated', 0)}, Skipped: {result.get('skipped', 0)}")

            click.echo("\nDatabase seeding completed successfully!")
        except Exception as e:
            click.echo(f"Error during seeding: {e}")
            raise e

@cli.command('clear')
@click.option('--yes', '-y', is_flag=True, help='Skip confirmation prompt')
def cmd_clear_db(yes):
    """Clear all data from the database."""
    with app.app_context():
        try:
            click.echo("Clearing database...")
            if clear_database(confirm=not yes):
                click.echo("Database cleared successfully!")
            else:
                click.echo("Database clearing aborted.")
        except Exception as e:
            click.echo(f"Error clearing database: {e}")
            raise e

def run_all_seeders(clear_first=False, skip_confirmation=False):
    """Run all seeder functions to populate the database.

    Args:
        clear_first (bool): Whether to clear the database before seeding
        skip_confirmation (bool): Whether to skip confirmation prompts
    """
    with app.app_context():
        try:
            print("Starting database seeding process...")

            # Clear database if requested
            if clear_first:
                print("\n=== Clearing Database ===")
                if not clear_database(confirm=not skip_confirmation):
                    print("Database seeding aborted.")
                    return

            # Seed settings first
            print("\n=== Seeding Settings ===")
            seed_settings()

            # Seed users
            print("\n=== Seeding Users ===")
            seed_users()

            # Seed business units and segments
            print("\n=== Seeding Business Units ===")
            seed_business_units()

            print("\n=== Seeding Business Segments ===")
            seed_business_segments()

            # Seed teams
            print("\n=== Seeding Teams ===")
            seed_teams()

            # Seed attendance types
            print("\n=== Seeding Attendance Types ===")
            seed_attendance_types()

            # Seed holidays
            print("\n=== Seeding Holidays ===")
            result = seed_holidays()
            print(f"Created: {result.get('created', 0)}, Updated: {result.get('updated', 0)}, Skipped: {result.get('skipped', 0)}")

            print("\nDatabase seeding completed successfully!")
        except Exception as e:
            print(f"Error during seeding: {e}")
            raise e

if __name__ == '__main__':
    # Support both Click CLI and traditional argparse
    if len(sys.argv) > 1 and sys.argv[1].startswith('-'):
        # If there are command line arguments starting with '-', use argparse
        parser = argparse.ArgumentParser(description='Seed the database with initial data.')
        parser.add_argument('--clear', '-c', action='store_true', help='Clear the database before seeding')
        parser.add_argument('--yes', '-y', action='store_true', help='Skip confirmation prompts')
        args = parser.parse_args()
        run_all_seeders(clear_first=args.clear, skip_confirmation=args.yes)
    else:
        # Otherwise use Click interface
        cli()

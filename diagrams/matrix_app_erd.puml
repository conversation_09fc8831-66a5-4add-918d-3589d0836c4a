@startuml Matrix App ERD

' Entity definitions
entity "User" as user {
  * id : Integer <<PK>>
  --
  * name : String
  * email : String <<Unique>>
  * password_hash : String
  * role : String
  * _is_active : Boolean
  * created_at : DateTime
  * updated_at : DateTime
  last_login : DateTime
  last_password_date : DateTime
  bio : Text
  deleted_at : DateTime
}

entity "EmployeeDetail" as employee {
  * id : Integer <<PK>>
  --
  * user_id : Integer <<FK>>
  employee_number : String <<Unique>>
  first_name : String
  middle_name : String
  last_name : String
  legal_name : String
  job_title : String
  phone : String
  emp_type : String
  enterprise_id : String
  manager_name : String
  job_code : String
  manager_level : String
  job_code_track_level : String
  business_unit_id : Integer <<FK>>
  business_segment_id : Integer <<FK>>
  hire_date : Date
  * emp_status : String
  * created_at : DateTime
  * updated_at : DateTime
}

entity "BusinessUnit" as business_unit {
  * id : Integer <<PK>>
  --
  * name : String
  * code : String <<Unique>>
  description : Text
  manager_id : Integer <<FK>>
  * is_active : Boolean
  * created_at : DateTime
  * updated_at : DateTime
}

entity "BusinessSegment" as business_segment {
  * id : Integer <<PK>>
  --
  * name : String
  * code : String <<Unique>>
  description : Text
  * business_unit_id : Integer <<FK>>
  manager_id : Integer <<FK>>
  * is_active : Boolean
  * created_at : DateTime
  * updated_at : DateTime
}

entity "Team" as team {
  * id : Integer <<PK>>
  --
  * name : String
  short_name : String
  * slug : String <<Unique>>
  description : Text
  * is_active : Boolean
  * created_at : DateTime
  * updated_at : DateTime
}

entity "TeamGroup" as team_group {
  * id : Integer <<PK>>
  --
  * team_id : Integer <<FK>>
  * name : String
  description : Text
  * is_active : Boolean
  * created_at : DateTime
  * updated_at : DateTime
}

entity "team_members" as team_members {
  * team_id : Integer <<PK, FK>>
  * employee_detail_id : Integer <<PK, FK>>
  * created_at : DateTime
}

entity "Activity" as activity {
  * id : Integer <<PK>>
  --
  * user_id : Integer <<FK>>
  * action : String
  entity_type : String
  entity_id : Integer
  details : Text
  ip_address : String
  user_agent : String
  url : String
  severity : String
  category : String
  method : String
  old_values : Text
  new_values : Text
  * created_at : DateTime
}

entity "Message" as message {
  * id : Integer <<PK>>
  --
  * sender_id : Integer <<FK>>
  * recipient_id : Integer <<FK>>
  * subject : String
  body : Text
  * is_read : Boolean
  * created_at : DateTime
}

entity "PasswordReset" as password_reset {
  * id : Integer <<PK>>
  --
  * email : String
  * token : String <<Unique>>
  * created_at : DateTime
  * expires_at : DateTime
  * is_used : Boolean
}

entity "Setting" as setting {
  * id : Integer <<PK>>
  --
  * key : String <<Unique>>
  value : Text
  * type : String
  description : String
  * is_public : Boolean
  * created_at : DateTime
  * updated_at : DateTime
}

' Relationships
user ||--o| employee : has
user ||--o{ activity : performs
user ||--o{ message : sends >
user ||--o{ message : receives >
user ||--o{ business_unit : manages >
user ||--o{ business_segment : manages >

business_unit ||--o{ business_segment : contains
business_unit ||--o{ employee : employs
business_segment ||--o{ employee : employs

team ||--o{ team_group : contains
team }|--o{ team_members : has
employee }|--o{ team_members : belongs to

@enduml

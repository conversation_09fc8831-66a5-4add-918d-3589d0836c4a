{% extends "base.html" %}

{% from "components/page_header.html" import page_header %}
{% from "components/table_header.html" import table_header %}
{% from "components/simple_table.html" import simple_table %}
{% from "components/form_select.html" import form_select, form_select_with_label %}
{% from "admin/partials/log_management_modal.html" import log_management_modal %}
{% from "admin/partials/activity_details.html" import activity_details %}
{% from "components/modal.html" import modal %}

{% block title %}Activity Logs{% endblock %}

{% block header %}Activity Logs{% endblock %}

{% block styles %}
<style>
  /* Hide browser's default date picker icon */
  input[type="date"]::-webkit-calendar-picker-indicator {
    display: none;
    -webkit-appearance: none;
  }

  /* Custom background for warning rows */
  tr.border-b.border-border:has(.badge-warning) {
    background-color: rgba(249, 115, 22, 0.05);
  }
</style>
{% endblock %}

{% block content %}
{{ log_management_modal(activity_categories) }}

<div class="space-y-6">
  {{ page_header(
    title="Activity Logs",
    description="View and analyze user activities in the system",
    button_text="Manage Logs",
    button_icon="settings",
    button_action="openLogManagementModal()"
  ) }}

  <!-- Quick Filters -->
  <div class="flex flex-wrap gap-2 mb-4">
    <a href="{{ url_for('admin.activities') }}"
       class="inline-flex items-center px-3 py-1.5 rounded-md text-sm {% if not request.args %}bg-primary text-white{% else %}bg-muted text-foreground hover:bg-muted/80{% endif %}">
      <i data-lucide="list" class="h-3.5 w-3.5 mr-1.5"></i>
      All Activities
    </a>
    <a href="{{ url_for('admin.activities', severity='error') }}"
       class="inline-flex items-center px-3 py-1.5 rounded-md text-sm {% if request.args.get('severity') == 'error' %}bg-destructive text-white{% else %}bg-muted text-foreground hover:bg-muted/80{% endif %}">
      <i data-lucide="alert-triangle" class="h-3.5 w-3.5 mr-1.5"></i>
      Errors
    </a>
    <a href="{{ url_for('admin.activities', severity='warning') }}"
       class="inline-flex items-center px-3 py-1.5 rounded-md text-sm {% if request.args.get('severity') == 'warning' %}bg-orange-500 text-white{% else %}bg-muted text-foreground hover:bg-muted/80{% endif %}">
      <i data-lucide="alert-circle" class="h-3.5 w-3.5 mr-1.5"></i>
      Warnings
    </a>
    <a href="{{ url_for('admin.activities', category='auth') }}"
       class="inline-flex items-center px-3 py-1.5 rounded-md text-sm {% if request.args.get('category') == 'auth' %}bg-primary text-white{% else %}bg-muted text-foreground hover:bg-muted/80{% endif %}">
      <i data-lucide="key" class="h-3.5 w-3.5 mr-1.5"></i>
      Auth Activities
    </a>
    <a href="{{ url_for('admin.activities', user_id=current_user.id) }}"
       class="inline-flex items-center px-3 py-1.5 rounded-md text-sm {% if request.args.get('user_id')|int == current_user.id %}bg-primary text-white{% else %}bg-muted text-foreground hover:bg-muted/80{% endif %}">
      <i data-lucide="user" class="h-3.5 w-3.5 mr-1.5"></i>
      My Activities
    </a>
    <a href="{{ url_for('admin.activities') }}?date_from={{ today_date }}"
       class="inline-flex items-center px-3 py-1.5 rounded-md text-sm {% if request.args.get('date_from') == today_date %}bg-primary text-white{% else %}bg-muted text-foreground hover:bg-muted/80{% endif %}">
      <i data-lucide="calendar" class="h-3.5 w-3.5 mr-1.5"></i>
      Today
    </a>
  </div>

  <!-- Active Filters Summary -->
  {% if has_filters %}
  <div class="bg-muted/40 rounded-md p-3 mb-4 flex items-center justify-between">
    <div class="flex items-center">
      <i data-lucide="filter" class="h-4 w-4 mr-2 text-muted-foreground"></i>
      <span class="text-sm">{{ filter_summary }}</span>
    </div>
    <a href="{{ url_for('admin.activities') }}" class="text-sm text-primary hover:underline">
      Clear all filters
    </a>
  </div>
  {% endif %}

  <!-- Filters -->
  <div class="mb-6 rounded-md border border-border bg-card shadow-sm overflow-hidden">
    <div class="border-b border-border px-5 py-4 flex justify-between items-center cursor-pointer hover:bg-accent/5 transition-colors duration-150" id="filter-toggle">
      <div class="flex items-center">
        <i data-lucide="sliders" class="h-4 w-4 mr-2 text-primary"></i>
        <h3 class="text-base font-medium">Advanced Filters</h3>
        {% if has_filters %}
        <span class="ml-2 inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-primary/10 text-primary">
          <i data-lucide="check" class="h-3 w-3 mr-1"></i>
          Active
        </span>
        {% endif %}
      </div>
      <span class="chevron-wrapper">
        <i data-lucide="chevron-down" class="h-4 w-4 text-muted-foreground" id="filter-chevron"></i>
      </span>
    </div>
    <div class="p-6 hidden" id="filter-content">
      <form method="get" action="{{ url_for('admin.activities') }}" class="space-y-6">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
          <!-- User filter -->
          {% set user_options = [{'value': '', 'label': 'All Users'}] %}
          {% for user in users %}
            {% set _ = user_options.append({'value': user.id, 'label': user.name}) %}
          {% endfor %}
          {{ form_select_with_label(
            id="user_id",
            name="user_id",
            label="User",
            options=user_options,
            selected_value=request.args.get('user_id')|int if request.args.get('user_id') else '',
            placeholder=None,
            left_icon="users",
            right_icon="chevron-down",
            label_icon="user"
          ) }}

          <!-- Category filter -->
          {% set category_options = [{'value': '', 'label': 'All Categories'}] %}
          {% for category_value, category_label in activity_categories %}
            {% set _ = category_options.append({'value': category_value, 'label': category_label}) %}
          {% endfor %}
          {{ form_select_with_label(
            id="category",
            name="category",
            label="Category",
            options=category_options,
            selected_value=request.args.get('category', ''),
            placeholder=None,
            left_icon="tag",
            right_icon="chevron-down",
            label_icon="tag"
          ) }}

          <!-- Search term -->
          <div class="grid gap-1.5">
            <label for="search" class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 flex items-center">
              <i data-lucide="search" class="h-3.5 w-3.5 mr-1.5 text-muted-foreground"></i>
              Search
            </label>
            <div class="relative">
              <input type="text" id="search" name="search" value="{{ request.args.get('search', '') }}"
                    class="flex h-9 w-full rounded-md border border-input bg-transparent px-3 py-1 pl-9 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50"
                    placeholder="Search in action or details...">
              <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                <i data-lucide="scan-search" class="h-4 w-4 text-muted-foreground"></i>
              </div>
            </div>
          </div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
          <!-- Method filter -->
          {% set method_options = [{'value': '', 'label': 'All Methods'}] %}
          {% for method_value, method_label in activity_methods %}
            {% set _ = method_options.append({'value': method_value, 'label': method_label}) %}
          {% endfor %}
          {{ form_select_with_label(
            id="method",
            name="method",
            label="Method",
            options=method_options,
            selected_value=request.args.get('method', ''),
            placeholder=None,
            left_icon="git-branch",
            right_icon="chevron-down",
            label_icon="git-branch"
          ) }}

          <!-- Date range filters -->
          <div class="grid gap-1.5">
            <label for="date_from" class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 flex items-center">
              <i data-lucide="calendar-range" class="h-3.5 w-3.5 mr-1.5 text-muted-foreground"></i>
              From Date
            </label>
            <div class="relative">
              <input type="date" id="date_from" name="date_from" value="{{ request.args.get('date_from', '') }}"
                    class="flex h-9 w-full rounded-md border border-input bg-transparent px-3 py-1 pl-9 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 appearance-none"
                    placeholder="mm/dd/yyyy">
              <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                <i data-lucide="calendar" class="h-4 w-4 text-muted-foreground"></i>
              </div>
            </div>
          </div>

          <div class="grid gap-1.5">
            <label for="date_to" class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 flex items-center">
              <i data-lucide="calendar-range" class="h-3.5 w-3.5 mr-1.5 text-muted-foreground"></i>
              To Date
            </label>
            <div class="relative">
              <input type="date" id="date_to" name="date_to" value="{{ request.args.get('date_to', '') }}"
                    class="flex h-9 w-full rounded-md border border-input bg-transparent px-3 py-1 pl-9 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 appearance-none"
                    placeholder="mm/dd/yyyy">
              <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                <i data-lucide="calendar" class="h-4 w-4 text-muted-foreground"></i>
              </div>
            </div>
          </div>

          <!-- Severity filter -->
          <div class="grid gap-1.5">
            <label class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 flex items-center">
              <i data-lucide="alert-circle" class="h-3.5 w-3.5 mr-1.5 text-muted-foreground"></i>
              Severity
            </label>
            <div class="flex flex-wrap gap-2">
              <label class="inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 border border-input hover:bg-accent hover:text-accent-foreground h-9 px-4 py-2 cursor-pointer {% if request.args.get('severity') == 'info' %}bg-primary text-primary-foreground hover:bg-primary/90 shadow{% endif %}">
                <input type="radio" name="severity" value="info" class="hidden"
                       {% if request.args.get('severity') == 'info' %}checked{% endif %}>
                <i data-lucide="info" class="h-4 w-4 mr-2"></i>
                Info
              </label>
              <label class="inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 border border-input hover:bg-accent hover:text-accent-foreground h-9 px-4 py-2 cursor-pointer {% if request.args.get('severity') == 'warning' %}bg-orange-500 text-white hover:bg-orange-600 shadow{% endif %}">
                <input type="radio" name="severity" value="warning" class="hidden"
                       {% if request.args.get('severity') == 'warning' %}checked{% endif %}>
                <i data-lucide="alert-triangle" class="h-4 w-4 mr-2"></i>
                Warning
              </label>
              <label class="inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 border border-input hover:bg-accent hover:text-accent-foreground h-9 px-4 py-2 cursor-pointer {% if request.args.get('severity') == 'error' %}bg-destructive text-destructive-foreground hover:bg-destructive/90 shadow{% endif %}">
                <input type="radio" name="severity" value="error" class="hidden"
                       {% if request.args.get('severity') == 'error' %}checked{% endif %}>
                <i data-lucide="alert-octagon" class="h-4 w-4 mr-2"></i>
                Error
              </label>
              <label class="inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 border border-input hover:bg-accent hover:text-accent-foreground h-9 px-4 py-2 cursor-pointer {% if not request.args.get('severity') %}bg-muted text-muted-foreground hover:bg-muted/90 shadow{% endif %}">
                <input type="radio" name="severity" value="" class="hidden"
                       {% if not request.args.get('severity') %}checked{% endif %}>
                <i data-lucide="filter-x" class="h-4 w-4 mr-2"></i>
                All
              </label>
            </div>
          </div>
        </div>

        <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 pt-4 border-t border-border mt-2">
          <div class="flex items-center">
            <label for="sort" class="text-sm font-medium mr-2 flex items-center whitespace-nowrap">
              <i data-lucide="arrow-up-down" class="h-3.5 w-3.5 mr-1.5 text-muted-foreground"></i>
              Sort by:
            </label>
            {% set sort_options = [
              {'value': 'created_at_desc', 'label': 'Date (Newest first)'},
              {'value': 'created_at_asc', 'label': 'Date (Oldest first)'},
              {'value': 'severity_desc', 'label': 'Severity (High to Low)'},
              {'value': 'user_asc', 'label': 'User (A-Z)'}
            ] %}
            {{ form_select(
              id="sort",
              name="sort",
              options=sort_options,
              selected_value=request.args.get('sort', 'created_at_desc'),
              placeholder=None,
              left_icon=None,
              right_icon="chevron-down"
            ) }}
          </div>

          <div class="flex flex-wrap gap-2">
            <a href="{{ url_for('admin.activities') }}" class="inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-9 px-4 py-2">
              <i data-lucide="rotate-ccw" class="h-4 w-4 mr-2"></i>
              Reset
            </a>
            <button type="submit" class="inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground shadow hover:bg-primary/90 h-9 px-4 py-2">
              <i data-lucide="filter" class="h-4 w-4 mr-2"></i>
              Apply Filters
            </button>
            <a href="{{ url_for('admin.export_activities') }}{{ '?' + request.query_string.decode() if request.query_string else '' }}" class="inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-9 px-4 py-2">
              <i data-lucide="download" class="h-4 w-4 mr-2"></i>
              Export
            </a>
          </div>
        </div>
      </form>
    </div>
  </div>

  <!-- Activity Table -->
  <div class="card">
    <div class="p-6 border-b border-border flex items-center justify-between">
      <div>
        <h3 class="text-lg font-medium">System Activity Logs</h3>
        <p class="text-sm text-muted-foreground mt-1">{{ activity_count_text }}</p>
      </div>
      <div id="table-actions" class="flex items-center space-x-2">
        <!-- Auto-refresh button will be inserted here by JavaScript -->
      </div>
    </div>

    <div class="overflow-x-auto">
      <table class="w-full">
        <thead>
          <tr class="border-b border-border">
            <th class="px-4 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">Severity</th>
            <th class="px-4 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">User</th>
            <th class="px-4 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">Action/Method</th>
            <th class="px-4 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">Category</th>
            <th class="px-4 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">Entity</th>
            <th class="px-4 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">Details</th>
            <th class="px-4 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">Date & Time</th>
            <th class="px-4 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">Actions</th>
          </tr>
        </thead>
        <tbody id="activity-table-body">
          {% if activities %}
            {% for activity in activities %}
            <tr class="border-b border-border hover:bg-muted/30 transition-colors duration-150 {% if activity.severity == 'error' %}bg-destructive/5{% elif activity.severity == 'warning' %}bg-warning/5{% endif %}">
              <!-- Severity with badge -->
              <td class="px-4 py-3">
                <span class="badge {{ activity.get_severity_badge_class() }}">
                  {{ activity.severity.title() if activity.severity else 'Info' }}
                </span>
              </td>

              <!-- User -->
              <td class="px-4 py-3">
                {% if activity.user %}
                <div class="flex items-center space-x-2">
                  <div class="w-7 h-7 rounded-full bg-primary/10 flex items-center justify-center text-primary font-medium text-xs">
                    {{ activity.user.name[:1] }}
                  </div>
                  <span class="font-medium text-sm">{{ activity.user.name }}</span>
                </div>
                {% else %}
                <span class="text-muted-foreground text-sm">Unknown</span>
                {% endif %}
              </td>

              <!-- Action/Method -->
              <td class="px-4 py-3">
                <div class="text-sm {% if activity.severity == 'error' %}text-destructive font-medium{% elif activity.severity == 'warning' %}text-warning font-medium{% endif %}">
                  {{ activity.action }}
                </div>
                {% if activity.method %}
                <div class="mt-1">
                  <span class="text-xs px-1.5 py-0.5 rounded-sm inline-flex items-center
                    {% if activity.method == 'create' %}bg-blue-100 text-blue-700 dark:bg-blue-900/30 dark:text-blue-400
                    {% elif activity.method == 'read' %}bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-400
                    {% elif activity.method == 'update' %}bg-amber-100 text-amber-700 dark:bg-amber-900/30 dark:text-amber-400
                    {% elif activity.method == 'delete' %}bg-red-100 text-red-700 dark:bg-red-900/30 dark:text-red-400
                    {% else %}bg-gray-100 text-gray-700 dark:bg-gray-900/30 dark:text-gray-400{% endif %}">
                    {% if activity.method == 'create' %}<i data-lucide="plus" class="h-3 w-3 mr-1"></i>
                    {% elif activity.method == 'read' %}<i data-lucide="eye" class="h-3 w-3 mr-1"></i>
                    {% elif activity.method == 'update' %}<i data-lucide="edit" class="h-3 w-3 mr-1"></i>
                    {% elif activity.method == 'delete' %}<i data-lucide="trash" class="h-3 w-3 mr-1"></i>
                    {% else %}<i data-lucide="activity" class="h-3 w-3 mr-1"></i>{% endif %}
                    {{ activity.get_method_display() }}
                  </span>
                </div>
                {% endif %}
              </td>

              <!-- Category -->
              <td class="px-4 py-3">
                <span class="text-xs px-2 py-1 rounded-full bg-muted inline-flex items-center">
                  {% if activity.category == 'auth' %}
                    <i data-lucide="key" class="h-3 w-3 mr-1"></i>
                  {% elif activity.category == 'admin' %}
                    <i data-lucide="shield" class="h-3 w-3 mr-1"></i>
                  {% elif activity.category == 'user' %}
                    <i data-lucide="user" class="h-3 w-3 mr-1"></i>
                  {% elif activity.category == 'system' %}
                    <i data-lucide="settings" class="h-3 w-3 mr-1"></i>
                  {% elif activity.category == 'data' %}
                    <i data-lucide="database" class="h-3 w-3 mr-1"></i>
                  {% endif %}
                  {{ activity.get_category_display() }}
                </span>
              </td>

              <!-- Entity -->
              <td class="px-4 py-3 text-sm">
                {% if activity.entity_type and activity.entity_id %}
                  <span class="inline-flex items-center">
                    <i data-lucide="file" class="h-3.5 w-3.5 mr-1 text-muted-foreground"></i>
                    {{ activity.entity_type }} #{{ activity.entity_id }}
                  </span>
                {% else %}
                  <span class="text-muted-foreground">-</span>
                {% endif %}
              </td>

              <!-- Details -->
              <td class="px-4 py-3 text-sm">
                <div class="max-w-xs truncate" title="{{ activity.details or '-' }}">
                  {{ activity.details or '-' }}
                </div>
              </td>

              <!-- Date & Time -->
              <td class="px-4 py-3 whitespace-nowrap">
                <div class="flex flex-col">
                  <span class="text-sm">{{ activity.created_at.strftime('%Y-%m-%d') }}</span>
                  <span class="text-xs text-muted-foreground">{{ activity.created_at.strftime('%H:%M:%S') }}</span>
                  {% if now and activity.created_at.tzinfo %}
                {% set time_diff = (now - activity.created_at).total_seconds() %}
                {% if time_diff < 300 %}
                  <span class="inline-flex items-center text-xs text-primary mt-1">
                    <i data-lucide="clock" class="h-3 w-3 mr-1"></i> Recent
                  </span>
                {% endif %}
              {% endif %}
                </div>
              </td>

              <!-- Actions -->
              <td class="px-4 py-3">
                <div class="flex space-x-1">
                  <button type="button" class="btn btn-icon btn-sm"
                          onclick="showActivityDetails({{ activity.id }})"
                          title="View Details">
                    <i data-lucide="eye" class="h-4 w-4"></i>
                  </button>
                  {% if activity.user_id == current_user.id %}
                  <button type="button" class="btn btn-icon btn-sm"
                          onclick="window.location.href='{{ url_for('admin.activities', user_id=activity.user_id) }}'"
                          title="View My Activities">
                    <i data-lucide="user" class="h-4 w-4"></i>
                  </button>
                  {% endif %}
                </div>
              </td>
            </tr>
            {% endfor %}
          {% else %}
            <tr>
              <td colspan="8" class="px-4 py-8 text-center">
                <div class="flex flex-col items-center justify-center">
                  <div class="rounded-full bg-muted p-3 mb-3">
                    <i data-lucide="activity" class="h-6 w-6 text-muted-foreground"></i>
                  </div>
                  <h3 class="text-lg font-medium mb-1">No activities found</h3>
                  <p class="text-muted-foreground text-sm">Try adjusting your filters to see more results</p>
                </div>
              </td>
            </tr>
          {% endif %}
        </tbody>
      </table>
    </div>

    {% include "partials/pagination.html" %}
  </div>
</div>

{{ activity_details() }}
{% endblock %}

{% block scripts %}
<!-- Ensure modal.js is loaded first -->
<script src="{{ url_for('static', filename='js/components/modal.js') }}"></script>
<!-- Then load the activity-specific scripts -->
<script src="{{ url_for('static', filename='js/admin/activity-log-management.js') }}"></script>
<script src="{{ url_for('static', filename='js/admin/activity-details.js') }}"></script>
<script src="{{ url_for('static', filename='js/admin/activity-filters.js') }}"></script>
{% endblock %}

/**
 * User Activity Log Component - Rendering Module
 *
 * This module handles rendering activities and UI states for the activity log component.
 */

import { formatDate, formatValue } from './utils.js';

/**
 * Render activities in the list container
 * 
 * @param {HTMLElement} container - Container element to render activities in
 * @param {Array} activities - Array of activity objects to render
 * @param {boolean} inProfilePage - Whether this is being rendered in the profile page
 */
function renderActivities(container, activities, inProfilePage = false) {
    const loadingEl = container.querySelector('.activities-loading');
    const listEl = container.querySelector('.activities-list');
    const emptyEl = container.querySelector('.activities-empty');
    const errorEl = container.querySelector('.activities-error');

    // Hide loading state
    loadingEl.classList.add('hidden');

    if (activities && activities.length > 0) {
        // Clear existing content
        listEl.innerHTML = '';

        // Get the template
        const template = document.getElementById('activity-item-template');

        // Render each activity
        activities.forEach(activity => {
            const activityEl = template.content.cloneNode(true);

            // Set activity icon based on category
            const iconWrapper = activityEl.querySelector('.activity-icon-wrapper');
            const iconEl = iconWrapper.querySelector('i');
            let iconName = 'activity';

            if (activity.category === 'auth') {
                iconName = 'key';
                iconWrapper.classList.add('bg-blue-100', 'text-blue-600');
                iconWrapper.classList.remove('bg-primary/10', 'text-primary');
            } else if (activity.category === 'user') {
                iconName = 'user';
            } else if (activity.category === 'admin') {
                iconName = 'shield';
                iconWrapper.classList.add('bg-violet-100', 'text-violet-600');
                iconWrapper.classList.remove('bg-primary/10', 'text-primary');
            } else if (activity.category === 'data') {
                iconName = 'database';
                iconWrapper.classList.add('bg-green-100', 'text-green-600');
                iconWrapper.classList.remove('bg-primary/10', 'text-primary');
            } else if (activity.category === 'system') {
                iconName = 'settings';
                iconWrapper.classList.add('bg-gray-100', 'text-gray-600');
                iconWrapper.classList.remove('bg-primary/10', 'text-primary');
            }

            iconEl.setAttribute('data-lucide', iconName);

            // Set basic activity info
            activityEl.querySelector('.activity-action').textContent = activity.action;
            activityEl.querySelector('.activity-time').textContent = formatDate(activity.created_at);

            // Set details if available
            const detailsEl = activityEl.querySelector('.activity-details');
            if (activity.details) {
                // Truncate details if they're too long
                const maxLength = 120;
                if (activity.details.length > maxLength) {
                    detailsEl.textContent = activity.details.substring(0, maxLength) + '...';
                } else {
                    detailsEl.textContent = activity.details;
                }
            } else {
                detailsEl.classList.add('hidden');
            }

            // Set severity badge
            const badgeEl = activityEl.querySelector('.activity-severity-badge');
            badgeEl.textContent = activity.severity;

            // Apply appropriate styling based on severity
            if (activity.severity === 'error') {
                badgeEl.classList.add('bg-red-100', 'text-red-700', 'dark:bg-red-900/30', 'dark:text-red-400');
            } else if (activity.severity === 'warning') {
                badgeEl.classList.add('bg-yellow-100', 'text-yellow-700', 'dark:bg-yellow-900/30', 'dark:text-yellow-400');
            } else {
                badgeEl.classList.add('bg-green-100', 'text-green-700', 'dark:bg-green-900/30', 'dark:text-green-400');
            }

            // Set method badge if available
            const methodBadgeEl = activityEl.querySelector('.activity-method-badge');
            if (activity.method) {
                methodBadgeEl.textContent = activity.method_display || activity.method;

                // Apply appropriate styling based on method
                if (activity.method === 'create') {
                    methodBadgeEl.classList.add('bg-blue-100', 'text-blue-700', 'dark:bg-blue-900/30', 'dark:text-blue-400');
                } else if (activity.method === 'update') {
                    methodBadgeEl.classList.add('bg-amber-100', 'text-amber-700', 'dark:bg-amber-900/30', 'dark:text-amber-400');
                } else if (activity.method === 'delete') {
                    methodBadgeEl.classList.add('bg-red-100', 'text-red-700', 'dark:bg-red-900/30', 'dark:text-red-400');
                } else if (activity.method === 'read') {
                    methodBadgeEl.classList.add('bg-green-100', 'text-green-700', 'dark:bg-green-900/30', 'dark:text-green-400');
                } else {
                    methodBadgeEl.classList.add('bg-gray-100', 'text-gray-700', 'dark:bg-gray-900/30', 'dark:text-gray-400');
                }
            } else {
                methodBadgeEl.classList.add('hidden');
            }

            // Handle changes if available
            if (activity.has_changes) {
                const changesEl = activityEl.querySelector('.activity-changes');
                changesEl.classList.remove('hidden');

                const tableBody = changesEl.querySelector('.changes-table-body');
                const rowTemplate = document.getElementById('changes-row-template');

                // Add rows for each changed field
                Object.keys(activity.old_values).forEach(field => {
                    const rowEl = rowTemplate.content.cloneNode(true);

                    rowEl.querySelector('.field-name').textContent = field;
                    rowEl.querySelector('.old-value').innerHTML = formatValue(activity.old_values[field]);
                    rowEl.querySelector('.new-value').innerHTML = formatValue(activity.new_values[field]);

                    tableBody.appendChild(rowEl);
                });
            }

            // Set up view details button
            const viewDetailsBtn = activityEl.querySelector('.view-details-btn');
            viewDetailsBtn.dataset.activityId = activity.id;

            // Add to container
            listEl.appendChild(activityEl);
        });

        // Show the list
        listEl.classList.remove('hidden');
        emptyEl.classList.add('hidden');

        // Animate the items in
        const items = listEl.querySelectorAll('.activity-item');
        items.forEach((item, index) => {
            item.style.opacity = '0';
            item.style.transform = 'translateY(10px)';
            setTimeout(() => {
                item.style.transition = 'opacity 0.3s ease, transform 0.3s ease';
                item.style.opacity = '1';
                item.style.transform = 'translateY(0)';
            }, 50 * index);
        });

        // Initialize Lucide icons for newly added elements
        if (window.lucide) {
            lucide.createIcons();
        }
    } else {
        // Show empty state
        listEl.classList.add('hidden');
        emptyEl.classList.remove('hidden');
    }
}

/**
 * Render loading state
 * 
 * @param {HTMLElement} container - Container element to render loading state in
 */
function renderLoadingState(container) {
    const loadingEl = container.querySelector('.activities-loading');
    const listEl = container.querySelector('.activities-list');
    const emptyEl = container.querySelector('.activities-empty');
    const errorEl = container.querySelector('.activities-error');

    // Show loading state
    loadingEl.classList.remove('hidden');
    listEl.classList.add('hidden');
    emptyEl.classList.add('hidden');
    errorEl.classList.add('hidden');
}

/**
 * Render error state
 * 
 * @param {HTMLElement} container - Container element to render error state in
 * @param {Error} error - Error object
 */
function renderErrorState(container, error) {
    const loadingEl = container.querySelector('.activities-loading');
    const listEl = container.querySelector('.activities-list');
    const emptyEl = container.querySelector('.activities-empty');
    const errorEl = container.querySelector('.activities-error');

    // Show error state
    loadingEl.classList.add('hidden');
    listEl.classList.add('hidden');
    emptyEl.classList.add('hidden');
    errorEl.classList.remove('hidden');

    // Set error message if available
    const errorMessageEl = errorEl.querySelector('.error-message');
    if (errorMessageEl) {
        errorMessageEl.textContent = error.message || 'An error occurred while loading activities.';
    }
}

/**
 * Render activity details in modal
 * 
 * @param {Object} activity - Activity object to render details for
 */
function renderActivityDetails(activity) {
    const modal = document.getElementById('activity-detail-modal');
    if (!modal) return;

    // Set basic activity info
    modal.querySelector('.activity-title').textContent = activity.action;
    modal.querySelector('.activity-time').textContent = formatDate(activity.created_at);
    
    // Set user info if available
    const userEl = modal.querySelector('.activity-user');
    if (activity.user_name) {
        userEl.textContent = activity.user_name;
        userEl.classList.remove('hidden');
    } else {
        userEl.classList.add('hidden');
    }

    // Set details if available
    const detailsEl = modal.querySelector('.activity-details');
    if (activity.details) {
        detailsEl.textContent = activity.details;
        detailsEl.classList.remove('hidden');
    } else {
        detailsEl.classList.add('hidden');
    }

    // Set entity info if available
    const entityEl = modal.querySelector('.activity-entity');
    if (activity.entity_type && activity.entity_id) {
        entityEl.textContent = `${activity.entity_type} #${activity.entity_id}`;
        entityEl.classList.remove('hidden');
    } else {
        entityEl.classList.add('hidden');
    }

    // Set IP address if available
    const ipEl = modal.querySelector('.activity-ip');
    if (activity.ip_address) {
        ipEl.textContent = activity.ip_address;
        ipEl.classList.remove('hidden');
    } else {
        ipEl.classList.add('hidden');
    }

    // Handle changes if available
    const changesEl = modal.querySelector('.activity-changes');
    if (activity.has_changes) {
        changesEl.classList.remove('hidden');

        const tableBody = changesEl.querySelector('.changes-table-body');
        tableBody.innerHTML = '';

        // Add rows for each changed field
        Object.keys(activity.old_values).forEach(field => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td class="px-4 py-2 border-b border-gray-200 dark:border-gray-700 font-medium">${field}</td>
                <td class="px-4 py-2 border-b border-gray-200 dark:border-gray-700">${formatValue(activity.old_values[field])}</td>
                <td class="px-4 py-2 border-b border-gray-200 dark:border-gray-700">${formatValue(activity.new_values[field])}</td>
            `;
            tableBody.appendChild(row);
        });
    } else {
        changesEl.classList.add('hidden');
    }

    // Show the modal
    modal.classList.remove('hidden');
}

// Export public API
export {
    renderActivities,
    renderLoadingState,
    renderErrorState,
    renderActivityDetails
};

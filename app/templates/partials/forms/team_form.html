{% from "partials/forms/base_form.html" import form_group, form_actions, form_header %}

<form action="{{ action_url }}" method="POST" class="space-y-4">
  <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">

  {{ form_header(
    title = "Edit Team" if entity else "Add Team",
    description = "Update team information" if entity else "Create a new team"
  ) }}

  <div class="grid grid-cols-1 gap-4">
    {{ form_group(
      label = "Team Name",
      name = "name",
      type = "text",
      value = entity.name if entity else "",
      required = true,
      placeholder = "Enter team name"
    ) }}

    {{ form_group(
      label = "Short Name",
      name = "short_name",
      type = "text",
      value = entity.short_name if entity else "",
      placeholder = "Enter short name or abbreviation"
    ) }}

    {{ form_group(
      label = "Description",
      name = "description",
      type = "textarea",
      value = entity.description if entity else "",
      placeholder = "Enter team description"
    ) }}
  </div>

  {{ form_actions(
    submit_label = "Update Team" if entity else "Create Team"
  ) }}
</form>

{% set created_at = activity.created_at %}
{% set formatted_date = created_at.strftime('%Y-%m-%d %H:%M:%S') %}

{% set severity_class = '' %}
{% if activity.severity == 'error' %}
  {% set severity_class = 'text-destructive' %}
{% elif activity.severity == 'warning' %}
  {% set severity_class = 'text-warning' %}
{% endif %}

{% set category_icon = 'tag' %}
{% if activity.category == 'auth' %}
  {% set category_icon = 'key' %}
{% elif activity.category == 'admin' %}
  {% set category_icon = 'shield' %}
{% elif activity.category == 'user' %}
  {% set category_icon = 'user' %}
{% elif activity.category == 'system' %}
  {% set category_icon = 'settings' %}
{% elif activity.category == 'data' %}
  {% set category_icon = 'database' %}
{% endif %}

<div class="space-y-4">
  <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
    <div>
      <h4 class="text-sm font-medium text-muted-foreground mb-1">User</h4>
      <div class="flex items-center">
        <div class="w-6 h-6 rounded-full bg-primary/10 flex items-center justify-center text-primary font-medium text-xs mr-2">
          {{ activity.user.name[0] if activity.user else '?' }}
        </div>
        <p class="font-medium">{{ activity.user.name if activity.user else 'Unknown' }}</p>
      </div>
    </div>
    <div>
      <h4 class="text-sm font-medium text-muted-foreground mb-1">Date & Time</h4>
      <p>{{ formatted_date }}</p>
    </div>
  </div>

  <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
    <div>
      <h4 class="text-sm font-medium text-muted-foreground mb-1">Category</h4>
      <p class="flex items-center">
        <i data-lucide="{{ category_icon }}" class="h-4 w-4 mr-1.5"></i>
        {{ activity.get_category_display() }}
      </p>
    </div>
    <div>
      <h4 class="text-sm font-medium text-muted-foreground mb-1">Method</h4>
      <p class="flex items-center">
        {% if activity.method %}
          <span class="inline-flex items-center text-xs px-2 py-1 rounded-sm mr-1.5
            {% if activity.method == 'create' %}bg-blue-100 text-blue-700 dark:bg-blue-900/30 dark:text-blue-400
            {% elif activity.method == 'read' %}bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-400
            {% elif activity.method == 'update' %}bg-amber-100 text-amber-700 dark:bg-amber-900/30 dark:text-amber-400
            {% elif activity.method == 'delete' %}bg-red-100 text-red-700 dark:bg-red-900/30 dark:text-red-400
            {% else %}bg-gray-100 text-gray-700 dark:bg-gray-900/30 dark:text-gray-400{% endif %}">
            {% if activity.method == 'create' %}<i data-lucide="plus" class="h-3 w-3 mr-1"></i>
            {% elif activity.method == 'read' %}<i data-lucide="eye" class="h-3 w-3 mr-1"></i>
            {% elif activity.method == 'update' %}<i data-lucide="edit" class="h-3 w-3 mr-1"></i>
            {% elif activity.method == 'delete' %}<i data-lucide="trash" class="h-3 w-3 mr-1"></i>
            {% else %}<i data-lucide="activity" class="h-3 w-3 mr-1"></i>{% endif %}
            {{ activity.get_method_display() }}
          </span>
        {% else %}
          <span class="text-muted-foreground">-</span>
        {% endif %}
      </p>
    </div>
    <div>
      <h4 class="text-sm font-medium text-muted-foreground mb-1">Entity</h4>
      <p class="flex items-center">
        {% if activity.entity_type %}
          <i data-lucide="file" class="h-4 w-4 mr-1.5"></i>
          {{ activity.entity_type }} #{{ activity.entity_id }}
        {% else %}
          -
        {% endif %}
      </p>
    </div>
    <div>
      <h4 class="text-sm font-medium text-muted-foreground mb-1">IP Address</h4>
      <p>{{ activity.ip_address or '-' }}</p>
    </div>
  </div>

  <div>
    <h4 class="text-sm font-medium text-muted-foreground mb-1">URL</h4>
    <div class="text-sm bg-muted/30 p-3 rounded-md max-h-[100px] overflow-y-auto">
      {% if activity.url %}
        <button type="button" class="text-primary hover:underline flex items-start w-full overflow-hidden">
          <i data-lucide="{{ 'file-json' if '?' in activity.url else 'link' }}" class="h-3.5 w-3.5 mr-1.5 mt-0.5 flex-shrink-0"></i>
          <span class="break-all">{{ activity.url }}</span>
        </button>
      {% else %}
        -
      {% endif %}
    </div>
  </div>

  <div>
    <h4 class="text-sm font-medium text-muted-foreground mb-1">Details</h4>
    <div class="bg-muted/30 p-3 rounded-md whitespace-pre-line break-words max-h-[200px] overflow-y-auto">{{ activity.details or '-' }}</div>
  </div>

  <div>
    <h4 class="text-sm font-medium text-muted-foreground mb-1">User Agent</h4>
    <div class="text-sm break-words bg-muted/30 p-3 rounded-md max-h-[100px] overflow-y-auto">{{ activity.user_agent or '-' }}</div>
  </div>

  {% if activity.old_values and activity.new_values %}
    <div class="pt-4 border-t border-border mt-4">
      <h4 class="text-sm font-medium text-muted-foreground mb-2">Changes</h4>
      <div class="bg-muted/30 p-3 rounded-md overflow-auto max-h-[300px]">
        <table class="w-full text-xs">
          <thead>
            <tr class="border-b border-border">
              <th class="text-left py-2 px-2 font-medium">Field</th>
              <th class="text-left py-2 px-2 font-medium">Old Value</th>
              <th class="text-left py-2 px-2 font-medium">New Value</th>
            </tr>
          </thead>
          <tbody>
            {% for field in activity.old_values.keys() %}
              <tr class="border-b border-border/50">
                <td class="py-2 px-2 font-medium align-top">{{ field }}</td>
                <td class="py-2 px-2 align-top whitespace-pre-wrap break-words max-w-[150px]">
                  {% if activity.old_values[field] is none %}
                    <span class="text-muted-foreground italic">null</span>
                  {% elif activity.old_values[field] is boolean %}
                    {% if activity.old_values[field] %}
                      <span class="text-green-500">true</span>
                    {% else %}
                      <span class="text-red-500">false</span>
                    {% endif %}
                  {% elif activity.old_values[field] == '' %}
                    <span class="text-muted-foreground italic">(empty string)</span>
                  {% else %}
                    {{ activity.old_values[field] }}
                  {% endif %}
                </td>
                <td class="py-2 px-2 align-top whitespace-pre-wrap break-words max-w-[150px]">
                  {% if activity.new_values[field] is none %}
                    <span class="text-muted-foreground italic">null</span>
                  {% elif activity.new_values[field] is boolean %}
                    {% if activity.new_values[field] %}
                      <span class="text-green-500">true</span>
                    {% else %}
                      <span class="text-red-500">false</span>
                    {% endif %}
                  {% elif activity.new_values[field] == '' %}
                    <span class="text-muted-foreground italic">(empty string)</span>
                  {% else %}
                    {{ activity.new_values[field] }}
                  {% endif %}
                </td>
              </tr>
            {% endfor %}
          </tbody>
        </table>
      </div>
    </div>
  {% endif %}

  {% if activity.entity_type and activity.entity_id and activity.get_entity_url() %}
    <div class="pt-2 border-t border-border mt-4 flex space-x-2">
      <a href="{{ activity.get_entity_url() }}" class="btn btn-outline btn-sm">
        <i data-lucide="external-link" class="h-4 w-4 mr-2"></i>
        View Entity
      </a>
    </div>
  {% endif %}
</div>

from flask import Blueprint, render_template, request, jsonify, url_for
from flask_login import login_required
from app.models import User, BusinessUnit, BusinessSegment, EmployeeDetail, Team, TeamGroup
from app.utils.helpers import generate_slug
from jinja2 import TemplateNotFound

forms_bp = Blueprint('forms', __name__, url_prefix='/forms')

# Create a blueprint for admin forms
from flask import Blueprint as FlaskBlueprint
admin_forms_bp = FlaskBlueprint('admin_forms', __name__, url_prefix='/admin/forms')

# For debugging
import traceback

# Define a function for the test route
def test_form_handler():
    """Test route to verify the forms API is working"""
    return jsonify({
        'success': True,
        'message': 'Forms API is working!'
    })

# Register the test route on both blueprints
forms_bp.route('/test', methods=['GET'])(test_form_handler)
admin_forms_bp.route('/test', methods=['GET'])(test_form_handler)

# Define the main form handler function
@login_required
def get_form_handler(form_type):
    """Generic form renderer for all admin forms"""
    print(f"\n\n[DEBUG] get_form_handler called with form_type: {form_type}\n\n")
    entity_id = request.args.get('id')
    print(f"[DEBUG] entity_id: {entity_id}")
    entity = None
    action_url = None
    template = f"partials/forms/{form_type}_form.html"
    print(f"[DEBUG] template path: {template}")

    # Handle different form types
    if form_type == 'user':
        if entity_id:
            entity = User.query.get_or_404(int(entity_id))
            action_url = url_for('admin.update_user', user_id=entity_id)
        else:
            action_url = url_for('admin.create_user')

    elif form_type == 'business_unit':
        if entity_id:
            entity = BusinessUnit.query.get_or_404(int(entity_id))
            action_url = url_for('admin.update_business_unit', unit_id=entity_id)
        else:
            action_url = url_for('admin.create_business_unit')

    elif form_type == 'business_segment':
        # Get all business units for the dropdown
        business_units = BusinessUnit.query.filter_by(is_active=True).all()

        if entity_id:
            entity = BusinessSegment.query.get_or_404(int(entity_id))
            action_url = url_for('admin.update_business_segment', segment_id=entity_id)
        else:
            action_url = url_for('admin.create_business_segment')

    elif form_type == 'employee_detail':
        # Get users without employee details, business units and segments for dropdowns
        users_without_details = User.query.outerjoin(EmployeeDetail, User.id == EmployeeDetail.user_id).filter(EmployeeDetail.id == None).all()
        business_units = BusinessUnit.query.filter_by(is_active=True).all()
        business_segments = BusinessSegment.query.filter_by(is_active=True).all()

        # Check if we have a pre-selected user
        pre_selected_user_id = request.args.get('user_id')
        pre_selected_user = None
        if pre_selected_user_id:
            pre_selected_user = User.query.get(int(pre_selected_user_id))

        if entity_id:
            entity = EmployeeDetail.query.get_or_404(int(entity_id))
            action_url = url_for('admin.update_employee_detail', detail_id=entity_id)
        else:
            action_url = url_for('admin.create_employee_detail')

    elif form_type == 'team':
        if entity_id:
            entity = Team.query.get_or_404(int(entity_id))
            action_url = url_for('teams.update', team_id=entity_id)
        else:
            action_url = url_for('teams.create')

    elif form_type == 'team_group':
        # Get teams for dropdown
        teams = Team.query.filter_by(is_active=True).all()
        team_options = [(str(team.id), team.name) for team in teams]

        # Check if we have a pre-selected team
        pre_selected_team_id = request.args.get('team_id')
        pre_selected_team = None
        if pre_selected_team_id:
            pre_selected_team = Team.query.get(int(pre_selected_team_id))

        if entity_id:
            entity = TeamGroup.query.get_or_404(int(entity_id))
            action_url = url_for('teams.update_group', group_id=entity_id)
        else:
            action_url = url_for('teams.create_group', team_id=pre_selected_team_id) if pre_selected_team_id else url_for('teams.create_group', team_id=0)

    elif form_type == 'team_member':
        # Get team ID from query parameters
        team_id = request.args.get('team_id')
        if not team_id:
            return jsonify({'success': False, 'error': 'Team ID is required'})

        team = Team.query.get_or_404(int(team_id))

        # Get all employees that are not already members of this team
        current_member_ids = [member.id for member in team.members]
        available_employees = EmployeeDetail.query.filter(EmployeeDetail.id.notin_(current_member_ids) if current_member_ids else True).all()

        # Check if there are any available employees
        if not available_employees:
            return jsonify({
                'success': False,
                'error': 'No available employees to add to this team. All employees are already members or no employee details exist.'
            })

        # Create options for the select dropdown
        employee_options = [(str(emp.id), f"{emp.user.name} ({emp.job_title or 'No Job Title'})") for emp in available_employees]

        # Set action URL
        action_url = url_for('teams.add_member', team_id=team_id)

    # Render the appropriate template with appropriate context
    try:
        context = {
            'entity': entity,
            'action_url': action_url
        }

        # Add additional context based on form type
        if form_type == 'business_segment':
            # Prepare options for the business unit dropdown
            business_unit_options = [(str(unit.id), unit.name) for unit in business_units]
            context['business_units'] = business_units
            context['business_unit_options'] = business_unit_options
        elif form_type == 'employee_detail':
            # Prepare options for the dropdowns
            user_options = [(str(user.id), f"{user.name} ({user.email})") for user in users_without_details]
            user_options.insert(0, ("", "Select User"))
            business_unit_options = [(str(unit.id), unit.name) for unit in business_units]
            business_segment_options = [(str(segment.id), segment.name) for segment in business_segments]

            context['users_without_details'] = users_without_details
            context['business_units'] = business_units
            context['business_segments'] = business_segments
            context['user_options'] = user_options
            context['business_unit_options'] = business_unit_options
            context['business_segment_options'] = business_segment_options

            # Add pre-selected user if available
            if pre_selected_user:
                context['pre_selected_user'] = pre_selected_user
        elif form_type == 'team_group':
            context['team_options'] = team_options

            # Add pre-selected team if available
            if pre_selected_team:
                context['pre_selected_team'] = pre_selected_team
        elif form_type == 'team_member':
            context['employee_options'] = employee_options
            context['team'] = team

        html = render_template(template, **context)

        return jsonify({
            'html': html,
            'success': True
        })
    except TemplateNotFound as e:
        print(f"\n\n[DEBUG] TemplateNotFound error: {str(e)}\n\n")
        return jsonify({
            'success': False,
            'error': f"Form template for '{form_type}' not found: {str(e)}"
        }), 404
    except Exception as e:
        # Log the error for debugging
        print(f"\n\n[DEBUG] Error rendering form {form_type}: {str(e)}\n\n")
        print(f"\n\n[DEBUG] Traceback: {traceback.format_exc()}\n\n")

        return jsonify({
            'success': False,
            'error': f"Error rendering form: {str(e)}"
        }), 500

# Define the form handler with entity ID
@login_required
def get_form_with_id_handler(form_type, entity_id):
    """Form handler for forms with an entity ID"""
    print(f"\n\n[DEBUG] get_form_with_id_handler called with form_type: {form_type}, entity_id: {entity_id}\n\n")

    # Get the entity based on the form type
    entity = None
    action_url = None
    template = f"partials/forms/{form_type}_form.html"

    # Handle different form types
    if form_type == 'user':
        entity = User.query.get_or_404(int(entity_id))
        action_url = url_for('admin.update_user', user_id=entity_id)
    elif form_type == 'business_unit':
        entity = BusinessUnit.query.get_or_404(int(entity_id))
        action_url = url_for('admin.update_business_unit', unit_id=entity_id)
    elif form_type == 'business_segment':
        entity = BusinessSegment.query.get_or_404(int(entity_id))
        action_url = url_for('admin.update_business_segment', segment_id=entity_id)
        business_units = BusinessUnit.query.filter_by(is_active=True).all()
    elif form_type == 'employee_detail':
        entity = EmployeeDetail.query.get_or_404(int(entity_id))
        action_url = url_for('admin.update_employee_detail', detail_id=entity_id)
        business_units = BusinessUnit.query.filter_by(is_active=True).all()
        business_segments = BusinessSegment.query.filter_by(is_active=True).all()
    elif form_type == 'team':
        entity = Team.query.get_or_404(int(entity_id))
        action_url = url_for('teams.update', team_id=entity_id)
    elif form_type == 'team_group':
        entity = TeamGroup.query.get_or_404(int(entity_id))
        action_url = url_for('teams.update_group', group_id=entity_id)
        teams = Team.query.filter_by(is_active=True).all()
        team_options = [(str(team.id), team.name) for team in teams]

    # Render the appropriate template with appropriate context
    try:
        context = {
            'entity': entity,
            'action_url': action_url
        }

        # Add additional context based on form type
        if form_type == 'business_segment':
            # Prepare options for the business unit dropdown
            business_unit_options = [(str(unit.id), unit.name) for unit in business_units]
            context['business_units'] = business_units
            context['business_unit_options'] = business_unit_options
        elif form_type == 'employee_detail':
            # Prepare options for the dropdowns
            business_unit_options = [(str(unit.id), unit.name) for unit in business_units]
            business_segment_options = [(str(segment.id), segment.name) for segment in business_segments]

            context['business_units'] = business_units
            context['business_segments'] = business_segments
            context['business_unit_options'] = business_unit_options
            context['business_segment_options'] = business_segment_options
        elif form_type == 'team_group':
            context['team_options'] = team_options

        html = render_template(template, **context)

        return jsonify({
            'html': html,
            'success': True
        })
    except TemplateNotFound as e:
        print(f"\n\n[DEBUG] TemplateNotFound error: {str(e)}\n\n")
        return jsonify({
            'success': False,
            'error': f"Form template for '{form_type}' not found: {str(e)}"
        }), 404
    except Exception as e:
        # Log the error for debugging
        print(f"\n\n[DEBUG] Error rendering form {form_type}: {str(e)}\n\n")
        print(f"\n\n[DEBUG] Traceback: {traceback.format_exc()}\n\n")

        return jsonify({
            'success': False,
            'error': f"Error rendering form: {str(e)}"
        }), 500

# Register the form handlers with both blueprints
forms_bp.route('/<string:form_type>', methods=['GET'])(get_form_handler)
admin_forms_bp.route('/<string:form_type>', methods=['GET'])(get_form_handler)

# Register the form handlers with entity ID
forms_bp.route('/<string:form_type>/<int:entity_id>', methods=['GET'])(get_form_with_id_handler)
admin_forms_bp.route('/<string:form_type>/<int:entity_id>', methods=['GET'])(get_form_with_id_handler)

"""
Authentication routes package initialization.
This file creates the auth Blueprint and imports all auth routes.
"""

from flask import Blueprint, redirect, url_for
from flask_login import current_user

# Create the auth Blueprint
auth_bp = Blueprint('auth', __name__, url_prefix='/auth')

def redirect_authenticated_user():
    """Helper function to redirect authenticated users based on role."""
    if current_user.is_authenticated:
        if current_user.is_admin:
            return redirect(url_for('admin.dashboard'))
        else:
            return redirect(url_for('main.user_dashboard'))
    return None

# Import all auth routes
from app.routes.auth.login import *
from app.routes.auth.register import *
from app.routes.auth.password import *
from app.routes.auth.profile import *

# Define __all__ to explicitly specify what is exported
__all__ = ['auth_bp', 'redirect_authenticated_user']

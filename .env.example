# Flask Configuration
FLASK_APP=run.py
FLASK_ENV=development
FLASK_DEBUG=1
FLASK_CONFIG=development

# Server Configuration
HOST=0.0.0.0
PORT=5002

# Database Configuration
DATABASE_URL=sqlite:///dev.db

# Security
SECRET_KEY=your-secret-key-here
JWT_SECRET_KEY=your-jwt-secret-key-here

# Mail Configuration
MAIL_SERVER=smtp.example.com
MAIL_PORT=587
MAIL_USE_TLS=True
MAIL_USERNAME=your-username
MAIL_PASSWORD=your-password
MAIL_DEFAULT_SENDER=<EMAIL>

# Logging
LOG_LEVEL=INFO

# Pagination Configuration
PAGINATION_PER_PAGE=10
PAGINATION_PER_PAGE_ADMIN=20
PAGINATION_MAX_PER_PAGE=100

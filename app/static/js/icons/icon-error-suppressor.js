/**
 * Icon Error Suppressor
 * This script runs before any other JavaScript to suppress icon-related errors
 */

// Immediately suppress all icon-related errors
(function() {
  // Store the original console methods
  const originalConsoleError = console.error;
  const originalConsoleWarn = console.warn;

  // Replace console.error with our filtered version
  console.error = function() {
    // Get the first argument
    const firstArg = arguments[0];

    // Check if it's an icon-related error
    if (typeof firstArg === 'string' &&
        (firstArg.includes('icon name was not found') ||
         firstArg.includes('lucide icon not found') ||
         firstArg.includes('was not found in the provided icons object') ||
         firstArg.includes('lucide') && firstArg.includes('icon'))) {
      // Completely suppress the error
      return;
    }

    // Pass through all other errors
    return originalConsoleError.apply(console, arguments);
  };

  // Also filter warnings
  console.warn = function() {
    // Get the first argument
    const firstArg = arguments[0];

    // Check if it's an icon-related warning
    if (typeof firstArg === 'string' &&
        (firstArg.includes('icon') || firstArg.includes('lucide'))) {
      // Suppress the warning
      return;
    }

    // Pass through all other warnings
    return originalConsoleWarn.apply(console, arguments);
  };

  // Define a global function to create fallback icons
  window.createFallbackIcon = function(element) {
    if (!element || element.tagName.toLowerCase() === 'svg') return;

    const iconName = element.getAttribute('data-lucide');
    if (!iconName) return;

    // Create a simple placeholder SVG
    const svg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
    svg.setAttribute('width', element.style.width ? element.style.width : '24');
    svg.setAttribute('height', element.style.height ? element.style.height : '24');
    svg.setAttribute('viewBox', '0 0 24 24');
    svg.setAttribute('fill', 'none');
    svg.setAttribute('stroke', 'currentColor');
    svg.setAttribute('stroke-width', '2');
    svg.setAttribute('stroke-linecap', 'round');
    svg.setAttribute('stroke-linejoin', 'round');
    svg.classList.add('icon-' + iconName);

    // Copy classes from the original element
    if (element.className) {
      const classes = element.className.split(' ').filter(cls => cls && cls.trim());
      classes.forEach(cls => {
        try {
          svg.classList.add(cls.trim());
        } catch (e) {
          // Silently ignore class errors
        }
      });
    }

    // Try to use the sprite if available
    const use = document.createElementNS('http://www.w3.org/2000/svg', 'use');
    use.setAttributeNS('http://www.w3.org/1999/xlink', 'xlink:href', '#' + iconName);
    svg.appendChild(use);

    // Replace the original element with the icon
    if (element.parentNode) {
      element.parentNode.replaceChild(svg, element);
    }
  };

  // Set up a MutationObserver to watch for new icon elements
  function setupIconObserver() {
    // Create a function to process icons
    function processIcons() {
      document.querySelectorAll('i[data-lucide]').forEach(element => {
        // Skip if already processed
        if (element.dataset.processed) return;

        // Mark as processed
        element.dataset.processed = 'true';

        // Use our fallback icon creator
        window.createFallbackIcon(element);
      });
    }

    // Process existing icons
    processIcons();

    // Set up observer for new icons
    const observer = new MutationObserver(mutations => {
      let shouldProcess = false;

      mutations.forEach(mutation => {
        if (mutation.type === 'childList') {
          mutation.addedNodes.forEach(node => {
            if (node.nodeType === 1) { // Element node
              if (node.tagName && node.tagName.toLowerCase() === 'i' && node.hasAttribute && node.hasAttribute('data-lucide')) {
                shouldProcess = true;
              } else if (node.querySelectorAll) {
                const icons = node.querySelectorAll('i[data-lucide]');
                if (icons.length > 0) {
                  shouldProcess = true;
                }
              }
            }
          });
        }
      });

      if (shouldProcess) {
        processIcons();
      }
    });

    // Start observing
    observer.observe(document.body, {
      childList: true,
      subtree: true
    });

    // Handle AJAX requests
    const originalXHROpen = XMLHttpRequest.prototype.open;
    const originalXHRSend = XMLHttpRequest.prototype.send;

    XMLHttpRequest.prototype.open = function() {
      this._iconSuppressorMethod = arguments[0];
      this._iconSuppressorUrl = arguments[1];
      return originalXHROpen.apply(this, arguments);
    };

    XMLHttpRequest.prototype.send = function() {
      const xhr = this;
      const originalOnReadyStateChange = xhr.onreadystatechange;

      xhr.onreadystatechange = function() {
        if (xhr.readyState === 4) {
          // Process icons after AJAX request completes
          setTimeout(processIcons, 300);
        }

        if (originalOnReadyStateChange) {
          originalOnReadyStateChange.apply(this, arguments);
        }
      };

      return originalXHRSend.apply(this, arguments);
    };

    // Handle fetch requests
    const originalFetch = window.fetch;

    window.fetch = function() {
      const fetchPromise = originalFetch.apply(this, arguments);

      fetchPromise.then(() => {
        // Process icons after fetch request completes
        setTimeout(processIcons, 300);
      });

      return fetchPromise;
    };

    // Expose the function globally
    window.processAllIcons = processIcons;
  }

  // Set up the observer when the DOM is ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', setupIconObserver);
  } else {
    setupIconObserver();
  }
})();

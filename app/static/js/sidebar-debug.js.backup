/**
 * Sidebar functionality with extensive debugging
 */

console.log('Sidebar script loading...');

// Prevent FOUC by applying sidebar state immediately
(function() {
  console.log('Running FOUC prevention...');
  var isCollapsed = localStorage.getItem('sidebarCollapsed') === 'true';
  console.log('Initial sidebar state (collapsed):', isCollapsed);
  if (isCollapsed) document.documentElement.classList.add('sidebar-pre-collapsed');
})();

// Wait for DOM to be ready
document.addEventListener('DOMContentLoaded', function() {
  console.log('DOM loaded, initializing sidebar...');

  // Get DOM elements
  const sidebar = document.getElementById('sidebar');
  const mainContent = document.getElementById('main-content');
  const sidebarOverlay = document.getElementById('sidebar-overlay');
  const sidebarToggleBtn = document.getElementById('sidebar-toggle');
  const collapseSidebar = document.getElementById('collapse-sidebar');
  const userMenuButton = document.getElementById('user-menu-button');
  const userDropdown = document.getElementById('user-dropdown');
  const userMenuChevron = document.getElementById('user-menu-chevron');

  console.log('DOM elements found:', {
    sidebar: !!sidebar,
    mainContent: !!mainContent,
    sidebarOverlay: !!sidebarOverlay,
    sidebarToggleBtn: !!sidebarToggleBtn,
    collapseSidebar: !!collapseSidebar,
    userMenuButton: !!userMenuButton,
    userDropdown: !!userDropdown,
    userMenuChevron: !!userMenuChevron
  });

  // Setup sidebar toggle button
  if (collapseSidebar) {
    console.log('Setting up collapse sidebar button...');

    // Remove any existing event listeners
    collapseSidebar.removeEventListener('click', sidebarToggleHandler);

    // Define the click handler function
    function sidebarToggleHandler(e) {
      console.log('Collapse sidebar button clicked');
      e.preventDefault();
      e.stopPropagation(); // Prevent event bubbling

      // For mobile views, toggle mobile sidebar
      if (window.innerWidth < 1024) {
        console.log('Mobile view detected, toggling mobile sidebar');
        toggleMobileSidebar();
        return;
      }

      // Toggle sidebar collapsed state for desktop
      const isCollapsed = !sidebar.classList.contains('sidebar-collapsed');
      console.log('Toggling sidebar collapsed state to:', isCollapsed);

      // Update the DOM
      if (isCollapsed) {
        sidebar.classList.add('sidebar-collapsed');
      } else {
        sidebar.classList.remove('sidebar-collapsed');
      }

      // Save preference
      localStorage.setItem('sidebarCollapsed', isCollapsed);
      document.cookie = `sidebarCollapsed=${isCollapsed}; path=/; max-age=31536000`;
      console.log('Saved sidebar state to localStorage and cookie');

      // Adjust main content margin
      if (isCollapsed) {
        console.log('Adjusting main content for collapsed sidebar');
        mainContent.classList.remove('lg:ml-64');
        mainContent.classList.add('lg:ml-0');

        // Update icon
        const collapseIcon = collapseSidebar.querySelector('[data-lucide]');
        if (collapseIcon) {
          console.log('Updating collapse icon to panel-right');
          collapseIcon.setAttribute('data-lucide', 'panel-right');
          if (window.lucide) {
            console.log('Refreshing Lucide icons');
            window.lucide.createIcons({
              selector: '#collapse-sidebar [data-lucide]'
            });
          }
        }
      } else {
        console.log('Adjusting main content for expanded sidebar');
        mainContent.classList.remove('lg:ml-0');
        mainContent.classList.add('lg:ml-64');

        // Update icon
        const collapseIcon = collapseSidebar.querySelector('[data-lucide]');
        if (collapseIcon) {
          console.log('Updating collapse icon to panel-left');
          collapseIcon.setAttribute('data-lucide', 'panel-left');
          if (window.lucide) {
            console.log('Refreshing Lucide icons');
            window.lucide.createIcons({
              selector: '#collapse-sidebar [data-lucide]'
            });
          }
        }
      }

      // Log the final state
      console.log('Sidebar collapsed state after toggle:', sidebar.classList.contains('sidebar-collapsed'));
    }

    // Add the event listener
    collapseSidebar.addEventListener('click', sidebarToggleHandler);
  }

  // Setup mobile sidebar toggle
  if (sidebarToggleBtn) {
    console.log('Setting up mobile sidebar toggle button...');
    sidebarToggleBtn.addEventListener('click', toggleMobileSidebar);
  }

  // Close sidebar when overlay is clicked
  if (sidebarOverlay) {
    console.log('Setting up sidebar overlay click handler...');
    sidebarOverlay.addEventListener('click', toggleMobileSidebar);
  }

  // Setup user dropdown
  if (userMenuButton && userDropdown) {
    console.log('Setting up user dropdown...');
    userMenuButton.addEventListener('click', function(e) {
      console.log('User menu button clicked');
      e.stopPropagation();

      // Toggle dropdown visibility
      const isVisible = !userDropdown.classList.contains('hidden');
      userDropdown.classList.toggle('hidden');
      console.log('User dropdown visibility toggled to:', !isVisible);

      // Update chevron rotation
      if (userMenuChevron) {
        userMenuChevron.classList.toggle('rotate-180');
        console.log('User menu chevron rotation toggled');
      }

      // Position dropdown
      if (!userDropdown.classList.contains('hidden')) {
        console.log('Positioning user dropdown');
        const buttonRect = userMenuButton.getBoundingClientRect();
        const sidebarRect = sidebar.getBoundingClientRect();

        userDropdown.style.left = `${sidebarRect.right + 8}px`;
        userDropdown.style.top = `${buttonRect.top}px`;
        console.log('Dropdown positioned at:', { left: userDropdown.style.left, top: userDropdown.style.top });

        // Initialize icons
        if (window.lucide) {
          console.log('Refreshing Lucide icons in dropdown');
          window.lucide.createIcons({
            selector: '#user-dropdown [data-lucide]'
          });
        }
      }
    });

    // Close dropdown when clicking outside
    document.addEventListener('click', function(e) {
      if (!userDropdown.classList.contains('hidden') &&
          !userMenuButton.contains(e.target) &&
          !userDropdown.contains(e.target)) {
        console.log('Click outside user dropdown detected, hiding dropdown');
        userDropdown.classList.add('hidden');

        if (userMenuChevron) {
          userMenuChevron.classList.remove('rotate-180');
          console.log('User menu chevron rotation reset');
        }
      }
    });
  }

  // Handle window resize
  window.addEventListener('resize', function() {
    console.log('Window resize detected, current width:', window.innerWidth);
    if (window.innerWidth < 1024) {
      console.log('Mobile view detected on resize');
      mainContent.classList.remove('lg:ml-64', 'lg:ml-16');

      if (!sidebar.classList.contains('mobile-open')) {
        console.log('Hiding sidebar on mobile');
        sidebar.style.transform = 'translateX(-100%)';
      }
    } else {
      console.log('Desktop view detected on resize');
      sidebar.style.transform = '';
      initSidebar();
    }
  });

  // Toggle mobile sidebar function
  function toggleMobileSidebar() {
    console.log('Toggling mobile sidebar');
    const isOpen = sidebar.classList.toggle('mobile-open');
    console.log('Mobile sidebar open state:', isOpen);
    sidebarOverlay.classList.toggle('hidden');

    if (isOpen) {
      console.log('Opening mobile sidebar');
      sidebar.style.transform = 'translateX(0)';
      sidebar.style.zIndex = '50';
      setTimeout(() => {
        console.log('Fading in overlay');
        sidebarOverlay.style.opacity = '1';
      }, 10);
    } else {
      console.log('Closing mobile sidebar');
      sidebar.style.transform = 'translateX(-100%)';
      sidebarOverlay.style.opacity = '0';

      setTimeout(() => {
        if (!sidebar.classList.contains('mobile-open')) {
          console.log('Hiding overlay after animation');
          sidebarOverlay.classList.add('hidden');
        }
      }, 300);
    }

    // Toggle icon
    const toggleIcon = sidebarToggleBtn.querySelector('[data-lucide]');
    if (toggleIcon) {
      const newIcon = isOpen ? 'x' : 'menu';
      console.log('Updating mobile toggle icon to:', newIcon);
      toggleIcon.setAttribute('data-lucide', newIcon);
      if (window.lucide) {
        console.log('Refreshing Lucide icons');
        window.lucide.createIcons({
          selector: '#sidebar-toggle [data-lucide]'
        });
      }
    }
  }

  // Initialize sidebar state
  function initSidebar() {
    console.log('Initializing sidebar state');
    if (!sidebar || !mainContent) {
      console.error('Required DOM elements not found');
      return;
    }

    const isCollapsed = localStorage.getItem('sidebarCollapsed') === 'true';
    console.log('Sidebar collapsed state from localStorage:', isCollapsed);

    if (window.innerWidth >= 1024) {
      console.log('Initializing for desktop view');
      sidebar.style.transform = '';
      sidebar.classList.remove('mobile-open');

      if (isCollapsed) {
        console.log('Setting sidebar to collapsed state');
        sidebar.classList.add('sidebar-collapsed');
        mainContent.classList.remove('lg:ml-64');
        mainContent.classList.add('lg:ml-0');

        // Update collapse button icon
        if (collapseSidebar) {
          const collapseIcon = collapseSidebar.querySelector('[data-lucide]');
          if (collapseIcon) {
            console.log('Setting collapse icon to panel-right');
            collapseIcon.setAttribute('data-lucide', 'panel-right');
            if (window.lucide) {
              console.log('Refreshing Lucide icons');
              window.lucide.createIcons({
                selector: '#collapse-sidebar [data-lucide]'
              });
            }
          }
        }
      } else {
        console.log('Setting sidebar to expanded state');
        sidebar.classList.remove('sidebar-collapsed');
        mainContent.classList.remove('lg:ml-0');
        mainContent.classList.add('lg:ml-64');

        // Update collapse button icon
        if (collapseSidebar) {
          const collapseIcon = collapseSidebar.querySelector('[data-lucide]');
          if (collapseIcon) {
            console.log('Setting collapse icon to panel-left');
            collapseIcon.setAttribute('data-lucide', 'panel-left');
            if (window.lucide) {
              console.log('Refreshing Lucide icons');
              window.lucide.createIcons({
                selector: '#collapse-sidebar [data-lucide]'
              });
            }
          }
        }
      }
    } else {
      console.log('Initializing for mobile view');
      if (!sidebar.classList.contains('mobile-open')) {
        console.log('Hiding sidebar on mobile');
        sidebar.style.transform = 'translateX(-100%)';
      }
    }

    console.log('Removing pre-collapsed class');
    document.documentElement.classList.remove('sidebar-pre-collapsed');

    // Initialize submenus
    console.log('Initializing submenus');
    initSubmenus();
  }

  // Initialize submenus
  function initSubmenus() {
    console.log('Setting up submenu functionality');

    // Get all submenu toggles
    const submenuToggles = document.querySelectorAll('.submenu-toggle');
    console.log('Found submenu toggles:', submenuToggles.length);

    // Add click event to each toggle
    submenuToggles.forEach((toggle, index) => {
      console.log(`Setting up submenu toggle ${index + 1}:`, toggle.getAttribute('data-submenu-id'));

      // Remove any existing event listeners by using a direct approach
      toggle.removeEventListener('click', submenuClickHandler);

      // Define the click handler function
      function submenuClickHandler(e) {
        console.log('Submenu toggle clicked:', this.getAttribute('data-submenu-id'));
        e.preventDefault();
        e.stopPropagation(); // Prevent event bubbling

        // Get the submenu content
        const submenu = this.nextElementSibling;
        if (!submenu || !submenu.classList.contains('submenu-content')) {
          console.error('No valid submenu content found');
          return;
        }

        // Toggle expanded state
        const isExpanded = submenu.classList.contains('submenu-expanded');
        console.log('Current submenu expanded state:', isExpanded);

        // Get the chevron icon
        const chevron = this.querySelector('[data-lucide="chevron-down"]');
        console.log('Chevron element found:', !!chevron);

        if (isExpanded) {
          console.log('Collapsing submenu');
          // Collapse the submenu
          submenu.classList.remove('submenu-expanded');
          submenu.classList.remove('submenu-open');

          // Rotate chevron back
          if (chevron) {
            console.log('Rotating chevron back');
            chevron.classList.remove('rotate-180');
          }
        } else {
          console.log('Expanding submenu');
          // Expand the submenu
          submenu.classList.add('submenu-expanded');
          submenu.classList.add('submenu-open');

          // Rotate chevron
          if (chevron) {
            console.log('Rotating chevron');
            chevron.classList.add('rotate-180');
          }
        }

        // Save state to localStorage
        const submenuId = this.getAttribute('data-submenu-id');
        if (submenuId) {
          console.log('Saving submenu state for:', submenuId);
          const openSubmenus = JSON.parse(localStorage.getItem('openSubmenus') || '[]');
          console.log('Current open submenus:', openSubmenus);

          if (!isExpanded) {
            // Add to open submenus
            if (!openSubmenus.includes(submenuId)) {
              openSubmenus.push(submenuId);
              console.log('Added to open submenus:', submenuId);
            }
          } else {
            // Remove from open submenus
            const index = openSubmenus.indexOf(submenuId);
            if (index > -1) {
              openSubmenus.splice(index, 1);
              console.log('Removed from open submenus:', submenuId);
            }
          }

          localStorage.setItem('openSubmenus', JSON.stringify(openSubmenus));
          console.log('Saved open submenus to localStorage:', openSubmenus);
        }
      }

      // Add the event listener
      toggle.addEventListener('click', submenuClickHandler);
    });

    // Apply saved submenu state
    const openSubmenus = JSON.parse(localStorage.getItem('openSubmenus') || '[]');
    const isSidebarCollapsed = localStorage.getItem('sidebarCollapsed') === 'true';
    console.log('Applying saved submenu state:', { openSubmenus, isSidebarCollapsed });

    if (!isSidebarCollapsed && openSubmenus.length > 0) {
      console.log('Expanding saved submenus');
      openSubmenus.forEach(submenuId => {
        console.log('Expanding submenu:', submenuId);
        const toggle = document.querySelector(`.submenu-toggle[data-submenu-id="${submenuId}"]`);
        if (toggle) {
          console.log('Found toggle for submenu:', submenuId);
          const submenu = toggle.nextElementSibling;
          const chevron = toggle.querySelector('[data-lucide="chevron-down"]');

          if (submenu) {
            console.log('Expanding submenu content');
            submenu.classList.add('submenu-expanded');
            submenu.classList.add('submenu-open');
          }

          if (chevron) {
            console.log('Rotating chevron');
            chevron.classList.add('rotate-180');
          }
        } else {
          console.warn('Toggle not found for submenu:', submenuId);
        }
      });
    }

    // Expand submenus for active items
    const activeItems = document.querySelectorAll('.sidebar-item.active, .submenu-item.active');
    console.log('Found active items:', activeItems.length);

    activeItems.forEach((item, index) => {
      console.log(`Processing active item ${index + 1}`);
      const parentSubmenu = item.closest('.submenu-content');
      if (parentSubmenu) {
        console.log('Found parent submenu for active item');
        const parentToggle = parentSubmenu.previousElementSibling;
        if (parentToggle && parentToggle.classList.contains('submenu-toggle')) {
          console.log('Expanding parent submenu for active item');
          parentSubmenu.classList.add('submenu-expanded');
          parentSubmenu.classList.add('submenu-open');

          const chevron = parentToggle.querySelector('[data-lucide="chevron-down"]');
          if (chevron) {
            console.log('Rotating chevron for active item submenu');
            chevron.classList.add('rotate-180');
          }
        }
      }
    });
  }

  // Initialize sidebar
  console.log('Starting sidebar initialization');
  initSidebar();
  console.log('Sidebar initialization complete');
});

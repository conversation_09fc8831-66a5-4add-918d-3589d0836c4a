"""
API routes for dashboard data.
"""

from flask import jsonify, current_app
from flask_login import login_required, current_user

from app import cache, db
from app.models import User, BusinessUnit, BusinessSegment, Activity
from app.routes.api import api_bp
from app.routes.api.utils import handle_api_error


@api_bp.route('/dashboard/stats', methods=['GET'])
@login_required
@handle_api_error
@cache.cached(timeout=60, key_prefix='dashboard_stats')
def dashboard_stats():
    """Get key stats for the dashboard."""
    current_app.logger.info("Fetching dashboard stats (may be cached)")
    user_count = User.query.count()
    active_user_count = User.query.filter_by(is_active=True).count()
    business_unit_count = BusinessUnit.query.count()
    business_segment_count = BusinessSegment.query.count()

    # Get recent activities with eager loading
    recent_activities = Activity.query.options(
        db.joinedload('user')
    ).order_by(Activity.created_at.desc()).limit(5).all()

    activities_data = [{
        'id': activity.id,
        'user_id': activity.user_id,
        'user_name': activity.user.name if activity.user else 'Unknown',
        'action': activity.action,
        'created_at': activity.created_at.isoformat()
    } for activity in recent_activities]

    return jsonify({
        'success': True,
        'stats': {
            'users': {
                'total': user_count,
                'active': active_user_count
            },
            'business_units': business_unit_count,
            'business_segments': business_segment_count,
        },
        'recent_activities': activities_data
    })

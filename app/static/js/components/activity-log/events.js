/**
 * User Activity Log Component - Events Module
 *
 * This module handles event binding and user interactions for the activity log component.
 */

import { loadMoreActivities, getActivityDetails } from './data.js';
import { renderActivities, renderActivityDetails } from './rendering.js';

/**
 * Set up the activity details modal
 */
function setupActivityModal() {
    const modal = document.getElementById('activity-detail-modal');
    if (!modal) return;

    // Close button in header
    const closeBtn = modal.querySelector('.modal-close');
    if (closeBtn) {
        closeBtn.addEventListener('click', () => {
            closeActivityModal();
        });
    }

    // Close button in footer
    const closeBtnFooter = modal.querySelector('.modal-close-btn');
    if (closeBtnFooter) {
        closeBtnFooter.addEventListener('click', () => {
            closeActivityModal();
        });
    }

    // Close on overlay click
    const overlay = modal.querySelector('.modal-overlay');
    if (overlay) {
        overlay.addEventListener('click', () => {
            closeActivityModal();
        });
    }

    // Close on Escape key
    document.addEventListener('keydown', (e) => {
        if (e.key === 'Escape' && !modal.classList.contains('hidden')) {
            closeActivityModal();
        }
    });

    // Set up event delegation for view details buttons
    document.addEventListener('click', function(event) {
        const viewDetailsBtn = event.target.closest('.view-details-btn');
        if (viewDetailsBtn) {
            const activityId = viewDetailsBtn.dataset.activityId;
            if (activityId) {
                showActivityDetails(activityId);
            }
        }
    });
}

/**
 * Close the activity details modal
 */
function closeActivityModal() {
    const modal = document.getElementById('activity-detail-modal');
    if (!modal) return;

    // Add closing animation
    modal.classList.add('modal-closing');

    // Remove modal after animation
    setTimeout(() => {
        modal.classList.add('hidden');
        modal.classList.remove('modal-closing');
    }, 300);
}

/**
 * Show activity details in the modal
 * 
 * @param {number} activityId - ID of the activity to show details for
 */
function showActivityDetails(activityId) {
    const modal = document.getElementById('activity-detail-modal');
    if (!modal) return;

    // Show loading state
    modal.querySelector('.modal-content').classList.add('hidden');
    modal.querySelector('.modal-loading').classList.remove('hidden');
    modal.classList.remove('hidden');

    // Fetch activity details
    getActivityDetails(activityId)
        .then(activity => {
            // Hide loading state
            modal.querySelector('.modal-loading').classList.add('hidden');
            modal.querySelector('.modal-content').classList.remove('hidden');

            // Render activity details
            renderActivityDetails(activity);
        })
        .catch(error => {
            console.error('Error loading activity details:', error);
            
            // Show error state
            modal.querySelector('.modal-loading').classList.add('hidden');
            modal.querySelector('.modal-error').classList.remove('hidden');
            
            // Set error message
            const errorMessageEl = modal.querySelector('.modal-error-message');
            if (errorMessageEl) {
                errorMessageEl.textContent = error.message || 'An error occurred while loading activity details.';
            }
        });
}

/**
 * Set up the view all button
 * 
 * @param {HTMLElement} container - Container element with the view all button
 * @param {string} entityType - Entity type to filter by
 * @param {number} entityId - Entity ID to filter by
 * @param {boolean} inProfilePage - Whether this is in the profile page
 */
function setupViewAllButton(container, entityType, entityId, inProfilePage = false) {
    const viewAllBtn = container.querySelector('.view-all-activities');
    if (!viewAllBtn) return;

    viewAllBtn.addEventListener('click', () => {
        // This would typically navigate to a full activity log page
        // For now, we'll just load more activities in the same container
        const limit = 20;
        const listEl = container.querySelector('.activities-list');

        // Change button text to indicate loading more
        viewAllBtn.innerHTML = '<span>Loading more...</span><i data-lucide="loader" class="h-3.5 w-3.5 ml-1.5 animate-spin"></i>';

        // Re-initialize Lucide icons
        if (window.lucide) {
            lucide.createIcons({
                icons: {
                    loader: viewAllBtn.querySelector('[data-lucide="loader"]')
                }
            });
        }

        // Disable the button temporarily
        viewAllBtn.disabled = true;

        // Load more activities
        loadMoreActivities(entityType, entityId, limit, inProfilePage)
            .then(result => {
                // Render the new activities
                renderActivities(container, result.activities, inProfilePage);

                // Change button text to indicate success
                viewAllBtn.innerHTML = '<span>View All</span><i data-lucide="check" class="h-3.5 w-3.5 ml-1.5"></i>';
                
                // Re-initialize Lucide icons
                if (window.lucide) {
                    lucide.createIcons({
                        icons: {
                            check: viewAllBtn.querySelector('[data-lucide="check"]')
                        }
                    });
                }

                // Re-enable the button
                viewAllBtn.disabled = false;
            })
            .catch(error => {
                console.error('Error loading more activities:', error);
                
                // Change button text to indicate error
                viewAllBtn.innerHTML = '<span>Try Again</span><i data-lucide="alert-triangle" class="h-3.5 w-3.5 ml-1.5"></i>';
                
                // Re-initialize Lucide icons
                if (window.lucide) {
                    lucide.createIcons({
                        icons: {
                            'alert-triangle': viewAllBtn.querySelector('[data-lucide="alert-triangle"]')
                        }
                    });
                }

                // Re-enable the button
                viewAllBtn.disabled = false;
            });
    });
}

/**
 * Set up the retry button
 * 
 * @param {HTMLElement} container - Container element with the retry button
 * @param {string} entityType - Entity type to filter by
 * @param {number} entityId - Entity ID to filter by
 * @param {number} limit - Number of activities to load
 * @param {boolean} inProfilePage - Whether this is in the profile page
 */
function setupRetryButton(container, entityType, entityId, limit, inProfilePage = false) {
    const retryBtn = container.querySelector('.retry-loading');
    if (!retryBtn) return;

    retryBtn.addEventListener('click', () => {
        // Show loading state
        container.querySelector('.activities-loading').classList.remove('hidden');
        container.querySelector('.activities-error').classList.add('hidden');

        // Load activities
        loadActivities(entityType, entityId, limit, inProfilePage)
            .then(activities => {
                // Render activities
                renderActivities(container, activities, inProfilePage);
            })
            .catch(error => {
                console.error('Error loading activities:', error);
                container.querySelector('.activities-loading').classList.add('hidden');
                container.querySelector('.activities-error').classList.remove('hidden');
            });
    });
}

// Export public API
export {
    setupActivityModal,
    setupViewAllButton,
    setupRetryButton,
    showActivityDetails,
    closeActivityModal
};

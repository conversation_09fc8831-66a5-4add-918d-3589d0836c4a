/**
 * User Activity Log Component
 *
 * This script is kept for backward compatibility.
 * It imports and re-exports the functionality from the modular activity-log component.
 */

// Import from modular structure
import { initializeUserActivityLogs } from './activity-log/index.js';
import { loadActivities, loadMoreActivities, getActivityDetails } from './activity-log/data.js';
import { renderActivities, renderLoadingState, renderErrorState, renderActivityDetails } from './activity-log/rendering.js';
import { setupActivityModal, setupViewAllButton, setupRetryButton, showActivityDetails, closeActivityModal } from './activity-log/events.js';
import { formatDate, formatValue, getMethodColor, getMethodIcon, getSeverityColor, getCategoryIcon } from './activity-log/utils.js';

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    // Initialize all user activity log components on the page
    initializeUserActivityLogs();

    // Set up modal close handlers
    setupActivityModal();
});

// Export all functions for backward compatibility
window.initializeUserActivityLogs = initializeUserActivityLogs;
window.loadActivities = loadActivities;
window.renderActivities = renderActivities;
window.formatDate = formatDate;
window.formatValue = formatValue;
window.setupActivityModal = setupActivityModal;
window.closeActivityModal = closeActivityModal;
window.showActivityDetails = showActivityDetails;

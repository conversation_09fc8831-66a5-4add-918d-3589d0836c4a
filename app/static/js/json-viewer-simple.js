/**
 * JSON Viewer functionality
 * Provides a modal for viewing JSON data with syntax highlighting
 */

// Global variable to store the current JSON data
let currentJsonData = null;

/**
 * Show the JSON viewer modal with the provided data
 * @param {string} title - The title to display in the modal
 * @param {string} url - The URL to fetch JSON data from
 * @param {string} entityType - The type of entity being viewed
 * @param {number} entityId - The ID of the entity being viewed
 * @param {string} method - The HTTP method (create, read, update, delete)
 */
window.showJsonViewer = function(title, url, entityType, entityId, method = '') {
  // Get the template content
  const template = document.getElementById('json-viewer-modal-template');
  if (!template) {
    console.error('JSON viewer modal template not found');
    return;
  }

  // Create a clone of the template content
  const content = template.content.cloneNode(true);

  // Add the modal to the page
  document.body.appendChild(content);

  // Now that the modal is in the DOM, we can update its content
  const modal = document.getElementById('json-viewer-modal');
  const contentDiv = document.getElementById('json-viewer-content');
  const titleElement = document.getElementById('json-viewer-title');
  const infoElement = document.getElementById('json-viewer-info');
  const actionButton = document.getElementById('json-viewer-action');

  if (!contentDiv) {
    console.error('Content div not found in the modal');
    return;
  }

  // Set the title
  if (titleElement) {
    titleElement.textContent = title || 'Entity Details';
  }

  // Set info text
  if (infoElement) {
    infoElement.textContent = entityType && entityId ? `${entityType} #${entityId}` : '';
  }

  // Hide the copy button in the footer for simple URLs (without query parameters)
  if (actionButton) {
    if (url && !url.includes('?')) {
      actionButton.style.display = 'none';
    } else {
      actionButton.style.display = '';
    }
  }

  // Initialize any icons
  if (typeof lucide !== 'undefined') {
    lucide.createIcons();
  }

  // Regular API endpoint URL - fetch JSON data
  fetch(url)
    .then(response => response.json())
    .then(data => {
      // Store the data for copy functionality
      currentJsonData = data;

      // Format the JSON with syntax highlighting
      const formattedJson = formatJsonWithSyntaxHighlighting(data);

      // Create a container with proper styling for smaller modal
      const jsonContainer = document.createElement('div');
      jsonContainer.className = 'bg-muted/30 p-3 rounded-md overflow-auto max-h-[50vh] text-sm';
      jsonContainer.innerHTML = formattedJson;

      // Clear content and append the formatted JSON
      if (contentDiv) {
        contentDiv.innerHTML = '';
        contentDiv.appendChild(jsonContainer);
      }

      // Initialize any icons
      if (typeof lucide !== 'undefined') {
        lucide.createIcons();
      }
    })
    .catch(error => {
      console.error('Error loading data:', error);
      if (contentDiv) {
        contentDiv.innerHTML = '<div class="p-4 text-center text-destructive">An error occurred while loading data</div>';
      }
    });
}

/**
 * Format JSON with syntax highlighting
 * @param {object} json - The JSON object to format
 * @returns {string} HTML string with syntax highlighting
 */
window.formatJsonWithSyntaxHighlighting = function(json) {
  try {
    // Convert the JSON to a string with proper indentation
    const jsonString = JSON.stringify(json, null, 2);

    // Apply syntax highlighting with improved regex handling
    let highlighted = jsonString
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;');

    // Handle different parts of the JSON with specific colors
    highlighted = highlighted.replace(/(\"(\\u[a-zA-Z0-9]{4}|\\[^u]|[^\\\"])*\"(\\s*:)?|\\b(true|false|null)\\b|-?\\d+(?:\\.\\d*)?(?:[eE][+\\-]?\\d+)?)/g, function (match) {
      // Default color for numbers
      let cls = 'text-blue-500 dark:text-blue-400';

      // Handle strings and keys
      if (/^\"/.test(match)) {
        if (/:$/.test(match)) {
          // Keys in JSON
          cls = 'text-gray-700 dark:text-gray-300 font-medium';
        } else {
          // String values
          cls = 'text-green-500 dark:text-green-400';
        }
      }
      // Handle booleans
      else if (/true|false/.test(match)) {
        cls = 'text-orange-500 dark:text-orange-400';
      }
      // Handle null values
      else if (/null/.test(match)) {
        cls = 'text-red-500 dark:text-red-400';
      }

      return '<span class="' + cls + '">' + match + '</span>';
    });

    // Replace newlines and spaces for proper HTML display
    highlighted = highlighted
      .replace(/\n/g, '<br>')
      .replace(/\s{2}/g, '&nbsp;&nbsp;');

    return highlighted;
  } catch (error) {
    console.error('Error formatting JSON:', error);
    // Return a simple pre-formatted version as fallback with smaller text
    return '<pre class="text-xs">' +
           JSON.stringify(json, null, 2)
             .replace(/&/g, '&amp;')
             .replace(/</g, '&lt;')
             .replace(/>/g, '&gt;') +
           '</pre>';
  }
}

/**
 * Copy the current JSON data to clipboard
 */
window.copyJsonToClipboard = function() {
  if (!currentJsonData) return;

  const jsonString = JSON.stringify(currentJsonData, null, 2);

  // Use the generic copy function
  window.copyToClipboard(jsonString, 'JSON data');
}

/**
 * Copy any text to clipboard
 * @param {string} text - The text to copy
 * @param {string} [description='Text'] - Description of what was copied for the notification
 */
window.copyToClipboard = function(text, description = 'Text') {
  if (!text) return;

  // Create a temporary textarea element to copy from
  const textarea = document.createElement('textarea');
  textarea.value = text;
  textarea.setAttribute('readonly', '');
  textarea.style.position = 'absolute';
  textarea.style.left = '-9999px';
  document.body.appendChild(textarea);

  // Select and copy the text
  textarea.select();
  document.execCommand('copy');

  // Remove the textarea
  document.body.removeChild(textarea);

  // Show a notification
  if (typeof showToast === 'function') {
    showToast(`${description} copied to clipboard`, { type: 'success' });
  } else {
    alert(`${description} copied to clipboard`);
  }
}

/**
 * Close the JSON viewer modal
 */
window.closeJsonModal = function() {
  // Find the modal and remove it
  const modal = document.getElementById('json-viewer-modal');
  if (modal) {
    modal.remove();
  }

  // Clear the current JSON data
  currentJsonData = null;
}

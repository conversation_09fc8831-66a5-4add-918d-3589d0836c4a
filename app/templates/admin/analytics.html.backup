{% extends "base.html" %}
{% block title %}Analytics{% endblock %}

{% block header %}Analytics{% endblock %}

{% block styles %}
<style>
    /* Custom styles for charts */
    .chart-tooltip {
        background-color: rgba(255, 255, 255, 0.95);
        border: 1px solid rgba(0, 0, 0, 0.1);
        border-radius: 0.375rem;
        padding: 0.5rem;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    }

    .dark .chart-tooltip {
        background-color: rgba(30, 41, 59, 0.95);
        border-color: rgba(255, 255, 255, 0.1);
    }

    /* Smooth theme transitions */
    .analytics-card {
        transition: background-color 0.3s ease, border-color 0.3s ease, box-shadow 0.3s ease;
    }

    .analytics-card-header {
        transition: background-color 0.3s ease, border-color 0.3s ease;
    }

    .analytics-text {
        transition: color 0.3s ease;
    }

    .analytics-progress {
        transition: background-color 0.3s ease, width 0.5s ease-out;
    }

    /* Responsive adjustments */
    @media (max-width: 640px) {
        .analytics-grid-sm {
            grid-template-columns: repeat(1, minmax(0, 1fr)) !important;
        }
    }

    @media (max-width: 768px) {
        .analytics-card-content {
            padding: 1rem !important;
        }
    }

    /* Hover effects */
    .analytics-stat-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    }

    .dark .analytics-stat-card:hover {
        box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.25), 0 4px 6px -2px rgba(0, 0, 0, 0.1);
    }

    /* Skeleton loading animation */
    @keyframes pulse {
        0%, 100% {
            opacity: 0.5;
        }
        50% {
            opacity: 0.8;
        }
    }

    .skeleton-loading {
        animation: pulse 1.5s ease-in-out infinite;
        background-color: #e5e7eb;
    }

    .dark .skeleton-loading {
        background-color: #374151;
    }

    /* Fix for chart loading overlay */
    .chart-loading-overlay.flex {
        display: flex !important;
    }

    /* Chart legend styling */
    canvas + .chartjs-legend ul {
        display: flex;
        justify-content: center;
        gap: 1.5rem;
        margin-top: 0.75rem;
    }

    canvas + .chartjs-legend li {
        display: flex;
        align-items: center;
        cursor: pointer;
        padding: 0.25rem 0.5rem;
    }

    canvas + .chartjs-legend li span {
        display: inline-block;
        width: 10px;
        height: 10px;
        margin-right: 12px;
        border-radius: 50%;
    }

    /* Custom styling for chart legend text */
    .chart-legend-text {
        font-size: 0.875rem;
        font-weight: 500;
        color: #4b5563;
    }

    .dark .chart-legend-text {
        color: #d1d5db;
    }
</style>

{% endblock %}

{% block content %}
<!-- Analytics Header -->
<div class="mb-8">
    <div class="analytics-card bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 overflow-hidden">
        <div class="p-6 md:p-8 bg-gradient-to-r from-blue-50/50 via-blue-50/20 to-transparent dark:from-blue-900/20 dark:via-blue-900/10 dark:to-transparent">
            <div class="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
                <div class="flex items-start gap-4">
                    <div class="hidden sm:flex items-center justify-center w-12 h-12 rounded-lg bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 shadow-sm">
                        <i data-lucide="bar-chart-2" class="w-6 h-6"></i>
                    </div>
                    <div>
                        <h1 class="text-2xl md:text-3xl font-bold text-gray-900 dark:text-white analytics-text">Analytics Dashboard</h1>
                        <p class="text-gray-600 dark:text-gray-400 mt-1.5 analytics-text">Monitor user activity and engagement metrics</p>
                    </div>
                </div>
                <div class="flex flex-wrap items-center gap-3">
                    <div class="text-sm text-gray-600 dark:text-gray-400 bg-gray-50 dark:bg-gray-700/50 px-3 py-1.5 rounded-full flex items-center">
                        <i data-lucide="calendar" class="w-3.5 h-3.5 mr-1.5"></i>
                        <span id="period-display">Last 30 days</span>
                    </div>
                    <button class="inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-700 h-9 px-4 py-2 shadow-sm" aria-label="Refresh Analytics" id="refresh-analytics-btn">
                        <i data-lucide="refresh-cw" class="refresh-icon w-4 h-4 mr-2"></i>
                        <span>Refresh</span>
                    </button>
                    <button class="inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-blue-600 text-white hover:bg-blue-700 dark:bg-blue-600 dark:hover:bg-blue-500 h-9 px-4 py-2 shadow-sm" aria-label="Export Data">
                        <i data-lucide="download" class="w-4 h-4 mr-2"></i>
                        <span>Export</span>
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Stats Overview -->
<div class="mb-10">
    <div class="flex flex-wrap items-center justify-between mb-6">
        <h2 class="text-xl font-semibold text-gray-900 dark:text-white flex items-center analytics-text">
            <i data-lucide="bar-chart-2" class="w-5 h-5 text-blue-600 dark:text-blue-400 mr-2"></i>
            <span>Key Metrics</span>
        </h2>
        <div class="flex items-center mt-2 sm:mt-0">
            <div class="flex items-center space-x-2">
                <div class="relative inline-block">
                    <select class="appearance-none pl-3 pr-8 py-1.5 text-sm rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-200 focus:outline-none focus:ring-2 focus:ring-blue-500 cursor-pointer">
                        <option value="7">Last 7 days</option>
                        <option value="30" selected>Last 30 days</option>
                        <option value="90">Last 90 days</option>
                    </select>
                    <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700 dark:text-gray-300">
                        <i data-lucide="chevron-down" class="w-4 h-4"></i>
                    </div>
                </div>
                <div class="keyMetrics-loading hidden">
                    <div class="w-5 h-5 border-2 border-blue-200 border-t-blue-600 rounded-full animate-spin"></div>
                </div>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-5 md:gap-6 analytics-grid-sm">
        <!-- Visitors Stats Card -->
        <div class="analytics-card analytics-stat-card bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 shadow-md hover:shadow-lg transition-all duration-200 overflow-hidden">
            <div class="p-5 md:p-6 bg-gradient-to-br from-blue-50/30 via-blue-50/20 to-transparent dark:from-blue-900/10 dark:via-blue-900/5 dark:to-transparent">
                <div class="flex justify-between items-start">
                    <div>
                        <p class="text-sm font-medium text-gray-600 dark:text-gray-400 analytics-text">Visitors</p>
                        <div class="flex items-baseline gap-2 mt-2">
                            <p class="text-3xl font-bold text-gray-900 dark:text-white analytics-text">{{ visitors }}</p>
                            <span class="text-xs px-1.5 py-0.5 rounded-full bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400 border border-green-200 dark:border-green-800/30">+12%</span>
                        </div>
                        <p class="text-xs text-gray-500 dark:text-gray-400 mt-2 analytics-text">Unique users in period</p>
                    </div>
                    <div class="rounded-full p-3 bg-blue-100 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400 shadow-sm">
                        <i data-lucide="users" class="w-6 h-6"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- Page Views Stats Card -->
        <div class="analytics-card analytics-stat-card bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 shadow-md hover:shadow-lg transition-all duration-200 overflow-hidden">
            <div class="p-5 md:p-6 bg-gradient-to-br from-indigo-50/30 via-indigo-50/20 to-transparent dark:from-indigo-900/10 dark:via-indigo-900/5 dark:to-transparent">
                <div class="flex justify-between items-start">
                    <div>
                        <p class="text-sm font-medium text-gray-600 dark:text-gray-400 analytics-text">Page Views</p>
                        <div class="flex items-baseline gap-2 mt-2">
                            <p class="text-3xl font-bold text-gray-900 dark:text-white analytics-text">{{ page_views }}</p>
                            <span class="text-xs px-1.5 py-0.5 rounded-full bg-indigo-100 text-indigo-800 dark:bg-indigo-900/30 dark:text-indigo-400 border border-indigo-200 dark:border-indigo-800/30">+8%</span>
                        </div>
                        <p class="text-xs text-gray-500 dark:text-gray-400 mt-2 analytics-text">Total activity count</p>
                    </div>
                    <div class="rounded-full p-3 bg-indigo-100 dark:bg-indigo-900/20 text-indigo-600 dark:text-indigo-400 shadow-sm">
                        <i data-lucide="activity" class="w-6 h-6"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- Avg Time Stats Card -->
        <div class="analytics-card analytics-stat-card bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 shadow-md hover:shadow-lg transition-all duration-200 overflow-hidden">
            <div class="p-5 md:p-6 bg-gradient-to-br from-purple-50/30 via-purple-50/20 to-transparent dark:from-purple-900/10 dark:via-purple-900/5 dark:to-transparent">
                <div class="flex justify-between items-start">
                    <div>
                        <p class="text-sm font-medium text-gray-600 dark:text-gray-400 analytics-text">Avg. Time</p>
                        <div class="flex items-baseline gap-2 mt-2">
                            <p class="text-3xl font-bold text-gray-900 dark:text-white analytics-text">{{ avg_time }}</p>
                            <span class="text-xs px-1.5 py-0.5 rounded-full bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-400 border border-purple-200 dark:border-purple-800/30">+5%</span>
                        </div>
                        <p class="text-xs text-gray-500 dark:text-gray-400 mt-2 analytics-text">Average session duration</p>
                    </div>
                    <div class="rounded-full p-3 bg-purple-100 dark:bg-purple-900/20 text-purple-600 dark:text-purple-400 shadow-sm">
                        <i data-lucide="clock" class="w-6 h-6"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- Bounce Rate Stats Card -->
        <div class="analytics-card analytics-stat-card bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 shadow-md hover:shadow-lg transition-all duration-200 overflow-hidden">
            <div class="p-5 md:p-6 bg-gradient-to-br from-amber-50/30 via-amber-50/20 to-transparent dark:from-amber-900/10 dark:via-amber-900/5 dark:to-transparent">
                <div class="flex justify-between items-start">
                    <div>
                        <p class="text-sm font-medium text-gray-600 dark:text-gray-400 analytics-text">Bounce Rate</p>
                        <div class="flex items-baseline gap-2 mt-2">
                            <p class="text-3xl font-bold text-gray-900 dark:text-white analytics-text">{{ bounce_rate }}</p>
                            <span class="text-xs px-1.5 py-0.5 rounded-full bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400 border border-green-200 dark:border-green-800/30">-3%</span>
                        </div>
                        <p class="text-xs text-gray-500 dark:text-gray-400 mt-2 analytics-text">Single page session rate</p>
                    </div>
                    <div class="rounded-full p-3 bg-amber-100 dark:bg-amber-900/20 text-amber-600 dark:text-amber-400 shadow-sm">
                        <i data-lucide="percent" class="w-6 h-6"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

</div>

<!-- Main Analytics Content -->
<div class="grid grid-cols-1 lg:grid-cols-3 gap-6 md:gap-8 mb-8">
    <!-- Traffic Overview Chart -->
    <div class="lg:col-span-2">
        <div class="analytics-card bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 shadow-md overflow-hidden h-full">
            <div class="flex flex-col sm:flex-row sm:items-center justify-between p-5 md:p-6 border-b border-gray-200 dark:border-gray-700 bg-gray-50/80 dark:bg-gray-800/80 analytics-card-header">
                <div>
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white flex items-center analytics-text">
                        <i data-lucide="line-chart" class="w-5 h-5 text-blue-600 dark:text-blue-400 mr-2"></i>
                        <span>User Activity Trends</span>
                    </h3>
                    <p class="text-sm text-gray-600 dark:text-gray-400 mt-1 analytics-text">System usage patterns over time</p>
                </div>
                <div class="flex items-center space-x-2 mt-3 sm:mt-0">
                    <div class="inline-flex items-center rounded-md border border-gray-300 dark:border-gray-600 p-1 text-xs bg-white dark:bg-gray-700 shadow-sm">
                        <button id="chartDailyBtn" class="px-2.5 py-1.5 bg-blue-50 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 rounded-sm shadow-sm transition-colors" aria-label="Daily View">
                            <span>Daily</span>
                        </button>
                        <button id="chartWeeklyBtn" class="px-2.5 py-1.5 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-300 rounded-sm transition-colors" aria-label="Weekly View">
                            <span>Weekly</span>
                        </button>
                        <button id="chartMonthlyBtn" class="px-2.5 py-1.5 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-300 rounded-sm transition-colors" aria-label="Monthly View">
                            <span>Monthly</span>
                        </button>
                    </div>
                </div>
            </div>
            <div class="p-5 md:p-6 bg-white dark:bg-gray-800 analytics-card-content" style="height: 400px;">
                <div class="h-full w-full bg-white dark:bg-gray-800 rounded-md relative">
                    <div class="absolute inset-0 items-center justify-center chart-loading-overlay hidden">
                        <div class="flex flex-col items-center">
                            <div class="w-10 h-10 border-4 border-blue-200 border-t-blue-600 rounded-full animate-spin mb-3"></div>
                            <p class="text-gray-500 dark:text-gray-400">Loading data...</p>
                        </div>
                    </div>
                    <canvas id="trafficOverviewChart"></canvas>
                </div>
            </div>
        </div>

    </div>

    <!-- User Activity by Department -->

    <div class="lg:col-span-1">
        <div class="analytics-card bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 shadow-md overflow-hidden h-full">
            <div class="flex flex-col sm:flex-row sm:items-center justify-between p-5 md:p-6 border-b border-gray-200 dark:border-gray-700 bg-gray-50/80 dark:bg-gray-800/80 analytics-card-header">
                <div>
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white flex items-center analytics-text">
                        <i data-lucide="briefcase" class="w-5 h-5 text-blue-600 dark:text-blue-400 mr-2"></i>
                        <span>Department Activity</span>
                    </h3>
                    <p class="text-sm text-gray-600 dark:text-gray-400 mt-1 analytics-text">User activity by department</p>
                </div>
                <!-- No period filter for department section -->
                <div class="flex items-center space-x-2 mt-3 sm:mt-0">
                    <!-- Empty space to maintain layout -->
                </div>
            </div>
            <div class="p-5 md:p-6 overflow-y-auto analytics-card-content" style="height: 400px;">
                <div class="space-y-4">
                {% if department_data %}
                    {% for dept in department_data %}
                        {% set icon_name = 'briefcase' %}
                        {% set bg_color = 'bg-blue-100 dark:bg-blue-900/20' %}
                        {% set text_color = 'text-blue-600 dark:text-blue-400' %}
                        {% set bar_color = 'bg-blue-500 dark:bg-blue-400' %}

                        {% if loop.index == 1 %}
                            {% set icon_name = 'code' %}
                            {% set bg_color = 'bg-blue-100 dark:bg-blue-900/20' %}
                            {% set text_color = 'text-blue-600 dark:text-blue-400' %}
                            {% set bar_color = 'bg-blue-500 dark:bg-blue-400' %}
                        {% elif loop.index == 2 %}
                            {% set icon_name = 'trending-up' %}
                            {% set bg_color = 'bg-green-100 dark:bg-green-900/20' %}
                            {% set text_color = 'text-green-600 dark:text-green-400' %}
                            {% set bar_color = 'bg-green-500 dark:bg-green-400' %}
                        {% elif loop.index == 3 %}
                            {% set icon_name = 'megaphone' %}
                            {% set bg_color = 'bg-purple-100 dark:bg-purple-900/20' %}
                            {% set text_color = 'text-purple-600 dark:text-purple-400' %}
                            {% set bar_color = 'bg-purple-500 dark:bg-purple-400' %}
                        {% elif loop.index == 4 %}
                            {% set icon_name = 'landmark' %}
                            {% set bg_color = 'bg-amber-100 dark:bg-amber-900/20' %}
                            {% set text_color = 'text-amber-600 dark:text-amber-400' %}
                            {% set bar_color = 'bg-amber-500 dark:bg-amber-400' %}
                        {% elif loop.index == 5 %}
                            {% set icon_name = 'users' %}
                            {% set bg_color = 'bg-red-100 dark:bg-red-900/20' %}
                            {% set text_color = 'text-red-600 dark:text-red-400' %}
                            {% set bar_color = 'bg-red-500 dark:bg-red-400' %}
                        {% endif %}

                        <div class="flex items-center justify-between p-3 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors">
                            <div class="flex items-center">
                                <div class="w-10 h-10 rounded-full {{ bg_color }} flex items-center justify-center shadow-sm">
                                    <i data-lucide="{{ icon_name }}" class="w-5 h-5 {{ text_color }}"></i>
                                </div>
                                <div class="ml-3">
                                    <span class="font-medium text-gray-900 dark:text-white analytics-text">{{ dept.name }}</span>
                                    <p class="text-xs text-gray-500 dark:text-gray-400 analytics-text">{{ dept.users }} active users</p>
                                </div>
                            </div>
                            <div class="flex items-center">
                                <div class="w-24 bg-gray-200 dark:bg-gray-700 rounded-full h-2 mr-3 overflow-hidden">
                                    <div class="{{ bar_color }} h-full analytics-progress" style="width: {{ dept.percentage }}"></div>
                                </div>
                                <span class="text-sm font-semibold text-gray-900 dark:text-white analytics-text">{{ dept.percentage }}</span>
                            </div>
                        </div>
                    {% endfor %}
                {% else %}
                    <!-- Fallback if no department data -->
                    <div class="flex items-center justify-center p-6">
                        <div class="text-center">
                            <i data-lucide="info" class="w-8 h-8 text-gray-400 dark:text-gray-600 mx-auto mb-2"></i>
                            <p class="text-gray-500 dark:text-gray-400 analytics-text">No department activity data available</p>
                        </div>
                    </div>
                {% endif %}
                </div>
            </div>
        </div>

    </div>
</div>

<!-- Additional Analytics Sections -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mt-6">
        <!-- User Activity Section -->
        <div>
            <div class="analytics-card bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 shadow-md overflow-hidden">
                <div class="flex flex-col sm:flex-row sm:items-center justify-between p-5 md:p-6 border-b border-gray-200 dark:border-gray-700 bg-gray-50/80 dark:bg-gray-800/80 analytics-card-header">
                    <div>
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white flex items-center analytics-text">
                            <i data-lucide="users" class="w-5 h-5 text-blue-600 dark:text-blue-400 mr-2"></i>
                            <span>User Activity</span>
                        </h3>
                        <p class="text-sm text-gray-600 dark:text-gray-400 mt-1 analytics-text">Active users and engagement</p>
                    </div>
                    <!-- No period filter for user activity section -->
                    <div class="flex items-center space-x-2 mt-3 sm:mt-0">
                        <!-- Empty space to maintain layout -->
                    </div>
                </div>
                <div class="p-5 md:p-6 analytics-card-content">
                    <div class="space-y-6">
                        <!-- Active Users -->
                        <div>
                            <div class="flex justify-between items-center mb-2">
                                <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 analytics-text">Active Users</h4>
                                <span class="text-sm font-semibold text-gray-900 dark:text-white analytics-text">{{ active_users }} / {{ total_users }}</span>
                            </div>
                            <div class="h-2.5 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden shadow-inner">
                                {% set active_percentage = ((active_users|int) / (total_users|int) * 100) if total_users|int > 0 else 0 %}
                                <div class="bg-blue-500 dark:bg-blue-400 h-full analytics-progress transition-all duration-500 ease-out" style="width: {{ active_percentage }}%"></div>
                            </div>
                            <div class="flex justify-between items-center mt-1.5">
                                <span class="text-xs text-gray-500 dark:text-gray-400 analytics-text">0</span>
                                <span class="text-xs text-gray-500 dark:text-gray-400 analytics-text">{{ total_users }}</span>
                            </div>
                        </div>

                        <!-- New Registrations -->
                        <div>
                            <div class="flex justify-between items-center mb-3">
                                <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 analytics-text">New Registrations (Last 7 days)</h4>
                                <span class="text-sm font-semibold text-gray-900 dark:text-white new-registrations-total analytics-text">0</span>
                            </div>
                            <div class="grid grid-cols-7 gap-1.5 h-20">
                                {# Since dates and registration_counts are already JSON strings, we'll use them directly in JavaScript #}
                                {# For the template, we'll just use placeholder data for now #}
                                {% set dates_list = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'] %}
                                {% set counts_list = [0, 0, 0, 0, 0, 0, 0] %}
                                {% set max_count = counts_list|map('int')|max if counts_list else 1 %}

                                {% for i in range(7) %}
                                    {% set date = dates_list[i] if i < dates_list|length else '' %}
                                    {% set count = counts_list[i]|int if i < counts_list|length else 0 %}
                                    {% set height = ((count|int) / (max_count|int) * 100) if max_count|int > 0 else 0 %}

                                    <div class="flex flex-col items-center">
                                        <div class="w-full bg-gray-100 dark:bg-gray-700 rounded-md relative shadow-inner" style="height: 100%">
                                            <div class="absolute bottom-0 left-0 right-0 bg-green-400 dark:bg-green-500 rounded-md registration-bar transition-all duration-500 ease-out" style="height: 0%"></div>
                                        </div>
                                        <span class="text-xs text-gray-500 dark:text-gray-400 mt-1.5 registration-date analytics-text"></span>
                                    </div>
                                {% endfor %}
                            </div>
                        </div>

                        <!-- User Engagement -->
                        <div>
                            <div class="flex justify-between items-center mb-3">
                                <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 analytics-text">User Engagement</h4>
                            </div>
                            <div class="grid grid-cols-3 gap-4">
                                <div class="bg-white dark:bg-gray-700/50 rounded-lg p-3 text-center border border-gray-200 dark:border-gray-700 shadow-sm">
                                    <p class="text-xs text-gray-500 dark:text-gray-400 analytics-text">Avg. Sessions</p>
                                    <p class="text-lg font-semibold text-gray-900 dark:text-white mt-1 analytics-text">{{ avg_sessions }}</p>
                                    <p class="text-xs text-gray-500 dark:text-gray-400 mt-1 analytics-text">per user</p>
                                </div>
                                <div class="bg-white dark:bg-gray-700/50 rounded-lg p-3 text-center border border-gray-200 dark:border-gray-700 shadow-sm">
                                    <p class="text-xs text-gray-500 dark:text-gray-400 analytics-text">Pages/Session</p>
                                    <p class="text-lg font-semibold text-gray-900 dark:text-white mt-1 analytics-text">{{ avg_pages_per_session }}</p>
                                    <p class="text-xs text-gray-500 dark:text-gray-400 mt-1 analytics-text">activities per day</p>
                                </div>
                                <div class="bg-white dark:bg-gray-700/50 rounded-lg p-3 text-center border border-gray-200 dark:border-gray-700 shadow-sm">
                                    <p class="text-xs text-gray-500 dark:text-gray-400 analytics-text">Retention</p>
                                    <p class="text-lg font-semibold text-gray-900 dark:text-white mt-1 analytics-text">{{ retention_rate }}%</p>
                                    <p class="text-xs text-gray-500 dark:text-gray-400 mt-1 analytics-text">return users</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>


    <!-- User Behavior Analysis -->
    <div>
        <div class="analytics-card bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 shadow-md overflow-hidden">
            <div class="flex flex-col sm:flex-row sm:items-center justify-between p-5 md:p-6 border-b border-gray-200 dark:border-gray-700 bg-gray-50/80 dark:bg-gray-800/80 analytics-card-header">
                <div>
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white flex items-center analytics-text">
                        <i data-lucide="user-cog" class="w-5 h-5 text-blue-600 dark:text-blue-400 mr-2"></i>
                        <span>User Behavior Analysis</span>
                    </h3>
                    <p class="text-sm text-gray-600 dark:text-gray-400 mt-1 analytics-text">Insights into how users interact with the system</p>
                </div>
                <div class="flex items-center space-x-2 mt-3 sm:mt-0">
                    <button class="px-3 py-1.5 text-sm rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500 flex items-center shadow-sm transition-colors" id="refresh-behavior-btn">
                        <i data-lucide="refresh-cw" class="w-3.5 h-3.5 mr-1.5"></i>
                        <span>Refresh</span>
                    </button>
                </div>
            </div>
            <div class="p-5 md:p-6 analytics-card-content">
                <div class="grid grid-cols-1 gap-6">
                    <!-- Usage Patterns -->
                    <div>
                        <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-4 analytics-text">Usage Patterns</h4>
                        <div class="space-y-5">
                            <!-- Most Active Time -->
                            <div>
                                <div class="flex justify-between items-center mb-2">
                                    <span class="text-sm font-medium text-gray-900 dark:text-white analytics-text">Most Active Time of Day</span>
                                    <span class="text-sm font-semibold text-gray-900 dark:text-white analytics-text">{{ user_behavior.most_active_time }}</span>
                                </div>
                                <div class="h-2.5 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden shadow-inner">
                                    <div class="bg-blue-500 dark:bg-blue-400 h-full analytics-progress" style="width: 100%"></div>
                                </div>
                                <div class="flex justify-between items-center mt-1.5">
                                    <span class="text-xs text-gray-500 dark:text-gray-400 analytics-text">Based on activity timestamps</span>
                                    <span class="text-xs text-blue-500 dark:text-blue-400 analytics-text">Peak Activity</span>
                                </div>
                            </div>

                            <!-- User Growth Rate -->
                            <div>
                                <div class="flex justify-between items-center mb-2">
                                    <span class="text-sm font-medium text-gray-900 dark:text-white analytics-text">User Growth Rate</span>
                                    <span class="text-sm font-semibold text-gray-900 dark:text-white analytics-text">{{ user_behavior.growth_rate }}%</span>
                                </div>
                                <div class="h-2.5 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden shadow-inner">
                                    {% set growth_width = 50 + (user_behavior.growth_rate / 2) if user_behavior.growth_rate > 0 else 50 - (user_behavior.growth_rate|abs / 2) %}
                                    {% set growth_color = 'bg-green-500 dark:bg-green-400' if user_behavior.growth_rate >= 0 else 'bg-red-500 dark:bg-red-400' %}
                                    <div class="{{ growth_color }} h-full analytics-progress transition-all duration-500 ease-out" style="width: {{ growth_width }}%"></div>
                                </div>
                                <div class="flex justify-between items-center mt-1.5">
                                    <span class="text-xs text-gray-500 dark:text-gray-400 analytics-text">Last 15 days vs previous 15 days</span>
                                    <span class="text-xs {{ 'text-green-500 dark:text-green-400' if user_behavior.growth_rate >= 0 else 'text-red-500 dark:text-red-400' }} analytics-text">
                                        {{ 'Growing' if user_behavior.growth_rate > 0 else 'Stable' if user_behavior.growth_rate == 0 else 'Declining' }}
                                    </span>
                                </div>
                            </div>

                            <!-- Average Activities Per User -->
                            <div>
                                <div class="flex justify-between items-center mb-2">
                                    <span class="text-sm font-medium text-gray-900 dark:text-white analytics-text">Avg. Activities Per User</span>
                                    <span class="text-sm font-semibold text-gray-900 dark:text-white analytics-text">{{ user_behavior.avg_activities_per_user }}</span>
                                </div>
                                <div class="h-2.5 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden shadow-inner">
                                    {% set activity_width = user_behavior.avg_activities_per_user * 10 if user_behavior.avg_activities_per_user <= 10 else 100 %}
                                    <div class="bg-purple-500 dark:bg-purple-400 h-full analytics-progress transition-all duration-500 ease-out" style="width: {{ activity_width }}%"></div>
                                </div>
                                <div class="flex justify-between items-center mt-1.5">
                                    <span class="text-xs text-gray-500 dark:text-gray-400 analytics-text">Higher is better</span>
                                    <span class="text-xs text-purple-500 dark:text-purple-400 analytics-text">User Engagement</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- User Actions -->
                    <div>
                        <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-4">User Actions</h4>
                        <div class="grid grid-cols-2 gap-4">
                            <!-- Top Actions -->
                            <div class="bg-white dark:bg-gray-700/50 border border-gray-200 dark:border-gray-700 rounded-lg p-4 shadow-sm">
                                <div class="flex items-center justify-between mb-2">
                                    <span class="text-sm font-medium text-gray-700 dark:text-gray-300 analytics-text">Most Common Actions</span>
                                    <div class="w-8 h-8 rounded-full bg-blue-100 dark:bg-blue-900/20 flex items-center justify-center">
                                        <i data-lucide="list" class="w-4 h-4 text-blue-600 dark:text-blue-400"></i>
                                    </div>
                                </div>
                                <div class="space-y-2">
                                    {% for action in user_behavior.top_actions[:3] %}
                                        <div class="flex items-center">
                                            <div class="w-2 h-2 rounded-full bg-blue-500 dark:bg-blue-400 mr-2"></div>
                                            <p class="text-sm text-gray-900 dark:text-white truncate analytics-text">{{ action }}</p>
                                        </div>
                                    {% else %}
                                        <p class="text-sm text-gray-500 dark:text-gray-400 analytics-text">No actions recorded</p>
                                    {% endfor %}
                                </div>
                            </div>

                            <!-- Active Sessions -->
                            <div class="bg-white dark:bg-gray-700/50 border border-gray-200 dark:border-gray-700 rounded-lg p-4 shadow-sm">
                                <div class="flex items-center justify-between mb-2">
                                    <span class="text-sm font-medium text-gray-700 dark:text-gray-300 analytics-text">Active Sessions</span>
                                    <div class="w-8 h-8 rounded-full bg-blue-100 dark:bg-blue-900/20 flex items-center justify-center">
                                        <i data-lucide="users" class="w-4 h-4 text-blue-600 dark:text-blue-400"></i>
                                    </div>
                                </div>
                                <p class="text-2xl font-bold text-gray-900 dark:text-white analytics-text">{{ user_behavior.active_sessions }}</p>
                                <div class="flex items-center mt-1">
                                    <span class="text-xs text-gray-500 dark:text-gray-400 analytics-text">Last 30 minutes</span>
                                </div>
                            </div>

                            <!-- User Engagement Score -->
                            <div class="bg-white dark:bg-gray-700/50 border border-gray-200 dark:border-gray-700 rounded-lg p-4 shadow-sm">
                                <div class="flex items-center justify-between mb-2">
                                    <span class="text-sm font-medium text-gray-700 dark:text-gray-300 analytics-text">Engagement Score</span>
                                    <div class="w-8 h-8 rounded-full bg-green-100 dark:bg-green-900/20 flex items-center justify-center">
                                        <i data-lucide="heart-pulse" class="w-4 h-4 text-green-600 dark:text-green-400"></i>
                                    </div>
                                </div>
                                {% set engagement_score = (retention_rate + (avg_sessions * 10) + (avg_pages_per_session * 5)) / 3 %}
                                {% set engagement_level = 'High' if engagement_score > 70 else 'Medium' if engagement_score > 40 else 'Low' %}
                                {% set engagement_color = 'text-green-500 dark:text-green-400' if engagement_score > 70 else 'text-amber-500 dark:text-amber-400' if engagement_score > 40 else 'text-red-500 dark:text-red-400' %}
                                <p class="text-2xl font-bold text-gray-900 dark:text-white analytics-text">{{ engagement_score|round }}</p>
                                <div class="flex items-center mt-1">
                                    <span class="text-xs {{ engagement_color }}">{{ engagement_level }} Engagement</span>
                                </div>
                            </div>

                            <!-- Retention Health -->
                            <div class="bg-white dark:bg-gray-700/50 border border-gray-200 dark:border-gray-700 rounded-lg p-4 shadow-sm">
                                <div class="flex items-center justify-between mb-2">
                                    <span class="text-sm font-medium text-gray-700 dark:text-gray-300 analytics-text">Retention Health</span>
                                    <div class="w-8 h-8 rounded-full bg-indigo-100 dark:bg-indigo-900/20 flex items-center justify-center">
                                        <i data-lucide="activity" class="w-4 h-4 text-indigo-600 dark:text-indigo-400"></i>
                                    </div>
                                </div>
                                {% set retention_health = 'Excellent' if retention_rate > 75 else 'Good' if retention_rate > 50 else 'Needs Improvement' if retention_rate > 25 else 'Poor' %}
                                {% set retention_color = 'text-green-500 dark:text-green-400' if retention_rate > 75 else 'text-blue-500 dark:text-blue-400' if retention_rate > 50 else 'text-amber-500 dark:text-amber-400' if retention_rate > 25 else 'text-red-500 dark:text-red-400' %}
                                <p class="text-lg font-bold text-gray-900 dark:text-white analytics-text">{{ retention_health }}</p>
                                <div class="flex items-center mt-1">
                                    <span class="text-xs {{ retention_color }}">{{ retention_rate }}% return rate</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block scripts %}
<!-- Include Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<script>
        // Global variables for chart data
        let activityDates = [];
        let activityCounts = [];
        let userCounts = [];
        let formattedDates = [];


document.addEventListener('DOMContentLoaded', function() {
    initAnalytics();
});

function initAnalytics() {
    // Initialize refresh button
    initRefreshButton();

    // Load data from backend
    loadChartData();

    // Initialize charts
    initTrafficChart();

    // Initialize chart period buttons
    initChartPeriodButtons();

    // Initialize registration data
    initRegistrationData();
}

function loadChartData() {
    // Get data from the backend
    try {
        // Check if the data is already parsed or needs to be parsed
        const activityDatesStr = '{{ activity_dates|safe }}';
        const activityCountsStr = '{{ activity_counts|safe }}';
        const userCountsStr = '{{ user_counts|safe }}';

        // Try to parse if it's a string, otherwise use as is
        activityDates = typeof activityDatesStr === 'string' ?
            (activityDatesStr.startsWith('[') ? JSON.parse(activityDatesStr) : activityDatesStr) :
            activityDatesStr;

        activityCounts = typeof activityCountsStr === 'string' ?
            (activityCountsStr.startsWith('[') ? JSON.parse(activityCountsStr) : activityCountsStr) :
            activityCountsStr;

        userCounts = typeof userCountsStr === 'string' ?
            (userCountsStr.startsWith('[') ? JSON.parse(userCountsStr) : userCountsStr) :
            userCountsStr;

        console.log('Activity Dates:', activityDates);
        console.log('Activity Counts:', activityCounts);
        console.log('User Counts:', userCounts);
    } catch (e) {
        console.error('Error parsing activity data:', e);
        activityDates = [];
        activityCounts = [];
        userCounts = [];
    }

    // Format dates for display (just show day and month)
    if (activityDates && activityDates.length > 0) {
        formattedDates = activityDates.map(date => {
            try {
                const dateObj = new Date(date);
                return dateObj.getDate() + '/' + (dateObj.getMonth() + 1);
            } catch (e) {
                console.error(`Error formatting date: ${date}`, e);
                return 'Invalid';
            }
        });
    } else {
        console.warn('No activity dates available');
        formattedDates = [];
    }
}

function initRegistrationData() {
    // Parse the registration data from the server
        try {
                        // Get the JSON strings from the template
                        const datesStr = '{{ dates|safe }}';
                        const countsStr = '{{ registration_counts|safe }}';

                        // Parse the JSON strings
                        const dates = JSON.parse(datesStr);
                        const counts = JSON.parse(countsStr);

                        // Calculate the total registrations
                        const totalRegistrations = counts.reduce((sum, count) => sum + count, 0);

                        // Update the total registrations display
                        const totalElement = document.querySelector('.new-registrations-total');
                        if (totalElement) {
                            totalElement.textContent = totalRegistrations;
                        }

                        // Update the registration bars
                        const maxCount = Math.max(...counts, 1);
                        const bars = document.querySelectorAll('.registration-bar');
                        const dateLabels = document.querySelectorAll('.registration-date');

                        for (let i = 0; i < bars.length; i++) {
                            if (i < counts.length) {
                                const height = (counts[i] / maxCount) * 100;
                                bars[i].style.height = `${height}%`;

                                // Format date for display (show day of month)
                                if (i < dates.length && dateLabels[i]) {
                                    const dateObj = new Date(dates[i]);
                                    dateLabels[i].textContent = dateObj.getDate();
                                }
                            }
                        }
                    } catch (error) {
                        console.error('Error initializing registration data:', error);
                    }
                }

                function initRefreshButton() {
                    // Initialize main refresh button
                    const refreshBtn = document.getElementById('refresh-analytics-btn');
                    if (refreshBtn) {
                        refreshBtn.addEventListener('click', function() {
                            // Show loading state
                            this.classList.add('opacity-50', 'pointer-events-none');

                            // Find the SVG directly
                            const svg = this.querySelector('svg');
                            if (svg) {
                                // Add spin animation directly to the SVG
                                svg.classList.add('animate-spin');
                            }

                            // Actually refresh the page data
                            setTimeout(() => {
                                // Reload the page to get fresh data
                                window.location.reload();
                            }, 300);
                        });
                    }

                    // Initialize user behavior refresh button
                    const behaviorBtn = document.getElementById('refresh-behavior-btn');
                    if (behaviorBtn) {
                        behaviorBtn.addEventListener('click', function() {
                            // Show loading state
                            this.classList.add('opacity-50', 'pointer-events-none');

                            // Find the SVG directly
                            const svg = this.querySelector('svg');
                            if (svg) {
                                // Add spin animation directly to the SVG
                                svg.classList.add('animate-spin');
                            }

                            // Actually refresh the page data
                            setTimeout(() => {
                                // Reload the page to get fresh data
                                window.location.reload();
                            }, 300);
                        });
                    }

                    // Initialize all period dropdowns
                    initPeriodDropdowns();
                }

                function getPeriodText(period) {
                    switch(period) {
                        case '7':
                            return 'Last 7 days';
                        case '30':
                            return 'Last 30 days';
                        case '90':
                            return 'Last 90 days';
                        default:
                            return 'Last 30 days';
                    }
                }

                function initPeriodDropdowns() {
                    // Get the main period dropdown
                    const mainPeriodDropdown = document.querySelector('.flex.items-center.mt-2 select');
                    if (!mainPeriodDropdown) return;

                    // Get URL parameters
                    const urlParams = new URLSearchParams(window.location.search);
                    const periodParam = urlParams.get('period');

                    // Update the period display in the header
                    const periodDisplay = document.getElementById('period-display');

                    // Set dropdown value if specified in URL
                    if (periodParam) {
                        const hasOption = Array.from(mainPeriodDropdown.options).some(option => option.value === periodParam);
                        if (hasOption) {
                            mainPeriodDropdown.value = periodParam;

                            // Update the period display text
                            if (periodDisplay) {
                                periodDisplay.textContent = getPeriodText(periodParam);
                            }

                            // No need to update other period displays as they've been removed
                        }
                    }

                    // Add event listener to the main period dropdown
                    mainPeriodDropdown.addEventListener('change', function() {
                        const period = this.value;
                        const loadingOverlay = document.querySelector('.chart-loading-overlay');

                        // Show loading state if available
                        if (loadingOverlay) {
                            loadingOverlay.classList.remove('hidden');
                            loadingOverlay.classList.add('flex');
                        }

                        // Update the keyMetrics loading indicator
                        const keyMetricsLoader = document.querySelector('.keyMetrics-loading');
                        if (keyMetricsLoader) {
                            keyMetricsLoader.classList.remove('hidden');
                        }

                        // Update the period display text
                        if (periodDisplay) {
                            periodDisplay.textContent = getPeriodText(period);
                        }

                        // No need to update other period displays as they've been removed

                        // Simulate loading data for the selected period
                        setTimeout(() => {
                            // Navigate to the updated URL with the new period
                            window.location.href = `${window.location.pathname}?period=${period}`;
                        }, 300);
                    });
                }

                function initTrafficChart() {
                    const ctx = document.getElementById('trafficOverviewChart');
                    if (!ctx) return;

                    // Get the loading overlay
                    const loadingOverlay = document.querySelector('.chart-loading-overlay');
                    if (loadingOverlay) {
                        loadingOverlay.classList.remove('hidden');
                        loadingOverlay.classList.add('flex');
                    }

                    // Check if we have data
                    if (!activityDates.length || !activityCounts.length || !userCounts.length) {
                        console.warn('No activity data available for chart');

                        // Hide loading overlay
                        if (loadingOverlay) {
                            loadingOverlay.classList.add('hidden');
                            loadingOverlay.classList.remove('flex');
                        }

                        // Display a message in the chart area
                        ctx.parentNode.innerHTML = `
                            <div class="flex items-center justify-center h-full">
                                <div class="text-center p-6">
                                    <i data-lucide="bar-chart" class="w-10 h-10 text-gray-400 dark:text-gray-600 mx-auto mb-3"></i>
                                    <p class="text-gray-500 dark:text-gray-400 mb-1 analytics-text">No activity data available</p>
                                    <p class="text-xs text-gray-400 dark:text-gray-500 analytics-text">Activity data will appear here once users start using the system</p>
                                </div>
                            </div>
                        `;

                        // Initialize Lucide icons for the new elements
                        if (window.lucide) {
                            window.lucide.createIcons();
                        }

                        return;
                    }

                    const data = {
                        labels: formattedDates,

                    datasets: [{
                        label: 'Active Users',
                        data: userCounts,
                        borderColor: '#3b82f6',
                        backgroundColor: 'rgba(59, 130, 246, 0.1)',
                        borderWidth: 2,
                        tension: 0.4,
                        fill: true
                    }, {
                        label: 'Activities',
                        data: activityCounts,
                        borderColor: '#10b981',
                        backgroundColor: 'rgba(16, 185, 129, 0.0)',
                        borderWidth: 2,
                        tension: 0.4,
                        fill: false
                    }]
                };

                const config = {
                    type: 'line',
                    data: data,

                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: true,
                            position: 'top',
                            labels: {
                                usePointStyle: true,
                                boxWidth: 8,
                                padding: 15,
                                color: '#4b5563',
                                font: {
                                    size: 13,
                                    family: "'Inter', sans-serif",
                                    weight: '500'
                                },
                                generateLabels: function(chart) {
                                    const originalLabels = Chart.defaults.plugins.legend.labels.generateLabels(chart);
                                    originalLabels.forEach(label => {
                                        label.text = ' ' + label.text + ' ';
                                    });
                                    return originalLabels;
                                }
                            }

                        },
                        tooltip: {
                            backgroundColor: 'rgba(255, 255, 255, 0.95)',
                            titleColor: '#1e293b',
                            bodyColor: '#475569',
                            borderColor: 'rgba(0, 0, 0, 0.1)',
                            borderWidth: 1,
                            padding: 10,
                            boxPadding: 5,
                            usePointStyle: true,
                            titleFont: {
                                size: 12,
                                family: "'Inter', sans-serif"
                            },
                            bodyFont: {
                                size: 11,
                                family: "'Inter', sans-serif"
                            },

                            callbacks: {
                                labelTextColor: function() {
                                    return '#475569';
                                }
                            }
                        }
                    },
                    scales: {
                        x: {
                            grid: {
                                display: false
                            },
                            ticks: {
                                color: '#94a3b8',
                                font: {
                                    size: window.innerWidth < 768 ? 9 : 10
                                },
                                maxRotation: 0,
                                autoSkipPadding: 20
                            }
                        },
                        y: {
                            beginAtZero: true,
                            grid: {
                                color: 'rgba(148, 163, 184, 0.1)'
                            },
                            ticks: {
                                color: '#94a3b8',
                                precision: 0,
                                font: {
                                    size: window.innerWidth < 768 ? 9 : 10
                                }
                            }
                        }
                    },
                    interaction: {
                        intersect: false,
                        mode: 'index'
                    },
                    elements: {
                        point: {
                            radius: window.innerWidth < 768 ? 1 : 2,
                            hoverRadius: window.innerWidth < 768 ? 3 : 4
                        }
                    },

                    animation: {
                        duration: 1000,
                        easing: 'easeOutQuart',
                        onComplete: function() {
                            // Hide loading overlay when animation completes
                            if (loadingOverlay) {
                                loadingOverlay.classList.add('hidden');
                                loadingOverlay.classList.remove('flex');

                    }
                }
            }
        }
    };


            // Check if we're in dark mode and update chart styles
            if (document.documentElement.classList.contains('dark')) {
                            // Update tooltip styles for dark mode
            config.options.plugins.tooltip.backgroundColor = 'rgba(30, 41, 59, 0.95)';
            config.options.plugins.tooltip.titleColor = '#f1f5f9';
            config.options.plugins.tooltip.bodyColor = '#cbd5e1';
            config.options.plugins.tooltip.borderColor = 'rgba(255, 255, 255, 0.1)';
            config.options.plugins.tooltip.callbacks.labelTextColor = function() {
                return '#cbd5e1';
            };

            // Update legend styles for dark mode
            config.options.plugins.legend.labels.color = '#d1d5db';
        }

        // Create the chart
        window.trafficChart = new Chart(ctx, config);

        // Handle window resize to make chart responsive
        window.addEventListener('resize', function() {
            if (window.trafficChart) {
                // Update font sizes based on screen width
                config.options.scales.x.ticks.font.size = window.innerWidth < 768 ? 9 : 10;
                config.options.scales.y.ticks.font.size = window.innerWidth < 768 ? 9 : 10;
                config.options.elements.point.radius = window.innerWidth < 768 ? 1 : 2;
                config.options.elements.point.hoverRadius = window.innerWidth < 768 ? 3 : 4;

                window.trafficChart.update();
            }
        });
    }

    function initChartPeriodButtons() {
        const dailyBtn = document.getElementById('chartDailyBtn');
        const weeklyBtn = document.getElementById('chartWeeklyBtn');
        const monthlyBtn = document.getElementById('chartMonthlyBtn');

        if (!dailyBtn || !weeklyBtn || !monthlyBtn || !window.trafficChart) return;

        // We'll use the data from the backend for daily view (default)
        // For weekly and monthly, we'll aggregate the data

        // Check if we have data
        if (!activityDates.length || !activityCounts.length || !userCounts.length) {
            console.warn('No activity data available for chart periods');

            // Disable the period buttons if there's no data
            [dailyBtn, weeklyBtn, monthlyBtn].forEach(btn => {
                btn.classList.add('opacity-50', 'cursor-not-allowed');
                btn.disabled = true;
            });

            return;
        }

        // Daily data (default) - already loaded from backend
        const dailyData = {
            users: userCounts,
            activities: activityCounts  // Changed from transactions to activities to match chart dataset name
        };
        const dailyLabels = formattedDates;

        // Weekly data - aggregate daily data into weeks
        const weeklyData = {
            users: [],
            activities: []
        };
        const weeklyLabels = [];

        // Group data into weeks (assuming 30 days of data)
        // We'll create 4 weeks, with the most recent week first
        for (let i = 0; i < 4; i++) {
            // Calculate week boundaries (most recent week first)
            const weekEnd = userCounts.length;
            const weekStart = Math.max(0, weekEnd - (i + 1) * 7);
            const weekEndIndex = Math.max(0, weekEnd - i * 7);

            // Sum the data for this week
            let userSum = 0;
            let activitySum = 0;
            for (let j = weekStart; j < weekEndIndex; j++) {
                userSum += userCounts[j] || 0;
                activitySum += activityCounts[j] || 0;

        }

            // Add to the beginning of the arrays to maintain chronological order
            weeklyData.users.unshift(userSum);
            weeklyData.activities.unshift(activitySum);

            // Create a safe label for this week
            let weekLabel = `Week ${i+1}`;

            // Try to create a date-based label if possible
            try {
                if (activityDates && activityDates.length > 0) {
                    // Make sure we have valid indices
                    const validStartIndex = Math.min(weekStart, activityDates.length - 1);
                    const validEndIndex = Math.min(weekEndIndex - 1, activityDates.length - 1);

                    if (validStartIndex >= 0 && validEndIndex >= 0) {
                        const startDate = new Date(activityDates[validStartIndex]);
                        const endDate = new Date(activityDates[validEndIndex]);

                        // Check if dates are valid
                        if (!isNaN(startDate) && !isNaN(endDate)) {
                            const startFormatted = `${startDate.getDate()}/${startDate.getMonth() + 1}`;
                            const endFormatted = `${endDate.getDate()}/${endDate.getMonth() + 1}`;
                            weekLabel = `${startFormatted} - ${endFormatted}`;
                        }
                    }
                }
            } catch (e) {
                console.warn('Error formatting week label:', e);
                // Keep the default week label
            }

            weeklyLabels.unshift(weekLabel);
        }

        // Monthly data - aggregate by actual months
        const monthlyData = {
            users: [],
            activities: []
        };
        const monthlyLabels = [];

        // Get the month names
        const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];

        try {
            // Group by month using a Map
            const monthlyMap = new Map();

            // Check if we have valid dates
            if (activityDates && activityDates.length > 0) {
                // Process all dates and aggregate by month
                for (let i = 0; i < activityDates.length; i++) {
                    try {
                        const date = new Date(activityDates[i]);

                        // Skip invalid dates
                        if (isNaN(date)) continue;

                        const monthKey = `${date.getFullYear()}-${date.getMonth() + 1}`;

                        if (!monthlyMap.has(monthKey)) {
                            monthlyMap.set(monthKey, {
                                users: 0,
                                activities: 0,
                                month: date.getMonth(),
                                year: date.getFullYear()
                            });
                        }

                        const monthData = monthlyMap.get(monthKey);
                        monthData.users += userCounts[i] || 0;
                        monthData.activities += activityCounts[i] || 0;
                    } catch (e) {
                        console.warn(`Error processing date at index ${i}:`, e);
                    }
                }

                // Convert the map to arrays and sort by date
                const monthEntries = Array.from(monthlyMap.entries())
                    .sort((a, b) => {
                        // Sort by year and month
                        if (a[1].year !== b[1].year) {
                            return a[1].year - b[1].year;
                        }
                        return a[1].month - b[1].month;
                    });

                // Extract the data
                for (const [_, data] of monthEntries) {
                    monthlyData.users.push(data.users);
                    monthlyData.activities.push(data.activities);
                    monthlyLabels.push(monthNames[data.month]);
                }
            }

            // If we don't have any monthly data, create default data for the current month
            if (monthlyLabels.length === 0) {
                const now = new Date();
                const currentMonth = now.getMonth();

                // Sum all user and activity counts for the current month
                let totalUsers = 0;
                let totalActivities = 0;
                for (let i = 0; i < userCounts.length; i++) {
                    totalUsers += userCounts[i] || 0;
                    totalActivities += activityCounts[i] || 0;
                }

                monthlyData.users.push(totalUsers);
                monthlyData.activities.push(totalActivities);
                monthlyLabels.push(monthNames[currentMonth]);

                console.log('Created default monthly data for current month:', {
                    month: monthNames[currentMonth],
                    users: totalUsers,
                    activities: totalActivities
                });
            }
        } catch (e) {
            console.error('Error creating monthly data:', e);

            // Create fallback data
            const now = new Date();
            monthlyData.users.push(0);
            monthlyData.activities.push(0);
            monthlyLabels.push(monthNames[now.getMonth()]);
        }

        // Update chart function
        function updateChart(labels, data, activeBtn) {
            console.log(`Updating chart with ${labels.length} labels:`, labels);
            console.log('User data:', data.users);
            console.log('Activity data:', data.activities);

            window.trafficChart.data.labels = labels;
            window.trafficChart.data.datasets[0].data = data.users;
            window.trafficChart.data.datasets[1].data = data.activities;
            window.trafficChart.update();

            // Update button states
            [dailyBtn, weeklyBtn, monthlyBtn].forEach(btn => {
                btn.classList.remove('bg-blue-50', 'dark:bg-blue-900/30', 'text-blue-600', 'dark:text-blue-400', 'shadow-sm');
                btn.classList.add('text-gray-600', 'dark:text-gray-400');
            });

            activeBtn.classList.add('bg-blue-50', 'dark:bg-blue-900/30', 'text-blue-600', 'dark:text-blue-400', 'shadow-sm');
            activeBtn.classList.remove('text-gray-600', 'dark:text-gray-400');
        }

        // Add event listeners
        dailyBtn.addEventListener('click', () => updateChart(dailyLabels, dailyData, dailyBtn));
        weeklyBtn.addEventListener('click', () => updateChart(weeklyLabels, weeklyData, weeklyBtn));
        monthlyBtn.addEventListener('click', () => updateChart(monthlyLabels, monthlyData, monthlyBtn));
    }

</script>
{% endblock %}

/**
 * Sidebar functionality for the Admin Panel
 * Optimized for performance and reliability
 */

// Prevent FOUC (Flash of Unstyled Content)
(function() {
  try {
    const isCollapsed = localStorage.getItem('sidebarCollapsed') === 'true';
    if (isCollapsed) {
      document.documentElement.classList.add('sidebar-pre-collapsed');
    }
  } catch (error) {
    console.warn('Failed to access localStorage:', error);
  }
})();

/**
 * Initializes submenu functionality
 */
function initSubmenus() {
  const submenuToggles = document.querySelectorAll('.submenu-toggle');
  let openSubmenus = [];

  try {
    openSubmenus = JSON.parse(localStorage.getItem('openSubmenus') || '[]');
  } catch (error) {
    console.warn('Failed to parse openSubmenus from localStorage:', error);
  }

  const isSidebarCollapsed = localStorage.getItem('sidebarCollapsed') === 'true';

  submenuToggles.forEach((toggle) => {
    const newToggle = toggle.cloneNode(true);
    toggle.parentNode.replaceChild(newToggle, toggle);

    if (window.lucide) {
      window.lucide.createIcons({
        icons: ['chevron-down'],
        elements: [newToggle]
      });
    }

    newToggle.addEventListener('click', function(e) {
      e.preventDefault();

      const submenu = this.nextElementSibling;
      if (!submenu || !submenu.classList.contains('submenu-content')) return;

      const isExpanded = submenu.classList.contains('submenu-expanded');
      const chevron = this.querySelector('[data-lucide="chevron-down"]');

      submenu.classList.toggle('submenu-expanded', !isExpanded);
      submenu.classList.toggle('submenu-open', !isExpanded);

      // Find the chevron wrapper
      const chevronWrapper = this.querySelector('.chevron-wrapper');
      if (chevronWrapper) {
        // Toggle the expanded class on the wrapper
        chevronWrapper.classList.toggle('expanded', !isExpanded);
      } else if (chevron) {
        // Fallback to the old method if wrapper not found
        chevron.classList.toggle('rotate-180', !isExpanded);
      }

      this.classList.toggle('has-chevron-rotated', !isExpanded);

      const submenuId = this.getAttribute('data-submenu-id');
      if (submenuId) {
        try {
          let currentOpenSubmenus = JSON.parse(localStorage.getItem('openSubmenus') || '[]');
          if (!isExpanded) {
            if (!currentOpenSubmenus.includes(submenuId)) {
              currentOpenSubmenus.push(submenuId);
            }
          } else {
            currentOpenSubmenus = currentOpenSubmenus.filter(id => id !== submenuId);
          }
          localStorage.setItem('openSubmenus', JSON.stringify(currentOpenSubmenus));
        } catch (error) {
          console.warn('Failed to update openSubmenus in localStorage:', error);
        }
      }
    });
  });

  if (!isSidebarCollapsed) {
    restoreSubmenuState(openSubmenus);
  }

  highlightActiveItems();
}

/**
 * Restores submenu state based on saved IDs
 */
function restoreSubmenuState(openSubmenus) {
  openSubmenus.forEach((submenuId) => {
    const toggle = document.querySelector(`.submenu-toggle[data-submenu-id="${submenuId}"]`);
    if (toggle) {
      const submenu = toggle.nextElementSibling;
      if (submenu) {
        submenu.classList.add('submenu-expanded', 'submenu-open');
      }

      toggle.classList.add('has-chevron-rotated');

      // Find the chevron wrapper
      const chevronWrapper = toggle.querySelector('.chevron-wrapper');
      if (chevronWrapper) {
        // Add the expanded class to the wrapper
        chevronWrapper.classList.add('expanded');
      } else {
        // Fallback to the old method if wrapper not found
        const chevron = toggle.querySelector('[data-lucide="chevron-down"]');
        if (chevron) {
          chevron.classList.add('rotate-180');
        }
      }
    }
  });
}

/**
 * Highlights currently active menu items
 */
function highlightActiveItems() {
  document.querySelectorAll('.sidebar-item.active, .submenu-item.active').forEach((item) => {
    const parentSubmenu = item.closest('.submenu-content');
    if (parentSubmenu) {
      const toggle = parentSubmenu.previousElementSibling;
      parentSubmenu.classList.add('submenu-expanded', 'submenu-open');

      if (toggle) {
        toggle.classList.add('has-chevron-rotated');

        // Find the chevron wrapper
        const chevronWrapper = toggle.querySelector('.chevron-wrapper');
        if (chevronWrapper) {
          // Add the expanded class to the wrapper
          chevronWrapper.classList.add('expanded');
        } else {
          // Fallback to the old method if wrapper not found
          const chevron = toggle.querySelector('[data-lucide="chevron-down"]');
          if (chevron) {
            chevron.classList.add('rotate-180');
          }
        }
      }
    }
  });
}

/**
 * Initializes core sidebar functionality
 */
function initSidebarCore() {
  // Get DOM elements
  const elements = {
    sidebar: document.getElementById('sidebar'),
    mainContent: document.getElementById('main-content'),
    sidebarToggle: document.getElementById('collapse-sidebar'),
    mobileToggle: document.getElementById('sidebar-toggle'),
    sidebarOverlay: document.getElementById('sidebar-overlay'),
    userMenuButton: document.getElementById('user-menu-button'),
    userDropdown: document.getElementById('user-dropdown'),
    userMenuChevron: document.getElementById('user-menu-chevron'),
    sidebarToggleContainer: document.getElementById('sidebar-toggle-container')
  };

  // Store elements reference globally but without exposing individual references
  window.sidebarElements = Object.freeze({ ...elements });

  // Setup event handlers if elements exist
  if (elements.sidebarToggle) {
    elements.sidebarToggle.addEventListener('click', (e) => handleSidebarToggle(e, elements));
    elements.sidebarToggle.addEventListener('mousedown', (e) => e.preventDefault());
  }

  if (elements.sidebarToggleContainer) {
    elements.sidebarToggleContainer.addEventListener('click', (e) => handleSidebarToggle(e, elements));
  }

  if (elements.mobileToggle) {
    elements.mobileToggle.addEventListener('click', () => toggleMobileSidebar(elements));
  }

  if (elements.sidebarOverlay) {
    elements.sidebarOverlay.addEventListener('click', () => toggleMobileSidebar(elements));
  }

  if (elements.userMenuButton && elements.userDropdown) {
    setupUserMenu(elements);
  }

  // Apply initial state and set resize handler
  applyInitialSidebarState(elements);
  window.addEventListener('resize', () => applyInitialSidebarState(elements));
}

/**
 * Handles sidebar toggle actions
 */
/**
 * Handles sidebar toggle actions
 */
function handleSidebarToggle(e, elements) {
  e.preventDefault();
  e.stopPropagation();

  const { sidebar, mainContent, sidebarToggle, userMenuButton, userDropdown, userMenuChevron } = elements;

  if (!sidebarToggle || !mainContent || !sidebar) return;

  const icon = sidebarToggle.querySelector('i');
  if (icon) {
    icon.classList.remove('pulse-animation');
    void icon.offsetWidth; // Force reflow
    icon.classList.add('pulse-animation');
    setTimeout(() => icon.classList.remove('pulse-animation'), 300);
  }

  if (userDropdown) {
    userDropdown.classList.add('hidden');
  }

  // Find the chevron wrapper
  const userChevronWrapper = userMenuButton ? userMenuButton.querySelector('.chevron-wrapper') : null;
  if (userChevronWrapper) {
    // Remove the expanded class from the wrapper
    userChevronWrapper.classList.remove('expanded');
  } else if (userMenuChevron) {
    // Fallback to the old method if wrapper not found
    userMenuChevron.classList.remove('rotate-180');
  }

  if (window.innerWidth < 1024) {
    toggleMobileSidebar(elements);
  } else {
    const isCollapsed = sidebar.classList.toggle('sidebar-collapsed');

    try {
      localStorage.setItem('sidebarCollapsed', isCollapsed.toString());
    } catch (error) {
      console.warn('Failed to save sidebar state to localStorage:', error);
    }

    mainContent.classList.toggle('lg:ml-64', !isCollapsed);
    mainContent.classList.toggle('lg:ml-0', isCollapsed);

    // Use CSS classes for transitions instead of direct style manipulation
    mainContent.classList.add('sidebar-transition');
    setTimeout(() => mainContent.classList.remove('sidebar-transition'), 300);
  }
}

/**
 * Toggles sidebar on mobile devices
 */
function toggleMobileSidebar(elements) {
  const { sidebar, sidebarOverlay } = elements;
  if (!sidebar) return;

  const isOpen = sidebar.classList.toggle('mobile-open');
  sidebar.style.transform = isOpen ? '' : 'translateX(-100%)';

  if (sidebarOverlay) {
    sidebarOverlay.classList.toggle('hidden', !isOpen);
  }
}

/**
 * Sets up user menu dropdown functionality
 */
function setupUserMenu(elements) {
  const { userMenuButton, userDropdown, userMenuChevron, sidebar } = elements;

  userMenuButton.addEventListener('click', (e) => {
    e.stopPropagation();
    const isVisible = !userDropdown.classList.contains('hidden');
    userDropdown.classList.toggle('hidden');

    // Find the chevron wrapper
    const chevronWrapper = userMenuButton.querySelector('.chevron-wrapper');
    if (chevronWrapper) {
      // Toggle the expanded class on the wrapper
      chevronWrapper.classList.toggle('expanded', !isVisible);
    } else if (userMenuChevron) {
      // Fallback to the old method if wrapper not found
      userMenuChevron.classList.toggle('rotate-180', !isVisible);
    }

    if (!isVisible) {
      positionUserDropdown(userMenuButton, userDropdown, sidebar);
    }
  });

  document.addEventListener('click', (e) => {
    if (
      userDropdown &&
      !userDropdown.classList.contains('hidden') &&
      !userMenuButton.contains(e.target) &&
      !userDropdown.contains(e.target)
    ) {
      userDropdown.classList.add('hidden');

      // Find the chevron wrapper
      const chevronWrapper = userMenuButton.querySelector('.chevron-wrapper');
      if (chevronWrapper) {
        // Remove the expanded class from the wrapper
        chevronWrapper.classList.remove('expanded');
      } else if (userMenuChevron) {
        // Fallback to the old method if wrapper not found
        userMenuChevron.classList.remove('rotate-180');
      }
    }
  });

  userDropdown.addEventListener('click', (e) => e.stopPropagation());
}

/**
 * Positions the user dropdown menu
 */
function positionUserDropdown(button, dropdown, sidebar) {
  dropdown.style.visibility = 'hidden';
  dropdown.style.display = 'block';

  const rect = button.getBoundingClientRect();
  const dropdownHeight = dropdown.offsetHeight;
  const viewportHeight = window.innerHeight;

  dropdown.style.left = `${sidebar.getBoundingClientRect().right + 8}px`;
  dropdown.style.top = rect.top + dropdownHeight > viewportHeight
    ? `${viewportHeight - dropdownHeight - 10}px`
    : `${rect.top}px`;

  dropdown.style.visibility = 'visible';
  dropdown.style.display = '';

  setTimeout(() => {
    if (window.lucide) {
      window.lucide.createIcons({ selector: '#user-dropdown [data-lucide]' });
    }
  }, 10);
}

/**
 * Applies initial sidebar state based on viewport and saved state
 */
function applyInitialSidebarState(elements) {
  const { sidebar, mainContent } = elements;
  if (!sidebar || !mainContent) return;

  let isCollapsed = false;

  try {
    isCollapsed = localStorage.getItem('sidebarCollapsed') === 'true';
  } catch (error) {
    console.warn('Failed to get sidebar state from localStorage:', error);
  }

  if (window.innerWidth >= 1024) {
    sidebar.style.transform = '';
    sidebar.classList.remove('mobile-open');

    sidebar.classList.toggle('sidebar-collapsed', isCollapsed);
    mainContent.classList.toggle('lg:ml-64', !isCollapsed);
    mainContent.classList.toggle('lg:ml-0', isCollapsed);
  } else {
    sidebar.style.transform = sidebar.classList.contains('mobile-open') ? '' : 'translateX(-100%)';
  }
}

// Global initializer with proper namespacing
window.MatrixUI = window.MatrixUI || {};
window.MatrixUI.initSidebar = function() {
  initSidebarCore();
  initSubmenus();
};

document.addEventListener('DOMContentLoaded', () => {
  if (window.MatrixUI && window.MatrixUI.initSidebar) {
    window.MatrixUI.initSidebar();
  }
});

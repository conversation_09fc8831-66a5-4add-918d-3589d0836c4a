"""
API routes for entity data.
"""

from flask import jsonify, current_app
from flask_login import login_required

from app.models import User, BusinessUnit, BusinessSegment, EmployeeDetail
from app.routes.api import api_bp
from app.routes.api.utils import handle_api_error


@api_bp.route('/entity/<string:entity_type>/<int:entity_id>', methods=['GET'])
@login_required
@handle_api_error
def get_entity_data(entity_type, entity_id):
    """Get entity data for JSON viewer.

    This endpoint returns the JSON data for a specific entity,
    used by the JSON viewer in the activity logs.
    """
    # Handle different entity types
    if entity_type == 'User':
        entity = User.query.get_or_404(entity_id)
        employee_detail = EmployeeDetail.query.filter_by(user_id=entity.id).first()

        data = {
            'id': entity.id,
            'name': entity.name,
            'email': entity.email,
            'role': entity.role,
            'is_active': entity.is_active,
            'created_at': entity.created_at.isoformat(),
            'last_login': entity.last_login.isoformat() if entity.last_login else None
        }

        # Add employee details if they exist
        if employee_detail:
            data.update({
                'position': employee_detail.job_title,  # Updated from position to job_title
                'phone': employee_detail.phone,
                'business_unit_id': employee_detail.business_unit_id,
                'business_segment_id': employee_detail.business_segment_id,
                'business_unit': employee_detail.business_unit.name if employee_detail.business_unit else None,
                'business_segment': employee_detail.business_segment.name if employee_detail.business_segment else None,
                'hire_date': employee_detail.hire_date.isoformat() if employee_detail.hire_date else None,
                'emp_status': employee_detail.emp_status
            })

    elif entity_type == 'BusinessUnit':
        entity = BusinessUnit.query.get_or_404(entity_id)
        manager = User.query.get(entity.manager_id) if entity.manager_id else None
        employee_count = EmployeeDetail.query.filter_by(business_unit_id=entity.id).count()

        data = {
            'id': entity.id,
            'name': entity.name,
            'code': entity.code,
            'description': entity.description,
            'manager_id': entity.manager_id,
            'manager': manager.name if manager else None,
            'is_active': entity.is_active,
            'employee_count': employee_count,
            'segment_count': entity.segments.count(),
            'created_at': entity.created_at.isoformat()
        }

    elif entity_type == 'BusinessSegment':
        entity = BusinessSegment.query.get_or_404(entity_id)
        manager = User.query.get(entity.manager_id) if entity.manager_id else None
        employee_count = EmployeeDetail.query.filter_by(business_segment_id=entity.id).count()

        data = {
            'id': entity.id,
            'name': entity.name,
            'code': entity.code,
            'description': entity.description,
            'business_unit_id': entity.business_unit_id,
            'business_unit': entity.business_unit.name,
            'manager_id': entity.manager_id,
            'manager': manager.name if manager else None,
            'is_active': entity.is_active,
            'employee_count': employee_count,
            'created_at': entity.created_at.isoformat()
        }

    elif entity_type == 'EmployeeDetail':
        entity = EmployeeDetail.query.get_or_404(entity_id)

        data = {
            'id': entity.id,
            'user_id': entity.user_id,
            'name': entity.user.name,
            'email': entity.user.email,
            'position': entity.job_title,  # Updated from position to job_title
            'employee_number': entity.employee_number,
            'phone': entity.phone,
            'business_unit_id': entity.business_unit_id,
            'business_segment_id': entity.business_segment_id,
            'business_unit': entity.business_unit.name if entity.business_unit else None,
            'business_segment': entity.business_segment.name if entity.business_segment else None,
            'hire_date': entity.hire_date.isoformat() if entity.hire_date else None,
            'emp_status': entity.emp_status
        }

    else:
        return jsonify({
            'success': False,
            'message': f'Unsupported entity type: {entity_type}'
        }), 400

    return jsonify({
        'success': True,
        'entity_type': entity_type,
        'entity_id': entity_id,
        'data': data
    })

{% extends "base.html" %}

{% from "components/page_header.html" import page_header %}
{% from "components/button.html" import button, button_group %}

{% block title %}{{ title }}{% endblock %}

{% block header %}{{ title }}{% endblock %}

{% block content %}
<div class="space-y-6">
  <!-- Page Header with Action Buttons -->
  <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
    <div>
      <h1 class="text-2xl font-bold">{{ title }}</h1>
      <p class="text-sm text-muted-foreground mt-1">
        View your attendance records in calendar format
      </p>
    </div>
    <div class="mt-4 sm:mt-0">
      {% set action_buttons = [
        {"text": "List View", "variant": "outline", "href": url_for('attendance.my_attendance'), "icon": "list"},
        {"text": "Request Attendance", "variant": "primary", "href": url_for('attendance.request_attendance'), "icon": "plus"}
      ] %}
      {{ button_group(action_buttons) }}
    </div>
  </div>

  <!-- Calendar Card -->
  <div class="card">
    <div class="card-header">
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-4">
          <h2 class="card-title" id="calendarTitle">Loading...</h2>
          <div class="flex items-center space-x-2">
            <label for="monthSelect" class="text-sm text-muted-foreground">Month:</label>
            <select id="monthSelect" class="form-select text-sm">
              <option value="0">January</option>
              <option value="1">February</option>
              <option value="2">March</option>
              <option value="3">April</option>
              <option value="4">May</option>
              <option value="5">June</option>
              <option value="6">July</option>
              <option value="7">August</option>
              <option value="8">September</option>
              <option value="9">October</option>
              <option value="10">November</option>
              <option value="11">December</option>
            </select>
            <label for="yearSelect" class="text-sm text-muted-foreground">Year:</label>
            <select id="yearSelect" class="form-select text-sm">
              <!-- Years will be populated by JavaScript -->
            </select>
          </div>
        </div>
        <div class="flex items-center space-x-2">
          {{ button("", variant="outline", size="sm", icon="chevron-left", id="prevMonth") }}
          {{ button("Today", variant="outline", size="sm", id="todayBtn") }}
          {{ button("", variant="outline", size="sm", icon="chevron-right", id="nextMonth") }}
        </div>
      </div>
    </div>

    <div class="card-content">
      <div id="calendarContainer">
        <!-- Calendar will be rendered here by JavaScript -->
        <div class="text-center py-8">
          <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
          <p class="mt-2 text-sm text-muted-foreground">Loading calendar...</p>
        </div>
      </div>
    </div>
  </div>

  <!-- Legend -->
  <div class="card">
    <div class="card-header">
      <h3 class="card-title">Legend</h3>
    </div>
    <div class="card-content">
      <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
        <div class="flex items-center">
          <div class="w-3 h-3 bg-green-500 rounded-full mr-2"></div>
          <span class="text-sm">Approved</span>
        </div>
        <div class="flex items-center">
          <div class="w-3 h-3 bg-yellow-500 rounded-full mr-2"></div>
          <span class="text-sm">Pending</span>
        </div>
        <div class="flex items-center">
          <div class="w-3 h-3 bg-red-500 rounded-full mr-2"></div>
          <span class="text-sm">Rejected</span>
        </div>
        <div class="flex items-center">
          <div class="w-3 h-3 bg-blue-500 rounded-full mr-2"></div>
          <span class="text-sm">Holiday</span>
        </div>
      </div>
      <div class="mt-4 pt-4 border-t border-border">
        <p class="text-xs text-muted-foreground">
          💡 Hover over dates for details, click for more information
        </p>
      </div>
    </div>
  </div>
</div>

<!-- Hidden data for JavaScript -->
<input type="hidden" id="attendanceApiUrl" value="{{ url_for('api.get_user_attendance_calendar') }}">
<input type="hidden" id="holidayApiUrl" value="{{ url_for('api.check_holiday_simple') }}">
<input type="hidden" id="currentUserRegion" value="{{ user_region }}">

<script>
document.addEventListener('DOMContentLoaded', function() {
  const calendarContainer = document.getElementById('calendarContainer');
  const calendarTitle = document.getElementById('calendarTitle');
  const monthSelect = document.getElementById('monthSelect');
  const yearSelect = document.getElementById('yearSelect');
  const prevMonthBtn = document.getElementById('prevMonth');
  const nextMonthBtn = document.getElementById('nextMonth');
  const todayBtn = document.getElementById('todayBtn');

  let currentDate = new Date();
  let attendanceData = {};
  let holidayData = {};

  // Initialize year select
  function initializeYearSelect() {
    const currentYear = new Date().getFullYear();
    yearSelect.innerHTML = '';
    for (let year = currentYear - 2; year <= currentYear + 2; year++) {
      const option = document.createElement('option');
      option.value = year;
      option.textContent = year;
      if (year === currentYear) option.selected = true;
      yearSelect.appendChild(option);
    }
  }

  // Update calendar title
  function updateCalendarTitle() {
    const monthNames = [
      'January', 'February', 'March', 'April', 'May', 'June',
      'July', 'August', 'September', 'October', 'November', 'December'
    ];
    calendarTitle.textContent = `${monthNames[currentDate.getMonth()]} ${currentDate.getFullYear()}`;
    monthSelect.value = currentDate.getMonth();
    yearSelect.value = currentDate.getFullYear();
  }

  // Load attendance data for the month
  async function loadAttendanceData() {
    try {
      const year = currentDate.getFullYear();
      const month = currentDate.getMonth() + 1;
      const userRegion = document.getElementById('currentUserRegion').value || 'PH';

      // Load user's attendance data
      const attendanceApiUrl = document.getElementById('attendanceApiUrl').value;
      const attendanceResponse = await fetch(`${attendanceApiUrl}?year=${year}&month=${month}`);
      if (attendanceResponse.ok) {
        const attendanceResult = await attendanceResponse.json();
        if (attendanceResult.success) {
          attendanceData = attendanceResult.calendar_data || {};
        } else {
          console.error('Failed to load attendance data:', attendanceResult.error);
          attendanceData = {};
        }
      } else {
        console.error('Failed to fetch attendance data');
        attendanceData = {};
      }

      // Load holiday data for user's region
      const holidayResponse = await fetch(`/api/holidays/calendar/${userRegion}/${year}?month=${month}`);
      if (holidayResponse.ok) {
        const holidayResult = await holidayResponse.json();
        if (holidayResult.success) {
          holidayData = holidayResult.calendar_data || {};
        } else {
          console.error('Failed to load holiday data:', holidayResult.error);
          holidayData = {};
        }
      } else {
        console.error('Failed to fetch holiday data');
        holidayData = {};
      }
    } catch (error) {
      console.error('Error loading calendar data:', error);
      attendanceData = {};
      holidayData = {};
    }
  }

  // Render calendar
  function renderCalendar() {
    const year = currentDate.getFullYear();
    const month = currentDate.getMonth();

    // Get first day of month and number of days
    const firstDay = new Date(year, month, 1);
    const lastDay = new Date(year, month + 1, 0);
    const daysInMonth = lastDay.getDate();
    const startingDayOfWeek = firstDay.getDay();

    let html = `
      <div class="grid grid-cols-7 gap-1">
        <!-- Day headers -->
        <div class="p-2 text-center text-sm font-medium text-gray-500 dark:text-gray-400">Sun</div>
        <div class="p-2 text-center text-sm font-medium text-gray-500 dark:text-gray-400">Mon</div>
        <div class="p-2 text-center text-sm font-medium text-gray-500 dark:text-gray-400">Tue</div>
        <div class="p-2 text-center text-sm font-medium text-gray-500 dark:text-gray-400">Wed</div>
        <div class="p-2 text-center text-sm font-medium text-gray-500 dark:text-gray-400">Thu</div>
        <div class="p-2 text-center text-sm font-medium text-gray-500 dark:text-gray-400">Fri</div>
        <div class="p-2 text-center text-sm font-medium text-gray-500 dark:text-gray-400">Sat</div>
    `;

    // Empty cells for days before month starts
    for (let i = 0; i < startingDayOfWeek; i++) {
      html += '<div class="p-2 h-24"></div>';
    }

    // Days of the month
    for (let day = 1; day <= daysInMonth; day++) {
      const dateStr = `${year}-${String(month + 1).padStart(2, '0')}-${String(day).padStart(2, '0')}`;
      const isToday = new Date().toDateString() === new Date(year, month, day).toDateString();
      const hasAttendance = attendanceData[dateStr];
      const isHoliday = holidayData[dateStr];

      let dayClass = 'p-2 h-24 border border-gray-200 dark:border-gray-600 relative cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors';

      // Today styling
      if (isToday) {
        dayClass += ' bg-blue-50 dark:bg-blue-900/20 border-blue-300 dark:border-blue-600';
      }

      // Holiday styling
      if (isHoliday) {
        dayClass += ' bg-blue-50 dark:bg-blue-900/10 border-blue-200 dark:border-blue-800';
      }

      let indicators = '';
      let tooltipText = `${dateStr}`;

      // Holiday indicator
      if (isHoliday) {
        indicators += '<div class="absolute top-1 right-1 w-2 h-2 bg-blue-500 rounded-full" title="Holiday"></div>';
        tooltipText += `\nHoliday: ${isHoliday.name}`;
        if (isHoliday.description) {
          tooltipText += `\n${isHoliday.description}`;
        }
      }

      // Attendance indicator
      if (hasAttendance) {
        const statusColor = {
          'pending': 'bg-yellow-500',
          'approved': 'bg-green-500',
          'rejected': 'bg-red-500',
          'auto_approved': 'bg-green-500',
          'cancelled': 'bg-gray-500'
        }[hasAttendance.status] || 'bg-gray-500';
        indicators += `<div class="absolute bottom-1 left-1 w-2 h-2 ${statusColor} rounded-full" title="Attendance: ${hasAttendance.status_display}"></div>`;
        tooltipText += `\nAttendance: ${hasAttendance.type} (${hasAttendance.status_display})`;
        if (hasAttendance.start_time && hasAttendance.end_time) {
          tooltipText += `\nTime: ${hasAttendance.start_time} - ${hasAttendance.end_time}`;
        }
      }

      html += `
        <div class="${dayClass}" onclick="selectDate('${dateStr}')" title="${tooltipText}">
          <div class="text-sm ${isToday ? 'font-bold text-blue-600 dark:text-blue-400' : 'text-gray-900 dark:text-white'}">${day}</div>
          ${indicators}
          ${isHoliday ? `<div class="text-xs text-blue-600 dark:text-blue-400 mt-1 truncate font-medium">${isHoliday.name}</div>` : ''}
          ${hasAttendance ? `<div class="text-xs text-gray-600 dark:text-gray-400 mt-1 truncate">${hasAttendance.type}</div>` : ''}
        </div>
      `;
    }

    html += '</div>';
    calendarContainer.innerHTML = html;
  }

  // Select date handler
  window.selectDate = function(dateStr) {
    const hasAttendance = attendanceData[dateStr];
    const isHoliday = holidayData[dateStr];
    const selectedDate = new Date(dateStr);
    const formattedDate = selectedDate.toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });

    let message = `📅 ${formattedDate}`;

    if (isHoliday) {
      message += `\n\n🎉 Holiday: ${isHoliday.name}`;
      if (isHoliday.description) {
        message += `\n📝 ${isHoliday.description}`;
      }
      message += `\n🌍 Region: ${isHoliday.region_code}`;
    }

    if (hasAttendance) {
      message += `\n\n📋 Attendance: ${hasAttendance.type}`;
      message += `\n📊 Status: ${hasAttendance.status_display}`;
      if (hasAttendance.start_time && hasAttendance.end_time) {
        message += `\n⏰ Time: ${hasAttendance.start_time} - ${hasAttendance.end_time}`;
      }
      if (hasAttendance.duration_hours) {
        message += `\n⏱️ Duration: ${hasAttendance.duration_hours} hours`;
      }
      if (hasAttendance.notes) {
        message += `\n💬 Notes: ${hasAttendance.notes}`;
      }
    }

    if (!hasAttendance && !isHoliday) {
      message += `\n\n✅ No attendance records or holidays on this date.`;
      message += `\n\n💡 Click "Request Attendance" to add a record for this date.`;
    }

    alert(message);
  };

  // Navigation handlers
  prevMonthBtn.addEventListener('click', () => {
    currentDate.setMonth(currentDate.getMonth() - 1);
    updateCalendar();
  });

  nextMonthBtn.addEventListener('click', () => {
    currentDate.setMonth(currentDate.getMonth() + 1);
    updateCalendar();
  });

  todayBtn.addEventListener('click', () => {
    currentDate = new Date();
    updateCalendar();
  });

  monthSelect.addEventListener('change', () => {
    currentDate.setMonth(parseInt(monthSelect.value));
    updateCalendar();
  });

  yearSelect.addEventListener('change', () => {
    currentDate.setFullYear(parseInt(yearSelect.value));
    updateCalendar();
  });

  // Update calendar
  async function updateCalendar() {
    updateCalendarTitle();
    await loadAttendanceData();
    renderCalendar();
  }

  // Initialize
  initializeYearSelect();
  updateCalendar();
});
</script>
{% endblock %}


{# This form partial is loaded into a drawer by client-side JavaScript #}
{# The `action_url` and `form_title` are passed from the route #}
{% from "partials/forms/base_form.html" import form_group, form_actions, form_header %}

<form method="POST" action="{{ action_url }}" id="attendanceTypeDrawerForm" class="space-y-4">
  {{ form.hidden_tag() }}

  {{ form_header(
    title = form_title,
    description = "Configure how attendance is tracked in your organization"
  ) }}

  <div class="grid grid-cols-1 gap-4">
    {{ form_group(
      label = form.code.label.text,
      name = form.code.name,
      value = form.code.data if form.code.data else "",
      required = form.code.flags.required,
      placeholder = "e.g., WFH, SICK, PTO"
    ) }}
    {% if form.code.errors %}
      <div class="text-xs text-destructive mt-1">
        <ul>
          {% for error in form.code.errors %}<li>{{ error }}</li>{% endfor %}
        </ul>
      </div>
    {% endif %}

    {{ form_group(
      label = form.name.label.text,
      name = form.name.name,
      value = form.name.data if form.name.data else "",
      required = form.name.flags.required,
      placeholder = "e.g., Work From Home, Sick Leave"
    ) }}
    {% if form.name.errors %}
      <div class="text-xs text-destructive mt-1">
        <ul>
          {% for error in form.name.errors %}<li>{{ error }}</li>{% endfor %}
        </ul>
      </div>
    {% endif %}

    {{ form_group(
      label = form.description.label.text,
      name = form.description.name,
      type = "textarea",
      value = form.description.data if form.description.data else "",
      placeholder = "Enter a description for this attendance type"
    ) }}
    {% if form.description.errors %}
      <div class="text-xs text-destructive mt-1">
        <ul>
          {% for error in form.description.errors %}<li>{{ error }}</li>{% endfor %}
        </ul>
      </div>
    {% endif %}

    <div class="flex items-center space-x-2">
      {{ form.requires_approval(class="checkbox checkbox-primary") }}
      <label for="{{ form.requires_approval.id }}" class="text-sm font-medium text-foreground">{{ form.requires_approval.label.text }}</label>
      {% if form.requires_approval.errors %}
        <div class="text-xs text-destructive ml-2">
          <ul>
            {% for error in form.requires_approval.errors %}<li>{{ error }}</li>{% endfor %}
          </ul>
        </div>
      {% endif %}
    </div>

    <div class="flex items-center space-x-2">
      {{ form.is_full_day(class="checkbox checkbox-primary") }}
      <label for="{{ form.is_full_day.id }}" class="text-sm font-medium text-foreground">{{ form.is_full_day.label.text }}</label>
      {% if form.is_full_day.errors %}
        <div class="text-xs text-destructive ml-2">
          <ul>
            {% for error in form.is_full_day.errors %}<li>{{ error }}</li>{% endfor %}
          </ul>
        </div>
      {% endif %}
    </div>

    {{ form_group(
      label = form.color_code.label.text,
      name = form.color_code.name,
      value = form.color_code.data if form.color_code.data else "",
      placeholder = "#3B82F6"
    ) }}
    {% if form.color_code.errors %}
      <div class="text-xs text-destructive mt-1">
        <ul>
          {% for error in form.color_code.errors %}<li>{{ error }}</li>{% endfor %}
        </ul>
      </div>
    {% endif %}
  </div>

  <div class="flex justify-end space-x-2 pt-4">
    <button type="button" class="btn btn-outline btn-md drawer-close">Cancel</button>
    <button type="button" onclick="submitDrawerForm('attendanceTypeDrawerForm')" class="btn btn-primary btn-md">
      {{ form.submit.label.text }}
    </button>
  </div>
</form>

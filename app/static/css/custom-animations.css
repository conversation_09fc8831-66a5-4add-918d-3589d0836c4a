/**
 * Custom animations for the application
 */

/* Spin animation for refresh icon */
@keyframes custom-spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Custom animation class */
.custom-spin {
  animation: custom-spin 1.5s linear infinite !important;
  transform-origin: center !important;
  display: inline-block !important;
}

/* Direct style for the refresh icon */
#auto-refresh-button svg {
  animation: custom-spin 1.5s linear infinite;
  transform-origin: center;
}

/* Chevron rotation styles - Filter Toggle */
/* Create a wrapper for the chevron icon */
.chevron-wrapper {
  display: inline-flex;
  transition: transform 0.3s ease-in-out;
}

/* Apply rotation to the wrapper when expanded */
.chevron-wrapper.expanded {
  transform: rotate(180deg);
}

/* Ensure the SVG inside inherits the transition */
.chevron-wrapper svg {
  display: block; /* Prevents extra spacing */
}

/* Direct style for the filter toggle chevron */
#filter-toggle .chevron-wrapper {
  transform-origin: center;
}

/* Force the rotation to be visible */
#filter-toggle .chevron-wrapper.expanded {
  transform: rotate(180deg) !important;
}

/* Chevron rotation styles - Sidebar */
/* Apply smooth transition to all chevron icons in the sidebar */
.submenu-toggle [data-lucide="chevron-down"] {
  transition: transform 0.3s ease-in-out !important;
  transform-origin: center;
}

/* Ensure the rotation is smooth */
.submenu-toggle [data-lucide="chevron-down"].rotate-180 {
  transform: rotate(180deg) !important;
}

/* Also apply to the user menu chevron */
#user-menu-chevron {
  transition: transform 0.3s ease-in-out !important;
}

#user-menu-chevron.rotate-180 {
  transform: rotate(180deg) !important;
}

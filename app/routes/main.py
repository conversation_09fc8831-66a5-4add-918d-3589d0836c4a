from flask import Blueprint, render_template, redirect, url_for, flash, request, jsonify
from flask_login import login_required, current_user, logout_user, login_user
from app.models import User, EmployeeDetail, BusinessUnit, BusinessSegment, Activity, PasswordReset
from app import db
from app.utils.decorators import admin_required, log_activity
from app.utils.pagination import paginate_query
from app.utils.ajax_helpers import ajax_response
from datetime import datetime, timedelta, date
import json
import uuid

# Create a Blueprint
main_bp = Blueprint('main', __name__)

# Add user dashboard route
@main_bp.route('/')
@login_required
@log_activity('Accessed main index', category=Activity.CATEGORY_USER, method=Activity.METHOD_READ, skip_ajax=True)
def index():
    if current_user.is_admin:
        return redirect(url_for('admin.dashboard'))
    else:
        return redirect(url_for('main.user_dashboard'))

@main_bp.route('/user-dashboard')
@login_required
@log_activity('Viewed user dashboard', category=Activity.CATEGORY_USER, method=Activity.METHOD_READ, skip_ajax=True)
def user_dashboard():
    # Get employee details if they exist
    employee_detail = EmployeeDetail.query.filter_by(user_id=current_user.id).first()

    # Get team count (teams where user is a member)
    team_count = 0
    if hasattr(current_user, 'teams'):
        team_count = len(current_user.teams)

    # Get recent activities for this user (last 5)
    recent_activities = Activity.query.filter_by(user_id=current_user.id)\
                                    .order_by(Activity.timestamp.desc())\
                                    .limit(5)\
                                    .all()

    return render_template('user_dashboard.html',
                          active_page='user_dashboard',
                          employee_detail=employee_detail,
                          team_count=team_count,
                          recent_activities=recent_activities)

# Add view my details route - Keep this in main.py for regular users
@main_bp.route('/my-details')
@login_required
@log_activity('Viewed my details', entity_type='EmployeeDetail', category=Activity.CATEGORY_USER, method=Activity.METHOD_READ, skip_ajax=True)
def view_my_details():
    # Get employee details if they exist
    employee_detail = EmployeeDetail.query.filter_by(user_id=current_user.id).first()

    # Get recent activities for this user for the details page
    recent_activities = Activity.query.filter_by(user_id=current_user.id)\
                                    .order_by(Activity.timestamp.desc())\
                                    .limit(10)\
                                    .all()

    return render_template('my_details.html',
                        active_page='my_details',
                        employee_detail=employee_detail,
                        recent_activities=recent_activities)

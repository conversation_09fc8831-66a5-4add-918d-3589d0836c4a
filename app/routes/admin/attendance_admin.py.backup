from flask import Blueprint, render_template, request, flash, redirect, url_for, jsonify
from flask_login import login_required
from app import db
from app.models.attendance import AttendanceType
from app.forms.admin_attendance import AttendanceTypeForm # Import the new form
from app.utils.decorators import admin_required # Assuming you have an admin_required decorator

admin_attendance_bp = Blueprint('admin_attendance', __name__, url_prefix='/admin/attendance')

@admin_attendance_bp.route('/types', methods=['GET'])
@login_required
@admin_required # Protect this route
def list_attendance_types():
    """Displays a list of all attendance types."""
    page = request.args.get('page', 1, type=int)
    # Add pagination from your app's config or a default
    # per_page = current_app.config.get('ADMIN_ITEMS_PER_PAGE', 10)
    per_page = 10 # Placeholder, adjust as needed

    attendance_types = AttendanceType.query.order_by(AttendanceType.name.asc()).paginate(page=page, per_page=per_page)
    return render_template('admin/attendance/types_index.html',
                           attendance_types=attendance_types,
                           title="Manage Attendance Types",
                           active_page="attendance_types") # For sidebar highlighting

@admin_attendance_bp.route('/types/form/new', methods=['GET'])
@login_required
@admin_required
def get_attendance_type_form_create():
   """Serves the HTML for the create attendance type form (for drawer)."""
   form = AttendanceTypeForm()
   # The action URL will be set by JavaScript on the client-side when injecting into drawer
   # Or, it can be passed to the template if the drawer system needs it.
   # For now, the template partial will have a placeholder or a specific create URL.
   return render_template('admin/attendance/partials/type_form.html',
                          form=form,
                          action_url=url_for('admin_attendance.create_attendance_type'),
                          form_title="Add New Attendance Type")

@admin_attendance_bp.route('/types/form/edit/<int:type_id>', methods=['GET'])
@login_required
@admin_required
def get_attendance_type_form_edit(type_id):
   """Serves the HTML for the edit attendance type form (for drawer)."""
   att_type = AttendanceType.query.get_or_404(type_id)
   form = AttendanceTypeForm(obj=att_type, original_code=att_type.code)
   return render_template('admin/attendance/partials/type_form.html',
                          form=form,
                          action_url=url_for('admin_attendance.update_attendance_type', type_id=type_id),
                          form_title="Edit Attendance Type")

@admin_attendance_bp.route('/types/create', methods=['POST'])
@login_required
@admin_required
def create_attendance_type():
   """Handles creation of a new attendance type."""
   form = AttendanceTypeForm()
   if form.validate_on_submit():
       new_type = AttendanceType()
       new_type.code = form.code.data
       new_type.name = form.name.data
       new_type.description = form.description.data
       new_type.requires_approval = form.requires_approval.data
       new_type.is_full_day = form.is_full_day.data
       new_type.color_code = form.color_code.data if form.color_code.data else None

       db.session.add(new_type)
       try:
           db.session.commit()
           flash('Attendance Type created successfully!', 'success')
           # If submitting via AJAX from drawer, return JSON
           if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
               return jsonify({'success': True, 'message': 'Attendance Type created successfully!'})
           return redirect(url_for('admin_attendance.list_attendance_types'))
       except Exception as e:
           db.session.rollback()
           flash(f'Error creating attendance type: {str(e)}', 'danger')
           if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
               # Consider sending form errors back if possible
               return jsonify({'success': False, 'message': f'Error creating attendance type: {str(e)}', 'errors': form.errors}), 400

   # If form validation fails and it's an AJAX request, return errors
   if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
       return jsonify({'success': False, 'message': 'Validation errors occurred.', 'errors': form.errors}), 400

   # For non-AJAX POST with errors (less likely with drawer pattern but good fallback)
   flash('Please correct the errors below.', 'danger')
   # This part might need adjustment depending on how you handle failed non-AJAX posts from a drawer
   attendance_types = AttendanceType.query.order_by(AttendanceType.name.asc()).paginate(page=1, per_page=10)
   return render_template('admin/attendance/types_index.html',
                          attendance_types=attendance_types, # To re-render the list page
                          title="Manage Attendance Types")
                          # Ideally, you'd re-render the form in the drawer with errors,
                          # but that's more complex without full page reload.
                          # Returning JSON errors is better for drawer UX.

@admin_attendance_bp.route('/types/edit/<int:type_id>', methods=['POST'])
@login_required
@admin_required
def update_attendance_type(type_id):
   """Handles updating an existing attendance type."""
   att_type = AttendanceType.query.get_or_404(type_id)
   form = AttendanceTypeForm(obj=att_type, original_code=att_type.code) # Pass original_code for validation

   # Temporarily set the form's code data to the current type's code if it wasn't submitted
   # This is to ensure validate_on_submit() doesn't fail if code is not part of the POST data
   # (e.g. if it's a disabled field in the form but still needed for validation context)
   # A better approach might be to ensure 'code' is always submitted or handle this in the form's logic.
   if 'code' not in request.form:
       form.code.data = att_type.code

   if form.validate_on_submit():
       att_type.code = form.code.data
       att_type.name = form.name.data
       att_type.description = form.description.data
       att_type.requires_approval = form.requires_approval.data
       att_type.is_full_day = form.is_full_day.data
       att_type.color_code = form.color_code.data if form.color_code.data else None

       try:
           db.session.commit()
           flash('Attendance Type updated successfully!', 'success')
           if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
               return jsonify({'success': True, 'message': 'Attendance Type updated successfully!'})
           return redirect(url_for('admin_attendance.list_attendance_types'))
       except Exception as e:
           db.session.rollback()
           flash(f'Error updating attendance type: {str(e)}', 'danger')
           if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
               return jsonify({'success': False, 'message': f'Error updating attendance type: {str(e)}', 'errors': form.errors}), 400

   if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
       return jsonify({'success': False, 'message': 'Validation errors occurred.', 'errors': form.errors}), 400

   flash('Please correct the errors below.', 'danger')
   attendance_types = AttendanceType.query.order_by(AttendanceType.name.asc()).paginate(page=1, per_page=10)
   return render_template('admin/attendance/types_index.html',
                          attendance_types=attendance_types,
                          title="Manage Attendance Types")

@admin_attendance_bp.route('/types/delete/<int:type_id>', methods=['POST']) # Using POST for CSRF protection
@login_required
@admin_required
def delete_attendance_type(type_id):
   """Handles deleting an attendance type."""
   att_type = AttendanceType.query.get_or_404(type_id)
   try:
       # Check for related records if necessary before deleting
       # For example, if AttendanceRecords link to AttendanceType, ensure none exist
       # or handle them (e.g., set to null, disallow delete, reassign).
       # For now, direct delete:
       db.session.delete(att_type)
       db.session.commit()
       flash(f'Attendance Type "{att_type.name}" deleted successfully!', 'success')
       if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
           return jsonify({'success': True, 'message': f'Attendance Type "{att_type.name}" deleted successfully!'})
   except Exception as e:
       db.session.rollback()
       flash(f'Error deleting attendance type "{att_type.name}": {str(e)}. It might be in use.', 'danger')
       if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
           return jsonify({'success': False, 'message': f'Error deleting attendance type: {str(e)}'}), 500 # Internal Server Error

   return redirect(url_for('admin_attendance.list_attendance_types'))

"""
API routes for settings management.
"""

from flask import jsonify, request
from flask_login import login_required, current_user

from app.models import Activity
from app.routes.api import api_bp
from app.routes.api.utils import handle_api_error
from app.routes.api.activities import APIError
from app.utils.decorators import admin_required, log_activity
from app.utils.settings import get_setting, set_setting, get_all_settings


@api_bp.route('/settings', methods=['GET'])
@login_required
@handle_api_error
def get_settings():
    """Get all public settings."""
    # Regular users can only see public settings
    include_private = current_user.is_admin

    settings = get_all_settings(include_private=include_private)

    return jsonify({
        'success': True,
        'settings': settings
    })


@api_bp.route('/settings/<key>', methods=['GET'])
@login_required
@handle_api_error
def get_setting_value(key):
    """Get a specific setting value."""
    # Validate key parameter
    if not key or not isinstance(key, str):
        return jsonify({
            'success': False,
            'message': 'Invalid setting key'
        }), 400

    # Sanitize key - only allow alphanumeric, underscore, and dash
    import re
    if not re.match(r'^[a-zA-Z0-9_-]+$', key):
        return jsonify({
            'success': False,
            'message': 'Invalid setting key format'
        }), 400

    # Limit key length
    if len(key) > 100:
        return jsonify({
            'success': False,
            'message': 'Setting key too long'
        }), 400

    # Get the setting
    value = get_setting(key)

    # Check if setting exists
    if value is None:
        return jsonify({
            'success': False,
            'message': f'Setting {key} not found'
        }), 404

    return jsonify({
        'success': True,
        'key': key,
        'value': value
    })


@api_bp.route('/settings/update', methods=['POST'])
@login_required
@admin_required
@handle_api_error
@log_activity('Updated setting', entity_type='Setting')
def update_setting():
    """Update a setting value."""
    data = request.get_json(force=True)

    # Validate request data
    if not data or 'key' not in data or 'value' not in data:
        raise APIError('Key and value are required', status_code=400)

    key = data['key']
    value = data['value']
    description = data.get('description')
    is_public = data.get('is_public')

    # Validate key parameter
    if not isinstance(key, str):
        raise APIError('Setting key must be a string', status_code=400)

    # Sanitize key - only allow alphanumeric, underscore, and dash
    import re
    if not re.match(r'^[a-zA-Z0-9_-]+$', key):
        raise APIError('Invalid setting key format. Only alphanumeric, underscore, and dash allowed', status_code=400)

    # Limit key length
    if len(key) > 100:
        raise APIError('Setting key too long (max 100 characters)', status_code=400)

    # Validate description if provided
    if description is not None:
        if not isinstance(description, str):
            raise APIError('Description must be a string', status_code=400)
        if len(description) > 500:
            raise APIError('Description too long (max 500 characters)', status_code=400)

    # Validate is_public if provided
    if is_public is not None and not isinstance(is_public, bool):
        raise APIError('is_public must be a boolean', status_code=400)

    # Update the setting
    setting = set_setting(
        key=key,
        value=value,
        description=description,
        is_public=is_public
    )

    # Commit the changes
    from app import db
    db.session.commit()

    return jsonify({
        'success': True,
        'message': f'Setting {key} updated successfully',
        'setting': {
            'key': setting.key,
            'value': setting.typed_value,
            'type': setting.type,
            'description': setting.description,
            'is_public': setting.is_public
        }
    })

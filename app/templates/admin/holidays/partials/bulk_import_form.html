{# This form partial is loaded into a drawer by client-side JavaScript #}
{# The `action_url` and `form_title` are passed from the route #}
{% from "partials/forms/base_form.html" import form_group, form_actions, form_header %}

<form method="POST" action="{{ action_url }}" id="bulkImportDrawerForm" enctype="multipart/form-data" class="space-y-4">
  {{ form.hidden_tag() }}

  {{ form_header(
    title = form_title,
    description = "Upload a CSV file to import multiple holidays at once"
  ) }}

  <div class="grid grid-cols-1 gap-4">
    {{ form_group(
      label = form.file.label.text,
      name = form.file.name,
      type = "file",
      required = form.file.flags.required,
      hint = "CSV format: name,date,description,region_code"
    ) }}
    {% if form.file.errors %}
      <div class="text-xs text-destructive mt-1">
        <ul>
          {% for error in form.file.errors %}<li>{{ error }}</li>{% endfor %}
        </ul>
      </div>
    {% endif %}

    {{ form_group(
      label = form.region_code.label.text,
      name = form.region_code.name,
      type = "select",
      value = form.region_code.data if form.region_code.data else "",
      required = form.region_code.flags.required,
      options = form.region_code.choices,
      hint = "Used for holidays that don't specify a region in the CSV"
    ) }}
    {% if form.region_code.errors %}
      <div class="text-xs text-destructive mt-1">
        <ul>
          {% for error in form.region_code.errors %}<li>{{ error }}</li>{% endfor %}
        </ul>
      </div>
    {% endif %}

    {{ form_group(
      label = form.overwrite_existing.label.text,
      name = form.overwrite_existing.name,
      type = "select",
      value = form.overwrite_existing.data if form.overwrite_existing.data else "",
      options = form.overwrite_existing.choices
    ) }}
    {% if form.overwrite_existing.errors %}
      <div class="text-xs text-destructive mt-1">
        <ul>
          {% for error in form.overwrite_existing.errors %}<li>{{ error }}</li>{% endfor %}
        </ul>
      </div>
    {% endif %}

    <!-- CSV Format Help -->
    <div class="bg-muted/30 border border-border rounded-lg p-4">
      <h4 class="text-sm font-medium mb-2">CSV Format Requirements:</h4>
      <ul class="text-xs text-muted-foreground space-y-1">
        <li>• Required columns: <code>name</code>, <code>date</code></li>
        <li>• Optional columns: <code>description</code>, <code>region_code</code></li>
        <li>• Date format: YYYY-MM-DD or MM/DD/YYYY</li>
        <li>• Region codes: US, PH, GLOBAL</li>
      </ul>
    </div>
  </div>

  <div class="flex justify-end space-x-2 pt-4">
    <button type="button" class="btn btn-outline btn-md drawer-close">Cancel</button>
    <button type="button" onclick="submitDrawerForm('bulkImportDrawerForm')" class="btn btn-primary btn-md">
      {{ form.submit.label.text }}
    </button>
  </div>
</form>

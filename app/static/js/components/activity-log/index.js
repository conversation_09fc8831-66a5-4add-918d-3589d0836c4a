/**
 * User Activity Log Component - Main Entry Point
 *
 * This script initializes the user activity log component and coordinates
 * between the data, rendering, and event handling modules.
 */

import { loadActivities, loadMoreActivities } from './data.js';
import { setupActivityModal, setupViewAllButton } from './events.js';
import { renderActivities, renderLoadingState, renderErrorState } from './rendering.js';

/**
 * Initialize all user activity log components on the page
 */
function initializeUserActivityLogs() {
    const activityLogs = document.querySelectorAll('.user-activity-log');

    activityLogs.forEach(logContainer => {
        const entityType = logContainer.dataset.entityType;
        const entityId = logContainer.dataset.entityId;
        const limit = parseInt(logContainer.dataset.limit || 5);
        const inProfilePage = document.getElementById('activities-tab') !== null;

        // Show loading state
        renderLoadingState(logContainer);

        // Load activities
        loadActivities(entityType, entityId, limit, inProfilePage)
            .then(activities => {
                // Render activities
                renderActivities(logContainer, activities, inProfilePage);
                
                // Set up view all button
                setupViewAllButton(logContainer, entityType, entityId, inProfilePage);
            })
            .catch(error => {
                console.error('Error loading activities:', error);
                renderErrorState(logContainer, error);
            });
    });
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    // Initialize all user activity log components on the page
    initializeUserActivityLogs();

    // Set up modal close handlers
    setupActivityModal();
});

// Export public API
export {
    initializeUserActivityLogs
};

{% extends "base.html" %}

{% block title %}{{ team.name }} - Team Members{% endblock %}

{% block header %}{{ team.name }} - Team Members{% endblock %}

{% block content %}
<div class="space-y-6">
  <div class="flex justify-between items-center">
    <div class="flex items-center space-x-2">
      <a href="{{ url_for('teams.view', slug=team.slug) }}" class="btn btn-ghost btn-sm">
        <i data-lucide="arrow-left" class="w-4 h-4 mr-2"></i>
        Back to Team
      </a>
      <h1 class="text-2xl font-bold">{{ team.name }} - Members</h1>
    </div>
    <button onclick="drawerManager.openForm('team_member', null, { queryParams: { team_id: {{ team.id }} } })" class="btn btn-primary btn-md">
      <i data-lucide="user-plus" class="w-4 h-4 mr-2"></i>
      <span>Add Member</span>
    </button>
  </div>

  <!-- Team Members Card -->
  <div class="card">
    <div class="p-6 border-b border-border">
      <div class="flex items-center justify-between">
        <h2 class="text-lg font-semibold">Team Members</h2>
        <div class="flex items-center space-x-2">
          <span class="text-sm text-muted-foreground">{{ team.members|length }} members</span>
        </div>
      </div>
    </div>

    <div class="overflow-x-auto">
      <table class="w-full">
        <thead>
          <tr class="border-b border-border bg-muted/40">
            <th class="px-4 py-3 text-left text-sm font-medium text-muted-foreground">Name</th>
            <th class="px-4 py-3 text-left text-sm font-medium text-muted-foreground">Email</th>
            <th class="px-4 py-3 text-left text-sm font-medium text-muted-foreground">Position</th>
            <th class="px-4 py-3 text-left text-sm font-medium text-muted-foreground">Status</th>
            <th class="px-4 py-3 text-right text-sm font-medium text-muted-foreground">Actions</th>
          </tr>
        </thead>
        <tbody>
          {% for member in team.members %}
          <tr class="border-b border-border hover:bg-muted/30">
            <td class="px-4 py-3 text-sm font-medium">
              <a href="{{ url_for('admin.view_employee_details', user_id=member.user_id) }}" class="text-primary hover:underline">
                {{ member.user.name }}
              </a>
            </td>
            <td class="px-4 py-3 text-sm">{{ member.user.email }}</td>
            <td class="px-4 py-3 text-sm">{{ member.position or '-' }}</td>
            <td class="px-4 py-3 text-sm">
              {% if member.emp_status == 'active' %}
              <span class="badge badge-success">Active</span>
              {% elif member.emp_status == 'terminated' %}
              <span class="badge badge-destructive">Terminated</span>
              {% elif member.emp_status == 'leave_of_absence' %}
              <span class="badge badge-warning">Leave of Absence</span>
              {% else %}
              <span class="badge badge-secondary">{{ member.emp_status|title }}</span>
              {% endif %}
            </td>
            <td class="px-4 py-3 text-sm text-right">
              <div class="flex justify-end space-x-2">
                <a href="{{ url_for('admin.view_employee_details', user_id=member.user_id) }}" class="btn btn-sm btn-ghost">
                  <i data-lucide="eye" class="w-4 h-4"></i>
                </a>
                <button class="btn btn-sm btn-ghost text-destructive" onclick="confirmRemoveMember({{ member.id }}, '{{ member.user.name }}')">
                  <i data-lucide="user-minus" class="w-4 h-4"></i>
                </button>
              </div>
            </td>
          </tr>
          {% else %}
          <tr>
            <td colspan="5" class="px-4 py-8 text-center text-muted-foreground">
              <div class="flex flex-col items-center justify-center space-y-3">
                <i data-lucide="users" class="w-12 h-12 text-muted-foreground/50"></i>
                <div>
                  <p class="text-lg font-medium">No team members</p>
                  <p class="text-sm text-muted-foreground">Add members to this team</p>
                </div>
                <button onclick="drawerManager.openForm('team_member', null, { queryParams: { team_id: {{ team.id }} } })" class="btn btn-primary btn-sm mt-2">
                  <i data-lucide="user-plus" class="w-4 h-4 mr-2"></i>
                  <span>Add Member</span>
                </button>
              </div>
            </td>
          </tr>
          {% endfor %}
        </tbody>
      </table>
    </div>
  </div>
</div>

{% endblock %}

{% block scripts %}
<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Process icons if needed
    if (window.processIcons) {
      window.processIcons();
    }
  });

  function confirmRemoveMember(employeeId, memberName) {
    showAlertDialog({
      title: 'Remove Team Member',
      description: `Are you sure you want to remove ${memberName} from this team?`,
      variant: 'destructive',
      onConfirm: () => {
        // Submit form to remove endpoint
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = "{{ url_for('teams.remove_member', team_id=team.id, employee_id=0) }}".replace('0', employeeId);

        const csrfToken = document.createElement('input');
        csrfToken.type = 'hidden';
        csrfToken.name = 'csrf_token';
        csrfToken.value = '{{ csrf_token() }}';

        form.appendChild(csrfToken);
        document.body.appendChild(form);
        form.submit();
      }
    });
  }
</script>
{% endblock %}

/**
 * Theme Colors using CSS Custom Properties
 * This approach allows for instant theme switching without repaints
 */

/* Base theme colors - Light mode is default */
:root {
  /* Primary colors */
  --color-primary: 59 130 246; /* blue-500 */
  --color-primary-foreground: 255 255 255;
  
  /* Neutral colors */
  --color-background: 255 255 255;
  --color-foreground: 17 24 39; /* gray-900 */
  
  /* Card colors */
  --color-card: 255 255 255;
  --color-card-foreground: 17 24 39; /* gray-900 */
  
  /* Muted colors */
  --color-muted: 243 244 246; /* gray-100 */
  --color-muted-foreground: 107 114 128; /* gray-500 */
  
  /* Border colors */
  --color-border: 229 231 235; /* gray-200 */
  
  /* Input colors */
  --color-input: 255 255 255;
  --color-input-foreground: 17 24 39; /* gray-900 */
  
  /* Ring colors */
  --color-ring: 59 130 246; /* blue-500 */
  
  /* Status colors */
  --color-success: 16 185 129; /* green-500 */
  --color-warning: 245 158 11; /* amber-500 */
  --color-error: 239 68 68; /* red-500 */
  --color-info: 59 130 246; /* blue-500 */
  
  /* Sidebar colors */
  --color-sidebar: 255 255 255;
  --color-sidebar-foreground: 17 24 39; /* gray-900 */
  --color-sidebar-muted: 243 244 246; /* gray-100 */
  --color-sidebar-active: 59 130 246; /* blue-500 */
  
  /* Button colors */
  --color-button-primary: 59 130 246; /* blue-500 */
  --color-button-primary-foreground: 255 255 255;
  --color-button-secondary: 229 231 235; /* gray-200 */
  --color-button-secondary-foreground: 17 24 39; /* gray-900 */
  
  /* Badge colors */
  --color-badge-primary: 59 130 246; /* blue-500 */
  --color-badge-primary-foreground: 255 255 255;
  --color-badge-secondary: 229 231 235; /* gray-200 */
  --color-badge-secondary-foreground: 17 24 39; /* gray-900 */
  --color-badge-warning: 245 158 11; /* amber-500 */
  --color-badge-warning-foreground: 255 255 255;
  --color-badge-danger: 239 68 68; /* red-500 */
  --color-badge-danger-foreground: 255 255 255;
  --color-badge-success: 16 185 129; /* green-500 */
  --color-badge-success-foreground: 255 255 255;
  --color-badge-info: 59 130 246; /* blue-500 */
  --color-badge-info-foreground: 255 255 255;
}

/* Dark mode colors */
.dark {
  /* Primary colors */
  --color-primary: 96 165 250; /* blue-400 */
  --color-primary-foreground: 255 255 255;
  
  /* Neutral colors */
  --color-background: 17 24 39; /* gray-900 */
  --color-foreground: 243 244 246; /* gray-100 */
  
  /* Card colors */
  --color-card: 31 41 55; /* gray-800 */
  --color-card-foreground: 243 244 246; /* gray-100 */
  
  /* Muted colors */
  --color-muted: 55 65 81; /* gray-700 */
  --color-muted-foreground: 156 163 175; /* gray-400 */
  
  /* Border colors */
  --color-border: 75 85 99; /* gray-600 */
  
  /* Input colors */
  --color-input: 31 41 55; /* gray-800 */
  --color-input-foreground: 243 244 246; /* gray-100 */
  
  /* Ring colors */
  --color-ring: 96 165 250; /* blue-400 */
  
  /* Status colors */
  --color-success: 34 197 94; /* green-500 */
  --color-warning: 251 191 36; /* amber-400 */
  --color-error: 248 113 113; /* red-400 */
  --color-info: 96 165 250; /* blue-400 */
  
  /* Sidebar colors */
  --color-sidebar: 31 41 55; /* gray-800 */
  --color-sidebar-foreground: 243 244 246; /* gray-100 */
  --color-sidebar-muted: 55 65 81; /* gray-700 */
  --color-sidebar-active: 96 165 250; /* blue-400 */
  
  /* Button colors */
  --color-button-primary: 96 165 250; /* blue-400 */
  --color-button-primary-foreground: 255 255 255;
  --color-button-secondary: 55 65 81; /* gray-700 */
  --color-button-secondary-foreground: 243 244 246; /* gray-100 */
  
  /* Badge colors */
  --color-badge-primary: 96 165 250; /* blue-400 */
  --color-badge-primary-foreground: 255 255 255;
  --color-badge-secondary: 55 65 81; /* gray-700 */
  --color-badge-secondary-foreground: 243 244 246; /* gray-100 */
  --color-badge-warning: 251 191 36; /* amber-400 */
  --color-badge-warning-foreground: 31 41 55; /* gray-800 */
  --color-badge-danger: 248 113 113; /* red-400 */
  --color-badge-danger-foreground: 255 255 255;
  --color-badge-success: 34 197 94; /* green-500 */
  --color-badge-success-foreground: 255 255 255;
  --color-badge-info: 96 165 250; /* blue-400 */
  --color-badge-info-foreground: 255 255 255;
}

/* Helper classes that use the CSS variables */
.bg-background {
  background-color: rgb(var(--color-background) / 1);
}

.text-foreground {
  color: rgb(var(--color-foreground) / 1);
}

.bg-card {
  background-color: rgb(var(--color-card) / 1);
}

.text-card-foreground {
  color: rgb(var(--color-card-foreground) / 1);
}

.bg-muted {
  background-color: rgb(var(--color-muted) / 1);
}

.text-muted-foreground {
  color: rgb(var(--color-muted-foreground) / 1);
}

.border-border {
  border-color: rgb(var(--color-border) / 1);
}

.bg-primary {
  background-color: rgb(var(--color-primary) / 1);
}

.text-primary {
  color: rgb(var(--color-primary) / 1);
}

.text-primary-foreground {
  color: rgb(var(--color-primary-foreground) / 1);
}

.bg-success {
  background-color: rgb(var(--color-success) / 1);
}

.bg-warning {
  background-color: rgb(var(--color-warning) / 1);
}

.bg-error {
  background-color: rgb(var(--color-error) / 1);
}

.bg-info {
  background-color: rgb(var(--color-info) / 1);
}

/* Button styles using CSS variables */
.btn-primary {
  background-color: rgb(var(--color-button-primary) / 1);
  color: rgb(var(--color-button-primary-foreground) / 1);
}

.btn-secondary {
  background-color: rgb(var(--color-button-secondary) / 1);
  color: rgb(var(--color-button-secondary-foreground) / 1);
}

/* Badge styles using CSS variables */
.badge-primary {
  background-color: rgb(var(--color-badge-primary) / 1);
  color: rgb(var(--color-badge-primary-foreground) / 1);
}

.badge-secondary {
  background-color: rgb(var(--color-badge-secondary) / 1);
  color: rgb(var(--color-badge-secondary-foreground) / 1);
}

.badge-warning {
  background-color: rgb(var(--color-badge-warning) / 1);
  color: rgb(var(--color-badge-warning-foreground) / 1);
}

.badge-danger {
  background-color: rgb(var(--color-badge-danger) / 1);
  color: rgb(var(--color-badge-danger-foreground) / 1);
}

.badge-success {
  background-color: rgb(var(--color-badge-success) / 1);
  color: rgb(var(--color-badge-success-foreground) / 1);
}

.badge-info {
  background-color: rgb(var(--color-badge-info) / 1);
  color: rgb(var(--color-badge-info-foreground) / 1);
}

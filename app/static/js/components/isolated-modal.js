/**
 * Modal component implementation
 * No dependencies, no conflicts with existing code
 */

// Use an IIFE to avoid polluting the global namespace
(function() {
  // Initialize when the DOM is loaded
  document.addEventListener('DOMContentLoaded', function() {
    // Find all modal triggers
    var triggers = document.querySelectorAll('[data-modal]');

    // Add click event to each trigger
    triggers.forEach(function(trigger) {
      var modalId = trigger.getAttribute('data-modal');

      trigger.addEventListener('click', function() {
        // Get modal content from the template
        var template = document.getElementById(modalId + '-template');
        if (template) {
          showModal({
            id: modalId,
            content: template.innerHTML,
            size: template.getAttribute('data-size') || 'md'
          });
        } else {
          console.error('Modal template with ID ' + modalId + '-template not found');
        }
      });
    });

    // For backward compatibility
    var oldTriggers = document.querySelectorAll('[data-isolated-modal]');
    oldTriggers.forEach(function(trigger) {
      var modalId = trigger.getAttribute('data-isolated-modal');

      trigger.addEventListener('click', function() {
        // Get modal content from the template
        var template = document.getElementById(modalId + '-template');
        if (template) {
          showModal({
            id: modalId,
            content: template.innerHTML,
            size: template.getAttribute('data-size') || 'md'
          });
        } else {
          console.error('Modal template with ID ' + modalId + '-template not found');
        }
      });
    });
  });

  /**
   * Initialize the modal container if it doesn't exist
   * @returns {HTMLElement} - The modal container element
   */
  function initModalContainer() {
    var container = document.getElementById('modal-container');

    if (!container) {
      container = document.createElement('div');
      container.id = 'modal-container';
      container.setAttribute('aria-live', 'polite');
      container.setAttribute('aria-atomic', 'true');
      document.body.appendChild(container);
    }

    return container;
  }

  /**
   * Show a modal with the specified options
   * @param {Object} options - Options for the modal
   * @param {string} options.id - ID of the modal
   * @param {string} options.content - HTML content of the modal
   * @param {string} options.size - Size of the modal ('sm', 'md', 'lg', 'xl', 'full')
   * @returns {Function} A function to close the modal
   */
  function showModal(options) {
    var container = document.getElementById('modal-container') || initModalContainer();

    // Store the currently focused element to restore focus when modal closes
    var previouslyFocusedElement = document.activeElement;

    // Create overlay
    var overlay = document.createElement('div');
    overlay.className = 'modal-overlay';
    overlay.setAttribute('data-state', 'open');

    // Create dialog content
    var dialog = document.createElement('div');
    dialog.className = 'modal-dialog ' + (options.size || 'md');
    dialog.setAttribute('data-state', 'open');
    dialog.setAttribute('role', 'dialog');
    dialog.setAttribute('aria-modal', 'true');
    dialog.setAttribute('aria-labelledby', options.id + '-title');
    dialog.innerHTML = options.content;

    // Add to container
    container.appendChild(overlay);
    container.appendChild(dialog);

    // Initialize icons in the dialog
    if (typeof lucide !== 'undefined' && lucide.createIcons) {
      lucide.createIcons({
        root: dialog
      });
    }

    // Find close buttons
    var closeButtons = dialog.querySelectorAll('[data-modal-close]');
    if (closeButtons.length === 0) {
      // For backward compatibility
      closeButtons = dialog.querySelectorAll('[data-isolated-modal-close]');
    }

    closeButtons.forEach(function(button) {
      button.addEventListener('click', function() {
        closeModal();
      });
    });

    // Close dialog function
    function closeModal() {
      overlay.setAttribute('data-state', 'closed');
      dialog.setAttribute('data-state', 'closed');

      // Remove elements after animation completes
      setTimeout(function() {
        if (container.contains(overlay)) {
          container.removeChild(overlay);
        }
        if (container.contains(dialog)) {
          container.removeChild(dialog);
        }

        // Restore focus to the element that had it before the dialog was opened
        if (previouslyFocusedElement && typeof previouslyFocusedElement.focus === 'function') {
          previouslyFocusedElement.focus();
        }
      }, 300);
    }

    // Event listeners
    overlay.addEventListener('click', function() {
      closeModal();
    });

    // Close on escape key
    function handleEscapeKey(e) {
      if (e.key === 'Escape') {
        closeModal();
        document.removeEventListener('keydown', handleEscapeKey);
      }
    }

    document.addEventListener('keydown', handleEscapeKey);

    // Return close function
    return closeModal;
  }

  // Export functions to global scope
  window.showModal = showModal;

  // For backward compatibility
  window.showIsolatedModal = showModal;
})();

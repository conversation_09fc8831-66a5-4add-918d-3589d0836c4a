# Development Guidelines

**Last Updated:** January 2025  
**Version:** 1.0.0

## Overview

This document outlines the coding standards, best practices, and development workflows for the Matrix App project. Following these guidelines ensures code consistency, maintainability, and team collaboration efficiency.

## Code Quality & Standards

### Python Code Standards

**PEP 8 Compliance**
- Use 4 spaces for indentation (no tabs)
- Maximum line length: 88 characters (Black formatter standard)
- Use descriptive variable and function names
- Follow Python naming conventions

**Type Hints**
```python
def get_user_by_email(email: str) -> Optional[User]:
    """Get user by email address."""
    return User.query.filter_by(email=email).first()

def create_user(name: str, email: str, role: str = 'User') -> User:
    """Create a new user with the specified details."""
    user = User(name=name, email=email, role=role)
    db.session.add(user)
    db.session.commit()
    return user
```

**Error Handling**
```python
# Use early returns and guard clauses
def process_user_data(user_data: Dict[str, Any]) -> Dict[str, Any]:
    if not user_data:
        return {'error': 'No data provided'}
    
    if 'email' not in user_data:
        return {'error': 'Email is required'}
    
    # Process valid data
    return {'success': True, 'user': processed_user}
```

**Documentation Standards**
```python
def calculate_attendance_percentage(
    employee_id: int, 
    start_date: datetime, 
    end_date: datetime
) -> float:
    """
    Calculate attendance percentage for an employee within a date range.
    
    Args:
        employee_id: The ID of the employee
        start_date: Start date for calculation (inclusive)
        end_date: End date for calculation (inclusive)
    
    Returns:
        Attendance percentage as a float between 0.0 and 100.0
    
    Raises:
        ValueError: If start_date is after end_date
        EmployeeNotFoundError: If employee doesn't exist
    """
    # Implementation here
```

### JavaScript Code Standards

**ES6+ Features**
```javascript
// Use const/let instead of var
const API_BASE_URL = '/api';
let currentUser = null;

// Use arrow functions for callbacks
const users = data.map(user => ({
    id: user.id,
    name: user.name,
    email: user.email
}));

// Use template literals
const message = `Welcome, ${user.name}!`;

// Use destructuring
const { name, email, role } = user;
```

**Module Organization**
```javascript
// utils/dom.js
export function getElement(selector) {
    return document.querySelector(selector);
}

export function createElement(tag, attributes = {}, children = []) {
    const element = document.createElement(tag);
    Object.assign(element, attributes);
    children.forEach(child => element.appendChild(child));
    return element;
}

// main.js
import { getElement, createElement } from './utils/dom.js';
```

### CSS/Tailwind Standards

**Utility-First Approach**
```html
<!-- Prefer Tailwind utilities -->
<button class="btn btn-primary px-4 py-2 rounded-md hover:bg-primary/90">
    Submit
</button>

<!-- Avoid custom CSS when possible -->
<div class="flex items-center justify-between p-4 bg-white dark:bg-gray-800">
    Content
</div>
```

**Component Classes**
```css
/* Use @layer components for reusable patterns */
@layer components {
    .btn {
        @apply inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors;
    }
    
    .btn-primary {
        @apply bg-primary text-primary-foreground hover:bg-primary/90;
    }
}
```

## Architecture Patterns

### Flask Application Structure

**Blueprint Organization**
```python
# routes/admin/__init__.py
from flask import Blueprint

admin_bp = Blueprint('admin', __name__, url_prefix='/admin')

# Import all admin routes
from . import dashboard, users, business
```

**Model Design**
```python
class User(UserMixin, db.Model):
    """User model with proper relationships and methods."""
    
    # Class methods for queries
    @classmethod
    def get_active_users(cls) -> List['User']:
        return cls.query.filter_by(_is_active=True).all()
    
    # Instance methods for business logic
    def can_manage_user(self, other_user: 'User') -> bool:
        return self.is_admin or self == other_user
    
    # Properties for computed values
    @property
    def is_admin(self) -> bool:
        return self.role == 'Admin'
```

### Frontend Component Architecture

**Component Structure**
```javascript
// components/modal.js
class Modal {
    constructor(options = {}) {
        this.options = { size: 'md', ...options };
        this.element = null;
        this.overlay = null;
    }
    
    show(content) {
        this.create();
        this.render(content);
        this.bindEvents();
    }
    
    hide() {
        this.cleanup();
    }
    
    // Private methods
    create() { /* ... */ }
    render(content) { /* ... */ }
    bindEvents() { /* ... */ }
    cleanup() { /* ... */ }
}
```

## Testing Standards

### Unit Testing

**Test Structure**
```python
# tests/test_user_model.py
import pytest
from app.models import User
from app import db

class TestUserModel:
    """Test cases for User model."""
    
    def test_user_creation(self, app):
        """Test basic user creation."""
        with app.app_context():
            user = User(name='Test User', email='<EMAIL>')
            db.session.add(user)
            db.session.commit()
            
            assert user.id is not None
            assert user.name == 'Test User'
            assert user.email == '<EMAIL>'
    
    def test_password_hashing(self, app):
        """Test password hashing functionality."""
        with app.app_context():
            user = User(name='Test', email='<EMAIL>')
            user.set_password('testpassword')
            
            assert user.password_hash is not None
            assert user.check_password('testpassword')
            assert not user.check_password('wrongpassword')
```

**Test Coverage Requirements**
- Minimum 80% code coverage for new features
- 100% coverage for critical security functions
- All API endpoints must have tests
- All model methods must have tests

### Integration Testing

**API Testing**
```python
def test_user_api_endpoints(client, auth_headers):
    """Test user API endpoints."""
    # Test GET /api/users
    response = client.get('/api/users', headers=auth_headers)
    assert response.status_code == 200
    assert 'users' in response.json
    
    # Test POST /api/users
    user_data = {
        'name': 'New User',
        'email': '<EMAIL>',
        'role': 'User'
    }
    response = client.post('/api/users', json=user_data, headers=auth_headers)
    assert response.status_code == 201
    assert response.json['success'] is True
```

## Security Guidelines

### Input Validation

**Always Validate Input**
```python
from marshmallow import Schema, fields, validate

class UserSchema(Schema):
    name = fields.Str(required=True, validate=validate.Length(min=1, max=100))
    email = fields.Email(required=True)
    role = fields.Str(validate=validate.OneOf(['Admin', 'Manager', 'User']))

# In route handler
schema = UserSchema()
try:
    data = schema.load(request.json)
except ValidationError as err:
    return jsonify({'error': 'Validation failed', 'details': err.messages}), 400
```

**SQL Injection Prevention**
```python
# Use SQLAlchemy ORM (preferred)
users = User.query.filter(User.email == email).all()

# If raw SQL is necessary, use parameterized queries
result = db.session.execute(
    text("SELECT * FROM users WHERE email = :email"),
    {'email': email}
)
```

### Authentication & Authorization

**Route Protection**
```python
@admin_bp.route('/users')
@login_required
@admin_required
def manage_users():
    """Admin-only user management page."""
    return render_template('admin/users.html')

@api_bp.route('/users/<int:user_id>')
@login_required
def get_user(user_id):
    """Get user details with proper authorization."""
    user = User.query.get_or_404(user_id)
    
    # Check if current user can access this user's data
    if not current_user.can_view_user(user):
        abort(403)
    
    return jsonify(user.to_dict())
```

## Performance Guidelines

### Database Optimization

**Query Optimization**
```python
# Use eager loading to prevent N+1 queries
employees = EmployeeDetail.query.options(
    db.joinedload('user'),
    db.joinedload('business_unit'),
    db.joinedload('teams')
).all()

# Use pagination for large datasets
def get_paginated_users(page=1, per_page=20):
    return User.query.paginate(
        page=page, 
        per_page=per_page, 
        error_out=False
    )
```

**Caching Strategy**
```python
from app import cache

@cache.cached(timeout=300, key_prefix='user_count')
def get_user_count():
    return User.query.count()

# Cache with dynamic keys
@cache.cached(timeout=60)
def get_user_activities(user_id):
    return Activity.query.filter_by(user_id=user_id).limit(10).all()
```

### Frontend Performance

**Asset Optimization**
```javascript
// Lazy load non-critical components
async function loadComponent(name) {
    const module = await import(`./components/${name}.js`);
    return module.default;
}

// Use event delegation for dynamic content
document.addEventListener('click', (event) => {
    if (event.target.matches('.btn-delete')) {
        handleDelete(event.target.dataset.id);
    }
});
```

## Git Workflow

### Commit Standards

**Commit Message Format**
```
type(scope): description

feat(auth): add password reset functionality
fix(api): resolve user creation validation error
docs(readme): update installation instructions
refactor(models): simplify user relationship queries
test(auth): add login endpoint tests
```

**Commit Types**
- `feat`: New features
- `fix`: Bug fixes
- `docs`: Documentation changes
- `style`: Code style changes
- `refactor`: Code refactoring
- `test`: Test additions/modifications
- `chore`: Build process or auxiliary tool changes

### Branch Strategy

**Branch Naming**
```
feature/user-management-improvements
bugfix/login-validation-error
hotfix/security-vulnerability-patch
docs/api-documentation-update
```

**Pull Request Process**
1. Create feature branch from `main`
2. Implement changes with tests
3. Update documentation if needed
4. Submit pull request with description
5. Code review and approval
6. Merge to `main` branch

## Code Review Guidelines

### Review Checklist

**Functionality**
- [ ] Code meets requirements
- [ ] Edge cases are handled
- [ ] Error handling is appropriate
- [ ] Performance considerations addressed

**Code Quality**
- [ ] Follows coding standards
- [ ] Proper naming conventions
- [ ] Adequate documentation
- [ ] No code duplication

**Security**
- [ ] Input validation implemented
- [ ] Authentication/authorization correct
- [ ] No sensitive data exposed
- [ ] SQL injection prevention

**Testing**
- [ ] Unit tests included
- [ ] Integration tests where appropriate
- [ ] Test coverage adequate
- [ ] Tests pass consistently

### Review Process

1. **Self-Review**: Author reviews own code before submission
2. **Peer Review**: At least one team member reviews changes
3. **Testing**: All tests must pass before merge
4. **Documentation**: Update relevant documentation

---

Following these guidelines ensures consistent, maintainable, and secure code that supports the long-term success of the Matrix App project.

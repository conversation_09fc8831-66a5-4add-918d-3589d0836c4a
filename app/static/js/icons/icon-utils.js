/**
 * Icon Utilities
 * Optimized icon loading system with sprite support and lazy loading
 */

import { criticalIcons } from './critical-icons.js';

// Track if the sprite has been loaded
let spriteLoaded = false;

// Track which icons have been loaded
const loadedIcons = new Set();

/**
 * Load the icon sprite if it hasn't been loaded yet
 * @returns {Promise} - Promise that resolves when the sprite is loaded
 */
export function loadIconSprite() {
  if (spriteLoaded) {
    return Promise.resolve();
  }

  return new Promise((resolve, reject) => {
    // Create a new XMLHttpRequest
    const xhr = new XMLHttpRequest();
    xhr.open('GET', '/static/js/icons/sprite.svg', true);

    // Handle load event
    xhr.onload = () => {
      if (xhr.status >= 200 && xhr.status < 400) {
        // Create a div to hold the sprite
        const div = document.createElement('div');
        div.style.display = 'none';
        div.innerHTML = xhr.responseText;
        document.body.appendChild(div);

        // Mark sprite as loaded
        spriteLoaded = true;
        resolve();
      } else {
        reject(new Error(`Failed to load sprite: ${xhr.statusText}`));
      }
    };

    // Handle error event
    xhr.onerror = () => {
      reject(new Error('Failed to load sprite'));
    };

    // Send the request
    xhr.send();
  });
}

/**
 * Create an icon element using the sprite
 * @param {string} name - The name of the icon
 * @param {Object} options - Options for the icon
 * @returns {HTMLElement} - The icon element
 */
export function createSpriteIcon(name, options = {}) {
  const {
    class: className = '',
    size = 24,
    color = 'currentColor',
    strokeWidth = 2
  } = options;

  // Create the SVG element
  const svg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
  svg.setAttribute('width', size);
  svg.setAttribute('height', size);
  svg.setAttribute('stroke', color);
  svg.setAttribute('stroke-width', strokeWidth);
  svg.setAttribute('fill', 'none');
  svg.setAttribute('stroke-linecap', 'round');
  svg.setAttribute('stroke-linejoin', 'round');

  if (className) {
    svg.setAttribute('class', className);
  }

  // Create the use element
  const use = document.createElementNS('http://www.w3.org/2000/svg', 'use');
  use.setAttributeNS('http://www.w3.org/1999/xlink', 'xlink:href', `#${name}`);
  svg.appendChild(use);

  return svg;
}

/**
 * Initialize icons in a container
 * @param {HTMLElement} container - The container element
 */
export function initIcons(container = document) {
  // Load the sprite first
  loadIconSprite().then(() => {
    // Find all icon elements
    const iconElements = container.querySelectorAll('[data-lucide]');

    // Process each icon
    iconElements.forEach(element => {
      const name = element.getAttribute('data-lucide');

      // Skip if the icon is not found
      if (!name) return;

      // Mark the element as processed to prevent double processing
      element.dataset.processed = 'true';

      // Check if the icon is in the sprite
      if (criticalIcons.includes(name)) {
        // Create options from element attributes
        const options = {
          class: element.className ? element.className + ' sprite-icon' : 'sprite-icon',
          size: element.style.width ? parseInt(element.style.width) : 24,
          color: element.style.color || 'currentColor',
          strokeWidth: element.getAttribute('stroke-width') || 2
        };

        // Create the sprite icon
        const icon = createSpriteIcon(name, options);

        // Add data attribute to mark as processed by sprite system
        icon.setAttribute('data-sprite-icon', name);

        // Replace the element with the icon
        if (element.parentNode) {
          element.parentNode.replaceChild(icon, element);
        }
      } else {
        // For non-critical icons, load them on demand using Lucide
        loadIconOnDemand(element);
      }

      // Make sure the global criticalIcons array is available
      if (window.criticalIcons === undefined) {
        window.criticalIcons = criticalIcons;
      }
    });
  }).catch(error => {
    console.error('Failed to load icon sprite:', error);

    // Fallback to loading the full Lucide library
    if (typeof lucide === 'undefined') {
      const script = document.createElement('script');
      script.src = '/static/js/vendor/lucide/lucide.min.js';
      script.onload = () => {
        if (typeof lucide !== 'undefined' && lucide.createIcons) {
          lucide.createIcons();
        }
      };
      document.head.appendChild(script);
    } else if (lucide.createIcons) {
      lucide.createIcons();
    }
  });
}

/**
 * Load an icon on demand using Lucide
 * @param {HTMLElement} element - The element to replace with an icon
 */
function loadIconOnDemand(element) {
  const name = element.getAttribute('data-lucide');

  // Skip if the icon is not found
  if (!name) return;

  // Skip if the icon is already loaded and initialized
  if (element.tagName.toLowerCase() === 'svg') return;

  // Mark the element as being processed
  element.dataset.processed = 'true';

  // We'll use the full Lucide library for non-critical icons
  // First, ensure the Lucide library is loaded
  if (typeof lucide === 'undefined') {
    // Check if we're already loading Lucide
    if (!document.querySelector('script[src*="lucide.min.js"]')) {
      // Load the Lucide library if it's not already loading
      const script = document.createElement('script');
      script.src = '/static/js/vendor/lucide/lucide.min.js';
      script.onload = () => {
        // Once loaded, initialize the icon
        if (typeof lucide !== 'undefined' && lucide.createIcons) {
          // Try to create the icon
          lucide.createIcons({
            elements: [element]
          });

          // Check if the icon was created successfully
          setTimeout(() => {
            if (element.parentNode && element.tagName.toLowerCase() === 'i') {
              if (typeof window.createFallbackIcon === 'function') {
                window.createFallbackIcon(element);
              }
            }
          }, 50);
        }
      };
      script.onerror = () => {
        console.error(`Failed to load Lucide library for icon: ${name}`);
        if (typeof window.createFallbackIcon === 'function') {
          window.createFallbackIcon(element);
        }
      };
      document.head.appendChild(script);
    }
  } else {
    // Lucide is already loaded, just initialize the icon
    if (lucide.createIcons) {
      // Try to create the icon
      lucide.createIcons({
        elements: [element]
      });

      // Check if the icon was created successfully
      setTimeout(() => {
        if (element.parentNode && element.tagName.toLowerCase() === 'i') {
          if (typeof window.createFallbackIcon === 'function') {
            window.createFallbackIcon(element);
          }
        }
      }, 50);
    }
  }

  // Add to loaded icons set
  loadedIcons.add(name);
}

// Initialize icons when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
  initIcons();
});

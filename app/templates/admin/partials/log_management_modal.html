{% from "components/form_select.html" import form_select, form_select_with_label %}

{% macro log_management_modal(activity_categories) %}
<!-- Log Management Modal Template -->
<template id="log-management-modal-template" data-size="md">
  <div class="flex flex-col">
    <!-- Header -->
    <div class="flex items-start justify-between px-6 pt-6 pb-4 border-b border-border">
      <div class="flex flex-col space-y-1 text-left pr-6">
        <h2 class="text-lg font-semibold leading-tight tracking-tight text-gray-900 dark:text-white">Manage Activity Logs</h2>
      </div>
      <button
        type="button"
        class="absolute right-4 top-4 rounded-full opacity-70 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 h-8 w-8 inline-flex items-center justify-center transition-all duration-200 hover:opacity-100 disabled:pointer-events-none"
        data-modal-close
        aria-label="Close"
      >
        <i data-lucide="x" class="h-4 w-4 pointer-events-none"></i>
        <span class="sr-only">Close</span>
      </button>
    </div>

    <!-- Content -->
    <div class="px-6 pb-6 pt-6 overflow-y-auto">
      <div id="log-management-form">
        <form id="log-management-form-element" action="/admin/activities/manage" method="post" onsubmit="return validateLogForm()">
          <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
          <div class="space-y-6">
            <!-- Action Selection -->
            <div class="space-y-2">
              <label for="log-action" class="text-sm font-medium leading-none text-gray-700 dark:text-gray-300 peer-disabled:cursor-not-allowed peer-disabled:opacity-70 flex items-center">
                <i data-lucide="trash-2" class="h-3.5 w-3.5 mr-1.5 text-gray-500 dark:text-gray-400"></i>
                Select Action
              </label>
              <div class="relative isolate overflow-visible">
                <select
                  id="log-action"
                  name="action"
                  class="flex h-9 w-full rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-200 px-3 py-1 pl-9 pr-9 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-gray-400 dark:placeholder:text-gray-500 focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-blue-500 dark:focus-visible:ring-blue-400 focus-visible:border-blue-500 dark:focus-visible:border-blue-400 hover:border-gray-400 dark:hover:border-gray-500 disabled:cursor-not-allowed disabled:opacity-50 appearance-none select-none bg-none z-10"
                  onchange="handleActionChange(this.value)"
                >
                  <option value="delete_older_than">Delete logs older than...</option>
                  <option value="delete_by_category">Delete logs by category</option>
                  <option value="delete_by_severity">Delete logs by severity</option>
                  <option value="delete_all">Delete all logs</option>
                </select>
                <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none z-20 select-none">
                  <i data-lucide="trash" class="h-4 w-4 text-gray-500 dark:text-gray-400 pointer-events-none"></i>
                </div>
                <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none z-20 select-none">
                  <i data-lucide="chevron-down" class="h-4 w-4 text-gray-500 dark:text-gray-400 pointer-events-none"></i>
                </div>
              </div>
            </div>

            <!-- Days Option -->
            <div id="days-option" style="display: block;">
              <div class="space-y-2">
                <label for="days-select" class="text-sm font-medium leading-none text-gray-700 dark:text-gray-300 peer-disabled:cursor-not-allowed peer-disabled:opacity-70 flex items-center">
                  <i data-lucide="calendar" class="h-3.5 w-3.5 mr-1.5 text-gray-500 dark:text-gray-400"></i>
                  Select Time Period
                </label>
                <div class="relative isolate overflow-visible">
                  <select
                    id="days-select"
                    name="days"
                    class="flex h-9 w-full rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-200 px-3 py-1 pl-9 pr-9 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-gray-400 dark:placeholder:text-gray-500 focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-blue-500 dark:focus-visible:ring-blue-400 focus-visible:border-blue-500 dark:focus-visible:border-blue-400 hover:border-gray-400 dark:hover:border-gray-500 disabled:cursor-not-allowed disabled:opacity-50 appearance-none select-none bg-none z-10"
                  >
                    <option value="30">30 days</option>
                    <option value="60">60 days</option>
                    <option value="90">90 days</option>
                    <option value="180">180 days</option>
                    <option value="365">1 year</option>
                  </select>
                  <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none z-20 select-none">
                    <i data-lucide="clock" class="h-4 w-4 text-gray-500 dark:text-gray-400 pointer-events-none"></i>
                  </div>
                  <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none z-20 select-none">
                    <i data-lucide="chevron-down" class="h-4 w-4 text-gray-500 dark:text-gray-400 pointer-events-none"></i>
                  </div>
                </div>
              </div>
            </div>

            <!-- Category Option -->
            <div id="category-option" style="display: none;">
              <div class="space-y-2">
                <label for="category-select" class="text-sm font-medium leading-none text-gray-700 dark:text-gray-300 peer-disabled:cursor-not-allowed peer-disabled:opacity-70 flex items-center">
                  <i data-lucide="tag" class="h-3.5 w-3.5 mr-1.5 text-gray-500 dark:text-gray-400"></i>
                  Select Category
                </label>
                <div class="relative isolate overflow-visible">
                  <select
                    id="category-select"
                    name="category"
                    class="flex h-9 w-full rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-200 px-3 py-1 pl-9 pr-9 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-gray-400 dark:placeholder:text-gray-500 focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-blue-500 dark:focus-visible:ring-blue-400 focus-visible:border-blue-500 dark:focus-visible:border-blue-400 hover:border-gray-400 dark:hover:border-gray-500 disabled:cursor-not-allowed disabled:opacity-50 appearance-none select-none bg-none z-10"
                  >
                    <option value="">Select a category</option>
                    {% for category_tuple in activity_categories %}
                    <option value="{{ category_tuple[0] }}">{{ category_tuple[1] }}</option>
                    {% endfor %}
                  </select>
                  <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none z-20 select-none">
                    <i data-lucide="tag" class="h-4 w-4 text-gray-500 dark:text-gray-400 pointer-events-none"></i>
                  </div>
                  <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none z-20 select-none">
                    <i data-lucide="chevron-down" class="h-4 w-4 text-gray-500 dark:text-gray-400 pointer-events-none"></i>
                  </div>
                </div>
              </div>
            </div>

            <!-- Severity Option -->
            <div id="severity-option" style="display: none;">
              <div class="space-y-2">
                <label for="severity-select" class="text-sm font-medium leading-none text-gray-700 dark:text-gray-300 peer-disabled:cursor-not-allowed peer-disabled:opacity-70 flex items-center">
                  <i data-lucide="alert-circle" class="h-3.5 w-3.5 mr-1.5 text-gray-500 dark:text-gray-400"></i>
                  Select Severity
                </label>
                <div class="relative isolate overflow-visible">
                  <select
                    id="severity-select"
                    name="severity"
                    class="flex h-9 w-full rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-200 px-3 py-1 pl-9 pr-9 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-gray-400 dark:placeholder:text-gray-500 focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-blue-500 dark:focus-visible:ring-blue-400 focus-visible:border-blue-500 dark:focus-visible:border-blue-400 hover:border-gray-400 dark:hover:border-gray-500 disabled:cursor-not-allowed disabled:opacity-50 appearance-none select-none bg-none z-10"
                  >
                    <option value="">Select a severity level</option>
                    <option value="info">Info</option>
                    <option value="warning">Warning</option>
                    <option value="error">Error</option>
                  </select>
                  <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none z-20 select-none">
                    <i data-lucide="alert-triangle" class="h-4 w-4 text-gray-500 dark:text-gray-400 pointer-events-none"></i>
                  </div>
                  <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none z-20 select-none">
                    <i data-lucide="chevron-down" class="h-4 w-4 text-gray-500 dark:text-gray-400 pointer-events-none"></i>
                  </div>
                </div>
              </div>
            </div>

            <!-- Confirmation Option -->
            <div id="confirmation-option" class="space-y-2" style="display: none;">
              <div class="bg-destructive/15 p-4 rounded-md border border-destructive/20 shadow-sm mb-4">
                <div class="flex items-start">
                  <i data-lucide="alert-octagon" class="h-5 w-5 text-destructive mr-3 mt-0.5 flex-shrink-0"></i>
                  <div>
                    <h4 class="text-sm font-medium text-destructive mb-1">Critical Warning: Irreversible Action</h4>
                    <p class="text-sm text-gray-400">Deleting all logs will permanently remove <span class="font-semibold">all activity records</span> from the system. This action cannot be undone or recovered.</p>
                  </div>
                </div>
              </div>
              <label for="confirmation-input" class="text-sm font-medium flex items-center">
                <i data-lucide="shield-alert" class="h-4 w-4 text-destructive mr-2"></i>
                Type <span class="font-mono font-bold ml-1 bg-destructive/10 text-destructive px-1.5 py-0.5 rounded">DELETE_ALL_LOGS</span> to confirm
              </label>
              <div class="relative mt-1.5">
                <input type="text"
                       id="confirmation-input"
                       name="confirmation"
                       class="w-full rounded-md border border-destructive/30 bg-destructive/5 px-3 py-2 text-sm shadow-sm focus:outline-none focus:ring-1 focus:ring-destructive"
                       placeholder="DELETE_ALL_LOGS">
                <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                  <i data-lucide="lock" class="h-4 w-4 text-destructive/50"></i>
                </div>
              </div>
            </div>

            <!-- Warning Message -->
            <div class="bg-warning/15 p-4 rounded-md border border-gray-600 shadow-sm">
              <div class="flex items-start">
                <i data-lucide="alert-triangle" class="h-5 w-5 text-warning mr-3 mt-0.5 flex-shrink-0"></i>
                <div>
                  <h4 class="text-sm font-medium text-warning-foreground mb-1">Warning: Permanent Action</h4>
                  <p class="text-sm text-muted-foreground">This action will permanently delete activity logs based on your selection. Once deleted, these records cannot be recovered.</p>
                </div>
              </div>
            </div>
          </div>
        </form>
      </div>

      <!-- Progress Section -->
      <div id="log-management-progress" style="display: none;">
        <div class="flex flex-col items-center justify-center py-8">
          <div class="animate-spin rounded-full h-12 w-12 border-2 border-t-transparent border-primary mb-4"></div>
          <p class="text-lg font-medium">Processing...</p>
          <p class="text-sm text-muted-foreground mt-2">Please wait while we process your request.</p>
        </div>
      </div>

      <!-- Result Section -->
      <div id="log-management-result" style="display: none;">
        <div class="flex flex-col items-center justify-center py-4">
          <div class="rounded-full bg-primary/10 p-3 mb-4">
            <i data-lucide="check" class="h-8 w-8 text-primary"></i>
          </div>
          <p class="text-lg font-medium text-center" id="log-management-result-message">Logs deleted successfully</p>
          <p class="text-sm text-muted-foreground mt-2 text-center">
            <span id="log-management-result-count">0</span> logs were affected by this operation.
          </p>
        </div>
      </div>

    </div>

    <!-- Warning message above the footer -->
    <div class="px-6 py-3 bg-muted/20 border-t border-border">
      <div class="text-xs text-muted-foreground flex items-center">
        <i data-lucide="alert-circle" class="h-3.5 w-3.5 mr-1.5 text-amber-500 flex-shrink-0"></i>
        <span>Please review your selection carefully before proceeding</span>
      </div>
    </div>

    <!-- Footer -->
    <div class="border-t border-gray-200 dark:border-gray-700 px-6 py-4 flex justify-end items-center gap-3">
      <button
        type="button"
        class="inline-flex items-center justify-center rounded-md text-sm font-medium border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-700 transition-all duration-200 focus:outline-none disabled:pointer-events-none disabled:opacity-50 h-10 px-4 py-2 shadow-sm hover:shadow"
        data-modal-close
      >
        <i data-lucide="x" class="h-4 w-4 mr-2"></i>
        <span>Cancel</span>
      </button>
      <button
        type="submit"
        form="log-management-form-element"
        class="inline-flex items-center justify-center rounded-md text-sm font-medium bg-amber-600 hover:bg-amber-700 text-white transition-all duration-200 focus:outline-none disabled:pointer-events-none disabled:opacity-50 h-10 px-4 py-2 shadow-sm hover:shadow min-w-[100px]"
        id="log-management-submit-btn"
      >
        <i data-lucide="trash" class="h-4 w-4 mr-2"></i>
        <span>Delete</span>
      </button>
    </div>
  </div>
</template>

<!-- Empty div for modal initialization -->
<div id="log-management-modal"></div>
{% endmacro %}

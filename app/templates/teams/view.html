{% extends "base.html" %}

{% block title %}{{ team.name }} - Team Details{% endblock %}

{% block header %}{{ team.name }} - Team Details{% endblock %}

{% block content %}
<div class="space-y-6">
  <div class="flex justify-between items-center">
    <div class="flex items-center space-x-2">
      <a href="{{ url_for('teams.index') }}" class="btn btn-ghost btn-sm">
        <i data-lucide="arrow-left" class="w-4 h-4 mr-2"></i>
        Back to Teams
      </a>
      <h1 class="text-2xl font-bold">{{ team.name }}</h1>
      {% if team.short_name %}
      <span class="badge badge-outline">{{ team.short_name }}</span>
      {% endif %}
    </div>
    <div class="flex space-x-2">
      <button onclick="drawerManager.openForm('team', {{ team.id }})" class="btn btn-outline btn-md">
        <i data-lucide="edit" class="w-4 h-4 mr-2"></i>
        <span>Edit Team</span>
      </button>
      <a href="{{ url_for('teams.members', team_id=team.id) }}" class="btn btn-primary btn-md">
        <i data-lucide="users" class="w-4 h-4 mr-2"></i>
        <span>Manage Members</span>
      </a>
    </div>
  </div>

  <!-- Team Details Card -->
  <div class="card">
    <div class="p-6 border-b border-border">
      <div class="flex items-center justify-between">
        <h2 class="text-lg font-semibold">Team Information</h2>
        <div class="flex items-center space-x-2">
          {% if team.is_active %}
          <span class="badge badge-success">Active</span>
          {% else %}
          <span class="badge badge-secondary">Inactive</span>
          {% endif %}
        </div>
      </div>
    </div>

    <div class="p-6 grid grid-cols-1 md:grid-cols-2 gap-6">
      <div>
        <h3 class="text-md font-medium mb-4">Details</h3>
        <div class="space-y-4">
          <div>
            <label class="block text-sm font-medium text-muted-foreground mb-1">Name</label>
            <div class="p-2 bg-muted/30 rounded-md">{{ team.name }}</div>
          </div>
          <div>
            <label class="block text-sm font-medium text-muted-foreground mb-1">Short Name</label>
            <div class="p-2 bg-muted/30 rounded-md">{{ team.short_name or 'Not specified' }}</div>
          </div>
          <div>
            <label class="block text-sm font-medium text-muted-foreground mb-1">Slug</label>
            <div class="p-2 bg-muted/30 rounded-md">{{ team.slug }}</div>
          </div>
        </div>
      </div>

      <div>
        <h3 class="text-md font-medium mb-4">Statistics</h3>
        <div class="space-y-4">
          <div>
            <label class="block text-sm font-medium text-muted-foreground mb-1">Members</label>
            <div class="p-2 bg-muted/30 rounded-md">{{ team.members|length }} members</div>
          </div>
          <div>
            <label class="block text-sm font-medium text-muted-foreground mb-1">Groups</label>
            <div class="p-2 bg-muted/30 rounded-md">{{ team.groups.count() }} groups</div>
          </div>
          <div>
            <label class="block text-sm font-medium text-muted-foreground mb-1">Created</label>
            <div class="p-2 bg-muted/30 rounded-md">{{ team.created_at.strftime('%Y-%m-%d') }}</div>
          </div>
        </div>
      </div>

      {% if team.description %}
      <div class="md:col-span-2">
        <h3 class="text-md font-medium mb-4">Description</h3>
        <div class="p-4 bg-muted/30 rounded-md">
          {{ team.description }}
        </div>
      </div>
      {% endif %}
    </div>
  </div>

  <!-- Team Groups Card -->
  <div class="card">
    <div class="p-6 border-b border-border">
      <div class="flex items-center justify-between">
        <h2 class="text-lg font-semibold">Team Groups</h2>
        <button onclick="drawerManager.openForm('team_group', null, { queryParams: { team_id: {{ team.id }} } })" class="btn btn-primary btn-sm">
          <i data-lucide="plus" class="w-4 h-4 mr-2"></i>
          <span>Add Group</span>
        </button>
      </div>
    </div>

    <div class="overflow-x-auto">
      <table class="w-full">
        <thead>
          <tr class="border-b border-border bg-muted/40">
            <th class="px-4 py-3 text-left text-sm font-medium text-muted-foreground">Name</th>
            <th class="px-4 py-3 text-left text-sm font-medium text-muted-foreground">Description</th>
            <th class="px-4 py-3 text-left text-sm font-medium text-muted-foreground">Status</th>
            <th class="px-4 py-3 text-right text-sm font-medium text-muted-foreground">Actions</th>
          </tr>
        </thead>
        <tbody>
          {% for group in groups %}
          <tr class="border-b border-border hover:bg-muted/30">
            <td class="px-4 py-3 text-sm font-medium">{{ group.name }}</td>
            <td class="px-4 py-3 text-sm">{{ group.description|truncate(100) or '-' }}</td>
            <td class="px-4 py-3 text-sm">
              {% if group.is_active %}
              <span class="badge badge-success">Active</span>
              {% else %}
              <span class="badge badge-secondary">Inactive</span>
              {% endif %}
            </td>
            <td class="px-4 py-3 text-sm text-right">
              <div class="flex justify-end space-x-2">
                <button class="btn btn-sm btn-ghost" onclick="drawerManager.openForm('team_group', {{ group.id }})">
                  <i data-lucide="edit" class="w-4 h-4"></i>
                </button>
                <button class="btn btn-sm btn-ghost text-destructive" onclick="confirmDeleteGroup({{ group.id }}, '{{ group.name }}')">
                  <i data-lucide="trash-2" class="w-4 h-4"></i>
                </button>
              </div>
            </td>
          </tr>
          {% else %}
          <tr>
            <td colspan="4" class="px-4 py-8 text-center text-muted-foreground">
              <div class="flex flex-col items-center justify-center space-y-3">
                <i data-lucide="layers" class="w-12 h-12 text-muted-foreground/50"></i>
                <div>
                  <p class="text-lg font-medium">No groups found</p>
                  <p class="text-sm text-muted-foreground">Create a new group to organize team members</p>
                </div>
                <button onclick="drawerManager.openForm('team_group', null, { queryParams: { team_id: {{ team.id }} } })" class="btn btn-primary btn-sm mt-2">
                  <i data-lucide="plus" class="w-4 h-4 mr-2"></i>
                  <span>Add Group</span>
                </button>
              </div>
            </td>
          </tr>
          {% endfor %}
        </tbody>
      </table>
    </div>
  </div>

  <!-- Team Members Preview -->
  <div class="card">
    <div class="p-6 border-b border-border">
      <div class="flex items-center justify-between">
        <h2 class="text-lg font-semibold">Team Members</h2>
        <a href="{{ url_for('teams.members', team_id=team.id) }}" class="btn btn-outline btn-sm">
          <i data-lucide="users" class="w-4 h-4 mr-2"></i>
          <span>Manage Members</span>
        </a>
      </div>
    </div>

    <div class="p-6">
      {% if team.members %}
        <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
          {% for member in team.members[:8] %}
            <div class="flex items-center space-x-3 p-3 rounded-md border border-border">
              <div class="flex-shrink-0">
                <div class="w-10 h-10 rounded-full bg-primary/10 flex items-center justify-center">
                  <i data-lucide="user" class="w-5 h-5 text-primary"></i>
                </div>
              </div>
              <div class="min-w-0 flex-1">
                <p class="text-sm font-medium truncate">{{ member.user.name }}</p>
                <p class="text-xs text-muted-foreground truncate">{{ member.job_title or 'No Job Title' }}</p>
              </div>
            </div>
          {% endfor %}

          {% if team.members|length > 8 %}
            <a href="{{ url_for('teams.members', team_id=team.id) }}" class="flex items-center justify-center p-3 rounded-md border border-border border-dashed hover:bg-muted/30">
              <div class="text-center">
                <p class="text-sm font-medium">+{{ team.members|length - 8 }} more</p>
                <p class="text-xs text-muted-foreground">View all members</p>
              </div>
            </a>
          {% endif %}
        </div>
      {% else %}
        <div class="flex flex-col items-center justify-center py-8 space-y-3">
          <i data-lucide="users" class="w-12 h-12 text-muted-foreground/50"></i>
          <div class="text-center">
            <p class="text-lg font-medium">No team members</p>
            <p class="text-sm text-muted-foreground">Add members to this team</p>
          </div>
          <a href="{{ url_for('teams.members', team_id=team.id) }}" class="btn btn-primary btn-sm mt-2">
            <i data-lucide="user-plus" class="w-4 h-4 mr-2"></i>
            <span>Add Members</span>
          </a>
        </div>
      {% endif %}
    </div>
  </div>
</div>

{% endblock %}

{% block scripts %}
<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Process icons if needed
    if (window.processIcons) {
      window.processIcons();
    }

    // Check URL parameters for success messages
    // We're now using flash messages converted to toasts via handleFlashMessages() in utils.js
    // This happens automatically on page load and provides a consistent notification experience

    // Clean URL parameters if they exist (for backward compatibility)
    const urlParams = new URLSearchParams(window.location.search);
    if (urlParams.has('status')) {
      const newUrl = window.location.pathname;
      window.history.replaceState({}, document.title, newUrl);
    }
  });

  function confirmDeleteGroup(groupId, groupName) {
    showAlertDialog({
      title: 'Delete Group',
      description: `Are you sure you want to delete the group "${groupName}"? This action cannot be undone.`,
      variant: 'destructive',
      onConfirm: () => {
        // Submit form to delete endpoint
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = "{{ url_for('teams.delete_group', group_id=0) }}".replace('0', groupId);

        const csrfToken = document.createElement('input');
        csrfToken.type = 'hidden';
        csrfToken.name = 'csrf_token';
        csrfToken.value = '{{ csrf_token() }}';

        form.appendChild(csrfToken);
        document.body.appendChild(form);
        form.submit();
      }
    });
  }
</script>
{% endblock %}

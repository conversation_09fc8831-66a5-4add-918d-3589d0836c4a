"""
Admin routes package initialization.
This file creates the admin Blueprint and imports all admin routes.
"""

from flask import Blueprint

# Create the admin Blueprint
admin_bp = Blueprint('admin', __name__, url_prefix='/admin')

# Import all admin routes
from app.routes.admin.dashboard import *
from app.routes.admin.users import *
from app.routes.admin.employee_details import *
from app.routes.admin.business import *
from app.routes.admin.settings import *
from app.routes.admin.analytics import *
from app.routes.admin.activities import *
from app.routes.admin.testing import *
from app.routes.admin.system import *
from app.routes.admin.holiday_admin import *

# Define __all__ to explicitly specify what is exported
__all__ = ['admin_bp']

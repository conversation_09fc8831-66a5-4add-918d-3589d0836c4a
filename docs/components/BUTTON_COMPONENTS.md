# Button Components Documentation

## Overview

The Matrix App uses a comprehensive button component system that ensures consistency across all pages. These components use the exact same Tailwind classes as the activities page for perfect visual consistency.

## Components

### 1. `button()` - Main Button Component

The primary button component that handles both `<button>` and `<a>` tags automatically.

#### Parameters

| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `text` | string | required | Button text content |
| `variant` | string | `"primary"` | Button style variant |
| `size` | string | `"md"` | Button size |
| `icon` | string | `None` | Lucide icon name |
| `icon_position` | string | `"left"` | Icon position (`"left"` or `"right"`) |
| `type` | string | `"button"` | Button type (for `<button>` tags) |
| `onclick` | string | `None` | JavaScript onclick handler |
| `href` | string | `None` | URL for links (creates `<a>` tag) |
| `disabled` | boolean | `false` | Whether button is disabled |
| `loading` | boolean | `false` | Whether button shows loading state |
| `class_extra` | string | `""` | Additional CSS classes |
| `title` | string | `None` | Tooltip text |
| `id` | string | `None` | Element ID |

#### Variants

- **`primary`**: Blue background, white text (for main actions)
- **`secondary`**: Gray background (for secondary actions)
- **`outline`**: Border with transparent background (most common)
- **`ghost`**: No background, hover effects only
- **`destructive`**: Red background for dangerous actions

#### Sizes

- **`sm`**: Small buttons (`h-8 px-3`)
- **`md`**: Medium buttons (`h-9 px-4 py-2`) - **Default**
- **`lg`**: Large buttons (`h-10 px-6 py-2`)

#### Usage Examples

```html
<!-- Import the component -->
{% from "components/button.html" import button %}

<!-- Primary button -->
{{ button("Save", variant="primary", icon="save") }}

<!-- Link button -->
{{ button("View Details", variant="outline", href="/details", icon="external-link") }}

<!-- Submit button -->
{{ button("Apply Filters", variant="primary", type="submit", icon="filter") }}

<!-- Button with onclick -->
{{ button("Delete", variant="destructive", icon="trash-2", onclick="confirmDelete()") }}

<!-- Loading state -->
{{ button("Processing...", variant="primary", loading=true) }}

<!-- Icon on right -->
{{ button("Next", variant="outline", icon="chevron-right", icon_position="right") }}
```

### 2. `button_group()` - Button Group Component

Groups multiple buttons with consistent spacing.

#### Parameters

| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `buttons` | list | required | List of button configurations |
| `gap` | string | `"2"` | Gap between buttons |

#### Usage Examples

```html
<!-- Import the component -->
{% from "components/button.html" import button_group %}

<!-- Filter buttons -->
{% set filter_buttons = [
  {"text": "Reset", "variant": "outline", "href": url_for('admin.holidays'), "icon": "rotate-ccw"},
  {"text": "Apply Filters", "variant": "primary", "type": "submit", "icon": "filter"}
] %}
{{ button_group(filter_buttons) }}

<!-- Quick action buttons -->
{% set quick_actions = [
  {"text": "Calendar View", "variant": "outline", "href": "/calendar", "icon": "calendar"},
  {"text": "Export", "variant": "outline", "onclick": "exportData()", "icon": "download"}
] %}
{{ button_group(quick_actions, gap="3") }}
```

### 3. `icon_button()` - Icon-Only Button Component

For buttons that only display an icon (commonly used in tables).

#### Parameters

| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `icon` | string | required | Lucide icon name |
| `variant` | string | `"ghost"` | Button style variant |
| `size` | string | `"md"` | Button size (uses icon-specific sizing) |
| `onclick` | string | `None` | JavaScript onclick handler |
| `href` | string | `None` | URL for links |
| `disabled` | boolean | `false` | Whether button is disabled |
| `title` | string | `None` | Tooltip text (recommended for accessibility) |
| `id` | string | `None` | Element ID |
| `class_extra` | string | `""` | Additional CSS classes |

#### Icon Sizes

- **`sm`**: `h-8 w-8`
- **`md`**: `h-9 w-9` - **Default**
- **`lg`**: `h-10 w-10`

#### Usage Examples

```html
<!-- Import the component -->
{% from "components/button.html" import icon_button %}

<!-- Table action buttons -->
{{ icon_button("edit", title="Edit item", onclick="editItem(1)") }}
{{ icon_button("trash-2", title="Delete item", onclick="deleteItem(1)", class_extra="text-destructive") }}

<!-- Navigation buttons -->
{{ icon_button("external-link", href="/details", title="View details") }}
{{ icon_button("refresh-cw", onclick="refreshData()", title="Refresh") }}
```

## Migration Guide

### Before (Old Pattern)

```html
<!-- Old manual Tailwind classes -->
<button type="submit" class="inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground shadow hover:bg-primary/90 h-9 px-4 py-2">
  <i data-lucide="filter" class="h-4 w-4 mr-2"></i>
  Apply Filters
</button>

<!-- Old simplified btn classes -->
<button class="btn btn-primary btn-lg">Save</button>
```

### After (New Components)

```html
<!-- New button component -->
{{ button("Apply Filters", variant="primary", type="submit", icon="filter") }}

<!-- Button group for multiple buttons -->
{% set actions = [
  {"text": "Reset", "variant": "outline", "href": "/reset"},
  {"text": "Apply Filters", "variant": "primary", "type": "submit", "icon": "filter"}
] %}
{{ button_group(actions) }}
```

## Best Practices

### 1. **Use Appropriate Variants**
- `primary`: Main actions (Save, Submit, Apply Filters)
- `outline`: Secondary actions (Reset, Cancel, Export, Navigation)
- `ghost`: Table actions, minimal UI elements
- `destructive`: Delete, Remove, Dangerous actions

### 2. **Consistent Sizing**
- Use `md` (default) for most buttons
- Use `sm` for table actions and compact UIs
- Use `lg` sparingly for prominent actions

### 3. **Icon Usage**
- Always include meaningful icons for better UX
- Use `title` attribute for icon-only buttons (accessibility)
- Position icons logically (`left` for most, `right` for "next" actions)

### 4. **Button Groups**
- Use `button_group()` for related actions
- Maintain consistent gap spacing (`gap="2"` is standard)

### 5. **Accessibility**
- Always provide `title` for icon-only buttons
- Use semantic button types (`submit`, `button`, `reset`)
- Ensure proper contrast and focus states

## Examples in Practice

### Filter Section
```html
{% set filter_buttons = [
  {"text": "Reset", "variant": "outline", "href": url_for('admin.holidays'), "icon": "rotate-ccw"},
  {"text": "Apply Filters", "variant": "primary", "type": "submit", "icon": "filter"}
] %}
{{ button_group(filter_buttons) }}
```

### Table Actions
```html
<div class="flex justify-end space-x-2">
  {{ icon_button("edit", onclick="editItem(" ~ item.id ~ ")", title="Edit") }}
  {{ icon_button("trash-2", onclick="deleteItem(" ~ item.id ~ ")", title="Delete", class_extra="text-destructive") }}
</div>
```

### Navigation
```html
{% set nav_buttons = [
  {"text": "Previous", "variant": "outline", "onclick": "navigate('prev')", "icon": "chevron-left"},
  {"text": "Next", "variant": "outline", "onclick": "navigate('next')", "icon": "chevron-right", "icon_position": "right"}
] %}
{{ button_group(nav_buttons) }}
```

This component system ensures perfect consistency across all pages while being flexible and easy to use!

/**
 * SVG Sprite Implementation
 * Efficient icon system using SVG sprite without Lucide dependency
 */

// Process all icons in the document
function processIcons() {
  document.querySelectorAll('i[data-lucide]').forEach(function(element) {
    if (element.dataset.processed) return;
    element.dataset.processed = true;

    const name = element.getAttribute('data-lucide');
    const className = element.className || '';

    // Create SVG element
    const svg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
    svg.setAttribute('class', className + ' icon-' + name);
    svg.setAttribute('width', '24');
    svg.setAttribute('height', '24');
    svg.setAttribute('fill', 'none');
    svg.setAttribute('stroke', 'currentColor');
    svg.setAttribute('stroke-width', '2');
    svg.setAttribute('stroke-linecap', 'round');
    svg.setAttribute('stroke-linejoin', 'round');

    // Create use element to reference sprite
    const use = document.createElementNS('http://www.w3.org/2000/svg', 'use');
    use.setAttributeNS('http://www.w3.org/1999/xlink', 'xlink:href', '#' + name);
    svg.appendChild(use);

    // Replace the original element
    if (element.parentNode) {
      element.parentNode.replaceChild(svg, element);
    }
  });
}

// Initialize the SVG sprite system
function initSvgSprite() {
  // Load the sprite
  fetch('/static/js/icons/sprite.svg')
    .then(response => response.text())
    .then(data => {
      // Create a container for the sprite
      const div = document.createElement('div');
      div.style.display = 'none';
      div.innerHTML = data;
      document.body.appendChild(div);

      // Process icons
      processIcons();

      // Set up observer for dynamically added icons
      const observer = new MutationObserver(function(mutations) {
        let shouldProcess = false;

        mutations.forEach(function(mutation) {
          if (mutation.type === 'childList') {
            mutation.addedNodes.forEach(function(node) {
              if (node.nodeType === 1) {
                if (node.hasAttribute && node.hasAttribute('data-lucide') ||
                    node.querySelectorAll && node.querySelectorAll('[data-lucide]').length > 0) {
                  shouldProcess = true;
                }
              }
            });
          }
        });

        if (shouldProcess) {
          processIcons();
        }
      });

      // Start observing
      observer.observe(document.body, {
        childList: true,
        subtree: true
      });

      // Expose function globally
      window.processIcons = processIcons;
    })
    .catch(error => {
      console.error('Failed to load icon sprite:', error);
    });
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', initSvgSprite);

/**
 * Asset Loader Utility
 * 
 * This module provides utilities for efficient asset loading and management.
 */

import { loadScript, preloadScript } from './script-loader.js';

// Track loaded assets to prevent duplicate loading
const loadedAssets = new Set();

/**
 * Load a CSS stylesheet asynchronously
 * 
 * @param {string} href - The stylesheet URL to load
 * @param {Object} options - Options for stylesheet loading
 * @param {string} options.media - Media attribute (default: 'all')
 * @param {string} options.id - ID attribute for the link element
 * @param {Function} options.onLoad - Callback function when stylesheet loads
 * @param {Function} options.onError - Callback function when stylesheet fails to load
 * @returns {Promise} - Promise that resolves when the stylesheet is loaded
 */
function loadStylesheet(href, options = {}) {
  // Default options
  const {
    media = 'all',
    id = null,
    onLoad = null,
    onError = null
  } = options;

  // Return existing promise if stylesheet is already loading
  if (loadedAssets.has(href)) {
    return Promise.resolve();
  }

  // Add to loaded assets set
  loadedAssets.add(href);

  return new Promise((resolve, reject) => {
    const link = document.createElement('link');
    link.rel = 'stylesheet';
    link.href = href;
    link.media = media;
    
    // Add ID if provided
    if (id) {
      link.id = id;
    }

    // Add to document head
    document.head.appendChild(link);

    // Handle load event
    link.onload = () => {
      if (onLoad) onLoad();
      resolve();
    };

    // Handle error event
    link.onerror = (error) => {
      // Remove from loaded assets set on error
      loadedAssets.delete(href);
      if (onError) onError(error);
      reject(new Error(`Failed to load stylesheet: ${href}`));
    };
  });
}

/**
 * Preload an image for future use
 * 
 * @param {string} src - The image URL to preload
 * @returns {Promise} - Promise that resolves when the image is loaded
 */
function preloadImage(src) {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.src = src;
    img.onload = resolve;
    img.onerror = reject;
  });
}

/**
 * Preload multiple images in parallel
 * 
 * @param {Array<string>} sources - Array of image URLs to preload
 * @returns {Promise} - Promise that resolves when all images are loaded
 */
function preloadImages(sources) {
  return Promise.all(sources.map(preloadImage));
}

/**
 * Load a font asynchronously using FontFace API
 * 
 * @param {string} family - Font family name
 * @param {string} source - Font source URL or data URI
 * @param {Object} descriptors - Font descriptors
 * @returns {Promise} - Promise that resolves when the font is loaded
 */
function loadFont(family, source, descriptors = {}) {
  // Check if FontFace API is supported
  if (!('FontFace' in window)) {
    return Promise.reject(new Error('FontFace API not supported'));
  }

  return new Promise((resolve, reject) => {
    try {
      // Create a new FontFace object
      const font = new FontFace(family, source, descriptors);
      
      // Add the font to the document fonts
      font.load().then(() => {
        document.fonts.add(font);
        resolve(font);
      }).catch(reject);
    } catch (error) {
      reject(error);
    }
  });
}

/**
 * Optimize asset loading based on connection speed
 * 
 * @param {Object} assets - Object containing assets to load based on connection speed
 * @param {Array} assets.slow - Assets to load on slow connections
 * @param {Array} assets.medium - Assets to load on medium connections
 * @param {Array} assets.fast - Assets to load on fast connections
 * @returns {Promise} - Promise that resolves when appropriate assets are loaded
 */
function loadOptimizedAssets(assets) {
  // Determine connection speed
  const connection = navigator.connection || navigator.mozConnection || navigator.webkitConnection;
  let speed = 'medium';
  
  if (connection) {
    // Use Network Information API if available
    if (connection.effectiveType === '4g') {
      speed = 'fast';
    } else if (connection.effectiveType === '2g') {
      speed = 'slow';
    }
  }
  
  // Load assets based on connection speed
  let assetsToLoad = [];
  
  if (speed === 'slow') {
    assetsToLoad = assets.slow || [];
  } else if (speed === 'medium') {
    assetsToLoad = assets.medium || [];
  } else {
    assetsToLoad = assets.fast || [];
  }
  
  // Load each asset based on its type
  const promises = assetsToLoad.map(asset => {
    if (asset.type === 'script') {
      return loadScript(asset.src, asset.options);
    } else if (asset.type === 'stylesheet') {
      return loadStylesheet(asset.href, asset.options);
    } else if (asset.type === 'image') {
      return preloadImage(asset.src);
    } else if (asset.type === 'font') {
      return loadFont(asset.family, asset.source, asset.descriptors);
    }
    return Promise.resolve();
  });
  
  return Promise.all(promises);
}

// Export public API
export {
  loadStylesheet,
  preloadImage,
  preloadImages,
  loadFont,
  loadOptimizedAssets
};

"""
API routes for holidays.
"""

from flask import jsonify, request, current_app
from flask_login import login_required
from datetime import datetime, date
from sqlalchemy import or_

from app import db
from app.models.attendance import Holiday
from app.services.holiday_service import HolidayService
from app.routes.api import api_bp
from app.routes.api.utils import handle_api_error


@api_bp.route('/holidays', methods=['GET'])
@login_required
@handle_api_error
def get_holidays():
    """Get holidays with optional filtering."""
    # Get query parameters
    region_code = request.args.get('region')
    start_date_str = request.args.get('start_date')
    end_date_str = request.args.get('end_date')
    search = request.args.get('search', '').strip()
    page = request.args.get('page', 1, type=int)
    per_page = min(request.args.get('per_page', 20, type=int), 100)  # Max 100 per page

    # Build base query
    query = Holiday.query

    # Apply filters
    if region_code:
        if region_code.upper() == 'ALL':
            # Don't filter by region
            pass
        else:
            query = query.filter(
                or_(Holiday.region_code == region_code.upper(), Holiday.region_code == 'GLOBAL')
            )

    # Date range filtering
    if start_date_str:
        try:
            start_date = datetime.strptime(start_date_str, '%Y-%m-%d').date()
            query = query.filter(Holiday.date >= start_date)
        except ValueError:
            return jsonify({
                'success': False,
                'error': 'Invalid start_date format. Use YYYY-MM-DD.'
            }), 400

    if end_date_str:
        try:
            end_date = datetime.strptime(end_date_str, '%Y-%m-%d').date()
            query = query.filter(Holiday.date <= end_date)
        except ValueError:
            return jsonify({
                'success': False,
                'error': 'Invalid end_date format. Use YYYY-MM-DD.'
            }), 400

    # Search filtering
    if search:
        query = query.filter(
            or_(
                Holiday.name.ilike(f'%{search}%'),
                Holiday.description.ilike(f'%{search}%')
            )
        )

    # Order by date
    query = query.order_by(Holiday.date.asc())

    # Paginate
    pagination = query.paginate(
        page=page,
        per_page=per_page,
        error_out=False
    )

    holidays = pagination.items

    return jsonify({
        'success': True,
        'holidays': [holiday.to_dict() for holiday in holidays],
        'pagination': {
            'page': pagination.page,
            'pages': pagination.pages,
            'per_page': pagination.per_page,
            'total': pagination.total,
            'has_next': pagination.has_next,
            'has_prev': pagination.has_prev
        },
        'filters': {
            'region_code': region_code,
            'start_date': start_date_str,
            'end_date': end_date_str,
            'search': search
        }
    })


@api_bp.route('/holidays/check/<date_str>/<region_code>', methods=['GET'])
@login_required
@handle_api_error
def check_holiday(date_str, region_code):
    """Check if a specific date is a holiday for the given region."""
    try:
        check_date = datetime.strptime(date_str, '%Y-%m-%d').date()
    except ValueError:
        return jsonify({
            'success': False,
            'error': 'Invalid date format. Use YYYY-MM-DD.'
        }), 400

    # Validate region code
    valid_regions = ['US', 'PH', 'GLOBAL']
    if region_code.upper() not in valid_regions:
        return jsonify({
            'success': False,
            'error': f'Invalid region code. Must be one of: {", ".join(valid_regions)}'
        }), 400

    is_holiday = HolidayService.is_holiday(check_date, region_code.upper())

    # Get holiday details if it exists
    holiday_details = None
    if is_holiday:
        holiday = Holiday.query.filter(
            Holiday.date == check_date,
            or_(Holiday.region_code == region_code.upper(), Holiday.region_code == 'GLOBAL')
        ).first()
        if holiday:
            holiday_details = holiday.to_dict()

    return jsonify({
        'success': True,
        'date': date_str,
        'region_code': region_code.upper(),
        'is_holiday': is_holiday,
        'holiday': holiday_details
    })


@api_bp.route('/holidays/upcoming/<region_code>', methods=['GET'])
@login_required
@handle_api_error
def get_upcoming_holidays_api(region_code):
    """Get upcoming holidays for a region."""
    days = request.args.get('days', 30, type=int)

    # Validate region code
    valid_regions = ['US', 'PH', 'GLOBAL']
    if region_code.upper() not in valid_regions:
        return jsonify({
            'success': False,
            'error': f'Invalid region code. Must be one of: {", ".join(valid_regions)}'
        }), 400

    # Validate days parameter
    if days < 1 or days > 365:
        return jsonify({
            'success': False,
            'error': 'Days parameter must be between 1 and 365.'
        }), 400

    try:
        holidays = HolidayService.get_upcoming_holidays(region_code.upper(), days)

        return jsonify({
            'success': True,
            'region_code': region_code.upper(),
            'days': days,
            'count': len(holidays),
            'holidays': [holiday.to_dict() for holiday in holidays]
        })
    except Exception as e:
        current_app.logger.error(f'Error getting upcoming holidays: {str(e)}')
        return jsonify({
            'success': False,
            'error': 'Failed to get upcoming holidays'
        }), 500


@api_bp.route('/holidays/calendar/<region_code>/<int:year>', methods=['GET'])
@login_required
@handle_api_error
def get_holiday_calendar(region_code, year):
    """Get holiday calendar data for a specific region and year."""
    month = request.args.get('month', type=int)

    # Validate region code
    valid_regions = ['US', 'PH', 'GLOBAL']
    if region_code.upper() not in valid_regions:
        return jsonify({
            'success': False,
            'error': f'Invalid region code. Must be one of: {", ".join(valid_regions)}'
        }), 400

    # Validate year
    current_year = date.today().year
    if year < current_year - 10 or year > current_year + 10:
        return jsonify({
            'success': False,
            'error': 'Year must be within 10 years of current year.'
        }), 400

    # Validate month if provided
    if month is not None and (month < 1 or month > 12):
        return jsonify({
            'success': False,
            'error': 'Month must be between 1 and 12.'
        }), 400

    try:
        calendar_data = HolidayService.get_holiday_calendar_data(region_code.upper(), year, month)

        return jsonify({
            'success': True,
            'calendar_data': calendar_data
        })
    except Exception as e:
        current_app.logger.error(f'Error getting holiday calendar: {str(e)}')
        return jsonify({
            'success': False,
            'error': 'Failed to get holiday calendar data'
        }), 500


@api_bp.route('/holidays/regions', methods=['GET'])
@login_required
@handle_api_error
def get_holiday_regions():
    """Get list of available holiday regions."""
    try:
        # Get distinct regions from database
        regions_query = db.session.query(Holiday.region_code).distinct().all()
        db_regions = [region[0] for region in regions_query if region[0]]

        # Combine with standard regions
        all_regions = list(set(['US', 'PH', 'GLOBAL'] + db_regions))

        # Create region info
        region_info = []
        for region in sorted(all_regions):
            region_data = {
                'code': region,
                'name': {
                    'US': 'United States',
                    'PH': 'Philippines',
                    'GLOBAL': 'Global'
                }.get(region, region)
            }

            # Get holiday count for this region
            holiday_count = Holiday.query.filter(Holiday.region_code == region).count()
            region_data['holiday_count'] = holiday_count

            region_info.append(region_data)

        return jsonify({
            'success': True,
            'regions': region_info
        })
    except Exception as e:
        current_app.logger.error(f'Error getting holiday regions: {str(e)}')
        return jsonify({
            'success': False,
            'error': 'Failed to get holiday regions'
        }), 500


@api_bp.route('/holidays/check', methods=['GET'])
@login_required
@handle_api_error
def check_holiday_simple():
    """Simple holiday check endpoint for form validation."""
    from flask_login import current_user

    date_str = request.args.get('date')
    if not date_str:
        return jsonify({
            'success': False,
            'error': 'Date parameter is required.'
        }), 400

    try:
        check_date = datetime.strptime(date_str, '%Y-%m-%d').date()
    except ValueError:
        return jsonify({
            'success': False,
            'error': 'Invalid date format. Use YYYY-MM-DD.'
        }), 400

    try:
        # Get employee region for current user
        if current_user.employee_detail:
            region_code = HolidayService.get_employee_region(current_user.employee_detail.id)
        else:
            region_code = 'PH'  # Default fallback

        is_holiday = HolidayService.is_holiday(check_date, region_code)

        # Get holiday details if it exists
        holiday_details = None
        if is_holiday:
            holiday = Holiday.query.filter(
                Holiday.date == check_date,
                or_(Holiday.region_code == region_code, Holiday.region_code == 'GLOBAL')
            ).first()
            if holiday:
                holiday_details = {
                    'name': holiday.name,
                    'description': holiday.description,
                    'region_code': holiday.region_code
                }

        return jsonify({
            'success': True,
            'date': date_str,
            'region_code': region_code,
            'is_holiday': is_holiday,
            'holiday_name': holiday_details['name'] if holiday_details else None,
            'holiday_description': holiday_details['description'] if holiday_details else None
        })
    except Exception as e:
        current_app.logger.error(f'Error checking holiday: {str(e)}')
        return jsonify({
            'success': False,
            'error': 'Failed to check holiday'
        }), 500


@api_bp.route('/holidays/conflicts/<int:employee_id>/<date_str>', methods=['GET'])
@login_required
@handle_api_error
def check_holiday_conflicts(employee_id, date_str):
    """Check if an employee has attendance conflicts with holidays."""
    try:
        check_date = datetime.strptime(date_str, '%Y-%m-%d').date()
    except ValueError:
        return jsonify({
            'success': False,
            'error': 'Invalid date format. Use YYYY-MM-DD.'
        }), 400

    try:
        conflict_info = HolidayService.check_holiday_conflicts(employee_id, check_date)

        return jsonify({
            'success': True,
            'employee_id': employee_id,
            'date': date_str,
            'conflict_info': conflict_info
        })
    except Exception as e:
        current_app.logger.error(f'Error checking holiday conflicts: {str(e)}')
        return jsonify({
            'success': False,
            'error': 'Failed to check holiday conflicts'
        }), 500

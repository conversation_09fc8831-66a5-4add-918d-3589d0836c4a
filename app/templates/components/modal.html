{% macro modal(id, title, description='', size='md', variant='default', show_footer=true, show_action=true, action_text='Action', cancel_text='Cancel', action_icon='', cancel_icon='', is_loading=false, show_divider=true) %}
<template id="{{ id }}-template" data-size="{{ size }}">
  <div class="flex flex-col">
    <!-- Header -->
    <div class="flex items-start justify-between px-6 pt-6 pb-4">
      <div class="flex flex-col space-y-1 text-left pr-6">
        <h2 id="{{ id }}-title" class="text-lg font-semibold leading-tight tracking-tight text-gray-900 dark:text-white">{{ title }}</h2>
        {% if description %}
        <p id="{{ id }}-description" class="mt-1 text-sm text-gray-500 dark:text-gray-400">{{ description }}</p>
        {% endif %}
      </div>
      <button
        type="button"
        class="absolute right-4 top-4 rounded-full opacity-70 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 h-8 w-8 inline-flex items-center justify-center transition-all duration-200 hover:opacity-100 disabled:pointer-events-none"
        data-modal-close
        aria-label="Close"
      >
        <i data-lucide="x" class="h-4 w-4 pointer-events-none"></i>
        <span class="sr-only">Close</span>
      </button>
    </div>

    <!-- Content -->
    <div class="px-6 pb-6 pt-2 overflow-y-auto {% if not show_footer %}mb-2{% endif %}">
      {{ caller() }}
    </div>

    <!-- Footer -->
    {% if show_footer %}
    <div class="{% if show_divider %}border-t border-gray-200 dark:border-gray-700{% endif %} px-6 py-4 flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-3 gap-3 sm:gap-0">
      <button
        type="button"
        class="inline-flex items-center justify-center rounded-md text-sm font-medium border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-700 transition-all duration-200 focus:outline-none disabled:pointer-events-none disabled:opacity-50 h-10 px-4 py-2 shadow-sm hover:shadow"
        data-modal-close
      >
        {% if cancel_icon %}
        <i data-lucide="{{ cancel_icon }}" class="h-4 w-4 mr-2"></i>
        {% endif %}
        <span>{{ cancel_text }}</span>
      </button>

      {% if show_action %}
      <button
        type="button"
        class="inline-flex items-center justify-center rounded-md text-sm font-medium transition-all duration-200 focus:outline-none disabled:pointer-events-none disabled:opacity-50 {% if variant == 'destructive' %}bg-red-600 hover:bg-red-700 dark:bg-red-600 dark:hover:bg-red-700 text-white{% else %}bg-blue-600 hover:bg-blue-700 dark:bg-blue-600 dark:hover:bg-blue-500 text-white{% endif %} h-10 px-4 py-2 shadow-sm hover:shadow"
        id="{{ id }}-action"
        {% if is_loading %}disabled{% endif %}
      >
        {% if is_loading %}
        <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
          <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
          <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
        <span>Processing...</span>
        {% else %}
        {% if action_icon %}
        <i data-lucide="{{ action_icon }}" class="h-4 w-4 mr-2"></i>
        {% endif %}
        <span>{{ action_text }}</span>
        {% endif %}
      </button>
      {% endif %}
    </div>
    {% endif %}
  </div>
</template>
{% endmacro %}

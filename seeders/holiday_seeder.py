"""
Holiday seeder for populating the database with common holidays.
"""

from datetime import date
from app import db
from app.models.attendance import Holiday
from .db_utils import get_or_create


def seed_holidays():
    """Seeds the database with common holidays for different regions."""
    
    # Get current year for seeding
    current_year = date.today().year
    
    # US Holidays
    us_holidays = [
        {
            'name': "New Year's Day",
            'date': date(current_year, 1, 1),
            'region_code': 'US',
            'description': 'Federal holiday celebrating the beginning of the new year'
        },
        {
            'name': "<PERSON> Jr. Day",
            'date': date(current_year, 1, 15),  # Third Monday in January (approximation)
            'region_code': 'US',
            'description': 'Federal holiday honoring civil rights leader <PERSON>.'
        },
        {
            'name': "Presidents' Day",
            'date': date(current_year, 2, 19),  # Third Monday in February (approximation)
            'region_code': 'US',
            'description': 'Federal holiday honoring all U.S. presidents'
        },
        {
            'name': "Memorial Day",
            'date': date(current_year, 5, 27),  # Last Monday in May (approximation)
            'region_code': 'US',
            'description': 'Federal holiday honoring military personnel who died in service'
        },
        {
            'name': "Independence Day",
            'date': date(current_year, 7, 4),
            'region_code': 'US',
            'description': 'Federal holiday celebrating American independence'
        },
        {
            'name': "Labor Day",
            'date': date(current_year, 9, 2),  # First Monday in September (approximation)
            'region_code': 'US',
            'description': 'Federal holiday celebrating the American labor movement'
        },
        {
            'name': "Columbus Day",
            'date': date(current_year, 10, 14),  # Second Monday in October (approximation)
            'region_code': 'US',
            'description': 'Federal holiday commemorating Christopher Columbus'
        },
        {
            'name': "Veterans Day",
            'date': date(current_year, 11, 11),
            'region_code': 'US',
            'description': 'Federal holiday honoring military veterans'
        },
        {
            'name': "Thanksgiving Day",
            'date': date(current_year, 11, 28),  # Fourth Thursday in November (approximation)
            'region_code': 'US',
            'description': 'Federal holiday for giving thanks'
        },
        {
            'name': "Christmas Day",
            'date': date(current_year, 12, 25),
            'region_code': 'US',
            'description': 'Federal holiday celebrating the birth of Jesus Christ'
        }
    ]
    
    # Philippines Holidays
    ph_holidays = [
        {
            'name': "New Year's Day",
            'date': date(current_year, 1, 1),
            'region_code': 'PH',
            'description': 'Regular holiday celebrating the beginning of the new year'
        },
        {
            'name': "People Power Anniversary",
            'date': date(current_year, 2, 25),
            'region_code': 'PH',
            'description': 'Special non-working holiday commemorating the EDSA Revolution'
        },
        {
            'name': "Maundy Thursday",
            'date': date(current_year, 3, 28),  # Varies each year (approximation)
            'region_code': 'PH',
            'description': 'Regular holiday during Holy Week'
        },
        {
            'name': "Good Friday",
            'date': date(current_year, 3, 29),  # Varies each year (approximation)
            'region_code': 'PH',
            'description': 'Regular holiday during Holy Week'
        },
        {
            'name': "Araw ng Kagitingan (Day of Valor)",
            'date': date(current_year, 4, 9),
            'region_code': 'PH',
            'description': 'Regular holiday commemorating the fall of Bataan'
        },
        {
            'name': "Labor Day",
            'date': date(current_year, 5, 1),
            'region_code': 'PH',
            'description': 'Regular holiday celebrating workers'
        },
        {
            'name': "Independence Day",
            'date': date(current_year, 6, 12),
            'region_code': 'PH',
            'description': 'Regular holiday celebrating Philippine independence'
        },
        {
            'name': "Ninoy Aquino Day",
            'date': date(current_year, 8, 21),
            'region_code': 'PH',
            'description': 'Special non-working holiday honoring Benigno Aquino Jr.'
        },
        {
            'name': "National Heroes Day",
            'date': date(current_year, 8, 26),  # Last Monday in August (approximation)
            'region_code': 'PH',
            'description': 'Regular holiday honoring Filipino heroes'
        },
        {
            'name': "All Saints\' Day",
            'date': date(current_year, 11, 1),
            'region_code': 'PH',
            'description': 'Special non-working holiday'
        },
        {
            'name': "Bonifacio Day",
            'date': date(current_year, 11, 30),
            'region_code': 'PH',
            'description': 'Regular holiday honoring Andres Bonifacio'
        },
        {
            'name': "Rizal Day",
            'date': date(current_year, 12, 30),
            'region_code': 'PH',
            'description': 'Regular holiday honoring Jose Rizal'
        },
        {
            'name': "Christmas Day",
            'date': date(current_year, 12, 25),
            'region_code': 'PH',
            'description': 'Regular holiday celebrating the birth of Jesus Christ'
        }
    ]
    
    # Global Holidays (observed worldwide)
    global_holidays = [
        {
            'name': "New Year's Day",
            'date': date(current_year, 1, 1),
            'region_code': 'GLOBAL',
            'description': 'Worldwide celebration of the new year'
        },
        {
            'name': "Christmas Day",
            'date': date(current_year, 12, 25),
            'region_code': 'GLOBAL',
            'description': 'Worldwide celebration of the birth of Jesus Christ'
        },
        {
            'name': "Christmas Eve",
            'date': date(current_year, 12, 24),
            'region_code': 'GLOBAL',
            'description': 'Day before Christmas, often observed as a holiday'
        },
        {
            'name': "New Year's Eve",
            'date': date(current_year, 12, 31),
            'region_code': 'GLOBAL',
            'description': 'Last day of the year, often observed as a holiday'
        }
    ]
    
    # Combine all holidays
    all_holidays = us_holidays + ph_holidays + global_holidays
    
    created_count = 0
    updated_count = 0
    skipped_count = 0
    
    for holiday_data in all_holidays:
        try:
            # Check if holiday already exists for this date and region
            existing_holiday = Holiday.query.filter(
                Holiday.date == holiday_data['date'],
                Holiday.region_code == holiday_data['region_code']
            ).first()
            
            if existing_holiday:
                # Update existing holiday if data is different
                updated = False
                if existing_holiday.name != holiday_data['name']:
                    existing_holiday.name = holiday_data['name']
                    updated = True
                if existing_holiday.description != holiday_data['description']:
                    existing_holiday.description = holiday_data['description']
                    updated = True
                
                if updated:
                    updated_count += 1
                    print(f"Updated holiday: {holiday_data['name']} ({holiday_data['region_code']}) on {holiday_data['date']}")
                else:
                    skipped_count += 1
            else:
                # Create new holiday
                holiday = Holiday(
                    name=holiday_data['name'],
                    date=holiday_data['date'],
                    region_code=holiday_data['region_code'],
                    description=holiday_data['description']
                )
                db.session.add(holiday)
                created_count += 1
                print(f"Created holiday: {holiday_data['name']} ({holiday_data['region_code']}) on {holiday_data['date']}")
        
        except Exception as e:
            print(f"Error processing holiday {holiday_data['name']}: {str(e)}")
            continue
    
    try:
        db.session.commit()
        print(f"\nHoliday seeding completed!")
        print(f"Created: {created_count} holidays")
        print(f"Updated: {updated_count} holidays")
        print(f"Skipped: {skipped_count} holidays")
        print(f"Total processed: {len(all_holidays)} holidays")
        
        return {
            'created': created_count,
            'updated': updated_count,
            'skipped': skipped_count,
            'total': len(all_holidays)
        }
    
    except Exception as e:
        db.session.rollback()
        print(f"Error committing holiday data: {str(e)}")
        return {
            'created': 0,
            'updated': 0,
            'skipped': 0,
            'total': 0,
            'error': str(e)
        }


if __name__ == "__main__":
    import os
    import sys
    # Add the parent directory to the Python path
    sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))
    from app import create_app
    app = create_app()
    with app.app_context():
        seed_holidays()

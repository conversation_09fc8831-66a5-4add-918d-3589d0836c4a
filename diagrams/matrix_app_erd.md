# Matrix App Entity-Relationship Diagram

## Entities and Relationships

### User
- **id**: Integer (PK)
- **name**: String
- **email**: String (Unique)
- **password_hash**: String
- **role**: String (Admin, Manager, User)
- **_is_active**: Boolean
- **created_at**: DateTime
- **updated_at**: DateTime
- **last_login**: DateTime
- **last_password_date**: DateTime
- **bio**: Text
- **deleted_at**: DateTime (for soft delete)

### EmployeeDetail
- **id**: Integer (PK)
- **user_id**: Integer (FK to User.id)
- **employee_number**: String (Unique)
- **first_name**: String
- **middle_name**: String
- **last_name**: String
- **legal_name**: String
- **job_title**: String
- **phone**: String
- **emp_type**: String
- **enterprise_id**: String
- **manager_name**: String
- **job_code**: String
- **manager_level**: String
- **job_code_track_level**: String
- **business_unit_id**: Integer (FK to BusinessUnit.id)
- **business_segment_id**: Integer (FK to BusinessSegment.id)
- **hire_date**: Date
- **emp_status**: String
- **created_at**: DateTime
- **updated_at**: DateTime

### BusinessUnit
- **id**: Integer (PK)
- **name**: String
- **code**: String (Unique)
- **description**: Text
- **manager_id**: Integer (FK to User.id)
- **is_active**: Boolean
- **created_at**: DateTime
- **updated_at**: DateTime

### BusinessSegment
- **id**: Integer (PK)
- **name**: String
- **code**: String (Unique)
- **description**: Text
- **business_unit_id**: Integer (FK to BusinessUnit.id)
- **manager_id**: Integer (FK to User.id)
- **is_active**: Boolean
- **created_at**: DateTime
- **updated_at**: DateTime

### Team
- **id**: Integer (PK)
- **name**: String
- **short_name**: String
- **slug**: String (Unique)
- **description**: Text
- **is_active**: Boolean
- **created_at**: DateTime
- **updated_at**: DateTime

### TeamGroup
- **id**: Integer (PK)
- **team_id**: Integer (FK to Team.id)
- **name**: String
- **description**: Text
- **is_active**: Boolean
- **created_at**: DateTime
- **updated_at**: DateTime

### team_members (Association Table)
- **team_id**: Integer (PK, FK to Team.id)
- **employee_detail_id**: Integer (PK, FK to EmployeeDetail.id)
- **created_at**: DateTime

### Activity
- **id**: Integer (PK)
- **user_id**: Integer (FK to User.id)
- **action**: String
- **entity_type**: String
- **entity_id**: Integer
- **details**: Text
- **ip_address**: String
- **user_agent**: String
- **url**: String
- **severity**: String
- **category**: String
- **method**: String
- **old_values**: Text (JSON)
- **new_values**: Text (JSON)
- **created_at**: DateTime

### Message
- **id**: Integer (PK)
- **sender_id**: Integer (FK to User.id)
- **recipient_id**: Integer (FK to User.id)
- **subject**: String
- **body**: Text
- **is_read**: Boolean
- **created_at**: DateTime

### PasswordReset
- **id**: Integer (PK)
- **email**: String
- **token**: String (Unique)
- **created_at**: DateTime
- **expires_at**: DateTime
- **is_used**: Boolean

### Setting
- **id**: Integer (PK)
- **key**: String (Unique)
- **value**: Text
- **type**: String
- **description**: String
- **is_public**: Boolean
- **created_at**: DateTime
- **updated_at**: DateTime

## Relationships

1. **User to EmployeeDetail**: One-to-One
   - A User has one EmployeeDetail
   - An EmployeeDetail belongs to one User

2. **User to Activity**: One-to-Many
   - A User has many Activities
   - An Activity belongs to one User

3. **User to Message (as sender)**: One-to-Many
   - A User can send many Messages
   - A Message has one sender User

4. **User to Message (as recipient)**: One-to-Many
   - A User can receive many Messages
   - A Message has one recipient User

5. **User to BusinessUnit (as manager)**: One-to-Many
   - A User can manage many BusinessUnits
   - A BusinessUnit has one manager User

6. **User to BusinessSegment (as manager)**: One-to-Many
   - A User can manage many BusinessSegments
   - A BusinessSegment has one manager User

7. **BusinessUnit to BusinessSegment**: One-to-Many
   - A BusinessUnit has many BusinessSegments
   - A BusinessSegment belongs to one BusinessUnit

8. **BusinessUnit to EmployeeDetail**: One-to-Many
   - A BusinessUnit has many EmployeeDetails
   - An EmployeeDetail belongs to one BusinessUnit

9. **BusinessSegment to EmployeeDetail**: One-to-Many
   - A BusinessSegment has many EmployeeDetails
   - An EmployeeDetail belongs to one BusinessSegment

10. **Team to TeamGroup**: One-to-Many
    - A Team has many TeamGroups
    - A TeamGroup belongs to one Team

11. **Team to EmployeeDetail**: Many-to-Many (through team_members)
    - A Team has many EmployeeDetails
    - An EmployeeDetail can belong to many Teams

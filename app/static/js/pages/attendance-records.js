/**
 * Attendance Records Management JavaScript
 * Handles CRUD operations for attendance records in the admin panel
 */

document.addEventListener('DOMContentLoaded', function() {
    // Initialize the page
    initializeAttendanceRecords();
});

function initializeAttendanceRecords() {
    // Any initialization code can go here
    console.log('Attendance Records page initialized');
}

/**
 * Initialize the alert dialog container if it doesn't exist
 * @returns {HTMLElement} - The alert dialog container element
 */
function initAlertDialogContainer() {
    let container = document.getElementById('alert-dialog-container');

    if (!container) {
        container = document.createElement('div');
        container.id = 'alert-dialog-container';
        container.setAttribute('aria-live', 'polite');
        container.setAttribute('aria-atomic', 'true');
        document.body.appendChild(container);
    }

    return container;
}

/**
 * Open the add attendance record form
 */
function openAddAttendanceRecordForm() {
    const addUrl = document.getElementById('add-attendance-record-url').dataset.url;
    if (!addUrl) {
        console.error('Add attendance record URL not found');
        if (typeof showToast === 'function') {
            showToast('Error loading form: URL not found', 'error');
        }
        return;
    }

    fetch(addUrl)
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! Status: ${response.status}`);
            }
            return response.text();
        })
        .then(html => {
            // Use the drawer manager's openHtml method like attendance types
            const drawer = window.drawerManager.openHtml(html, {
                position: 'right',
                size: 'md'
            });
            return drawer;
        })
        .catch(error => {
            console.error('Error opening add attendance record form:', error);
            if (typeof showToast === 'function') {
                showToast(`Error loading form: ${error.message}`, 'error');
            }
        });
}

/**
 * Open the edit attendance record form
 * @param {number} recordId - The ID of the record to edit
 */
function openEditAttendanceRecordForm(recordId) {
    const editBaseUrl = document.getElementById('edit-attendance-record-base-url').dataset.url;
    if (!editBaseUrl) {
        console.error('Edit attendance record base URL not found');
        if (typeof showToast === 'function') {
            showToast('Error loading form: URL not found', 'error');
        }
        return;
    }

    const editUrl = editBaseUrl.replace('0', recordId);

    fetch(editUrl)
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! Status: ${response.status}`);
            }
            return response.text();
        })
        .then(html => {
            // Use the drawer manager's openHtml method like attendance types
            const drawer = window.drawerManager.openHtml(html, {
                position: 'right',
                size: 'md'
            });
            return drawer;
        })
        .catch(error => {
            console.error('Error opening edit attendance record form:', error);
            if (typeof showToast === 'function') {
                showToast(`Error loading form: ${error.message}`, 'error');
            }
        });
}

/**
 * Approve an attendance record
 * @param {number} recordId - The ID of the record to approve
 */
function approveAttendanceRecord(recordId) {
    showAlertDialog({
        title: 'Approve Attendance Record',
        description: 'Are you sure you want to approve this attendance record?',
        variant: 'default',
        onConfirm: () => {
            const approveBaseUrl = document.getElementById('approve-attendance-record-base-url').dataset.url;
            const csrfToken = document.getElementById('csrf-token').dataset.token;

            if (!approveBaseUrl || !csrfToken) {
                showToast('Error: Required URLs or tokens not found', 'error');
                return;
            }

            const approveUrl = approveBaseUrl.replace('0', recordId);

            fetch(approveUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-CSRFToken': csrfToken
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showToast(data.message, 'success');
                    location.reload();
                } else {
                    showToast(data.message, 'error');
                }
            })
            .catch(error => {
                console.error('Error approving record:', error);
                showToast('An error occurred while approving the record.', 'error');
            });
        }
    });
}

/**
 * Reject an attendance record
 * @param {number} recordId - The ID of the record to reject
 */
function rejectAttendanceRecord(recordId) {
    // Create a custom dialog with input field for rejection reason
    const container = document.getElementById('alert-dialog-container') || initAlertDialogContainer();

    // Store the currently focused element
    const previouslyFocusedElement = document.activeElement;

    // Create overlay
    const overlay = document.createElement('div');
    overlay.className = 'fixed inset-0 z-50 bg-black/50 backdrop-blur-sm transition-opacity duration-300';
    overlay.setAttribute('data-state', 'open');
    container.appendChild(overlay);

    // Create dialog content
    const dialog = document.createElement('div');
    dialog.className = 'fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border border-border bg-background p-6 shadow-lg duration-200 sm:rounded-lg md:w-full';
    dialog.setAttribute('data-state', 'open');
    dialog.setAttribute('role', 'dialog');
    dialog.setAttribute('aria-modal', 'true');
    dialog.setAttribute('aria-labelledby', 'dialog-title');
    dialog.setAttribute('aria-describedby', 'dialog-description');
    container.appendChild(dialog);

    // Dialog content
    dialog.innerHTML = `
        <div class="flex flex-col space-y-1.5 text-center sm:text-left">
            <h2 id="dialog-title" class="text-lg font-semibold leading-none tracking-tight">Reject Attendance Record</h2>
            <p id="dialog-description" class="text-sm text-muted-foreground">Please provide a reason for rejecting this attendance record.</p>
        </div>
        <div class="space-y-2">
            <label for="rejection-reason" class="text-sm font-medium">Rejection Reason</label>
            <textarea id="rejection-reason" class="flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50" placeholder="Enter reason for rejection..." required></textarea>
        </div>
        <div class="flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2">
            <button type="button" id="cancel-reject" class="inline-flex h-10 items-center justify-center rounded-md border border-input bg-background px-4 py-2 text-sm font-medium ring-offset-background transition-colors hover:bg-accent hover:text-accent-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 mt-2 sm:mt-0">Cancel</button>
            <button type="button" id="confirm-reject" class="inline-flex h-10 items-center justify-center rounded-md bg-destructive px-4 py-2 text-sm font-medium text-destructive-foreground ring-offset-background transition-colors hover:bg-destructive/90 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50">Reject Record</button>
        </div>
    `;

    const reasonTextarea = dialog.querySelector('#rejection-reason');
    const cancelButton = dialog.querySelector('#cancel-reject');
    const confirmButton = dialog.querySelector('#confirm-reject');

    // Focus the textarea
    setTimeout(() => reasonTextarea.focus(), 100);

    function closeDialog() {
        overlay.classList.add('opacity-0');
        dialog.classList.add('opacity-0', 'scale-95');

        setTimeout(() => {
            if (container.contains(overlay)) container.removeChild(overlay);
            if (container.contains(dialog)) container.removeChild(dialog);
            if (previouslyFocusedElement && typeof previouslyFocusedElement.focus === 'function') {
                previouslyFocusedElement.focus();
            }
        }, 300);
    }

    function handleReject() {
        const reason = reasonTextarea.value.trim();
        if (!reason) {
            showToast('Please provide a reason for rejection.', 'error');
            reasonTextarea.focus();
            return;
        }

        closeDialog();

        const rejectBaseUrl = document.getElementById('reject-attendance-record-base-url').dataset.url;
        const csrfToken = document.getElementById('csrf-token').dataset.token;

        if (!rejectBaseUrl || !csrfToken) {
            showToast('Error: Required URLs or tokens not found', 'error');
            return;
        }

        const rejectUrl = rejectBaseUrl.replace('0', recordId);

        fetch(rejectUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
                'X-Requested-With': 'XMLHttpRequest',
                'X-CSRFToken': csrfToken
            },
            body: `rejection_reason=${encodeURIComponent(reason)}`
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showToast(data.message, 'success');
                location.reload();
            } else {
                showToast(data.message, 'error');
            }
        })
        .catch(error => {
            console.error('Error rejecting record:', error);
            showToast('An error occurred while rejecting the record.', 'error');
        });
    }

    // Event listeners
    overlay.addEventListener('click', closeDialog);
    cancelButton.addEventListener('click', closeDialog);
    confirmButton.addEventListener('click', handleReject);

    // Handle Enter key in textarea
    reasonTextarea.addEventListener('keydown', (e) => {
        if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
            e.preventDefault();
            handleReject();
        }
    });
}

/**
 * Confirm and delete an attendance record
 * @param {number} recordId - The ID of the record to delete
 * @param {string} employeeName - The name of the employee
 * @param {string} recordDate - The date of the record
 */
function confirmDeleteAttendanceRecord(recordId, employeeName, recordDate) {
    showAlertDialog({
        title: 'Delete Attendance Record',
        description: `Are you sure you want to delete the attendance record for ${employeeName} on ${recordDate}? This action cannot be undone.`,
        variant: 'destructive',
        onConfirm: () => {
            const deleteBaseUrl = document.getElementById('delete-attendance-record-base-url').dataset.url;
            const csrfToken = document.getElementById('csrf-token').dataset.token;

            if (!deleteBaseUrl || !csrfToken) {
                showToast('Error: Required URLs or tokens not found', 'error');
                return;
            }

            const deleteUrl = deleteBaseUrl.replace('0', recordId);

            fetch(deleteUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-CSRFToken': csrfToken
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showToast(data.message, 'success');
                    location.reload();
                } else {
                    showToast(data.message, 'error');
                }
            })
            .catch(error => {
                console.error('Error deleting record:', error);
                showToast('An error occurred while deleting the record.', 'error');
            });
        }
    });
}

/**
 * Export attendance records
 */
function exportAttendanceRecords() {
    // Get current filter parameters
    const urlParams = new URLSearchParams(window.location.search);
    const exportParams = new URLSearchParams();

    // Add filter parameters to export
    ['employee', 'attendance_type', 'status', 'start_date', 'end_date'].forEach(param => {
        const value = urlParams.get(param);
        if (value) {
            exportParams.append(param, value);
        }
    });

    // Add export format
    exportParams.append('format', 'csv');

    // Create download link
    const exportUrl = `${window.location.pathname}/export?${exportParams.toString()}`;

    // Create temporary link and trigger download
    const link = document.createElement('a');
    link.href = exportUrl;
    link.download = `attendance_records_${new Date().toISOString().split('T')[0]}.csv`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    showToast('Export started. Download will begin shortly.', 'info');
}

// Export functions for use in other modules
window.openAddAttendanceRecordForm = openAddAttendanceRecordForm;
window.openEditAttendanceRecordForm = openEditAttendanceRecordForm;
window.approveAttendanceRecord = approveAttendanceRecord;
window.rejectAttendanceRecord = rejectAttendanceRecord;
window.confirmDeleteAttendanceRecord = confirmDeleteAttendanceRecord;
window.exportAttendanceRecords = exportAttendanceRecords;

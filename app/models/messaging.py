"""
Authentication models for password reset functionality.
"""

from datetime import datetime, <PERSON><PERSON><PERSON>
import uuid
from typing import Optional

from app import db
from app.models import PH_TZ


class PasswordReset(db.Model):
    """Password reset token model for recovery workflow."""
    __tablename__ = 'password_resets'

    id = db.Column(db.Integer, primary_key=True)
    email = db.Column(db.String(120), nullable=False, index=True)
    token = db.Column(db.String(100), unique=True, nullable=False, default=lambda: str(uuid.uuid4()))
    created_at = db.Column(db.DateTime, default=lambda: datetime.now(PH_TZ))
    expires_at = db.Column(db.DateTime, nullable=False)
    is_used = db.Column(db.Bo<PERSON>, default=False)

    def __repr__(self) -> str:
        return f'<PasswordReset {self.email}>'

    @property
    def is_expired(self) -> bool:
        """Check if the token has expired."""
        return datetime.now(PH_TZ) > self.expires_at

    @classmethod
    def create_token(cls, email: str, expiration: int = 3600) -> 'PasswordReset':
        """
        Create a password reset token.

        Args:
            email: User's email address
            expiration: Token expiration time in seconds (default: 1 hour)

        Returns:
            PasswordReset: The created token object
        """
        # First, invalidate any existing tokens
        existing_tokens = cls.query.filter_by(email=email, is_used=False).all()
        for token in existing_tokens:
            token.is_used = True

        # Create a new token
        token = cls(
            email=email,
            expires_at=datetime.now(PH_TZ) + timedelta(seconds=expiration)
        )

        db.session.add(token)
        db.session.commit()

        return token

    @classmethod
    def validate_token(cls, token: str) -> Optional['PasswordReset']:
        """Validate a token and return it if valid."""
        token_obj = cls.query.filter_by(token=token, is_used=False).first()
        if not token_obj or token_obj.is_expired:
            return None
        return token_obj

    @classmethod
    def cleanup_expired_tokens(cls) -> int:
        """Delete all expired and used tokens to keep the database clean."""
        expired_tokens = cls.query.filter(
            db.or_(
                cls.expires_at < datetime.now(PH_TZ),
                cls.is_used == True
            )
        ).all()

        count = len(expired_tokens)
        for token in expired_tokens:
            db.session.delete(token)

        db.session.commit()
        return count

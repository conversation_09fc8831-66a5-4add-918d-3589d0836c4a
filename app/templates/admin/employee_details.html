{% extends "base.html" %}

{% from "components/page_header.html" import page_header %}
{% from "components/table_header.html" import table_header %}
{% from "components/simple_table.html" import simple_table %}
{% from "components/action_buttons.html" import action_buttons %}

{% block title %}Employee Details{% endblock %}

{% block header %}Employee Details{% endblock %}

{% block content %}
<div class="space-y-6">
  {{ page_header(
    title="Employee Details",
    button_text="Add Employee Details",
    button_icon="plus",
    button_action="drawerManager.openForm('employee_detail')",
    description="Manage employee information and assignments"
  ) }}

  <div class="card">
    {{ table_header(
      title="Employee Details List"
    ) }}

    {% call simple_table(
      headers=[
        {'label': 'Employee'},
        {'label': 'Job Title'},
        {'label': 'Employee Number'},
        {'label': 'Business Unit'},
        {'label': 'Business Segment'},
        {'label': 'Phone'},
        {'label': 'Status'},
        {'label': 'Actions', 'align': 'right'}
      ],
      items=employee_details,
      empty_icon="users",
      empty_title="No employee details found",
      empty_description="Add employee details to get started",
      empty_button_text="Add Employee Details",
      empty_button_icon="plus",
      empty_button_action="drawerManager.openForm('employee_detail')"
    ) %}
      {% for detail in employee_details %}
      <tr class="border-b border-border hover:bg-muted/30">
        <td class="px-4 py-3 text-sm">{{ detail.user.name }}</td>
        <td class="px-4 py-3 text-sm">{{ detail.job_title or 'Not specified' }}</td>
        <td class="px-4 py-3 text-sm">{{ detail.employee_number or 'Not specified' }}</td>
        <td class="px-4 py-3 text-sm">{{ detail.business_unit.name if detail.business_unit else 'Not assigned' }}</td>
        <td class="px-4 py-3 text-sm">{{ detail.business_segment.name if detail.business_segment else 'Not assigned' }}</td>
        <td class="px-4 py-3 text-sm">{{ detail.phone or 'Not specified' }}</td>
        <td class="px-4 py-3 text-sm">
          {% if detail.emp_status %}
            {% if detail.emp_status == 'active' %}
              <span class="badge badge-success">Active</span>
            {% elif detail.emp_status == 'leave_of_absence' %}
              <span class="badge badge-warning">Leave of Absence</span>
            {% elif detail.emp_status == 'terminated' %}
              <span class="badge badge-destructive">Terminated</span>
            {% else %}
              <span class="badge badge-secondary">{{ detail.emp_status|title }}</span>
            {% endif %}
          {% else %}
            <span class="badge badge-secondary">Not specified</span>
          {% endif %}
        </td>
        <td class="px-4 py-3 text-sm text-right">
          {{ action_buttons([
            {'type': 'link', 'url': url_for('admin.view_employee_details', user_id=detail.user_id), 'icon': 'eye', 'title': 'View'},
            {'type': 'button', 'action': 'drawerManager.openForm(\'employee_detail\', ' ~ detail.id ~ ')', 'icon': 'edit', 'title': 'Edit'},
            {'type': 'button', 'action': 'confirmDeleteEmployeeDetail(' ~ detail.id ~ ', \'' ~ detail.user.name ~ '\')', 'icon': 'trash-2', 'variant': 'text-destructive', 'title': 'Delete'}
          ]) }}
        </td>
      </tr>
      {% endfor %}
    {% endcall %}

    {% include "partials/pagination.html" %}
  </div>
</div>
{% endblock %}

{% block scripts %}
<script>
  // Lucide icons are now initialized automatically
  document.addEventListener('DOMContentLoaded', function() {

      // We're now using flash messages converted to toasts via handleFlashMessages() in utils.js
      // This happens automatically on page load and provides a consistent notification experience

      // Clean URL parameters if they exist (for backward compatibility)
      const urlParams = new URLSearchParams(window.location.search);
      if (urlParams.has('status')) {
          const newUrl = window.location.pathname;
          window.history.replaceState({}, document.title, newUrl);
      }
  });

  // Confirm delete employee detail
  function confirmDeleteEmployeeDetail(detailId, employeeName) {
      showAlertDialog({
          title: 'Delete Employee Details',
          description: `Are you sure you want to delete details for ${employeeName}? This action cannot be undone.`,
          variant: 'destructive',
          onConfirm: () => {
              // Submit form to delete endpoint
              const form = document.createElement('form');
              form.method = 'POST';
              form.action = `{{ url_for('admin.delete_employee_detail', detail_id=0) }}`.replace('0', detailId);

              const csrfToken = document.createElement('input');
              csrfToken.type = 'hidden';
              csrfToken.name = 'csrf_token';
              csrfToken.value = '{{ csrf_token() }}';

              form.appendChild(csrfToken);
              document.body.appendChild(form);
              form.submit();
          }
      });
  }
</script>
{% endblock %}

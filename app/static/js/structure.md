app/static/js/
├── core/                     # Core application functionality
│   ├── asset-manager.js      # Asset loading optimization
│   ├── critical-init.js      # Critical initialization scripts
│   └── utils.js              # Core utilities
├── icons/                    # Icon system files
│   ├── critical-icons.js     # Critical icons list
│   ├── icon-utils.js         # Icon utilities
│   ├── icons.js              # Main icon implementation
│   ├── icon-definitions.js   # Icon definitions
│   ├── lucide-manager.js     # Lucide integration
│   ├── icon-error-suppressor.js # Error handling for icons
│   └── sprite.svg            # SVG sprite
├── components/               # UI component scripts
│   ├── sidebar.js            # Sidebar functionality
│   ├── toast.js              # Toast notifications
│   ├── drawer.js             # Drawer component
│   └── dialog.js             # Dialog component
├── utils/                    # Utility functions
│   ├── index.js              # Main utilities entry point
│   ├── dom.js                # DOM manipulation utilities
│   ├── asset-loader.js       # Asset loading utilities
│   └── script-loader.js      # Script loading utilities
├── vendor/                   # Third-party libraries
│   └── lucide/               # Lucide icon library
├── pages/                    # Page-specific scripts
│   ├── dashboard.js          # Dashboard page
│   ├── users.js              # Users page
│   └── settings.js           # Settings page
└── build/                    # Build scripts
    ├── build-icon-sprite.js  # Icon sprite generator
    └── icon-scanner.js       # Icon usage scanner

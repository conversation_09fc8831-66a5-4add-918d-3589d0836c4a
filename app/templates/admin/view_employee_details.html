{% extends "base.html" %}

{% block title %}View Employee Details{% endblock %}

{% block header %}View Employee Details{% endblock %}

{% block content %}
<div class="card shadow-md">
  <div class="p-6 border-b border-border bg-card">
      <div class="flex items-center justify-between">
          <div>
              <h2 class="text-xl font-semibold tracking-tight">Employee Information</h2>
              <p class="text-sm text-muted-foreground mt-1">Details for {{ user.name }}</p>
          </div>
          {% if employee_detail %}
          <div class="flex items-center space-x-2">
              {% if employee_detail.emp_status == 'active' %}
                <span class="badge badge-success">Active</span>
              {% elif employee_detail.emp_status == 'terminated' %}
                <span class="badge badge-destructive">Terminated</span>
              {% elif employee_detail.emp_status == 'leave_of_absence' %}
                <span class="badge badge-warning">Leave of Absence</span>
              {% else %}
                <span class="badge badge-secondary">{{ employee_detail.emp_status|title }}</span>
              {% endif %}
          </div>
          {% endif %}
      </div>
  </div>

  <div class="p-6">
      {% if employee_detail %}
          <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
              <div class="space-y-6">
                  <div class="space-y-4">
                      <h3 class="text-md font-semibold flex items-center">
                          <i data-lucide="user" class="w-5 h-5 mr-2 text-primary"></i>
                          Personal Information
                      </h3>
                      <div class="rounded-md border border-border overflow-hidden">
                          <div class="grid grid-cols-1 divide-y divide-border">
                              <div class="grid grid-cols-3 p-3">
                                  <div class="text-sm font-medium text-muted-foreground">Name</div>
                                  <div class="col-span-2 text-sm">{{ user.name }}</div>
                              </div>
                              <div class="grid grid-cols-3 p-3">
                                  <div class="text-sm font-medium text-muted-foreground">Email</div>
                                  <div class="col-span-2 text-sm break-all">{{ user.email }}</div>
                              </div>
                              <div class="grid grid-cols-3 p-3">
                                  <div class="text-sm font-medium text-muted-foreground">Job Title</div>
                                  <div class="col-span-2 text-sm">{{ employee_detail.job_title or 'Not specified' }}</div>
                              </div>
                              <div class="grid grid-cols-3 p-3">
                                  <div class="text-sm font-medium text-muted-foreground">Full Name</div>
                                  <div class="col-span-2 text-sm">{{ employee_detail.first_name }} {{ employee_detail.middle_name }} {{ employee_detail.last_name }}</div>
                              </div>
                              <div class="grid grid-cols-3 p-3">
                                  <div class="text-sm font-medium text-muted-foreground">Legal Name</div>
                                  <div class="col-span-2 text-sm">{{ employee_detail.legal_name or 'Not specified' }}</div>
                              </div>
                              <div class="grid grid-cols-3 p-3">
                                  <div class="text-sm font-medium text-muted-foreground">Phone</div>
                                  <div class="col-span-2 text-sm">{{ employee_detail.phone or 'Not specified' }}</div>
                              </div>
                              <div class="grid grid-cols-3 p-3">
                                  <div class="text-sm font-medium text-muted-foreground">Employment Type</div>
                                  <div class="col-span-2 text-sm">{{ employee_detail.emp_type or 'Not specified' }}</div>
                              </div>
                              <div class="grid grid-cols-3 p-3">
                                  <div class="text-sm font-medium text-muted-foreground">Enterprise ID</div>
                                  <div class="col-span-2 text-sm">{{ employee_detail.enterprise_id or 'Not specified' }}</div>
                              </div>
                          </div>
                      </div>
                  </div>
              </div>

              <div class="space-y-6">
                  <div class="space-y-4">
                      <h3 class="text-md font-semibold flex items-center">
                          <i data-lucide="building" class="w-5 h-5 mr-2 text-primary"></i>
                          Organization Information
                      </h3>
                      <div class="rounded-md border border-border overflow-hidden">
                          <div class="grid grid-cols-1 divide-y divide-border">
                              <div class="grid grid-cols-3 p-3">
                                  <div class="text-sm font-medium text-muted-foreground">Business Unit</div>
                                  <div class="col-span-2 text-sm">{{ employee_detail.business_unit.name if employee_detail.business_unit else 'Not assigned' }}</div>
                              </div>
                              <div class="grid grid-cols-3 p-3">
                                  <div class="text-sm font-medium text-muted-foreground">Business Segment</div>
                                  <div class="col-span-2 text-sm">{{ employee_detail.business_segment.name if employee_detail.business_segment else 'Not assigned' }}</div>
                              </div>
                              <div class="grid grid-cols-3 p-3">
                                  <div class="text-sm font-medium text-muted-foreground">Hire Date</div>
                                  <div class="col-span-2 text-sm">{{ employee_detail.hire_date.strftime('%Y-%m-%d') if employee_detail.hire_date else 'Not specified' }}</div>
                              </div>
                              <div class="grid grid-cols-3 p-3">
                                  <div class="text-sm font-medium text-muted-foreground">Role</div>
                                  <div class="col-span-2 text-sm">{{ user.role }}</div>
                              </div>
                              <div class="grid grid-cols-3 p-3">
                                  <div class="text-sm font-medium text-muted-foreground">Manager Name</div>
                                  <div class="col-span-2 text-sm">{{ employee_detail.manager_name or 'Not specified' }}</div>
                              </div>
                              <div class="grid grid-cols-3 p-3">
                                  <div class="text-sm font-medium text-muted-foreground">Job Code</div>
                                  <div class="col-span-2 text-sm">{{ employee_detail.job_code or 'Not specified' }}</div>
                              </div>
                              <div class="grid grid-cols-3 p-3">
                                  <div class="text-sm font-medium text-muted-foreground">Manager Level</div>
                                  <div class="col-span-2 text-sm">{{ employee_detail.manager_level or 'Not specified' }}</div>
                              </div>
                              <div class="grid grid-cols-3 p-3">
                                  <div class="text-sm font-medium text-muted-foreground">Job Code Track Level</div>
                                  <div class="col-span-2 text-sm">{{ employee_detail.job_code_track_level or 'Not specified' }}</div>
                              </div>
                          </div>
                      </div>
                  </div>
              </div>

              {% if employee_detail.notes %}
              <div class="md:col-span-2 space-y-4">
                  <h3 class="text-md font-semibold flex items-center">
                      <i data-lucide="file-text" class="w-5 h-5 mr-2 text-primary"></i>
                      Notes
                  </h3>
                  <div class="p-4 rounded-md border border-border">
                      {{ employee_detail.notes }}
                  </div>
              </div>
              {% endif %}
          </div>
      {% else %}
          <div class="p-8 bg-muted/30 rounded-md text-center">
              <div class="inline-flex items-center justify-center w-16 h-16 rounded-full bg-muted mb-4">
                  <i data-lucide="user-x" class="w-8 h-8 text-muted-foreground"></i>
              </div>
              <h3 class="text-lg font-medium mb-2">No Employee Details Found</h3>
              <p class="text-muted-foreground mb-6 max-w-md mx-auto">Employee details have not been set up for this user yet. You can add details by clicking the button below.</p>
              <button onclick="openAddEmployeeDetailDrawer({{ user.id }})" class="btn btn-primary btn-md">
                  <i data-lucide="plus" class="w-4 h-4 mr-2"></i>
                  Add Employee Details
              </button>
          </div>
      {% endif %}
  </div>

  <div class="p-6 border-t border-border bg-card">
      <div class="flex flex-wrap justify-end items-center gap-3">
          <a href="{{ url_for('admin.employee_details') }}" class="btn btn-outline btn-md">
              <i data-lucide="arrow-left" class="w-4 h-4 mr-2"></i>
              Back to List
          </a>
          {% if employee_detail %}
          <a href="#" onclick="drawerManager.openForm('employee_detail', {{ employee_detail.id }})" class="btn btn-primary btn-md">
              <i data-lucide="edit" class="w-4 h-4 mr-2"></i>
              Edit Details
          </a>
          {% endif %}
      </div>
  </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// Initialize Lucide icons
document.addEventListener('DOMContentLoaded', function() {
    lucide.createIcons();
});

// Function to open the employee detail drawer with a pre-selected user
function openAddEmployeeDetailDrawer(userId) {
    drawerManager.openForm('employee_detail', null, { queryParams: { user_id: userId } });
}
</script>
{% endblock %}

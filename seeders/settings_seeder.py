"""
Settings seeder module.
This module provides functions to seed the database with settings data.
"""

import os
import sys

def seed_settings():
    """
    Seed the database with default settings.

    Returns:
        list: The created setting objects
    """
    # Import here to allow running as standalone script
    from app import db
    from app.models import Setting
    # Define default settings
    default_settings = [
        {
            'key': 'app_name',
            'value': 'Matrix App',
            'description': 'Application name displayed in the UI'
        },
        {
            'key': 'session_lifetime',
            'value': '7',
            'description': 'Session lifetime in days'
        },
        {
            'key': 'min_password_length',
            'value': '8',
            'description': 'Minimum password length'
        },
        {
            'key': 'password_requires_uppercase',
            'value': 'true',
            'description': 'Whether passwords require at least one uppercase letter'
        },
        {
            'key': 'password_requires_number',
            'value': 'true',
            'description': 'Whether passwords require at least one number'
        },
        {
            'key': 'password_requires_special',
            'value': 'false',
            'description': 'Whether passwords require at least one special character'
        },
        {
            'key': 'enable_automatic_maintenance',
            'value': 'true',
            'description': 'Whether to run maintenance tasks automatically'
        },
        {
            'key': 'activity_log_retention_days',
            'value': '15',
            'description': 'Number of days to keep activity logs'
        }
    ]

    created_settings = []

    for setting_data in default_settings:
        # Check if setting already exists
        existing_setting = Setting.query.filter_by(key=setting_data['key']).first()
        if existing_setting:
            print(f"Setting '{setting_data['key']}' already exists, skipping...")
            created_settings.append(existing_setting)
            continue

        # Determine type based on value if not specified
        value = setting_data['value']
        if isinstance(value, bool) or value.lower() in ['true', 'false']:
            type_name = Setting.TYPE_BOOLEAN
        elif value.isdigit():
            type_name = Setting.TYPE_INTEGER
        else:
            type_name = Setting.TYPE_STRING

        # Create setting
        setting = Setting()
        setting.key = setting_data['key']
        setting.value = setting_data['value']
        setting.description = setting_data['description']
        setting.type = setting_data.get('type', type_name) # Use provided type or inferred
        setting.is_public = setting_data.get('is_public', False) # Default to False if not specified

        db.session.add(setting)
        created_settings.append(setting)

    db.session.commit()
    print(f"Created {len(created_settings)} settings")
    return created_settings

if __name__ == "__main__":
    import os
    import sys
    # Add the parent directory to the Python path
    sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))
    from app import create_app
    app = create_app()
    with app.app_context():
        seed_settings()

<!DOCTYPE html>
<html lang="en" class="{% if app_settings.default_theme == 'dark' %}dark{% else %}light{% endif %}">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Register - {{ app_settings.site_name|default('Matrix') }}</title>
  <!-- Dynamic primary color from settings -->
  <style>
    :root {
      {% set primary = app_settings.primary_color|default('#0284c7') %}
      {% set rgb = hex_to_rgb(primary) %}
      --primary: {{ primary }};
      --primary-rgb: {{ rgb[0] }}, {{ rgb[1] }}, {{ rgb[2] }};
    }

    /* Ensure buttons have proper contrast - shadcn style */
    .btn-primary {
      {% set neutral_colors = ['#71717a', '#64748b', '#78716c', '#6b7280', '#525252'] %}
      {% if primary in neutral_colors %}
        /* For neutral colors, use white/black based on theme */
        background-color: var(--foreground);
        border-color: var(--foreground);
        color: var(--background);
      {% else %}
        /* For other colors, use the actual color with black/white text based on contrast */
        background-color: var(--primary);
        border-color: var(--primary);
        color: {% if hex_to_rgb(primary)|sum < 382 %}white{% else %}black{% endif %};
      {% endif %}
      transition: opacity 0.2s, background-color 0.2s;
    }

    .btn-primary:hover {
      {% if primary in neutral_colors %}
        background-color: var(--foreground);
        border-color: var(--foreground);
      {% else %}
        background-color: var(--primary);
        border-color: var(--primary);
      {% endif %}
      opacity: 0.9;
    }

    .text-primary {
      color: var(--primary);
    }
  </style>
  <link rel="stylesheet" href="{{ url_for('static', filename='css/main.css') }}">
  <script src="https://unpkg.com/lucide@latest"></script>
  <script src="https://unpkg.com/@lucide/web/dist/umd/lucide.js"></script>
</head>
<body class="bg-background text-foreground antialiased flex min-h-screen items-center justify-center p-6 md:p-10">
  <div class="w-full max-w-sm animate-fade-in">
    <!-- Register Form Card -->
    <div class="border border-border rounded-md p-6 bg-card">
      <!-- Header -->
      <div class="flex flex-col space-y-2 text-center mb-8">
        <h1 class="text-2xl font-semibold tracking-tight">Create an account</h1>
        <p class="text-sm text-muted-foreground">Enter your details to create your account</p>
      </div>

      <!-- Flash Messages -->
      {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
          {% for category, message in messages %}
            <div class="mb-6 flex items-start gap-2 rounded-md border p-4 text-sm {% if category == 'error' %}border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive{% else %}border-primary/50 text-primary dark:border-primary [&>svg]:text-primary{% endif %}">
              <i data-lucide="{% if category == 'error' %}alert-circle{% else %}info{% endif %}" class="h-4 w-4 mt-0.5"></i>
              <div>
                <h5 class="font-medium leading-none mb-1">{% if category == 'error' %}Error{% else %}Info{% endif %}</h5>
                <p>{{ message }}</p>
              </div>
            </div>
          {% endfor %}
        {% endif %}
      {% endwith %}

      <form action="{{ url_for('auth.register') }}" method="POST" class="space-y-4">
        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">

        <div class="space-y-2">
          <label for="name" class="text-sm font-medium leading-none">Name</label>
          <input type="text" id="name" name="name" placeholder="John Doe" required
                class="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                value="{{ request.form.name }}">
        </div>

        <div class="space-y-2">
          <label for="email" class="text-sm font-medium leading-none">Email</label>
          <input type="email" id="email" name="email" placeholder="<EMAIL>" required
                class="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                value="{{ request.form.email }}">
        </div>

        <div class="space-y-2">
          <label for="password" class="text-sm font-medium leading-none">Password</label>
          <input type="password" id="password" name="password" required
                class="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50">
        </div>

        <div class="space-y-2">
          <label for="confirm_password" class="text-sm font-medium leading-none">Confirm Password</label>
          <input type="password" id="confirm_password" name="confirm_password" required
                class="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50">
        </div>

        <button type="submit"
                class="inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-4 py-2 w-full">
          Register
        </button>
      </form>

      <!-- Login Link -->
      <div class="text-center text-sm mt-6">
        Already have an account?
        <a href="{{ url_for('auth.login') }}" class="font-medium text-primary hover:underline">Login</a>
      </div>
    </div>
  </div>

  <script>
    // Lucide icons are now initialized automatically

    // Theme handling
    const getThemePreference = () => {
      if (typeof localStorage !== 'undefined' && localStorage.getItem('theme')) {
        return localStorage.getItem('theme');
      }
      return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
    };

    const setTheme = (theme) => {
      const html = document.documentElement;
      if (theme === 'dark') {
        html.classList.add('dark');
      } else {
        html.classList.remove('dark');
      }
      localStorage.setItem('theme', theme);
    };

    // Set initial theme
    setTheme(getThemePreference());
  </script>
</body>
</html>

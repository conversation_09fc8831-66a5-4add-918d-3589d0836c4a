{% macro activity_details_modal() %}
<!-- Activity Details Modal -->
<div id="activity-details-modal" style="position: fixed; top: 0; left: 0; right: 0; bottom: 0; width: 100vw; height: 100vh; display: flex; align-items: center; justify-content: center; z-index: 9999; opacity: 0; visibility: hidden; transition: opacity 0.3s ease, visibility 0.3s ease;" aria-modal="true" role="dialog">
  <div class="modal-overlay bg-black/50 backdrop-blur-sm" style="position: fixed; top: 0; left: 0; right: 0; bottom: 0; width: 100vw; height: 100vh; z-index: 9998;"></div>
  <div class="modal-container" style="position: relative; z-index: 10000; max-width: 90%; max-height: 90vh; margin: 2rem; display: flex; flex-direction: column; transform: translateY(20px); transition: transform 0.3s ease;">
    <div class="modal-content max-w-2xl bg-card rounded-lg shadow-lg border border-border overflow-hidden" style="width: 100%; display: flex; flex-direction: column;">
      <div id="activity-details-header" class="p-4 border-b border-border bg-card">
        <!-- Header content will be dynamically populated -->
      </div>
      <div id="activity-details-content" class="p-6 max-h-[70vh] overflow-y-auto">
        <div class="flex justify-center items-center py-16">
          <div class="animate-spin rounded-full h-10 w-10 border-2 border-t-transparent border-primary"></div>
        </div>
      </div>
      <div class="modal-footer px-6 py-4 border-t border-border flex justify-end">
        <button type="button" class="inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-9 px-4 py-2" onclick="closeModal()">
          <i data-lucide="x" class="h-4 w-4 mr-2"></i>
          Close
        </button>
      </div>
    </div>
  </div>
</div>
{% endmacro %}

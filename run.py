import os
import logging
from app import create_app, db
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# Get configuration from environment or default to development
config_name = os.environ.get('FLASK_CONFIG', 'development')
app = create_app(config_name)

# Configure logging
if not app.debug:
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s %(levelname)s %(name)s %(message)s'
    )
    app.logger.info(f'Application startup in {config_name} mode')

# Configure SQLAlchemy logging - reduce verbosity
logging.getLogger('sqlalchemy.engine').setLevel(logging.WARNING)

@app.shell_context_processor
def make_shell_context():
    """Add database and models to flask shell context for interactive use."""
    from app.models import (
        User, EmployeeDetail, BusinessUnit, BusinessSegment,
        Message, Activity, PasswordReset, Team, TeamGroup
    )

    return {
        'db': db,
        'User': User,
        'EmployeeDetail': EmployeeDetail,
        'BusinessUnit': BusinessUnit,
        'BusinessSegment': BusinessSegment,
        'Message': Message,
        'Activity': Activity,
        'PasswordReset': PasswordReset,
        'Team': Team,
        'TeamGroup': TeamGroup
    }

@app.cli.command("init-db")
def init_db_command():
    """Initialize the database with schema and initial data."""
    try:
        db.create_all()
        # Check if there are any users
        from app.models import User
        if User.query.count() == 0:
            # Create an admin user if no users exist
            admin = User(name='Admin', email='<EMAIL>', role='Admin')
            admin.set_password('Admin@123')
            db.session.add(admin)
            db.session.commit()
            print('Created default admin user: <EMAIL> (password: Admin@123)')
        print('Database initialized successfully.')
    except Exception as e:
        print(f'Error initializing database: {str(e)}')

if __name__ == '__main__':
    import argparse

    # Parse command line arguments
    parser = argparse.ArgumentParser(description='Run the Flask application')
    parser.add_argument('--port', type=int, default=int(os.environ.get('PORT', 5003)),
                        help='Port to run the application on')
    parser.add_argument('--host', type=str, default=os.environ.get('HOST', '0.0.0.0'),
                        help='Host to run the application on')
    args = parser.parse_args()

    # Run the application
    app.run(host=args.host, port=args.port)

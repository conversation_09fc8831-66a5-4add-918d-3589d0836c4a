/**
 * Holiday Management JavaScript
 * Handles holiday CRUD operations, filtering, and bulk import
 */

// Global variables
let currentHolidayId = null;
let isEditMode = false;

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    initializeHolidayManagement();
    registerHolidayForms();
});

/**
 * Register holiday forms with drawer manager
 */
function registerHolidayForms() {
    // Check if drawerManager exists
    if (!window.drawerManager) {
        console.error('DrawerManager not found');
        return;
    }

    // Register holiday form - using openHtml method like attendance types
    window.drawerManager.registerFormType('holiday', {
        createUrl: '/admin/holidays/form',
        editUrl: '/admin/holidays/form/{id}',
        size: 'md',
        position: 'right'
    });

    // Register bulk import form
    window.drawerManager.registerFormType('holiday_bulk_import', {
        createUrl: '/admin/holidays/bulk-import/form',
        editUrl: '/admin/holidays/bulk-import/form',
        size: 'md',
        position: 'right'
    });

    console.log('Holiday forms registered with drawer manager');
}

/**
 * Open the form to add a new holiday
 */
function openAddHolidayForm() {
    const url = document.getElementById('add-holiday-url').getAttribute('data-url');
    if (!url) {
        console.error('Add holiday URL not found');
        if (typeof window.showToast === 'function') {
            window.showToast('Error loading form: URL not found', { type: 'error' });
        }
        return;
    }

    fetch(url)
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! Status: ${response.status}`);
            }
            return response.text();
        })
        .then(html => {
            // Use the openHtml method of the DrawerManager
            const drawer = window.drawerManager.openHtml(html, {
                position: 'right',
                size: 'md'
            });
            return drawer;
        })
        .catch(error => {
            console.error('Error opening add holiday form:', error);
            if (typeof window.showToast === 'function') {
                window.showToast(`Error loading form: ${error.message}`, { type: 'error' });
            }
        });
}

/**
 * Open the form to edit an existing holiday
 * @param {number} holidayId - ID of the holiday to edit
 */
function openEditHolidayForm(holidayId) {
    const baseUrl = document.getElementById('edit-holiday-base-url').getAttribute('data-url');
    if (!baseUrl) {
        console.error('Edit holiday base URL not found');
        if (typeof window.showToast === 'function') {
            window.showToast('Error loading form: URL not found', { type: 'error' });
        }
        return;
    }

    const url = baseUrl.replace('0', holidayId);

    fetch(url)
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! Status: ${response.status}`);
            }
            return response.text();
        })
        .then(html => {
            // Use the openHtml method of the DrawerManager
            const drawer = window.drawerManager.openHtml(html, {
                position: 'right',
                size: 'md'
            });
            return drawer;
        })
        .catch(error => {
            console.error('Error opening edit holiday form:', error);
            if (typeof window.showToast === 'function') {
                window.showToast(`Error loading form: ${error.message}`, { type: 'error' });
            }
        });
}

/**
 * Initialize holiday management functionality
 */
function initializeHolidayManagement() {
    // Initialize filter form
    initializeFilterForm();

    // Initialize tooltips and other UI components
    initializeTooltips();

    console.log('Holiday management initialized');
}



/**
 * Initialize filter form handlers
 */
function initializeFilterForm() {
    // Auto-submit filter form when inputs change (with debounce)
    const searchInput = document.getElementById('search');
    if (searchInput) {
        let searchTimeout;
        searchInput.addEventListener('input', function() {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                this.form.submit();
            }, 500);
        });
    }
}



/**
 * Confirm holiday deletion
 */
function confirmDeleteHoliday(holidayId, holidayName) {
    showAlertDialog({
        title: 'Delete Holiday',
        description: `Are you sure you want to delete the holiday "${holidayName}"? This action cannot be undone.`,
        variant: 'destructive',
        onConfirm: () => {
            const baseUrl = document.getElementById('delete-holiday-base-url').getAttribute('data-url');
            if (!baseUrl) {
                console.error('Delete holiday base URL not found');
                if (typeof showToast === 'function') {
                    showToast('Error deleting holiday: URL not found', { type: 'error' });
                }
                return;
            }

            const form = document.createElement('form');
            form.method = 'POST';
            form.action = baseUrl.replace('0', holidayId);

            const csrfTokenInput = document.createElement('input');
            csrfTokenInput.type = 'hidden';
            csrfTokenInput.name = 'csrf_token';
            csrfTokenInput.value = document.getElementById('csrf-token').getAttribute('data-token');
            form.appendChild(csrfTokenInput);

            document.body.appendChild(form);
            form.submit();
        }
    });
}



/**
 * Export holidays
 */
function exportHolidays() {
    // Get current filter parameters
    const urlParams = new URLSearchParams(window.location.search);
    const exportUrl = `/admin/holidays/export?${urlParams.toString()}`;

    // Create temporary link and trigger download
    const link = document.createElement('a');
    link.href = exportUrl;
    link.download = 'holidays.csv';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    if (typeof window.showToast === 'function') {
        window.showToast('Export started', 'success');
    }
}



/**
 * Initialize tooltips
 */
function initializeTooltips() {
    // Add tooltips to action buttons
    const actionButtons = document.querySelectorAll('[title]');
    actionButtons.forEach(button => {
        // Simple tooltip implementation
        button.addEventListener('mouseenter', function() {
            // Could implement custom tooltip here
        });
    });
}





/**
 * Handle keyboard shortcuts
 */
document.addEventListener('keydown', function(event) {
    // Ctrl/Cmd + N opens add holiday form
    if ((event.ctrlKey || event.metaKey) && event.key === 'n') {
        event.preventDefault();
        openAddHolidayForm();
    }
});

// Export functions for use in other modules
window.openAddHolidayForm = openAddHolidayForm;
window.openEditHolidayForm = openEditHolidayForm;
window.confirmDeleteHoliday = confirmDeleteHoliday;

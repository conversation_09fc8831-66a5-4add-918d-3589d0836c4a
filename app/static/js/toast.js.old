/**
 * Toast notification system for the application
 * Provides a consistent way to show toast notifications across the app
 * Styled to match shadcn UI design system
 */

// Global toast system
const ToastSystem = {
    /**
     * Show a toast notification
     * @param {string} message - The message to display
     * @param {string} type - The type of toast (success, error, info, warning)
     * @param {number} duration - Duration in milliseconds
     */
    show: function(message, type = 'info', duration = 3000) {
        // Remove any existing toasts first
        const existingContainer = document.getElementById('toast-container');
        if (existingContainer) {
            existingContainer.remove();
        }

        // Create new toast container
        const toastContainer = document.createElement('div');
        toastContainer.id = 'toast-container';
        toastContainer.className = 'fixed bottom-4 right-4 z-50 max-w-md';
        document.body.appendChild(toastContainer);

        // Create toast element with shadcn styling
        const toast = document.createElement('div');
        toast.className = `group pointer-events-auto relative flex w-full items-center justify-between space-x-4 overflow-hidden rounded-md border border-border p-4 pr-8 shadow-lg transition-all bg-background text-foreground`;

        // Determine icon and title based on type
        let icon, iconClass, title;
        switch (type) {
            case 'success':
                icon = 'check';
                iconClass = 'text-green-500';
                title = 'Success';
                break;
            case 'error':
                icon = 'x-circle';
                iconClass = 'text-red-500';
                title = 'Error';
                break;
            case 'warning':
                icon = 'alert-triangle';
                iconClass = 'text-yellow-500';
                title = 'Warning';
                break;
            case 'info':
            default:
                icon = 'info';
                iconClass = 'text-blue-500';
                title = 'Info';
                break;
        }

        // Create toast content with shadcn styling
        toast.innerHTML = `
            <div class="grid gap-1">
                <div class="flex items-center gap-2">
                    <div class="icon-container ${iconClass}" style="width: 16px; height: 16px;"></div>
                    <span class="text-sm font-semibold">${title}</span>
                </div>
                <div class="text-sm opacity-90">${message}</div>
            </div>
            <button class="absolute right-2 top-2 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-2 group-hover:opacity-100" data-dismiss="toast">
                <div class="close-icon-container" style="width: 16px; height: 16px;"></div>
                <span class="sr-only">Close</span>
            </button>
        `;

        // Add to container
        toastContainer.appendChild(toast);

        // Create and add SVG icons manually
        const iconContainer = toast.querySelector('.icon-container');
        const closeIconContainer = toast.querySelector('.close-icon-container');

        if (iconContainer) {
            let svgContent = '';

            // Set SVG based on type
            switch (type) {
                case 'success':
                    svgContent = '<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="20 6 9 17 4 12"></polyline></svg>';
                    break;
                case 'error':
                    svgContent = '<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"></circle><line x1="15" y1="9" x2="9" y2="15"></line><line x1="9" y1="9" x2="15" y2="15"></line></svg>';
                    break;
                case 'warning':
                    svgContent = '<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"></path><line x1="12" y1="9" x2="12" y2="13"></line><line x1="12" y1="17" x2="12.01" y2="17"></line></svg>';
                    break;
                case 'info':
                default:
                    svgContent = '<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"></circle><line x1="12" y1="16" x2="12" y2="12"></line><line x1="12" y1="8" x2="12.01" y2="8"></line></svg>';
                    break;
            }

            iconContainer.innerHTML = svgContent;
        }

        // Add close icon
        if (closeIconContainer) {
            closeIconContainer.innerHTML = '<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><line x1="18" y1="6" x2="6" y2="18"></line><line x1="6" y1="6" x2="18" y2="18"></line></svg>';
        }

        // Add click event to close button
        const closeBtn = toast.querySelector('[data-dismiss="toast"]');
        if (closeBtn) {
            closeBtn.addEventListener('click', () => {
                toast.classList.add('opacity-0', 'translate-y-2');
                setTimeout(() => {
                    toastContainer.remove();
                }, 300);
            });
        }

        // Auto-remove after specified duration
        setTimeout(() => {
            toast.classList.add('opacity-0', 'translate-y-2');
            setTimeout(() => {
                toastContainer.remove();
            }, 300);
        }, duration);
    },

    /**
     * Show a success toast
     * @param {string} message - The message to display
     * @param {number} duration - Duration in milliseconds
     */
    success: function(message, duration = 3000) {
        this.show(message, 'success', duration);
    },

    /**
     * Show an error toast
     * @param {string} message - The message to display
     * @param {number} duration - Duration in milliseconds
     */
    error: function(message, duration = 3000) {
        this.show(message, 'error', duration);
    },

    /**
     * Show an info toast
     * @param {string} message - The message to display
     * @param {number} duration - Duration in milliseconds
     */
    info: function(message, duration = 3000) {
        this.show(message, 'info', duration);
    },

    /**
     * Show a warning toast
     * @param {string} message - The message to display
     * @param {number} duration - Duration in milliseconds
     */
    warning: function(message, duration = 3000) {
        this.show(message, 'warning', duration);
    }
};

// Add CSS for toast animations and theme styles
document.addEventListener('DOMContentLoaded', function() {
    // Create a style element if it doesn't exist
    let toastStyle = document.getElementById('toast-style');
    if (!toastStyle) {
        toastStyle = document.createElement('style');
        toastStyle.id = 'toast-style';
        toastStyle.textContent = `
            /* Toast notification styles - shadcn style */
            #toast-container {
                transition: all 0.3s ease;
            }

            #toast-container > div {
                transition: all 0.3s ease;
                animation: toastSlideIn 0.2s ease-out;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
                border-width: 1px;
                min-width: 300px;
            }

            #toast-container > div:hover {
                box-shadow: 0 5px 16px rgba(0, 0, 0, 0.15);
            }

            @keyframes toastSlideIn {
                from {
                    transform: translateY(10px);
                    opacity: 0;
                }
                to {
                    transform: translateY(0);
                    opacity: 1;
                }
            }

            /* Ensure the close button is properly positioned */
            #toast-container button[data-dismiss="toast"] {
                top: 0.75rem;
                right: 0.75rem;
            }

            /* Ensure the toast has proper padding */
            #toast-container > div {
                padding: 1rem 2rem 1rem 1rem;
            }

            /* Icon colors */
            .text-green-500 svg {
                color: rgb(34, 197, 94);
            }

            .text-red-500 svg {
                color: rgb(239, 68, 68);
            }

            .text-yellow-500 svg {
                color: rgb(234, 179, 8);
            }

            .text-blue-500 svg {
                color: rgb(59, 130, 246);
            }

            /* Theme styles to ensure JS-applied styles take precedence */
            .btn-primary, .btn-blue {
                transition: background-color 0.2s, color 0.2s, border-color 0.2s, opacity 0.2s !important;
            }

            .theme-updated {
                /* This class is added and removed to force a style recalculation */
            }
        `;
        document.head.appendChild(toastStyle);
    }
});

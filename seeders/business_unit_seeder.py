"""
Business Unit seeder module.
This module provides functions to seed the database with business unit data.
"""

import os
import sys

def seed_business_units():
    """
    Seed the database with business unit data.

    Returns:
        list: The created business unit objects
    """
    # Import here to allow running as standalone script
    from app import db
    from app.models import BusinessUnit, BusinessSegment
    units = []

    # Check if Operations unit exists
    existing_ops = BusinessUnit.query.filter_by(code="OPS").first()
    if not existing_ops:
        # Create Operations unit
        unit1 = BusinessUnit(
            name="Operations",
            code="OPS",
            description="Operations department"
        )
        db.session.add(unit1)
        units.append(unit1)
    else:
        print("Operations business unit already exists, skipping...")
        units.append(existing_ops)

    # Check if Finance unit exists
    existing_fin = BusinessUnit.query.filter_by(code="FIN").first()
    if not existing_fin:
        # Create Finance unit
        unit2 = BusinessUnit(
            name="Finance",
            code="FIN",
            description="Finance department"
        )
        db.session.add(unit2)
        units.append(unit2)
    else:
        print("Finance business unit already exists, skipping...")
        units.append(existing_fin)

    # Commit if any new units were added
    if len(units) > 0:
        db.session.commit()
        print("Business units seeded successfully!")

    return units

def seed_business_segments():
    """
    Seed the database with business segment data.

    Returns:
        list: The created business segment objects
    """
    # Import here to allow running as standalone script
    from app import db
    from app.models import BusinessUnit, BusinessSegment
    segments = []

    # Get business units
    ops_unit = BusinessUnit.query.filter_by(code="OPS").first()
    fin_unit = BusinessUnit.query.filter_by(code="FIN").first()

    if ops_unit:
        # Check if Operations Management segment exists
        existing_ops_mgmt = BusinessSegment.query.filter_by(code="OPS-MGMT").first()
        if not existing_ops_mgmt:
            # Create Operations Management segment
            segment1 = BusinessSegment(
                name="Operations Management",
                code="OPS-MGMT",
                description="Operations Management segment",
                business_unit_id=ops_unit.id
            )
            db.session.add(segment1)
            segments.append(segment1)
        else:
            print("Operations Management segment already exists, skipping...")
            segments.append(existing_ops_mgmt)

    if fin_unit:
        # Check if Finance Management segment exists
        existing_fin_mgmt = BusinessSegment.query.filter_by(code="FIN-MGMT").first()
        if not existing_fin_mgmt:
            # Create Finance Management segment
            segment2 = BusinessSegment(
                name="Finance Management",
                code="FIN-MGMT",
                description="Finance Management segment",
                business_unit_id=fin_unit.id
            )
            db.session.add(segment2)
            segments.append(segment2)
        else:
            print("Finance Management segment already exists, skipping...")
            segments.append(existing_fin_mgmt)

    # Commit if any new segments were added
    if len(segments) > 0:
        db.session.commit()
        print("Business segments seeded successfully!")

    return segments

def seed_business():
    """
    Seed all business-related data.

    Returns:
        dict: Dictionary containing the created business units and segments
    """
    units = seed_business_units()
    segments = seed_business_segments()

    return {
        'units': units,
        'segments': segments
    }

if __name__ == "__main__":
    import os
    import sys
    # Add the parent directory to the Python path
    sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))
    from app import create_app
    app = create_app()
    with app.app_context():
        seed_business()

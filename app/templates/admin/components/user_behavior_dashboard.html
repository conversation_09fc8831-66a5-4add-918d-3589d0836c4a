{% macro user_behavior_dashboard(active_users, total_users, avg_sessions, avg_pages_per_session, retention_rate, user_behavior_data=None) %}
<div class="analytics-card bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 shadow-lg overflow-hidden transition-all duration-200 h-full flex flex-col">
    <div class="flex flex-col sm:flex-row sm:items-center justify-between p-5 md:p-6 border-b border-gray-200 dark:border-gray-700 bg-gray-50/80 dark:bg-gray-800/80 analytics-card-header">
        <div>
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white flex items-center analytics-text">
                <i data-lucide="user-cog" class="w-5 h-5 text-blue-600 dark:text-blue-400 mr-2"></i>
                <span>User Behavior & Engagement</span>
            </h3>
            <p class="text-sm text-gray-600 dark:text-gray-400 mt-1 analytics-text">Insights into user interaction patterns</p>
        </div>
        <div class="flex items-center space-x-2 mt-3 sm:mt-0">
            <button class="px-3 py-1.5 text-sm rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500 flex items-center shadow-sm transition-colors" id="refresh-behavior-btn">
                <i data-lucide="refresh-cw" class="w-3.5 h-3.5 mr-1.5"></i>
                <span>Refresh</span>
            </button>
        </div>
    </div>
    <div class="p-5 md:p-6 analytics-card-content flex-grow">
        <!-- Main Grid - 3x2 Layout -->
        <div class="grid grid-cols-3 gap-4 mb-4">
            <!-- Pages/Session Card -->
            <div class="bg-gray-50 dark:bg-gray-800/60 rounded-lg p-4 border border-gray-200 dark:border-gray-700 shadow-sm hover:shadow-md transition-all duration-200">
                <div class="flex items-center justify-between mb-1">
                    <p class="text-sm font-medium text-gray-700 dark:text-gray-300 analytics-text">Pages/Session</p>
                    <div class="w-8 h-8 rounded-full bg-indigo-100 dark:bg-indigo-900/30 flex items-center justify-center">
                        <i data-lucide="file-bar-chart" class="w-4 h-4 text-indigo-600 dark:text-indigo-400"></i>
                    </div>
                </div>
                <p class="text-2xl font-bold text-gray-900 dark:text-white analytics-text">{{ avg_pages_per_session }}</p>
                <p class="text-xs text-gray-500 dark:text-gray-400 mt-1 analytics-text">activities per day</p>
            </div>

            <!-- Most Active Time -->
            <div class="bg-gray-50 dark:bg-gray-800/60 rounded-lg p-4 border border-gray-200 dark:border-gray-700 shadow-sm hover:shadow-md transition-all duration-200">
                <div class="flex items-center justify-between mb-1">
                    <p class="text-sm font-medium text-gray-700 dark:text-gray-300 analytics-text">Most Active Time</p>
                    <div class="w-8 h-8 rounded-full bg-blue-100 dark:bg-blue-900/30 flex items-center justify-center">
                        <i data-lucide="clock" class="w-4 h-4 text-blue-600 dark:text-blue-400"></i>
                    </div>
                </div>
                <p class="text-2xl font-bold text-gray-900 dark:text-white analytics-text">{{ user_behavior_data.most_active_time if user_behavior_data else '11 PM' }}</p>
                <p class="text-xs text-gray-500 dark:text-gray-400 mt-1 analytics-text">peak activity period</p>
            </div>

            <!-- Average Activities Per User -->
            <div class="bg-gray-50 dark:bg-gray-800/60 rounded-lg p-4 border border-gray-200 dark:border-gray-700 shadow-sm hover:shadow-md transition-all duration-200">
                <div class="flex items-center justify-between mb-1">
                    <p class="text-sm font-medium text-gray-700 dark:text-gray-300 analytics-text">Avg. Activities Per User</p>
                    <div class="w-8 h-8 rounded-full bg-purple-100 dark:bg-purple-900/30 flex items-center justify-center">
                        <i data-lucide="activity" class="w-4 h-4 text-purple-600 dark:text-purple-400"></i>
                    </div>
                </div>
                <p class="text-2xl font-bold text-gray-900 dark:text-white analytics-text">{{ user_behavior_data.avg_activities_per_user if user_behavior_data else '56.0' }}</p>
                <p class="text-xs text-gray-500 dark:text-gray-400 mt-1 analytics-text">higher is better</p>
            </div>

            <!-- Retention Card -->
            <div class="bg-gray-50 dark:bg-gray-800/60 rounded-lg p-4 border border-gray-200 dark:border-gray-700 shadow-sm hover:shadow-md transition-all duration-200">
                <div class="flex items-center justify-between mb-1">
                    <p class="text-sm font-medium text-gray-700 dark:text-gray-300 analytics-text">Retention</p>
                    <div class="w-8 h-8 rounded-full bg-green-100 dark:bg-green-900/30 flex items-center justify-center">
                        <i data-lucide="repeat" class="w-4 h-4 text-green-600 dark:text-green-400"></i>
                    </div>
                </div>
                <p class="text-2xl font-bold text-gray-900 dark:text-white analytics-text">{{ retention_rate }}%</p>
                <p class="text-xs text-gray-500 dark:text-gray-400 mt-1 analytics-text">return users</p>
            </div>

            <!-- Active Sessions -->
            <div class="bg-gray-50 dark:bg-gray-800/60 rounded-lg p-4 border border-gray-200 dark:border-gray-700 shadow-sm hover:shadow-md transition-all duration-200">
                <div class="flex items-center justify-between mb-1">
                    <p class="text-sm font-medium text-gray-700 dark:text-gray-300 analytics-text">Active Sessions</p>
                    <div class="w-8 h-8 rounded-full bg-blue-100 dark:bg-blue-900/30 flex items-center justify-center">
                        <i data-lucide="users" class="w-4 h-4 text-blue-600 dark:text-blue-400"></i>
                    </div>
                </div>
                <p class="text-2xl font-bold text-gray-900 dark:text-white analytics-text">{{ user_behavior_data.active_sessions if user_behavior_data else '64' }}</p>
                <p class="text-xs text-gray-500 dark:text-gray-400 mt-1 analytics-text">last 30 minutes</p>
            </div>

            <!-- Retention Health -->
            <div class="bg-gray-50 dark:bg-gray-800/60 rounded-lg p-4 border border-gray-200 dark:border-gray-700 shadow-sm hover:shadow-md transition-all duration-200">
                <div class="flex items-center justify-between mb-1">
                    <p class="text-sm font-medium text-gray-700 dark:text-gray-300 analytics-text">Retention Health</p>
                    <div class="w-8 h-8 rounded-full bg-green-100 dark:bg-green-900/30 flex items-center justify-center">
                        <i data-lucide="heart-pulse" class="w-4 h-4 text-green-600 dark:text-green-400"></i>
                    </div>
                </div>
                {% set retention_health = 'Excellent' if retention_rate > 75 else 'Good' if retention_rate > 50 else 'Needs Improvement' if retention_rate > 25 else 'Poor' %}
                <p class="text-2xl font-bold text-gray-900 dark:text-white analytics-text">{{ retention_health }}</p>
                <p class="text-xs text-gray-500 dark:text-gray-400 mt-1 analytics-text">health status</p>
            </div>
        </div>

        <!-- Bottom section: Most Common Actions -->
        <div class="bg-gray-50 dark:bg-gray-800/60 rounded-lg p-4 border border-gray-200 dark:border-gray-700 shadow-sm">
            <div class="flex items-center justify-between mb-3">
                <p class="text-sm font-medium text-gray-700 dark:text-gray-300 analytics-text">Most Common Actions</p>
                <div class="w-8 h-8 rounded-full bg-blue-100 dark:bg-blue-900/30 flex items-center justify-center">
                    <i data-lucide="list" class="w-4 h-4 text-blue-600 dark:text-blue-400"></i>
                </div>
            </div>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-2">
                {% if user_behavior_data and user_behavior_data.top_actions %}
                    {% for action in user_behavior_data.top_actions[:3] %}
                    <div class="flex items-center py-1 px-2 bg-white dark:bg-gray-700/50 rounded border border-gray-200 dark:border-gray-700">
                        <div class="w-2 h-2 rounded-full bg-blue-500 dark:bg-blue-400 mr-2 flex-shrink-0"></div>
                        <p class="text-sm text-gray-900 dark:text-white truncate analytics-text">{{ action }}</p>
                    </div>
                    {% endfor %}
                {% else %}
                    <div class="flex items-center py-1 px-2 bg-white dark:bg-gray-700/50 rounded border border-gray-200 dark:border-gray-700">
                        <div class="w-2 h-2 rounded-full bg-blue-500 dark:bg-blue-400 mr-2 flex-shrink-0"></div>
                        <p class="text-sm text-gray-900 dark:text-white truncate analytics-text">Viewed analytics</p>
                    </div>
                    <div class="flex items-center py-1 px-2 bg-white dark:bg-gray-700/50 rounded border border-gray-200 dark:border-gray-700">
                        <div class="w-2 h-2 rounded-full bg-blue-500 dark:bg-blue-400 mr-2 flex-shrink-0"></div>
                        <p class="text-sm text-gray-900 dark:text-white truncate analytics-text">Viewed Activity</p>
                    </div>
                    <div class="flex items-center py-1 px-2 bg-white dark:bg-gray-700/50 rounded border border-gray-200 dark:border-gray-700">
                        <div class="w-2 h-2 rounded-full bg-blue-500 dark:bg-blue-400 mr-2 flex-shrink-0"></div>
                        <p class="text-sm text-gray-900 dark:text-white truncate analytics-text">Viewed User</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endmacro %}

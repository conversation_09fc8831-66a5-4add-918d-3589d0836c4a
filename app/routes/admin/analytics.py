"""
Admin analytics routes.
"""

from flask import render_template, request, session
from flask_login import login_required
from datetime import datetime, timedelta
import json

from app import db
from app.models import User, Activity, PH_TZ, BusinessUnit, EmployeeDetail
from app.utils.decorators import admin_required, log_activity
from app.routes.admin import admin_bp


@admin_bp.route('/analytics')
@login_required
@admin_required
@log_activity('Viewed analytics', category=Activity.CATEGORY_ADMIN, method=Activity.METHOD_READ)
def analytics():
    """Display general analytics dashboard."""
    # Get period from URL parameter (default to 30 days)
    period_days = int(request.args.get('period', 30))

    # Get current date and time
    now = datetime.now(PH_TZ)
    period_start = now - timedelta(days=period_days)

    # Clear session data to force refresh
    if 'entity_labels' in session:
        del session['entity_labels']
    if 'entity_data' in session:
        del session['entity_data']

    # Get basic user metrics
    total_users = User.query.count()
    active_users = User.query.filter_by(_is_active=True, deleted_at=None).count()

    # Get visitor and page view metrics
    visitors = db.session.query(db.func.count(db.func.distinct(Activity.user_id)))\
        .filter(Activity.created_at >= period_start).scalar()

    page_views = Activity.query.filter(Activity.created_at >= period_start).count()

    # Calculate user engagement metrics
    engagement_metrics = _calculate_engagement_metrics(period_start, visitors)
    avg_sessions = engagement_metrics['avg_sessions']
    avg_pages_per_session = engagement_metrics['avg_pages_per_session']
    retention_rate = engagement_metrics['retention_rate']

    # Format for display
    avg_time = f"{avg_sessions} days"
    bounce_rate = f"{100 - retention_rate}%"

    # Registration data has been removed

    # Get department activity data
    department_data = _get_department_data(period_start)

    # Get activity chart data
    activity_dates, activity_counts, user_counts = _get_activity_chart_data(period_days, now)

    # Get user behavior metrics
    user_behavior_data = _get_user_behavior_metrics(period_start, now, visitors, page_views)

    # Get activity distribution
    activity_labels, activity_data = _get_activity_distribution()

    # Get entity data from session or regenerate if needed
    entity_labels = session.get('entity_labels', '[]')
    entity_data = session.get('entity_data', '[]')

    # If entity data is empty, force a refresh
    if entity_labels == '[]' or entity_data == '[]':
        # Force refresh from database
        db.session.expire_all()

        # Get distribution by entity type for the chart
        entity_distribution = db.session.query(
            Activity.entity_type,
            db.func.count(Activity.id)
        ).filter(Activity.entity_type != None)\
         .group_by(Activity.entity_type)\
         .order_by(db.func.count(Activity.id).desc())\
         .limit(5)\
         .all()

        # Format entity types to be more readable
        entity_labels_list = []
        entity_data_list = []

        for entity_type, count in entity_distribution:
            # Format entity type names
            if entity_type:
                entity_labels_list.append(entity_type)
                entity_data_list.append(count)
            else:
                entity_labels_list.append('Other')
                entity_data_list.append(count)

        # Update session data
        entity_labels = json.dumps(entity_labels_list)
        entity_data = json.dumps(entity_data_list)
        session['entity_labels'] = entity_labels
        session['entity_data'] = entity_data

    return render_template('admin/analytics.html',
                          active_page='analytics',
                          total_users=total_users,
                          active_users=active_users,
                          visitors=visitors,
                          page_views=page_views,
                          avg_time=avg_time,
                          bounce_rate=bounce_rate,
                          avg_sessions=avg_sessions,
                          avg_pages_per_session=avg_pages_per_session,
                          retention_rate=retention_rate,
                          activity_labels=json.dumps(activity_labels),
                          activity_data=json.dumps(activity_data),
                          entity_labels=entity_labels,
                          entity_data=entity_data,
                          department_data=department_data,
                          activity_dates=json.dumps(activity_dates),
                          activity_counts=json.dumps(activity_counts),
                          user_counts=json.dumps(user_counts),
                          user_behavior_data=user_behavior_data,
                          now=now)


def _calculate_engagement_metrics(period_start, visitors):
    """Calculate user engagement metrics."""
    # 1. Calculate average sessions per user
    user_sessions = db.session.query(
        Activity.user_id,
        db.func.count(db.func.distinct(db.func.date(Activity.created_at)))
    ).filter(Activity.created_at >= period_start)\
     .group_by(Activity.user_id)\
     .all()

    total_sessions = sum(count for _, count in user_sessions)
    avg_sessions = round(total_sessions / len(user_sessions), 1) if user_sessions else 0

    # 2. Calculate pages per session
    daily_user_activities = db.session.query(
        Activity.user_id,
        db.func.date(Activity.created_at),
        db.func.count(Activity.id)
    ).filter(Activity.created_at >= period_start)\
     .group_by(Activity.user_id, db.func.date(Activity.created_at))\
     .all()

    total_daily_activities = sum(count for _, _, count in daily_user_activities)
    avg_pages_per_session = round(total_daily_activities / total_sessions, 1) if total_sessions > 0 else 0

    # 3. Calculate retention rate
    users_with_multiple_days = db.session.query(Activity.user_id)\
        .filter(Activity.created_at >= period_start)\
        .group_by(Activity.user_id)\
        .having(db.func.count(db.func.distinct(db.func.date(Activity.created_at))) > 1)\
        .count()

    retention_rate = round((users_with_multiple_days / visitors) * 100) if visitors > 0 else 0

    return {
        'avg_sessions': avg_sessions,
        'avg_pages_per_session': avg_pages_per_session,
        'retention_rate': retention_rate
    }


def _get_department_data(period_start):
    """Get department activity data."""
    # Get activity by department
    department_activity = db.session.query(
        BusinessUnit.name,
        db.func.count(Activity.id)
    ).join(EmployeeDetail, EmployeeDetail.business_unit_id == BusinessUnit.id)\
     .join(User, User.id == EmployeeDetail.user_id)\
     .join(Activity, Activity.user_id == User.id)\
     .filter(Activity.created_at >= period_start)\
     .group_by(BusinessUnit.name)\
     .order_by(db.func.count(Activity.id).desc())\
     .all()

    # Get active users by department
    department_users = db.session.query(
        BusinessUnit.name,
        db.func.count(db.func.distinct(Activity.user_id))
    ).join(EmployeeDetail, EmployeeDetail.business_unit_id == BusinessUnit.id)\
     .join(User, User.id == EmployeeDetail.user_id)\
     .join(Activity, Activity.user_id == User.id)\
     .filter(Activity.created_at >= period_start)\
     .group_by(BusinessUnit.name)\
     .order_by(db.func.count(db.func.distinct(Activity.user_id)).desc())\
     .all()

    # Combine department activity and users
    departments = {}
    for dept, count in department_activity:
        if dept not in departments:
            departments[dept] = {'activity': 0, 'users': 0}
        departments[dept]['activity'] = count

    for dept, count in department_users:
        if dept not in departments:
            departments[dept] = {'activity': 0, 'users': 0}
        departments[dept]['users'] = count

    # Format for display
    department_data = []
    total_activity = sum(dept['activity'] for dept in departments.values())

    for dept_name, data in departments.items():
        percentage = f"{round((data['activity'] / total_activity) * 100)}%" if total_activity > 0 else "0%"
        department_data.append({
            'name': dept_name,
            'activity': data['activity'],
            'users': data['users'],
            'percentage': percentage
        })

    # Sort by activity count
    department_data.sort(key=lambda x: x['activity'], reverse=True)

    return department_data


def _get_activity_chart_data(period_days, now):
    """Get activity chart data for the specified period."""
    # Get all activities
    all_activities = Activity.query.all()

    # Group activities by date
    activity_by_date = {}
    user_by_date = {}

    for activity in all_activities:
        if activity.created_at:
            # Use the exact date from the activity
            date_str = activity.created_at.strftime('%Y-%m-%d')

            if date_str not in activity_by_date:
                activity_by_date[date_str] = 0
                user_by_date[date_str] = set()

            activity_by_date[date_str] += 1
            if activity.user_id:
                user_by_date[date_str].add(activity.user_id)

    # Create a date range that includes the activity dates
    # Default date range (fallback)
    activity_dates = [(now - timedelta(days=i)).strftime('%Y-%m-%d')
                     for i in range(period_days, 0, -1)]

    if activity_by_date:
        # Find the min and max dates from the activities
        date_keys = list(activity_by_date.keys())
        date_keys.sort()  # Sort dates

        if date_keys:  # If we have dates
            min_date_str = date_keys[0]

            # Parse the dates
            from datetime import datetime
            min_date = datetime.strptime(min_date_str, '%Y-%m-%d')

            # Create a date range centered on the activity dates
            # Start from the min date and go forward period_days days
            activity_dates = [(min_date + timedelta(days=i)).strftime('%Y-%m-%d')
                             for i in range(period_days)]

    # Initialize arrays with zeros
    activity_counts = [0] * len(activity_dates)
    user_counts = [0] * len(activity_dates)

    # Map the activities to the chart dates
    for i, date_str in enumerate(activity_dates):
        if date_str in activity_by_date:
            activity_counts[i] = activity_by_date[date_str]
            user_counts[i] = len(user_by_date[date_str])

    # Return the data for the chart
    return activity_dates, activity_counts, user_counts

def _get_user_behavior_metrics(period_start, now, visitors, page_views):
    """Get user behavior metrics."""
    user_behavior = {}

    # 1. Most active time of day
    hour_distribution = db.session.query(
        db.func.extract('hour', Activity.created_at).label('hour'),
        db.func.count(Activity.id)
    ).filter(Activity.created_at >= period_start)\
     .group_by('hour')\
     .order_by(db.func.count(Activity.id).desc())\
     .first()

    most_active_hour = hour_distribution[0] if hour_distribution else 0
    # Convert to 12-hour format with AM/PM
    if most_active_hour == 0:
        most_active_time = "12 AM"
    elif most_active_hour < 12:
        most_active_time = f"{most_active_hour} AM"
    elif most_active_hour == 12:
        most_active_time = "12 PM"
    else:
        most_active_time = f"{most_active_hour - 12} PM"

    user_behavior['most_active_time'] = most_active_time

    # 2. Most common actions
    most_common_actions = db.session.query(
        Activity.action,
        db.func.count(Activity.id)
    ).filter(Activity.created_at >= period_start)\
     .group_by(Activity.action)\
     .order_by(db.func.count(Activity.id).desc())\
     .limit(3)\
     .all()

    user_behavior['top_actions'] = [action for action, _ in most_common_actions] if most_common_actions else ["No actions recorded"]

    # 3. User growth rate (comparing last half of period to first half)
    half_period = (now - period_start) / 2
    mid_point = now - half_period

    recent_users = db.session.query(db.func.count(db.func.distinct(Activity.user_id)))\
        .filter(Activity.created_at >= mid_point).scalar()

    previous_users = db.session.query(db.func.count(db.func.distinct(Activity.user_id)))\
        .filter(Activity.created_at >= period_start, Activity.created_at < mid_point).scalar()

    if previous_users > 0:
        growth_rate = ((recent_users - previous_users) / previous_users) * 100
    else:
        growth_rate = 100 if recent_users > 0 else 0

    user_behavior['growth_rate'] = round(growth_rate, 1)

    # 4. Active sessions (users active in the last 30 minutes)
    user_behavior['active_sessions'] = Activity.query.filter(
        Activity.created_at >= now - timedelta(minutes=30)
    ).count()

    # 5. Average activities per user
    if visitors > 0:
        avg_activities_per_user = page_views / visitors
    else:
        avg_activities_per_user = 0

    user_behavior['avg_activities_per_user'] = round(avg_activities_per_user, 1)

    return user_behavior


def _get_activity_distribution():
    """Get activity distribution data."""
    # Force refresh from database
    db.session.expire_all()

    # Get distribution by action (original behavior)
    activity_distribution = db.session.query(
        Activity.action,
        db.func.count(Activity.id)
    ).group_by(Activity.action)\
     .order_by(db.func.count(Activity.id).desc())\
     .limit(5)\
     .all()

    activity_labels = [activity[0] for activity in activity_distribution]
    activity_data = [activity[1] for activity in activity_distribution]

    # Get distribution by entity type for the chart
    entity_distribution = db.session.query(
        Activity.entity_type,
        db.func.count(Activity.id)
    ).filter(Activity.entity_type != None)\
     .group_by(Activity.entity_type)\
     .order_by(db.func.count(Activity.id).desc())\
     .limit(5)\
     .all()

    # Format entity types to be more readable
    entity_labels = []
    entity_data = []

    for entity_type, count in entity_distribution:
        # Format entity type names
        if entity_type:
            entity_labels.append(entity_type)
            entity_data.append(count)
        else:
            entity_labels.append('Other')
            entity_data.append(count)

    # Store entity data in session for the chart
    session['entity_labels'] = json.dumps(entity_labels)
    session['entity_data'] = json.dumps(entity_data)

    return activity_labels, activity_data


# Activity analytics route has been removed

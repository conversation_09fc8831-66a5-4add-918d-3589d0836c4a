/**
 * Drawer Component
 *
 * This script is kept for backward compatibility.
 * It imports and re-exports the functionality from the modular drawer package.
 */

// Import from modular structure
import { DrawerComponent, DrawerManager } from './drawer/index.js';
import { loadForm, initFormValidation, validateForm } from './drawer/form-handler.js';
import { trapFocus, createLoadingSpinner, createErrorMessage } from './drawer/utils.js';

// Create global instances
window.DrawerComponent = DrawerComponent;
window.drawerManager = new DrawerManager();

// Make these functions globally available for backward compatibility
window.loadForm = loadForm;
window.initFormValidation = initFormValidation;
window.validateForm = validateForm;

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
  // Initialize drawer triggers
  document.querySelectorAll('[data-drawer-trigger]').forEach(trigger => {
    trigger.addEventListener('click', function(e) {
      e.preventDefault();
      
      const formType = this.dataset.drawerTrigger;
      const entityId = this.dataset.entityId;
      const position = this.dataset.drawerPosition || 'right';
      const size = this.dataset.drawerSize || 'md';
      
      window.drawerManager.openForm(formType, entityId, {
        position: position,
        size: size
      });
    });
  });
});

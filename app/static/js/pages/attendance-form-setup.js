// Initialize form URLs for attendance types
document.addEventListener('DOMContentLoaded', function() {
  // Check if drawerManager exists
  if (!window.drawerManager) {
    console.error('DrawerManager not found');
    return;
  }

  // Register form URLs for attendance types
  window.drawerManager.registerFormType('attendance_type', {
    createUrl: '/admin/attendance/types/form',
    editUrl: '/admin/attendance/types/form/{id}',
    size: 'md',
    position: 'right'
  });
});

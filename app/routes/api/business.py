"""
API routes for business units and segments.
"""

from flask import jsonify, request, current_app
from flask_login import login_required
from sqlalchemy import text, func

from app import db, cache
from app.models import BusinessUnit, BusinessSegment, User, EmployeeDetail
from app.routes.api import api_bp
from app.routes.api.utils import handle_api_error
from app.routes.api.activities import APIError


@api_bp.route('/business-units', methods=['GET'])
@login_required
@handle_api_error
@cache.cached(timeout=60, key_prefix='business_units')
def get_business_units():
    """Get all business units."""
    current_app.logger.info("Fetching business units (may be cached)")
    is_active = request.args.get('is_active')

    # Base query
    query = BusinessUnit.query

    # Apply filter if provided with validation
    if is_active is not None:
        # Validate is_active parameter
        if isinstance(is_active, str) and is_active.lower() in ['true', 'false']:
            is_active_bool = is_active.lower() == 'true'
            query = query.filter(BusinessUnit.is_active == is_active_bool)

    # Get all business units
    business_units = query.all()

    # Get employee counts in a single query to avoid N+1 problem
    unit_ids = [unit.id for unit in business_units]
    employee_counts = {}
    segment_counts = {}

    if unit_ids:
        # Get employee counts
        employee_count_query = db.session.query(
            EmployeeDetail.business_unit_id,
            db.func.count(EmployeeDetail.id)
        ).filter(EmployeeDetail.business_unit_id.in_(unit_ids))\
         .group_by(EmployeeDetail.business_unit_id).all()

        employee_counts = {unit_id: count for unit_id, count in employee_count_query}

        # Get segment counts
        segment_count_query = db.session.query(
            BusinessSegment.business_unit_id,
            db.func.count(BusinessSegment.id)
        ).filter(BusinessSegment.business_unit_id.in_(unit_ids))\
         .group_by(BusinessSegment.business_unit_id).all()

        segment_counts = {unit_id: count for unit_id, count in segment_count_query}

    # Format results
    result = []
    for unit in business_units:
        result.append({
            'id': unit.id,
            'name': unit.name,
            'code': unit.code,
            'description': unit.description,
            'manager_id': unit.manager_id,
            'manager': unit.manager.name if unit.manager else None,
            'is_active': unit.is_active,
            'employee_count': employee_counts.get(unit.id, 0),
            'segment_count': segment_counts.get(unit.id, 0),
            'created_at': unit.created_at.isoformat()
        })

    return jsonify({
        'success': True,
        'count': len(result),
        'business_units': result
    })


@api_bp.route('/business-units/<int:unit_id>/segments', methods=['GET'])
@login_required
@handle_api_error
def get_unit_segments(unit_id):
    """Get all segments for a specific business unit."""
    unit = BusinessUnit.query.get_or_404(unit_id)

    # Optional filtering by active status
    is_active = request.args.get('is_active')

    if is_active is not None:
        is_active_bool = is_active.lower() == 'true'
        segments = unit.segments.filter_by(is_active=is_active_bool).all()
    else:
        segments = unit.segments.all()

    return jsonify({
        'success': True,
        'count': len(segments),
        'business_unit': unit.name,
        'segments': [{
            'id': segment.id,
            'name': segment.name,
            'code': segment.code,
            'is_active': segment.is_active
        } for segment in segments]
    })


@api_bp.route('/business-segments', methods=['GET'])
@login_required
@handle_api_error
@cache.cached(timeout=60, key_prefix='business_segments')
def get_business_segments():
    """Get all business segments."""
    current_app.logger.info("Fetching business segments (may be cached)")
    is_active = request.args.get('is_active')
    business_unit_id = request.args.get('business_unit_id', type=int)

    # Base query
    query = BusinessSegment.query

    # Apply filters if provided with validation
    if is_active is not None:
        # Validate is_active parameter
        if isinstance(is_active, str) and is_active.lower() in ['true', 'false']:
            is_active_bool = is_active.lower() == 'true'
            query = query.filter(BusinessSegment.is_active == is_active_bool)

    if business_unit_id:
        # Validate business_unit_id is a positive integer
        if isinstance(business_unit_id, int) and business_unit_id > 0:
            query = query.filter(BusinessSegment.business_unit_id == business_unit_id)

    # Get all business segments
    business_segments = query.all()

    # Get employee counts in a single query to avoid N+1 problem
    segment_ids = [segment.id for segment in business_segments]
    employee_counts = {}

    if segment_ids:
        # Get employee counts
        employee_count_query = db.session.query(
            EmployeeDetail.business_segment_id,
            db.func.count(EmployeeDetail.id)
        ).filter(EmployeeDetail.business_segment_id.in_(segment_ids))\
         .group_by(EmployeeDetail.business_segment_id).all()

        employee_counts = {segment_id: count for segment_id, count in employee_count_query}

    # Format results
    result = []
    for segment in business_segments:
        result.append({
            'id': segment.id,
            'name': segment.name,
            'code': segment.code,
            'description': segment.description,
            'business_unit_id': segment.business_unit_id,
            'business_unit': segment.business_unit.name,
            'manager_id': segment.manager_id,
            'manager': segment.manager.name if segment.manager else None,
            'is_active': segment.is_active,
            'employee_count': employee_counts.get(segment.id, 0),
            'created_at': segment.created_at.isoformat()
        })

    return jsonify({
        'success': True,
        'count': len(result),
        'business_segments': result
    })

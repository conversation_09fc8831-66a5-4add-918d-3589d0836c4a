{% macro key_metrics(total_users, active_users, visitors, page_views, avg_time, bounce_rate) %}
<div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-5 md:gap-6 analytics-grid-sm">
    <!-- Total Users -->
    <div class="analytics-card analytics-stat-card bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 shadow-md hover:shadow-lg transition-all duration-200 overflow-hidden">
        <div class="p-5 md:p-6 bg-gradient-to-br from-blue-50/30 via-blue-50/20 to-transparent dark:from-blue-900/10 dark:via-blue-900/5 dark:to-transparent">
            <div class="flex justify-between items-start">
                <div>
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400 analytics-text">Total Users</p>
                    <div class="flex items-baseline gap-2 mt-2">
                        <p class="text-3xl font-bold text-gray-900 dark:text-white analytics-text">{{ total_users }}</p>
                    </div>
                    <p class="text-xs text-gray-500 dark:text-gray-400 mt-2 analytics-text">Registered accounts</p>
                </div>
                <div class="rounded-full p-3 bg-blue-100 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400 shadow-sm">
                    <i data-lucide="users" class="w-6 h-6"></i>
                </div>
            </div>
        </div>
    </div>

    <!-- Active Users -->
    <div class="analytics-card analytics-stat-card bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 shadow-md hover:shadow-lg transition-all duration-200 overflow-hidden">
        <div class="p-5 md:p-6 bg-gradient-to-br from-green-50/30 via-green-50/20 to-transparent dark:from-green-900/10 dark:via-green-900/5 dark:to-transparent">
            <div class="flex justify-between items-start">
                <div>
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400 analytics-text">Active Users</p>
                    <div class="flex items-baseline gap-2 mt-2">
                        <p class="text-3xl font-bold text-gray-900 dark:text-white analytics-text">{{ active_users }}</p>
                    </div>
                    <p class="text-xs text-gray-500 dark:text-gray-400 mt-2 analytics-text">Enabled accounts</p>
                </div>
                <div class="rounded-full p-3 bg-green-100 dark:bg-green-900/20 text-green-600 dark:text-green-400 shadow-sm">
                    <i data-lucide="user-check" class="w-6 h-6"></i>
                </div>
            </div>
        </div>
    </div>

    <!-- Page Views -->
    <div class="analytics-card analytics-stat-card bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 shadow-md hover:shadow-lg transition-all duration-200 overflow-hidden">
        <div class="p-5 md:p-6 bg-gradient-to-br from-indigo-50/30 via-indigo-50/20 to-transparent dark:from-indigo-900/10 dark:via-indigo-900/5 dark:to-transparent">
            <div class="flex justify-between items-start">
                <div>
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400 analytics-text">Page Views</p>
                    <div class="flex items-baseline gap-2 mt-2">
                        <p class="text-3xl font-bold text-gray-900 dark:text-white analytics-text">{{ page_views }}</p>
                        <span class="text-xs px-1.5 py-0.5 rounded-full bg-indigo-100 text-indigo-800 dark:bg-indigo-900/30 dark:text-indigo-400 border border-indigo-200 dark:border-indigo-800/30">+8%</span>
                    </div>
                    <p class="text-xs text-gray-500 dark:text-gray-400 mt-2 analytics-text">Total activity count</p>
                </div>
                <div class="rounded-full p-3 bg-indigo-100 dark:bg-indigo-900/20 text-indigo-600 dark:text-indigo-400 shadow-sm">
                    <i data-lucide="activity" class="w-6 h-6"></i>
                </div>
            </div>
        </div>
    </div>

    <!-- System Health -->
    <div class="analytics-card analytics-stat-card bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 shadow-md hover:shadow-lg transition-all duration-200 overflow-hidden">
        <div class="p-5 md:p-6 bg-gradient-to-br from-emerald-50/30 via-emerald-50/20 to-transparent dark:from-blue-900/10 dark:via-blue-900/5 dark:to-transparent">
            <div class="flex justify-between items-start">
                <div>
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400 analytics-text">System Health</p>
                    <div class="flex items-baseline gap-2 mt-2">
                        <p class="text-3xl font-bold text-gray-900 dark:text-white analytics-text system-health-value">Excellent</p>
                    </div>
                    <p class="text-xs text-gray-500 dark:text-gray-400 mt-2 analytics-text">Based on system logs</p>
                </div>
                <div class="rounded-full p-3 bg-emerald-100 dark:bg-blue-900/20 text-emerald-600 dark:text-blue-400 shadow-sm">
                    <i data-lucide="activity-square" class="w-6 h-6"></i>
                </div>
            </div>
        </div>
    </div>
</div>
{% endmacro %}

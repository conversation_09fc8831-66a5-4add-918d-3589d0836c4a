{% from "partials/forms/base_form.html" import form_group, form_actions, form_header %}

<form action="{{ action_url }}" method="POST" class="space-y-4">
  <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">

  {{ form_header(
    title = "Edit Business Segment" if entity else "Add Business Segment",
    description = "Update business segment details" if entity else "Create a new business segment"
  ) }}

  <div class="grid grid-cols-1 gap-4">
    {{ form_group(
      label = "Name",
      name = "name",
      value = entity.name if entity else "",
      required = true,
      placeholder = "Enterprise Solutions"
    ) }}

    {{ form_group(
      label = "Code",
      name = "code",
      value = entity.code if entity else "",
      required = true,
      placeholder = "ENT"
    ) }}

    {{ form_group(
      label = "Business Unit",
      name = "business_unit_id",
      type = "select",
      value = entity.business_unit_id|string if entity else "",
      required = true,
      options = business_unit_options
    ) }}

    {{ form_group(
      label = "Description",
      name = "description",
      type = "textarea",
      value = entity.description if entity else "",
      placeholder = "Enter a description for this business segment"
    ) }}

    {% if entity %}
      {{ form_group(
        label = "Active",
        name = "is_active",
        type = "checkbox",
        value = entity.is_active if entity else true
      ) }}
    {% endif %}
  </div>

  {{ form_actions(
    submit_label = "Update Business Segment" if entity else "Add Business Segment"
  ) }}
</form>

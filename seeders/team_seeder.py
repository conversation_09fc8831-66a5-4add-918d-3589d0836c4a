"""
Team seeder module.
This module provides functions to seed the database with team data.
"""

import re
import os
import sys

def slugify(text):
    """
    Convert a string to a slug format.

    Args:
        text (str): The text to convert to slug

    Returns:
        str: The slugified text
    """
    # Convert to lowercase and replace spaces with hyphens
    text = text.lower()
    # Remove special characters
    text = re.sub(r'[^a-z0-9\s-]', '', text)
    # Replace spaces with hyphens
    text = re.sub(r'\s+', '-', text)
    # Remove multiple hyphens
    text = re.sub(r'-+', '-', text)
    # Remove leading/trailing hyphens
    text = text.strip('-')
    return text

def seed_teams():
    """
    Seed the database with team data.

    Returns:
        list: The created team objects
    """
    # Import here to allow running as standalone script
    from app import db
    from app.models import Team, TeamGroup
    teams_data = [
        {
            'name': 'Client Productivity and Collaboration Services',
            'short_name': 'CPCS',
            'description': 'Client Systems Client Productivity and Collaboration Services (CPCS) team',
            'groups': [
                {'name': 'EIT-EI-Cloud Collaboration-Run', 'description': 'The assignment group responsible for running the Client Productivity and Collaboration Services environment'},
                {'name': 'EIT-UC-Lync', 'description': 'The assignment group responsible for Lync'},
                {'name': 'EIT-UC-ZoomPhone', 'description': 'The assignment group responsible for Zoom Phones'},
            ],
        },
        {
            'name': 'myTechnology Anywhere',
            'short_name': 'MTA',
            'description': 'Client Systems myTechnology Anywhere (MTA) team',
            'groups': [
                {'name': 'EIT-myTechnology Anywhere - Service Delivery', 'description': 'The assignment group responsible for delivering myTechnology Anywhere services'},
            ],
        },
        {
            'name': 'Client Engineering',
            'short_name': 'CE',
            'description': 'Client Systems Client Engineering (CE) team for Windows and Mac',
            'groups': [
                {'name': 'EITSS-Client Engineering', 'description': 'The assignment group responsible for client engineering for Windows Devices'},
                {'name': 'EITSS-Special-Platforms-Engineering', 'description': 'The assignment group responsible for client engineering for Mac Devices'},
                {'name': 'SP-Cellular-Device', 'description': 'The assignment group responsible for cellular devices'},
                {'name': 'EIT-Application Management-Solution Engineering', 'description': 'The assignment group responsible for application deploment engineering'},
            ],
        },
        {
            'name': 'Enterprise IT Solution Delivery',
            'short_name': 'HRIT',
            'description': 'Client Systems Enterprise IT Solution Delivery (HRIT) team',
            'groups': [
                {'name': 'EIT-PS&S-Enterprise Solution Delivery', 'description': 'The assignment group responsible for delivering Enterprise IT Solution Delivery services'},
                {'name': 'EIT-PS&S-Enterprise Solution Delivery - Run Support', 'description': 'The assignment group responsible for delivering Enterprise IT Solution Delivery services run support'},
            ],
        },
    ]

    created_teams = []

    for team_data in teams_data:
        # Check if team already exists
        existing_team = Team.query.filter_by(name=team_data['name']).first()
        if existing_team:
            print(f"Team '{team_data['name']}' already exists, skipping...")
            created_teams.append(existing_team)
            continue

        # Create team
        team = Team(
            name=team_data['name'],
            short_name=team_data['short_name'],
            slug=slugify(team_data['name']),
            description=team_data['description'],
            is_active=True
        )
        db.session.add(team)
        db.session.flush()  # Flush to get the team ID

        # Create team groups
        for group_data in team_data['groups']:
            # Check if group already exists
            existing_group = TeamGroup.query.filter_by(team_id=team.id, name=group_data['name']).first()
            if existing_group:
                print(f"Team Group '{group_data['name']}' already exists for team '{team_data['name']}', skipping...")
                continue

            group = TeamGroup(
                team_id=team.id,
                name=group_data['name'],
                description=group_data['description'],
                is_active=True
            )
            db.session.add(group)

        created_teams.append(team)

    db.session.commit()
    print(f"Created {len(created_teams)} teams with their respective groups")
    return created_teams

if __name__ == "__main__":
    import os
    import sys
    # Add the parent directory to the Python path
    sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))
    from app import create_app
    app = create_app()
    with app.app_context():
        seed_teams()

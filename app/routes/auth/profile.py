"""
Authentication profile management routes.
"""

from flask import render_template, redirect, url_for, flash, request, current_app
from flask_login import login_required, current_user, fresh_login_required, logout_user

from app import db
from app.models import User, Activity, EmployeeDetail, WeakPasswordError
from app.utils.decorators import log_activity
from app.utils.request_info import get_client_info
from app.routes.auth import auth_bp


@auth_bp.route('/profile', methods=['GET', 'POST'])
@login_required
@log_activity('Viewed profile', entity_type='User', category=Activity.CATEGORY_USER, method=Activity.METHOD_READ, skip_ajax=True)
def profile():
    """Handle user profile view and updates."""
    if request.method == 'POST':
        # Get the form type to determine which tab was submitted
        form_type = request.form.get('form_type')

        # Handle personal info form
        if form_type == 'personal_info':
            name = request.form.get('name')
            bio = request.form.get('bio')
            timezone = request.form.get('timezone')

            if not name:
                flash('Name is required', 'error')
                return render_template('auth/profile.html', active_page='profile')

            current_user.name = name

            # Update user's bio and timezone directly
            current_user.bio = bio
            if timezone:
                current_user.timezone = timezone

        # Handle security form
        elif form_type == 'security':
            # Security form processing will be handled in the password change section below
            pass

        # Handle employee details form
        elif form_type == 'employee_details':
            # Employee details form processing
            pass

        # If no form type is specified, return an error
        else:
            flash('Invalid form submission', 'error')
            return render_template('auth/profile.html', active_page='profile')

        # Handle phone update based on form type
        if form_type == 'personal_info' or form_type == 'employee_details':
            # Get the employee detail if it exists
            employee_detail = current_user.employee_detail

            # Get phone information if it's a personal info form
            if form_type == 'personal_info':
                phone_code = request.form.get('phone_code', '+63')  # Default to Philippines code
                phone_number = request.form.get('phone', '')
                # Combine phone code and number if both are provided
                phone = f"{phone_code}{phone_number}" if phone_number else ''
            else:
                # For other forms, don't update phone
                phone = ''

            # Update employee details if they exist and phone is provided
            if employee_detail and phone:
                employee_detail.phone = phone

            # Create employee details if they don't exist but phone is provided
            elif not employee_detail and phone:
                employee_detail = EmployeeDetail(
                    user_id=current_user.id,
                    phone=phone
                )
                db.session.add(employee_detail)

        # Handle password change if provided (only for security tab)
        if form_type == 'security':
            current_password = request.form.get('current_password')
            new_password = request.form.get('new_password')
            confirm_password = request.form.get('confirm_password')

            if current_password and new_password:
                if not current_user.check_password(current_password):
                    flash('Current password is incorrect', 'error')
                    return render_template('auth/profile.html', active_page='profile')

                if new_password != confirm_password:
                    flash('New passwords do not match', 'error')
                    return render_template('auth/profile.html', active_page='profile')

                try:
                    current_user.set_password(new_password)
                    # Password success will be included in the overall profile update success message
                except WeakPasswordError as e:
                    flash(str(e), 'error')
                    return render_template('auth/profile.html', active_page='profile')

        try:
            db.session.commit()

            # Log activity
            client_info = get_client_info()
            Activity.log(
                user_id=current_user.id,
                action='Profile updated',
                entity_type='User',
                entity_id=current_user.id,
                ip_address=client_info['ip_address'],
                user_agent=client_info['user_agent'],
                details=f"Updated profile information and settings. Hostname: {client_info['hostname'] or 'Unknown'}",
                category=Activity.CATEGORY_DATA,
                method=Activity.METHOD_UPDATE,
                severity=Activity.SEVERITY_INFO
            )

            # Success message will be shown via toast notification using URL parameters
            return redirect(url_for('auth.profile', status='success', action='updated'))
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"Profile update error: {str(e)}")
            flash('An error occurred. Please try again.', 'error')

    # Get user's timezone from user model or set default
    user_timezone = current_user.timezone or 'Asia/Manila'

    # Get password information from user model
    last_password_date = current_user.last_password_date or current_user.created_at
    days_since_password_change = current_user.get_days_since_password_change()
    password_status = current_user.get_password_status()

    # Make sure employee details are loaded with teams relationship
    if current_user.employee_detail:
        db.session.refresh(current_user.employee_detail, ['teams'])

    return render_template('auth/profile.html',
                           active_page='profile',
                           user_timezone=user_timezone,
                           last_password_date=last_password_date,
                           days_since_password_change=days_since_password_change,
                           password_status=password_status)


@auth_bp.route('/api/profile-activities')
@login_required
def get_profile_activities():
    """API endpoint for user profile activities that filters out unrelated logs and system errors.
    This ensures users only see their own relevant activities."""
    try:
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 5, type=int)

        # Limit the maximum number of activities per page to prevent abuse
        per_page = min(per_page, 50)

        # Only show activities that:
        # 1. Belong to the current user
        # 2. Are not system errors
        # 3. Are relevant to the user (auth, user, data categories)

        query = Activity.query.filter(
            Activity.user_id == current_user.id,
            # Filter out system errors
            Activity.severity != Activity.SEVERITY_ERROR
        )

        # Only include relevant categories
        relevant_categories = [
            Activity.CATEGORY_AUTH,
            Activity.CATEGORY_USER,
            Activity.CATEGORY_DATA
        ]
        query = query.filter(Activity.category.in_(relevant_categories))

        # Order by most recent first
        query = query.order_by(Activity.created_at.desc())

        # Paginate results
        pagination = query.paginate(page=page, per_page=per_page)

        # Format activities for the response
        activities = []
        for activity in pagination.items:
            activities.append({
                'id': activity.id,
                'action': activity.action,
                'category': activity.category,
                'category_display': activity.get_category_display(),
                'severity': activity.severity,
                'details': activity.details,
                'created_at': activity.created_at.isoformat(),
                'user_id': activity.user_id,
                'user_name': activity.user.name if activity.user else None,
                'user_email': activity.user.email if activity.user else None,
                'entity_type': activity.entity_type,
                'entity_id': activity.entity_id,
                'ip_address': activity.ip_address,
                'user_agent': activity.user_agent,
                'has_changes': bool(activity.old_values),
                'old_values': activity.old_values,
                'new_values': activity.new_values,
                'method': activity.method,
                'method_display': activity.get_method_display()
            })

        return {
            'success': True,
            'activities': activities,
            'total': pagination.total,
            'pages': pagination.pages,
            'current_page': pagination.page
        }, 200

    except Exception as e:
        current_app.logger.error(f"Error fetching profile activities: {str(e)}")
        return {'success': False, 'message': 'An error occurred while fetching activities'}, 500

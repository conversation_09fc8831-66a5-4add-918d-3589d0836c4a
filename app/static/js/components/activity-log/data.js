/**
 * User Activity Log Component - Data Module
 *
 * This module handles data fetching and processing for the activity log component.
 */

/**
 * Load activities for a specific entity or user
 * 
 * @param {string} entityType - Optional entity type to filter by
 * @param {number} entityId - Optional entity ID to filter by
 * @param {number} limit - Number of activities to load
 * @param {boolean} inProfilePage - Whether this is being loaded in the profile page
 * @returns {Promise<Array>} - Promise resolving to activities array
 */
function loadActivities(entityType, entityId, limit, inProfilePage = false) {
    // Build the API URL with optional entity type filter
    // Use filtered endpoint for profile page activities to hide system errors and unrelated logs
    let apiUrl = inProfilePage
        ? `/auth/api/profile-activities?per_page=${limit}`
        : `/api/activities/user?per_page=${limit}`;

    // Add entity type filter if provided
    if (entityType) {
        apiUrl += `&entity_type=${entityType}`;
    }

    // Add entity id filter if provided
    if (entityId) {
        apiUrl += `&entity_id=${entityId}`;
    }

    // Fetch activities
    return fetch(apiUrl)
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! Status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            if (data.success && data.activities && data.activities.length > 0) {
                return data.activities;
            } else {
                return [];
            }
        });
}

/**
 * Load more activities for a specific entity or user
 * 
 * @param {string} entityType - Optional entity type to filter by
 * @param {number} entityId - Optional entity ID to filter by
 * @param {number} limit - Number of activities to load
 * @param {boolean} inProfilePage - Whether this is being loaded in the profile page
 * @param {number} page - Page number to load
 * @returns {Promise<Array>} - Promise resolving to activities array
 */
function loadMoreActivities(entityType, entityId, limit, inProfilePage = false, page = 2) {
    // Build the API URL with optional entity type filter
    // Use filtered endpoint for profile page activities to hide system errors and unrelated logs
    let apiUrl = inProfilePage
        ? `/auth/api/profile-activities?per_page=${limit}&page=${page}`
        : `/api/activities/user?per_page=${limit}&page=${page}`;

    // Add entity type filter if provided
    if (entityType) {
        apiUrl += `&entity_type=${entityType}`;
    }

    // Add entity id filter if provided
    if (entityId) {
        apiUrl += `&entity_id=${entityId}`;
    }

    // Fetch activities
    return fetch(apiUrl)
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! Status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            if (data.success && data.activities && data.activities.length > 0) {
                return {
                    activities: data.activities,
                    pagination: {
                        current_page: data.current_page,
                        total_pages: data.pages,
                        total: data.total
                    }
                };
            } else {
                return {
                    activities: [],
                    pagination: {
                        current_page: page,
                        total_pages: page,
                        total: 0
                    }
                };
            }
        });
}

/**
 * Get activity details by ID
 * 
 * @param {number} activityId - Activity ID to fetch details for
 * @returns {Promise<Object>} - Promise resolving to activity details
 */
function getActivityDetails(activityId) {
    return fetch(`/admin/activities/${activityId}/details`)
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! Status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            if (data.success && data.activity) {
                return data.activity;
            } else {
                throw new Error('Activity details not found');
            }
        });
}

// Export public API
export {
    loadActivities,
    loadMoreActivities,
    getActivityDetails
};

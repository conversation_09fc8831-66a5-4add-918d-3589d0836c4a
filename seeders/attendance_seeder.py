from app import db
from app.models.attendance import AttendanceType
from .db_utils import get_or_create # Assuming db_utils has a helper

def seed_attendance_types():
    """Seeds the database with initial attendance types."""
    attendance_types_data = [
        {
            'code': 'PVL', 'name': 'Planned Vacation Leave', 'requires_approval': True,
            'is_full_day': True, 'color_code': '#2ECC71' # Green
        },
        {
            'code': 'SL', 'name': 'Sick Leave', 'requires_approval': True,
            'is_full_day': True, 'color_code': '#E74C3C' # Red
        },
        {
            'code': 'RTO', 'name': 'Return to Office', 'requires_approval': False,
            'is_full_day': True, 'color_code': '#3498DB' # Blue
        },
        {
            'code': 'WFH', 'name': 'Work From Home', 'requires_approval': False,
            'is_full_day': True, 'color_code': '#9B59B6' # Purple
        },
        {
            'code': 'HD', 'name': 'Half Day', 'requires_approval': True,
            'is_full_day': False, 'color_code': '#F1C40F' # Yellow
        },
        {
            'code': 'USH', 'name': 'US Holiday Taken', 'requires_approval': False,
            'is_full_day': True, 'color_code': '#E67E22' # Orange
        },
        {
            'code': 'OCW', 'name': 'On Call Work', 'requires_approval': False,
            'is_full_day': False, 'color_code': '#34495E' # Dark Blue/Grey
        },
        {
            'code': 'HW', 'name': 'Holiday Work', 'requires_approval': True,
            'is_full_day': False, 'color_code': '#1ABC9C' # Turquoise
        },
        # Add CTO - Company Time Off / Compensatory Time Off
        {
            'code': 'CTO', 'name': 'Compensatory Time Off', 'requires_approval': True,
            'is_full_day': True, 'color_code': '#16A085' # Darker Turquoise
        }
    ]

    created_count = 0
    updated_count = 0

    for at_data in attendance_types_data:
        attendance_type, created = get_or_create(
            db.session,
            AttendanceType,
            code=at_data['code'],
            defaults={
                'name': at_data['name'],
                'requires_approval': at_data['requires_approval'],
                'is_full_day': at_data['is_full_day'],
                'color_code': at_data.get('color_code')
            }
        )
        if created:
            created_count += 1
        else:
            # Optionally update if it already exists and you want to ensure data is fresh
            updated = False
            if attendance_type.name != at_data['name']:
                attendance_type.name = at_data['name']
                updated = True
            if attendance_type.requires_approval != at_data['requires_approval']:
                attendance_type.requires_approval = at_data['requires_approval']
                updated = True
            if attendance_type.is_full_day != at_data['is_full_day']:
                attendance_type.is_full_day = at_data['is_full_day']
                updated = True
            if attendance_type.color_code != at_data.get('color_code'):
                attendance_type.color_code = at_data.get('color_code')
                updated = True

            if updated:
                updated_count +=1

    try:
        db.session.commit()
        print(f"Attendance types seeded: {created_count} created, {updated_count} updated.")
    except Exception as e:
        db.session.rollback()
        print(f"Error seeding attendance types: {e}")
        # This will likely be the disk I/O error if not resolved

if __name__ == '__main__':
    # This allows running the seeder directly, but ensure app context is handled
    # For direct execution, you might need to create an app context
    # from app import create_app
    # app = create_app()
    # with app.app_context():
    #     seed_attendance_types()
    print("Please run this seeder via the main seed script or Flask CLI command that sets up app context.")

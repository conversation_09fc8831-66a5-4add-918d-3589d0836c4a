/**
 * User Activity Log Component - Utilities Module
 *
 * This module provides utility functions for the activity log component.
 */

/**
 * Format a date string for display
 * 
 * @param {string} dateString - ISO date string to format
 * @returns {string} - Formatted date string
 */
function formatDate(dateString) {
    const date = new Date(dateString);
    const now = new Date();

    // If it's today, show relative time
    if (date.toDateString() === now.toDateString()) {
        const diffInHours = Math.floor((now - date) / (1000 * 60 * 60));
        const diffInMinutes = Math.floor((now - date) / (1000 * 60));

        if (diffInHours === 0) {
            if (diffInMinutes === 0) {
                return 'Just now';
            }
            return `${diffInMinutes} minute${diffInMinutes !== 1 ? 's' : ''} ago`;
        } else {
            return `${diffInHours} hour${diffInHours !== 1 ? 's' : ''} ago`;
        }
    }

    // If it's yesterday, show 'Yesterday'
    const yesterday = new Date(now);
    yesterday.setDate(now.getDate() - 1);
    if (date.toDateString() === yesterday.toDateString()) {
        return `Yesterday, ${date.toLocaleTimeString(undefined, { hour: 'numeric', minute: '2-digit' })}`;
    }

    // For other dates, show full date
    return date.toLocaleString(undefined, {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: 'numeric',
        minute: '2-digit',
        hour12: true
    });
}

/**
 * Format a value for display in the changes table
 * 
 * @param {any} value - Value to format
 * @returns {string} - Formatted HTML string
 */
function formatValue(value) {
    if (value === null || value === undefined) {
        return '<span class="text-muted-foreground italic">null</span>';
    } else if (typeof value === 'object') {
        return '<span class="bg-muted/50 px-1.5 py-0.5 rounded text-xs font-mono">' + JSON.stringify(value) + '</span>';
    } else if (typeof value === 'boolean') {
        return value ?
            '<span class="text-green-500 font-medium">true</span>' :
            '<span class="text-red-500 font-medium">false</span>';
    } else if (value === '') {
        return '<span class="text-muted-foreground italic">(empty string)</span>';
    } else if (typeof value === 'string' && value.length > 50) {
        // For long strings, show truncated version with full text on hover
        return `<span title="${value}" class="cursor-help">${value.substring(0, 50)}...</span>`;
    } else {
        return String(value);
    }
}

/**
 * Get color for method badge
 * 
 * @param {string} method - Method name (create, read, update, delete)
 * @returns {string} - Color name
 */
function getMethodColor(method) {
    switch(method) {
        case 'create': return 'blue';
        case 'read': return 'green';
        case 'update': return 'amber';
        case 'delete': return 'red';
        default: return 'gray';
    }
}

/**
 * Get icon for method badge
 * 
 * @param {string} method - Method name (create, read, update, delete)
 * @returns {string} - Icon name
 */
function getMethodIcon(method) {
    switch(method) {
        case 'create': return 'plus';
        case 'read': return 'eye';
        case 'update': return 'edit';
        case 'delete': return 'trash';
        default: return 'activity';
    }
}

/**
 * Get color for severity badge
 * 
 * @param {string} severity - Severity name (info, warning, error)
 * @returns {string} - Color name
 */
function getSeverityColor(severity) {
    switch(severity) {
        case 'error': return 'red';
        case 'warning': return 'yellow';
        case 'info': return 'green';
        default: return 'gray';
    }
}

/**
 * Get icon for category
 * 
 * @param {string} category - Category name (auth, user, admin, data, system)
 * @returns {string} - Icon name
 */
function getCategoryIcon(category) {
    switch(category) {
        case 'auth': return 'key';
        case 'user': return 'user';
        case 'admin': return 'shield';
        case 'data': return 'database';
        case 'system': return 'settings';
        default: return 'activity';
    }
}

// Export public API
export {
    formatDate,
    formatValue,
    getMethodColor,
    getMethodIcon,
    getSeverityColor,
    getCategoryIcon
};

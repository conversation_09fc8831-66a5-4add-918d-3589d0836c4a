{% extends "base.html" %}
{% from 'components/user_activity_log.html' import user_activity_log %}
{% from 'components/entity_activity_log.html' import entity_activity_log %}

{% block title %}Profile{% endblock %}

{% block header %}Profile{% endblock %}

{% block content %}
<!-- Profile Header -->
<div class="bg-card rounded-lg shadow-sm border border-border overflow-hidden mb-6">
    <div class="relative h-40 bg-gradient-to-r from-primary/30 via-primary/20 to-primary/10">
        <!-- Background Pattern -->
        <div class="absolute inset-0 opacity-10">
            <svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%">
                <defs>
                    <pattern id="grid" width="20" height="20" patternUnits="userSpaceOnUse">
                        <circle cx="10" cy="10" r="1" fill="currentColor" />
                    </pattern>
                </defs>
                <rect width="100%" height="100%" fill="url(#grid)" />
            </svg>
        </div>

        <!-- Profile Avatar -->
        <div class="absolute -bottom-14 left-6 sm:left-8">
            <div class="relative group">
                <div class="h-28 w-28 rounded-full bg-card border-4 border-card shadow-md flex items-center justify-center overflow-hidden">
                    {% if current_user.avatar %}
                        <img src="{{ current_user.avatar }}" alt="{{ current_user.name }}" class="h-full w-full object-cover">
                    {% else %}
                        <div class="h-full w-full bg-primary/10 flex items-center justify-center">
                            <i data-lucide="user" class="h-14 w-14 text-primary"></i>
                        </div>
                    {% endif %}
                </div>
                <!-- Avatar Status Indicator -->
                <div class="absolute bottom-1 right-1 h-5 w-5 rounded-full bg-green-500 border-2 border-card" title="Active"></div>
            </div>
        </div>

        <!-- Last Login Info -->
        <div class="absolute top-4 right-4 text-xs text-white/80 bg-black/20 backdrop-blur-sm px-3 py-1 rounded-full">
            <span class="flex items-center">
                <i data-lucide="clock" class="h-3 w-3 mr-1"></i>
                Last login: {{ current_user.last_login.strftime('%b %d, %Y %H:%M') if current_user.last_login else 'Never' }}
            </span>
        </div>
    </div>

    <div class="pt-16 pb-4 px-6 sm:px-8">
        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
            <div>
                <h1 class="text-2xl font-bold">{{ current_user.name }}</h1>
                <div class="flex items-center text-muted-foreground mt-1">
                    <i data-lucide="mail" class="h-3.5 w-3.5 mr-1.5"></i>
                    <span>{{ current_user.email }}</span>
                </div>
            </div>
            <div class="mt-4 sm:mt-0 flex flex-wrap items-center gap-2">
                <span class="badge badge-outline">
                    <i data-lucide="shield" class="h-3.5 w-3.5 mr-1"></i>
                    {{ current_user.role }}
                </span>
                {% if current_user.employee_details.job_title %}
                <span class="badge badge-outline">
                    <i data-lucide="briefcase" class="h-3.5 w-3.5 mr-1"></i>
                    {{ current_user.employee_details.job_title }}
                </span>
                {% endif %}
                {% if current_user.employee_details.business_unit %}
                <span class="badge badge-outline">
                    <i data-lucide="building" class="h-3.5 w-3.5 mr-1"></i>
                    {{ current_user.employee_details.business_unit.name }}
                </span>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Tabs -->
<div class="mb-6">
    <div class="bg-white dark:bg-[#0f172a] py-0 tab-header border-b border-gray-200 dark:border-gray-800">
        <div class="max-w-3xl mx-auto">
            <nav class="tabs-container" aria-label="Tabs">
                <button type="button" class="tab-button active" data-tab="personal-info" id="personal-info-tab" role="tab" aria-selected="true" aria-controls="personal-info">
                    <div class="flex items-center justify-center">
                        <i data-lucide="user" class="h-4 w-4 mr-2"></i>
                        <span>Personal Info</span>
                    </div>
                </button>
                <button type="button" class="tab-button" data-tab="security" id="security-tab" role="tab" aria-selected="false" aria-controls="security">
                    <div class="flex items-center justify-center">
                        <i data-lucide="shield" class="h-4 w-4 mr-2"></i>
                        <span>Security</span>
                    </div>
                </button>
                {% if current_user.employee_details.exists %}
                <button type="button" class="tab-button" data-tab="employee-details" id="employee-details-tab" role="tab" aria-selected="false" aria-controls="employee-details">
                    <div class="flex items-center justify-center">
                        <i data-lucide="briefcase" class="h-4 w-4 mr-2"></i>
                        <span>Employee Details</span>
                    </div>
                </button>
                {% endif %}
                <button type="button" class="tab-button" data-tab="activities" id="activities-tab" role="tab" aria-selected="false" aria-controls="activities">
                    <div class="flex items-center justify-center">
                        <i data-lucide="activity" class="h-4 w-4 mr-2"></i>
                        <span>Activities</span>
                    </div>
                </button>
            </nav>
        </div>
    </div>
</div>

<!-- Tab Content -->
<div id="personal-info" class="tab-content active" role="tabpanel" aria-labelledby="personal-info-tab">
<form action="{{ url_for('auth.profile') }}" method="POST" id="personal-info-form">
    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
    <input type="hidden" name="form_type" value="personal_info">
        <div class="card">
            <div class="p-6 border-b border-border flex items-center justify-between">
                <div>
                    <h2 class="text-lg font-semibold">Personal Information</h2>
                    <p class="text-sm text-muted-foreground">Update your personal details</p>
                </div>
                <div class="hidden sm:block">
                    <span class="inline-flex items-center rounded-full bg-primary/10 px-2.5 py-1 text-xs font-medium text-primary">
                        <i data-lucide="info" class="h-3 w-3 mr-1"></i>
                        Profile 80% complete
                    </span>
                </div>
            </div>
            <div class="p-6">
                <div class="grid grid-cols-1 sm:grid-cols-2 gap-6">
                    <div class="space-y-2">
                        <label for="name" class="flex items-center text-sm font-medium">
                            Full Name
                            <span class="ml-1 text-red-500">*</span>
                        </label>
                        <div class="relative rounded-md shadow-sm">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <i data-lucide="user" class="h-4 w-4 text-muted-foreground"></i>
                            </div>
                            <input type="text" id="name" name="name" value="{{ current_user.name }}"
                                class="input pl-10 focus:ring-2 focus:ring-primary/30 transition-shadow" required>
                            <div class="absolute inset-y-0 right-0 pr-3 flex items-center">
                                <i data-lucide="check" class="h-4 w-4 text-green-500"></i>
                            </div>
                        </div>
                    </div>

                    <div class="space-y-2">
                        <label for="email" class="flex items-center text-sm font-medium">
                            Email Address
                            <span class="ml-1 text-red-500">*</span>
                        </label>
                        <div class="relative rounded-md shadow-sm">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <i data-lucide="mail" class="h-4 w-4 text-muted-foreground"></i>
                            </div>
                            <input type="email" id="email" value="{{ current_user.email }}"
                                class="input pl-10 bg-muted cursor-not-allowed" disabled>
                            <div class="absolute inset-y-0 right-0 pr-3 flex items-center">
                                <i data-lucide="lock" class="h-4 w-4 text-muted-foreground"></i>
                            </div>
                        </div>
                        <p class="text-xs text-muted-foreground flex items-center">
                            <i data-lucide="info" class="h-3 w-3 mr-1"></i>
                            Email cannot be changed
                        </p>
                    </div>

                    <div class="space-y-2">
                        <label for="phone" class="block text-sm font-medium">Phone Number</label>
                        <div class="flex rounded-md shadow-sm">
                            <div class="relative inline-flex items-center px-3 rounded-l-md border border-r-0 border-border bg-muted text-muted-foreground text-sm">
                                <select id="phone_code" name="phone_code" class="bg-transparent border-0 focus:ring-0">
                                    <option value="+63" selected>+63 (PH)</option>
                                    <option value="+1">+1 (US/CA)</option>
                                    <option value="+44">+44 (UK)</option>
                                    <option value="+61">+61 (AU)</option>
                                    <option value="+65">+65 (SG)</option>
                                    <option value="+81">+81 (JP)</option>
                                </select>
                            </div>
                            <div class="relative flex-1">
                                <div class="absolute inset-y-0 left-3 flex items-center pointer-events-none">
                                    <i data-lucide="phone" class="h-4 w-4 text-muted-foreground"></i>
                                </div>
                                <input type="tel" id="phone" name="phone" value="{{ current_user.employee_details.phone|replace('+63', '')|replace('+1', '')|replace('+44', '')|replace('+61', '')|replace('+65', '')|replace('+81', '') }}"
                                    class="input rounded-l-none pl-10 focus:ring-2 focus:ring-primary/30 transition-shadow"
                                    placeholder="9XX XXX XXXX">
                            </div>
                        </div>
                        <p class="text-xs text-muted-foreground flex items-center">
                            <i data-lucide="info" class="h-3 w-3 mr-1"></i>
                            Add your contact number for notifications (default: Philippines +63)
                        </p>
                    </div>

                    <div class="space-y-2">
                        <label for="timezone" class="block text-sm font-medium">Timezone</label>
                        <div class="relative rounded-md shadow-sm">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <i data-lucide="globe" class="h-4 w-4 text-muted-foreground"></i>
                            </div>
                            <select id="timezone" name="timezone" class="input pl-10 focus:ring-2 focus:ring-primary/30 transition-shadow">
                                <option value="Asia/Manila" {% if user_timezone == 'Asia/Manila' %}selected{% endif %}>Manila (Philippines)</option>
                                <option value="UTC" {% if user_timezone == 'UTC' %}selected{% endif %}>UTC (Coordinated Universal Time)</option>
                                <option value="America/New_York" {% if user_timezone == 'America/New_York' %}selected{% endif %}>Eastern Time (US & Canada)</option>
                                <option value="America/Chicago" {% if user_timezone == 'America/Chicago' %}selected{% endif %}>Central Time (US & Canada)</option>
                                <option value="America/Denver" {% if user_timezone == 'America/Denver' %}selected{% endif %}>Mountain Time (US & Canada)</option>
                                <option value="America/Los_Angeles" {% if user_timezone == 'America/Los_Angeles' %}selected{% endif %}>Pacific Time (US & Canada)</option>
                                <option value="Asia/Tokyo" {% if user_timezone == 'Asia/Tokyo' %}selected{% endif %}>Tokyo (Japan)</option>
                                <option value="Europe/London" {% if user_timezone == 'Europe/London' %}selected{% endif %}>London (United Kingdom)</option>
                            </select>
                            <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                <i data-lucide="chevron-down" class="h-4 w-4 text-muted-foreground"></i>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Bio Section -->
                <div class="mt-6 pt-6 border-t border-border">
                    <div class="space-y-2">
                        <label for="bio" class="block text-sm font-medium">Bio</label>
                        <div class="relative rounded-md shadow-sm">
                            <textarea id="bio" name="bio" rows="4"
                                class="input w-full focus:ring-2 focus:ring-primary/30 transition-shadow"
                                placeholder="Write a short bio about yourself...">{{ current_user.bio or '' }}</textarea>
                        </div>
                        <div class="flex justify-between">
                            <p class="text-xs text-muted-foreground flex items-center">
                                <i data-lucide="info" class="h-3 w-3 mr-1"></i>
                                Brief description for your profile
                            </p>
                            <span class="text-xs text-muted-foreground" id="bio-counter">0/200</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Form Actions for Personal Info -->
        <div class="mt-6 bg-card rounded-lg border border-border p-4 flex flex-col sm:flex-row justify-between items-center gap-4">
            <div class="text-sm text-muted-foreground hidden sm:block">
                <span class="flex items-center">
                    <i data-lucide="info" class="h-4 w-4 mr-2 text-primary"></i>
                    Changes will be applied immediately after saving
                </span>
            </div>
            <div class="flex items-center space-x-3 w-full sm:w-auto">
                <a href="{{ url_for('main.user_dashboard') }}" class="btn btn-outline btn-md w-full sm:w-auto">
                    <i data-lucide="x" class="h-4 w-4 mr-2"></i>
                    Cancel
                </a>
                <button type="submit" class="btn btn-primary btn-md w-full sm:w-auto">
                    <i data-lucide="save" class="h-4 w-4 mr-2"></i>
                    Save Changes
                </button>
            </div>
        </div>
</form>
</div>

<!-- Security Tab -->
<div id="security" class="tab-content" role="tabpanel" aria-labelledby="security-tab">
<form action="{{ url_for('auth.profile') }}" method="POST" id="security-form">
    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
    <input type="hidden" name="form_type" value="security">
        <div class="card">
            <div class="p-6 border-b border-border flex items-center justify-between">
                <div>
                    <h2 class="text-lg font-semibold">Security Settings</h2>
                    <p class="text-sm text-muted-foreground">Manage your password and account security</p>
                </div>
                <div class="hidden sm:block">
                    <span class="inline-flex items-center rounded-full bg-green-100 dark:bg-green-900/30 px-2.5 py-1 text-xs font-medium text-green-700 dark:text-green-400">
                        <i data-lucide="shield-check" class="h-3 w-3 mr-1"></i>
                        Account secured
                    </span>
                </div>
            </div>
            <div class="p-6">
                <!-- Last Password Change Info -->
                <div class="mb-6 p-4 bg-muted/30 rounded-lg border border-border">
                    <div class="flex items-center">
                        <div class="flex-shrink-0 h-10 w-10 rounded-full bg-primary/10 flex items-center justify-center">
                            <i data-lucide="clock" class="h-5 w-5 text-primary"></i>
                        </div>
                        <div class="ml-4">
                            <h4 class="text-sm font-medium">Last password change</h4>
                            <p class="text-xs text-muted-foreground">
                                {% if days_since_password_change == 0 %}
                                    Your password was changed today
                                {% elif days_since_password_change == 1 %}
                                    Your password was changed yesterday
                                {% else %}
                                    Your password was changed {{ days_since_password_change }} days ago
                                {% endif %}
                                {% if last_password_date %}
                                    ({{ last_password_date.strftime('%b %d, %Y') }})
                                {% endif %}
                            </p>
                        </div>
                        <div class="ml-auto">
                            {% if password_status == 'expired' %}
                            <span class="inline-flex items-center rounded-full bg-red-100 dark:bg-red-900/30 px-2.5 py-1 text-xs font-medium text-red-700 dark:text-red-400">
                                <i data-lucide="alert-circle" class="h-3 w-3 mr-1"></i>
                                Update recommended
                            </span>
                            {% elif password_status == 'warning' %}
                            <span class="inline-flex items-center rounded-full bg-yellow-100 dark:bg-yellow-900/30 px-2.5 py-1 text-xs font-medium text-yellow-700 dark:text-yellow-400">
                                <i data-lucide="alert-triangle" class="h-3 w-3 mr-1"></i>
                                Consider updating
                            </span>
                            {% else %}
                            <span class="inline-flex items-center rounded-full bg-green-100 dark:bg-green-900/30 px-2.5 py-1 text-xs font-medium text-green-700 dark:text-green-400">
                                <i data-lucide="check-circle" class="h-3 w-3 mr-1"></i>
                                Up to date
                            </span>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <div class="space-y-6">
                    <div class="space-y-2">
                        <label for="current_password" class="text-sm font-medium">Current Password</label>
                        <div class="relative rounded-md shadow-sm">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <i data-lucide="key" class="h-4 w-4 text-muted-foreground"></i>
                            </div>
                            <input type="password" id="current_password" name="current_password"
                                class="input pl-10 focus:ring-2 focus:ring-primary/30 transition-shadow">
                            <button type="button" class="absolute inset-y-0 right-0 pr-3 flex items-center toggle-password" data-target="current_password">
                                <i data-lucide="eye" class="h-4 w-4 text-muted-foreground hover:text-foreground transition-colors"></i>
                            </button>
                        </div>
                    </div>

                    <div class="grid grid-cols-1 sm:grid-cols-2 gap-6">
                        <div class="space-y-2">
                            <label for="new_password" class="text-sm font-medium">New Password</label>
                            <div class="relative rounded-md shadow-sm">
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <i data-lucide="lock" class="h-4 w-4 text-muted-foreground"></i>
                                </div>
                                <input type="password" id="new_password" name="new_password"
                                    class="input pl-10 focus:ring-2 focus:ring-primary/30 transition-shadow"
                                    onkeyup="checkPasswordStrength()">
                                <button type="button" class="absolute inset-y-0 right-0 pr-3 flex items-center toggle-password" data-target="new_password">
                                    <i data-lucide="eye" class="h-4 w-4 text-muted-foreground hover:text-foreground transition-colors"></i>
                                </button>
                            </div>
                            <div id="password-strength" class="hidden mt-2">
                                <div class="h-1.5 w-full bg-muted rounded-full overflow-hidden">
                                    <div id="strength-bar" class="h-full bg-red-500 transition-all duration-300" style="width: 0%"></div>
                                </div>
                                <p id="strength-text" class="text-xs mt-1 text-muted-foreground">Password strength</p>
                            </div>
                        </div>

                        <div class="space-y-2">
                            <label for="confirm_password" class="text-sm font-medium">Confirm New Password</label>
                            <div class="relative rounded-md shadow-sm">
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <i data-lucide="check-circle" class="h-4 w-4 text-muted-foreground"></i>
                                </div>
                                <input type="password" id="confirm_password" name="confirm_password"
                                    class="input pl-10 focus:ring-2 focus:ring-primary/30 transition-shadow"
                                    onkeyup="checkPasswordMatch()">
                                <button type="button" class="absolute inset-y-0 right-0 pr-3 flex items-center toggle-password" data-target="confirm_password">
                                    <i data-lucide="eye" class="h-4 w-4 text-muted-foreground hover:text-foreground transition-colors"></i>
                                </button>
                            </div>
                            <p id="password-match" class="text-xs mt-1 hidden"></p>
                        </div>
                    </div>

                    <div class="bg-muted/30 rounded-lg p-4 border border-border">
                        <h4 class="text-sm font-medium mb-3 flex items-center">
                            <i data-lucide="shield-alert" class="h-4 w-4 mr-2 text-primary"></i>
                            Password Requirements
                        </h4>
                        <div class="grid grid-cols-1 sm:grid-cols-2 gap-2">
                            <div class="flex items-center text-xs text-muted-foreground bg-card p-2 rounded-md">
                                <div class="h-5 w-5 rounded-full bg-green-100 dark:bg-green-900/30 flex items-center justify-center mr-2">
                                    <i data-lucide="check" class="h-3 w-3 text-green-600 dark:text-green-400"></i>
                                </div>
                                Minimum 8 characters
                            </div>
                            <div class="flex items-center text-xs text-muted-foreground bg-card p-2 rounded-md">
                                <div class="h-5 w-5 rounded-full bg-green-100 dark:bg-green-900/30 flex items-center justify-center mr-2">
                                    <i data-lucide="check" class="h-3 w-3 text-green-600 dark:text-green-400"></i>
                                </div>
                                At least one uppercase letter
                            </div>
                            <div class="flex items-center text-xs text-muted-foreground bg-card p-2 rounded-md">
                                <div class="h-5 w-5 rounded-full bg-green-100 dark:bg-green-900/30 flex items-center justify-center mr-2">
                                    <i data-lucide="check" class="h-3 w-3 text-green-600 dark:text-green-400"></i>
                                </div>
                                At least one number
                            </div>
                            <div class="flex items-center text-xs text-muted-foreground bg-card p-2 rounded-md">
                                <div class="h-5 w-5 rounded-full bg-green-100 dark:bg-green-900/30 flex items-center justify-center mr-2">
                                    <i data-lucide="check" class="h-3 w-3 text-green-600 dark:text-green-400"></i>
                                </div>
                                At least one special character
                            </div>
                        </div>
                    </div>

                    <!-- Two-Factor Authentication -->
                    <div class="mt-8 pt-6 border-t border-border">
                        <div class="flex items-center justify-between">
                            <div>
                                <h4 class="text-sm font-medium">Two-Factor Authentication</h4>
                                <p class="text-xs text-muted-foreground mt-1">Add an extra layer of security to your account</p>
                            </div>
                            <div class="flex items-center">
                                <span class="mr-3 text-xs text-muted-foreground">Not enabled</span>
                                <button type="button" class="relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent bg-muted transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2">
                                    <span class="sr-only">Enable two-factor authentication</span>
                                    <span aria-hidden="true" class="pointer-events-none inline-block h-5 w-5 transform rounded-full bg-card shadow ring-0 transition duration-200 ease-in-out translate-x-0"></span>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Form Actions for Security -->
        <div class="mt-6 bg-card rounded-lg border border-border p-4 flex flex-col sm:flex-row justify-between items-center gap-4">
            <div class="text-sm text-muted-foreground hidden sm:block">
                <span class="flex items-center">
                    <i data-lucide="info" class="h-4 w-4 mr-2 text-primary"></i>
                    Changes will be applied immediately after saving
                </span>
            </div>
            <div class="flex items-center space-x-3 w-full sm:w-auto">
                <a href="{{ url_for('main.user_dashboard') }}" class="btn btn-outline btn-md w-full sm:w-auto">
                    <i data-lucide="x" class="h-4 w-4 mr-2"></i>
                    Cancel
                </a>
                <button type="submit" class="btn btn-primary btn-md w-full sm:w-auto">
                    <i data-lucide="save" class="h-4 w-4 mr-2"></i>
                    Save Changes
                </button>
            </div>
        </div>
</form>
</div>

<!-- Employee Details Tab -->
{% if current_user.employee_details.exists %}
<div id="employee-details" class="tab-content" role="tabpanel" aria-labelledby="employee-details-tab">
<form action="{{ url_for('auth.profile') }}" method="POST" id="employee-details-form">
    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
    <input type="hidden" name="form_type" value="employee_details">
        <div class="card">
            <div class="p-6 border-b border-border flex items-center justify-between">
                <div>
                    <h2 class="text-lg font-semibold">Employee Information</h2>
                    <p class="text-sm text-muted-foreground">Your employment details</p>
                </div>
                <div class="hidden sm:block">
                    <span class="inline-flex items-center rounded-full bg-blue-100 dark:bg-blue-900/30 px-2.5 py-1 text-xs font-medium text-blue-700 dark:text-blue-400">
                        <i data-lucide="briefcase" class="h-3 w-3 mr-1"></i>
                        {{ current_user.employee_details.emp_status|title if current_user.employee_details.emp_status else 'Active' }}
                    </span>
                </div>
            </div>
            <div class="p-6">
                <!-- Employee Summary Card -->
                <div class="mb-6 bg-muted/30 rounded-lg border border-border overflow-hidden">
                    <div class="grid grid-cols-1 sm:grid-cols-3 divide-y sm:divide-y-0 sm:divide-x divide-border">
                        <div class="p-4 flex items-center">
                            <div class="h-10 w-10 rounded-full bg-primary/10 flex items-center justify-center mr-3">
                                <i data-lucide="briefcase" class="h-5 w-5 text-primary"></i>
                            </div>
                            <div>
                                <div class="text-xs text-muted-foreground">Job Title</div>
                                <div class="text-sm font-medium">{{ current_user.employee_details.job_title or 'Not specified' }}</div>
                            </div>
                        </div>
                        <div class="p-4 flex items-center">
                            <div class="h-10 w-10 rounded-full bg-primary/10 flex items-center justify-center mr-3">
                                <i data-lucide="calendar" class="h-5 w-5 text-primary"></i>
                            </div>
                            <div>
                                <div class="text-xs text-muted-foreground">Hire Date</div>
                                <div class="text-sm font-medium">{{ current_user.employee_details.hire_date.strftime('%b %d, %Y') if current_user.employee_details.hire_date else 'Not specified' }}</div>
                            </div>
                        </div>
                        <div class="p-4 flex items-center">
                            <div class="h-10 w-10 rounded-full bg-primary/10 flex items-center justify-center mr-3">
                                <i data-lucide="id-card" class="h-5 w-5 text-primary"></i>
                            </div>
                            <div>
                                <div class="text-xs text-muted-foreground">Employee ID</div>
                                <div class="text-sm font-medium">{{ current_user.employee_details.employee_number or 'Not assigned' }}</div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="grid grid-cols-1 sm:grid-cols-2 gap-6">
                    <div class="space-y-2">
                        <label class="text-sm font-medium">Full Name</label>
                        <div class="relative rounded-md shadow-sm">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <i data-lucide="user" class="h-4 w-4 text-muted-foreground"></i>
                            </div>
                            <input type="text" value="{{ current_user.employee_details.first_name }} {{ current_user.employee_details.middle_name }} {{ current_user.employee_details.last_name }}" class="input pl-10 bg-muted cursor-not-allowed" disabled>
                        </div>
                    </div>

                    <div class="space-y-2">
                        <label class="text-sm font-medium">Legal Name</label>
                        <div class="relative rounded-md shadow-sm">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <i data-lucide="file-text" class="h-4 w-4 text-muted-foreground"></i>
                            </div>
                            <input type="text" value="{{ current_user.employee_details.legal_name or 'Not specified' }}" class="input pl-10 bg-muted cursor-not-allowed" disabled>
                        </div>
                    </div>

                    <div class="space-y-2">
                        <label class="text-sm font-medium">Employment Type</label>
                        <div class="relative rounded-md shadow-sm">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <i data-lucide="briefcase" class="h-4 w-4 text-muted-foreground"></i>
                            </div>
                            <input type="text" value="{{ current_user.employee_details.emp_type or 'Not specified' }}" class="input pl-10 bg-muted cursor-not-allowed" disabled>
                        </div>
                    </div>

                    <div class="space-y-2">
                        <label class="text-sm font-medium">Enterprise ID</label>
                        <div class="relative rounded-md shadow-sm">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <i data-lucide="badge" class="h-4 w-4 text-muted-foreground"></i>
                            </div>
                            <input type="text" value="{{ current_user.employee_details.enterprise_id or 'Not specified' }}" class="input pl-10 bg-muted cursor-not-allowed" disabled>
                        </div>
                    </div>

                    {% if current_user.employee_details.business_unit %}
                    <div class="space-y-2">
                        <label class="text-sm font-medium">Business Unit</label>
                        <div class="relative rounded-md shadow-sm">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <i data-lucide="building" class="h-4 w-4 text-muted-foreground"></i>
                            </div>
                            <input type="text" value="{{ current_user.employee_details.business_unit.name }}" class="input pl-10 bg-muted cursor-not-allowed" disabled>
                        </div>
                    </div>
                    {% endif %}

                    {% if current_user.employee_details.business_segment %}
                    <div class="space-y-2">
                        <label class="text-sm font-medium">Business Segment</label>
                        <div class="relative rounded-md shadow-sm">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <i data-lucide="layers" class="h-4 w-4 text-muted-foreground"></i>
                            </div>
                            <input type="text" value="{{ current_user.employee_details.business_segment.name }}" class="input pl-10 bg-muted cursor-not-allowed" disabled>
                        </div>
                    </div>
                    {% endif %}

                    <div class="space-y-2">
                        <label class="text-sm font-medium">Manager Name</label>
                        <div class="relative rounded-md shadow-sm">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <i data-lucide="user-check" class="h-4 w-4 text-muted-foreground"></i>
                            </div>
                            <input type="text" value="{{ current_user.employee_details.manager_name or 'Not specified' }}" class="input pl-10 bg-muted cursor-not-allowed" disabled>
                        </div>
                    </div>

                    <div class="space-y-2">
                        <label class="text-sm font-medium">Job Code</label>
                        <div class="relative rounded-md shadow-sm">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <i data-lucide="code" class="h-4 w-4 text-muted-foreground"></i>
                            </div>
                            <input type="text" value="{{ current_user.employee_details.job_code or 'Not specified' }}" class="input pl-10 bg-muted cursor-not-allowed" disabled>
                        </div>
                    </div>

                    <div class="space-y-2">
                        <label class="text-sm font-medium">Manager Level</label>
                        <div class="relative rounded-md shadow-sm">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <i data-lucide="bar-chart-3" class="h-4 w-4 text-muted-foreground"></i>
                            </div>
                            <input type="text" value="{{ current_user.employee_details.manager_level or 'Not specified' }}" class="input pl-10 bg-muted cursor-not-allowed" disabled>
                        </div>
                    </div>

                    <div class="space-y-2">
                        <label class="text-sm font-medium">Job Code Track Level</label>
                        <div class="relative rounded-md shadow-sm">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <i data-lucide="git-branch" class="h-4 w-4 text-muted-foreground"></i>
                            </div>
                            <input type="text" value="{{ current_user.employee_details.job_code_track_level or 'Not specified' }}" class="input pl-10 bg-muted cursor-not-allowed" disabled>
                        </div>
                    </div>

                    <!-- Teams Section -->
                    <div class="sm:col-span-2 space-y-2 mt-4">
                        <label class="text-sm font-medium">Teams</label>
                        <div class="bg-muted/30 rounded-lg border border-border p-4">
                            {% if current_user.employee_details.teams %}
                                <div class="flex flex-wrap gap-2">
                                    {% for team in current_user.employee_details.teams %}
                                        <span class="inline-flex items-center rounded-full bg-primary/10 px-2.5 py-1 text-xs font-medium text-primary">
                                            <i data-lucide="users" class="h-3 w-3 mr-1"></i>
                                            {{ team.name }}
                                        </span>
                                    {% endfor %}
                                </div>
                            {% else %}
                                <div class="flex items-center justify-center h-16 text-sm text-muted-foreground">
                                    <i data-lucide="users-x" class="h-4 w-4 mr-2"></i>
                                    You are not assigned to any teams
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Form Actions for Employee Details -->
        <div class="mt-6 bg-card rounded-lg border border-border p-4 flex flex-col sm:flex-row justify-between items-center gap-4">
            <div class="text-sm text-muted-foreground hidden sm:block">
                <span class="flex items-center">
                    <i data-lucide="info" class="h-4 w-4 mr-2 text-primary"></i>
                    Changes will be applied immediately after saving
                </span>
            </div>
            <div class="flex items-center space-x-3 w-full sm:w-auto">
                <a href="{{ url_for('main.user_dashboard') }}" class="btn btn-outline btn-md w-full sm:w-auto">
                    <i data-lucide="x" class="h-4 w-4 mr-2"></i>
                    Cancel
                </a>
                <button type="submit" class="btn btn-primary btn-md w-full sm:w-auto">
                    <i data-lucide="save" class="h-4 w-4 mr-2"></i>
                    Save Changes
                </button>
            </div>
        </div>
</form>
</div>
{% endif %}

<!-- Activities Tab -->
<div id="activities" class="tab-content" role="tabpanel" aria-labelledby="activities-tab">
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <!-- My Activities -->
        <div class="md:col-span-2">
            <div class="card">
                <div class="p-6 border-b border-border flex items-center justify-between">
                    <div>
                        <h2 class="text-lg font-semibold">My Activities</h2>
                        <p class="text-sm text-muted-foreground">View your recent account activities</p>
                    </div>
                    <div class="hidden sm:block">
                        <span class="inline-flex items-center rounded-full bg-primary/10 px-2.5 py-1 text-xs font-medium text-primary">
                            <i data-lucide="shield" class="h-3 w-3 mr-1"></i>
                            Security Information
                        </span>
                    </div>
                </div>
                <div class="p-6">
                    {{ user_activity_log(title="", limit=10, entity_type="User", entity_id=current_user.id, max_height="500px") }}
                </div>
            </div>
        </div>

        <!-- Changes to My Profile -->
        <div class="md:col-span-1">
            <div class="card">
                <div class="p-6 border-b border-border">
                    <h2 class="text-lg font-semibold">Profile Changes</h2>
                    <p class="text-sm text-muted-foreground">Changes made to your profile</p>
                </div>
                <div class="p-6">
                    {{ entity_activity_log(entity_type="User", entity_id=current_user.id, limit=5, show_title=false, show_all_link=false) }}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="{{ url_for('static', filename='js/components/user-activity-log.js') }}"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Lucide icons are now initialized automatically

        // Hide save button section if activities tab is active on page load
        const activeTab = document.querySelector('.tab-button.active');
        if (activeTab && activeTab.getAttribute('data-tab') === 'activities') {
            const saveButtonSection = document.querySelector('.mt-6.bg-card.rounded-lg.border.border-border.p-4');
            if (saveButtonSection) {
                saveButtonSection.style.display = 'none';
            }
        }

        // Enhanced tab functionality with smooth transitions and accessibility
        const tabButtons = document.querySelectorAll('.tab-button');
        const tabContents = document.querySelectorAll('.tab-content');

        tabButtons.forEach(button => {
            button.addEventListener('click', () => {
                const tabId = button.getAttribute('data-tab');

                // If the tab is already active, do nothing
                if (button.classList.contains('active')) {
                    return;
                }

                // Update ARIA attributes and classes for all tabs
                tabButtons.forEach(btn => {
                    btn.classList.remove('active');
                    btn.setAttribute('aria-selected', 'false');
                });

                // Update the clicked tab
                button.classList.add('active');
                button.setAttribute('aria-selected', 'true');

                // Hide all tab contents
                tabContents.forEach(content => {
                    content.classList.remove('active');
                });

                // Show the selected tab content
                const selectedContent = document.getElementById(tabId);
                selectedContent.classList.add('active');

                // Hide or show the save button section based on the selected tab
                const saveButtonSection = document.querySelector('.mt-6.bg-card.rounded-lg.border.border-border.p-4');
                if (tabId === 'activities') {
                    saveButtonSection.style.display = 'none';
                } else {
                    saveButtonSection.style.display = 'flex';
                }

                // Focus the tab button for accessibility
                button.focus();
            });

            // Add keyboard navigation
            button.addEventListener('keydown', (e) => {
                // Handle left/right arrow keys for tab navigation
                if (e.key === 'ArrowLeft' || e.key === 'ArrowRight') {
                    e.preventDefault();

                    const tabs = Array.from(tabButtons);
                    const currentIndex = tabs.indexOf(button);
                    let newIndex;

                    if (e.key === 'ArrowLeft') {
                        newIndex = currentIndex > 0 ? currentIndex - 1 : tabs.length - 1;
                    } else {
                        newIndex = currentIndex < tabs.length - 1 ? currentIndex + 1 : 0;
                    }

                    tabs[newIndex].click();
                    tabs[newIndex].focus();
                }
            });
        });

        // Toggle password visibility
        const toggleButtons = document.querySelectorAll('.toggle-password');
        toggleButtons.forEach(button => {
            button.addEventListener('click', () => {
                const targetId = button.getAttribute('data-target');
                const passwordInput = document.getElementById(targetId);
                const icon = button.querySelector('i');

                if (passwordInput.type === 'password') {
                    passwordInput.type = 'text';
                    icon.setAttribute('data-lucide', 'eye-off');
                } else {
                    passwordInput.type = 'password';
                    icon.setAttribute('data-lucide', 'eye');
                }

                lucide.createIcons();
            });
        });
    });

    // Password strength checker
    function checkPasswordStrength() {
        const password = document.getElementById('new_password').value;
        const strengthBar = document.getElementById('strength-bar');
        const strengthText = document.getElementById('strength-text');
        const strengthContainer = document.getElementById('password-strength');

        if (password.length === 0) {
            strengthContainer.classList.add('hidden');
            return;
        }

        strengthContainer.classList.remove('hidden');

        // Calculate strength
        let strength = 0;

        // Length check
        if (password.length >= 8) strength += 25;

        // Character type checks
        if (password.match(/[A-Z]/)) strength += 25;
        if (password.match(/[0-9]/)) strength += 25;
        if (password.match(/[^A-Za-z0-9]/)) strength += 25;

        // Update UI
        strengthBar.style.width = strength + '%';

        if (strength <= 25) {
            strengthBar.className = 'h-full bg-red-500 transition-all duration-300';
            strengthText.textContent = 'Weak password';
            strengthText.className = 'text-xs mt-1 text-red-500';
        } else if (strength <= 50) {
            strengthBar.className = 'h-full bg-orange-500 transition-all duration-300';
            strengthText.textContent = 'Fair password';
            strengthText.className = 'text-xs mt-1 text-orange-500';
        } else if (strength <= 75) {
            strengthBar.className = 'h-full bg-yellow-500 transition-all duration-300';
            strengthText.textContent = 'Good password';
            strengthText.className = 'text-xs mt-1 text-yellow-500';
        } else {
            strengthBar.className = 'h-full bg-green-500 transition-all duration-300';
            strengthText.textContent = 'Strong password';
            strengthText.className = 'text-xs mt-1 text-green-500';
        }
    }

    // Check if passwords match
    function checkPasswordMatch() {
        const newPassword = document.getElementById('new_password').value;
        const confirmPassword = document.getElementById('confirm_password').value;
        const matchText = document.getElementById('password-match');

        if (confirmPassword.length === 0) {
            matchText.classList.add('hidden');
            return;
        }

        matchText.classList.remove('hidden');

        if (newPassword === confirmPassword) {
            matchText.textContent = 'Passwords match';
            matchText.className = 'text-xs mt-1 text-green-500';
        } else {
            matchText.textContent = 'Passwords do not match';
            matchText.className = 'text-xs mt-1 text-red-500';
        }
    }

    // Bio character counter
    function updateBioCounter() {
        const bioTextarea = document.getElementById('bio');
        const bioCounter = document.getElementById('bio-counter');
        const maxLength = 200;
        const currentLength = bioTextarea.value.length;

        bioCounter.textContent = `${currentLength}/${maxLength}`;

        if (currentLength > maxLength) {
            bioCounter.className = 'text-xs text-red-500';
        } else if (currentLength > maxLength * 0.8) {
            bioCounter.className = 'text-xs text-yellow-500';
        } else {
            bioCounter.className = 'text-xs text-muted-foreground';
        }
    }

    // Handle phone code selection
    function initPhoneCodeSelection() {
        const phoneInput = document.getElementById('phone');
        const phoneCodeSelect = document.getElementById('phone_code');

        if (phoneInput && phoneCodeSelect) {
            // Try to extract the country code from the current phone number
            const fullPhone = '{{ current_user.employee_details.phone }}';
            if (fullPhone) {
                // Check for common country codes
                const countryCodes = ["+63", "+1", "+44", "+61", "+65", "+81"];
                let foundCode = false;

                for (const code of countryCodes) {
                    if (fullPhone.startsWith(code)) {
                        // Set the select to the found code
                        phoneCodeSelect.value = code;
                        foundCode = true;
                        break;
                    }
                }

                // If no code was found, default to +63
                if (!foundCode) {
                    phoneCodeSelect.value = "+63";
                }
            }
        }
    }

    document.addEventListener('DOMContentLoaded', function() {
        // Initialize bio counter
        const bioTextarea = document.getElementById('bio');
        if (bioTextarea) {
            updateBioCounter();
            bioTextarea.addEventListener('input', updateBioCounter);
        }

        // Initialize phone code selection
        initPhoneCodeSelection();

        // Handle form submission - no toast on submit, only after successful save
        const form = document.querySelector('form');
        form.addEventListener('submit', function(e) {
            // Don't prevent default - let the form submit normally
            // No toast on submit - we'll show it only after successful save
        });

        // Process flash messages with the standard handler
        if (typeof handleFlashMessages === 'function') {
            handleFlashMessages();
        }

        // Check URL parameters for success messages (standard approach across the app)
        // We're now using flash messages converted to toasts via handleFlashMessages() in utils.js
        // This happens automatically on page load and provides a consistent notification experience

        // Clean URL parameters if they exist (for backward compatibility)
        const urlParams = new URLSearchParams(window.location.search);
        if (urlParams.has('status')) {
            const newUrl = window.location.pathname;
            window.history.replaceState({}, document.title, newUrl);
        }
    });
</script>

<style>
    /* Clean, modern tab styles that work in both light and dark modes */
    .tabs-container {
        display: flex;
        justify-content: center;
        width: 100%;
    }

    .tab-button {
        job_title: relative;
        padding: 0.75rem 1.25rem;
        font-size: 0.875rem;
        font-weight: 500;
        background: transparent;
        border: none;
        outline: none;
        transition: color 0.2s ease;
        min-width: 120px;
        /* Light mode colors (default) */
        color: rgba(15, 23, 42, 0.7);
    }

    .tab-button:hover {
        /* Light mode hover */
        color: rgba(15, 23, 42, 0.9);
    }

    .tab-button::after {
        content: '';
        job_title: absolute;
        bottom: -1px;
        left: 0;
        width: 100%;
        height: 2px;
        background-color: transparent;
        transition: background-color 0.2s ease;
    }

    /* Active state - light mode */
    .tab-button.active {
        color: rgb(15, 23, 42);
        font-weight: 500;
    }

    .tab-button.active::after {
        background-color: rgb(59, 130, 246); /* Primary blue color */
    }

    /* Focus state for keyboard navigation - light mode */
    .tab-button:focus-visible {
        outline: 2px solid rgba(15, 23, 42, 0.5);
        outline-offset: -2px;
    }

    /* Dark mode styles */
    .dark .tab-button {
        color: rgba(255, 255, 255, 0.7);
    }

    .dark .tab-button:hover {
        color: rgba(255, 255, 255, 0.9);
    }

    .dark .tab-button.active {
        color: white;
    }

    .dark .tab-button.active::after {
        background-color: rgb(59, 130, 246); /* Primary blue color */
    }

    .dark .tab-button:focus-visible {
        outline-color: rgba(255, 255, 255, 0.5);
    }

    /* Enhanced animation for tab content */
    .tab-content {
        opacity: 0;
        transform: translateY(10px);
        transition: opacity 0.3s ease, transform 0.3s ease;
        display: none;
    }

    .tab-content.active {
        opacity: 1;
        transform: translateY(0);
        display: block;
    }

    /* Add a subtle scale animation to the active tab icon */
    .tab-button.active i {
        animation: pulse 1s ease-in-out;
    }

    @keyframes pulse {
        0% { transform: scale(1); }
        50% { transform: scale(1.2); }
        100% { transform: scale(1); }
    }

    /* Modal positioning fix */
    .modal {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        z-index: 50;
        display: none;
    }

    .modal.modal-open {
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .modal-overlay {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        z-index: 40;
    }

    .modal-container {
        position: relative;
        z-index: 50;
        max-width: 90vw;
        max-height: 90vh;
    }

    /* Animation for modal */
    .modal-content {
        transform: translateY(0);
        opacity: 1;
        transition: transform 0.3s ease, opacity 0.3s ease;
    }

    .modal-closing .modal-content {
        transform: translateY(20px);
        opacity: 0;
    }
</style>
{% endblock %}

/**
 * Flash Messages Handler
 * Converts server-side flash messages to client-side toast notifications
 */

// Initialize flash message handling
function initFlashHandler() {
  // Process flash messages and convert to toasts
  // Check if handleFlashMessages is available
  if (typeof handleFlashMessages === 'function') {
    handleFlashMessages();
  } else {
    // If not available yet, wait for utils.js to load
    console.log('Waiting for handleFlashMessages to be available...');
    const checkInterval = setInterval(function() {
      if (typeof handleFlashMessages === 'function') {
        handleFlashMessages();
        clearInterval(checkInterval);
      }
    }, 100);

    // Safety timeout after 3 seconds
    setTimeout(function() {
      clearInterval(checkInterval);
    }, 3000);
  }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', initFlashHandler);

/**
 * Drawer Utilities Module
 *
 * This module provides utility functions for the drawer component.
 */

/**
 * Trap focus within a container
 * @param {HTMLElement} container - Container element to trap focus within
 * @param {Function} onEscape - Callback function to call when the escape key is pressed
 * @returns {Function} - Function to remove the event listener
 */
function trapFocus(container, onEscape) {
  // Get all focusable elements
  const focusableElements = container.querySelectorAll(
    'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
  );

  if (focusableElements.length === 0) {
    return () => {};
  }

  const firstElement = focusableElements[0];
  const lastElement = focusableElements[focusableElements.length - 1];

  // Focus the first element
  setTimeout(() => {
    firstElement.focus();
  }, 100);

  // Handle keydown events
  function handleKeyDown(e) {
    // Handle escape key
    if (e.key === 'Escape' && typeof onEscape === 'function') {
      onEscape();
      return;
    }

    // Handle tab key
    if (e.key === 'Tab') {
      // If shift+tab on first element, move to last element
      if (e.shiftKey && document.activeElement === firstElement) {
        e.preventDefault();
        lastElement.focus();
      }
      // If tab on last element, move to first element
      else if (!e.shiftKey && document.activeElement === lastElement) {
        e.preventDefault();
        firstElement.focus();
      }
    }
  }

  // Add event listener
  document.addEventListener('keydown', handleKeyDown);

  // Return function to remove event listener
  return () => {
    document.removeEventListener('keydown', handleKeyDown);
  };
}

/**
 * Create a loading spinner element
 * @returns {HTMLElement} - The loading spinner element
 */
function createLoadingSpinner() {
  const spinner = document.createElement('div');
  spinner.className = 'loading-spinner flex items-center justify-center p-4';
  spinner.innerHTML = `
    <svg class="animate-spin h-8 w-8 text-primary" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
      <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
      <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
    </svg>
  `;
  return spinner;
}

/**
 * Create an error message element
 * @param {string} message - Error message to display
 * @returns {HTMLElement} - The error message element
 */
function createErrorMessage(message) {
  const errorElement = document.createElement('div');
  errorElement.className = 'error-message bg-destructive/10 border border-destructive/20 text-destructive px-4 py-3 rounded relative my-4';
  errorElement.innerHTML = `
    <div class="flex items-center">
      <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
      </svg>
      <span>${message}</span>
    </div>
  `;
  return errorElement;
}

/**
 * Apply theme-based styles to an element
 * @param {HTMLElement} element - Element to apply styles to
 * @returns {string} - The border color used, for reference
 */
function applyThemeStyles(element) {
  const isDarkMode = document.documentElement.classList.contains('dark');

  // Apply background color
  element.style.backgroundColor = isDarkMode ? '#111827' : '#ffffff';

  // Apply text color
  element.style.color = isDarkMode ? '#fff' : '#111827';

  // Apply border color
  const borderColor = isDarkMode ? '#1f2937' : '#e5e7eb';
  element.style.borderColor = borderColor;

  // Apply shadow
  element.style.boxShadow = isDarkMode
    ? '0 0 15px rgba(0, 0, 0, 0.5)'
    : '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)';

  return borderColor; // Return border color for use in other elements
}

// Export the utility functions
export {
  trapFocus,
  createLoadingSpinner,
  createErrorMessage,
  applyThemeStyles
};

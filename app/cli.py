"""
CLI commands for the application.
"""
import click
from flask.cli import with_appcontext
from app import db
from app.utils.settings import initialize_default_settings

@click.command('seed-settings')
@with_appcontext
def seed_settings_command():
    """Initialize default settings in the database."""
    try:
        click.echo("Seeding default settings...")
        initialize_default_settings()
        click.echo("Default settings seeded successfully!")
    except Exception as e:
        click.echo(f"Error seeding settings: {e}")
        raise e

def register_commands(app):
    """Register CLI commands with the Flask application."""
    app.cli.add_command(seed_settings_command)

{% extends "base.html" %}
{% from "components/page_header.html" import page_header %}
{% from 'components/entity_activity_log.html' import entity_activity_log %}

{% block title %}{{ business_unit.name }} - Business Unit{% endblock %}

{% block header %}Business Unit Details{% endblock %}

{% block content %}
<div class="space-y-6">
  {{ page_header(
    title=business_unit.name,
    button_text="Edit Business Unit",
    button_icon="edit",
    button_action="drawerManager.openForm('business_unit', " ~ business_unit.id ~ ")",
    description="View and manage business unit details"
  ) }}

  <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
    <!-- Business Unit Details -->
    <div class="md:col-span-2">
      <div class="card">
        <div class="p-6 border-b border-border">
          <h2 class="text-lg font-semibold">Business Unit Information</h2>
          <p class="text-sm text-muted-foreground">Details about this business unit</p>
        </div>
        <div class="p-6">
          <div class="space-y-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div class="space-y-4">
                <div>
                  <label class="block text-sm font-medium text-muted-foreground mb-1">Name</label>
                  <div class="p-2 bg-muted/30 rounded-md">{{ business_unit.name }}</div>
                </div>
                <div>
                  <label class="block text-sm font-medium text-muted-foreground mb-1">Code</label>
                  <div class="p-2 bg-muted/30 rounded-md">{{ business_unit.code }}</div>
                </div>
                <div>
                  <label class="block text-sm font-medium text-muted-foreground mb-1">Status</label>
                  <div class="p-2 bg-muted/30 rounded-md">
                    {% if business_unit.is_active %}
                      <span class="badge badge-success">Active</span>
                    {% else %}
                      <span class="badge badge-secondary">Inactive</span>
                    {% endif %}
                  </div>
                </div>
              </div>
              <div class="space-y-4">
                <div>
                  <label class="block text-sm font-medium text-muted-foreground mb-1">Manager</label>
                  <div class="p-2 bg-muted/30 rounded-md">{{ business_unit.manager.name if business_unit.manager else 'Not assigned' }}</div>
                </div>
                <div>
                  <label class="block text-sm font-medium text-muted-foreground mb-1">Employees</label>
                  <div class="p-2 bg-muted/30 rounded-md">{{ business_unit.employee_details|length }}</div>
                </div>
                <div>
                  <label class="block text-sm font-medium text-muted-foreground mb-1">Business Segments</label>
                  <div class="p-2 bg-muted/30 rounded-md">{{ business_unit.segments.count() }}</div>
                </div>
              </div>
            </div>

            {% if business_unit.description %}
            <div>
              <label class="block text-sm font-medium text-muted-foreground mb-1">Description</label>
              <div class="p-4 bg-muted/30 rounded-md">{{ business_unit.description }}</div>
            </div>
            {% endif %}
          </div>
        </div>
      </div>
    </div>

    <!-- Activity Log -->
    <div class="md:col-span-1">
      <div class="card">
        <div class="p-6 border-b border-border">
          <h2 class="text-lg font-semibold">Activity History</h2>
          <p class="text-sm text-muted-foreground">Recent changes to this business unit</p>
        </div>
        <div class="p-6">
          {{ entity_activity_log(entity_type="BusinessUnit", entity_id=business_unit.id, limit=5, show_title=false) }}
        </div>
      </div>
    </div>
  </div>

  <!-- Employees in this Business Unit -->
  {% if business_unit.employee_details %}
  <div class="card">
    <div class="p-6 border-b border-border">
      <h2 class="text-lg font-semibold">Employees</h2>
      <p class="text-sm text-muted-foreground">Employees assigned to this business unit</p>
    </div>
    <div class="p-6">
      <div class="overflow-x-auto">
        <table class="w-full">
          <thead>
            <tr class="border-b border-border text-left">
              <th class="px-4 py-3 text-sm font-medium">Name</th>
              <th class="px-4 py-3 text-sm font-medium">Position</th>
              <th class="px-4 py-3 text-sm font-medium">Employee Number</th>
              <th class="px-4 py-3 text-sm font-medium">Business Segment</th>
              <th class="px-4 py-3 text-sm font-medium">Actions</th>
            </tr>
          </thead>
          <tbody>
            {% for detail in business_unit.employee_details %}
            <tr class="border-b border-border hover:bg-muted/30">
              <td class="px-4 py-3 text-sm">{{ detail.user.name }}</td>
              <td class="px-4 py-3 text-sm">{{ detail.job_title or 'Not specified' }}</td>
              <td class="px-4 py-3 text-sm">{{ detail.employee_number or 'Not specified' }}</td>
              <td class="px-4 py-3 text-sm">{{ detail.business_segment.name if detail.business_segment else 'Not assigned' }}</td>
              <td class="px-4 py-3 text-sm">
                <a href="{{ url_for('admin.view_employee_detail', employee_id=detail.id) }}" class="btn btn-sm btn-outline">
                  <i data-lucide="eye" class="h-3.5 w-3.5 mr-1"></i>
                  View
                </a>
              </td>
            </tr>
            {% endfor %}
          </tbody>
        </table>
      </div>
    </div>
  </div>
  {% endif %}

  <!-- Business Segments in this Business Unit -->
  {% if business_unit.segments.count() > 0 %}
  <div class="card">
    <div class="p-6 border-b border-border">
      <h2 class="text-lg font-semibold">Business Segments</h2>
      <p class="text-sm text-muted-foreground">Segments within this business unit</p>
    </div>
    <div class="p-6">
      <div class="overflow-x-auto">
        <table class="w-full">
          <thead>
            <tr class="border-b border-border text-left">
              <th class="px-4 py-3 text-sm font-medium">Code</th>
              <th class="px-4 py-3 text-sm font-medium">Name</th>
              <th class="px-4 py-3 text-sm font-medium">Manager</th>
              <th class="px-4 py-3 text-sm font-medium">Employees</th>
              <th class="px-4 py-3 text-sm font-medium">Status</th>
              <th class="px-4 py-3 text-sm font-medium">Actions</th>
            </tr>
          </thead>
          <tbody>
            {% for segment in business_unit.segments %}
            <tr class="border-b border-border hover:bg-muted/30">
              <td class="px-4 py-3 text-sm">{{ segment.code }}</td>
              <td class="px-4 py-3 text-sm">{{ segment.name }}</td>
              <td class="px-4 py-3 text-sm">{{ segment.manager.name if segment.manager else 'Not assigned' }}</td>
              <td class="px-4 py-3 text-sm">{{ segment.employees|length }}</td>
              <td class="px-4 py-3 text-sm">
                {% if segment.is_active %}
                  <span class="badge badge-success">Active</span>
                {% else %}
                  <span class="badge badge-secondary">Inactive</span>
                {% endif %}
              </td>
              <td class="px-4 py-3 text-sm">
                <button class="btn btn-sm btn-outline" onclick="drawerManager.openForm('business_segment', {{ segment.id }})">
                  <i data-lucide="edit" class="h-3.5 w-3.5 mr-1"></i>
                  Edit
                </button>
              </td>
            </tr>
            {% endfor %}
          </tbody>
        </table>
      </div>
    </div>
  </div>
  {% endif %}
</div>
{% endblock %}

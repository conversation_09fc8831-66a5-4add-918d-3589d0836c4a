/**
 * Attendance Calendar Component
 * Handles attendance calendar display with holiday integration
 */
class AttendanceCalendar {
  constructor(containerId, options = {}) {
    this.container = document.getElementById(containerId);
    this.options = {
      showHolidays: true,
      showAttendance: true,
      enableDateSelection: true,
      apiEndpoints: {
        attendance: '/api/attendance/calendar',
        holidays: '/api/holidays/calendar'
      },
      userRegion: 'PH',
      ...options
    };
    
    this.currentDate = new Date();
    this.attendanceData = {};
    this.holidayData = {};
    this.isLoading = false;
    
    this.init();
  }

  /**
   * Initialize the calendar
   */
  init() {
    if (!this.container) {
      console.error('Calendar container not found');
      return;
    }
    
    this.setupEventListeners();
    this.render();
  }

  /**
   * Setup event listeners for navigation
   */
  setupEventListeners() {
    // Navigation buttons
    const prevBtn = document.getElementById('prevMonth');
    const nextBtn = document.getElementById('nextMonth');
    const todayBtn = document.getElementById('todayBtn');
    
    if (prevBtn) {
      prevBtn.addEventListener('click', () => this.navigateToPreviousMonth());
    }
    
    if (nextBtn) {
      nextBtn.addEventListener('click', () => this.navigateToNextMonth());
    }
    
    if (todayBtn) {
      todayBtn.addEventListener('click', () => this.navigateToToday());
    }

    // Month/Year selectors
    const monthSelect = document.getElementById('monthSelect');
    const yearSelect = document.getElementById('yearSelect');
    
    if (monthSelect) {
      monthSelect.addEventListener('change', (e) => {
        this.currentDate.setMonth(parseInt(e.target.value));
        this.render();
      });
    }
    
    if (yearSelect) {
      yearSelect.addEventListener('change', (e) => {
        this.currentDate.setFullYear(parseInt(e.target.value));
        this.render();
      });
    }
  }

  /**
   * Navigate to previous month
   */
  navigateToPreviousMonth() {
    this.currentDate.setMonth(this.currentDate.getMonth() - 1);
    this.render();
  }

  /**
   * Navigate to next month
   */
  navigateToNextMonth() {
    this.currentDate.setMonth(this.currentDate.getMonth() + 1);
    this.render();
  }

  /**
   * Navigate to today
   */
  navigateToToday() {
    this.currentDate = new Date();
    this.render();
  }

  /**
   * Load data and render calendar
   */
  async render() {
    this.showLoading();
    await this.loadData();
    this.renderCalendar();
    this.updateTitle();
    this.updateSelectors();
  }

  /**
   * Show loading state
   */
  showLoading() {
    this.container.innerHTML = `
      <div class="text-center py-8">
        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
        <p class="mt-2 text-sm text-gray-500 dark:text-gray-400">Loading calendar...</p>
      </div>
    `;
  }

  /**
   * Load attendance and holiday data
   */
  async loadData() {
    this.isLoading = true;
    
    try {
      const year = this.currentDate.getFullYear();
      const month = this.currentDate.getMonth() + 1;
      
      // Load attendance data
      if (this.options.showAttendance) {
        await this.loadAttendanceData(year, month);
      }
      
      // Load holiday data
      if (this.options.showHolidays) {
        await this.loadHolidayData(year, month);
      }
      
    } catch (error) {
      console.error('Error loading calendar data:', error);
    } finally {
      this.isLoading = false;
    }
  }

  /**
   * Load attendance data from API
   */
  async loadAttendanceData(year, month) {
    try {
      const response = await fetch(`${this.options.apiEndpoints.attendance}?year=${year}&month=${month}`);
      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          this.attendanceData = result.calendar_data || {};
        } else {
          console.error('Failed to load attendance data:', result.error);
          this.attendanceData = {};
        }
      } else {
        console.error('Failed to fetch attendance data');
        this.attendanceData = {};
      }
    } catch (error) {
      console.error('Error loading attendance data:', error);
      this.attendanceData = {};
    }
  }

  /**
   * Load holiday data from API
   */
  async loadHolidayData(year, month) {
    try {
      const response = await fetch(`${this.options.apiEndpoints.holidays}/${this.options.userRegion}/${year}?month=${month}`);
      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          this.holidayData = result.calendar_data || {};
        } else {
          console.error('Failed to load holiday data:', result.error);
          this.holidayData = {};
        }
      } else {
        console.error('Failed to fetch holiday data');
        this.holidayData = {};
      }
    } catch (error) {
      console.error('Error loading holiday data:', error);
      this.holidayData = {};
    }
  }

  /**
   * Render the calendar grid
   */
  renderCalendar() {
    const year = this.currentDate.getFullYear();
    const month = this.currentDate.getMonth();
    
    // Get calendar structure
    const firstDay = new Date(year, month, 1);
    const lastDay = new Date(year, month + 1, 0);
    const daysInMonth = lastDay.getDate();
    const startingDayOfWeek = firstDay.getDay();
    
    let html = this.renderCalendarHeader();
    html += this.renderCalendarDays(year, month, daysInMonth, startingDayOfWeek);
    
    this.container.innerHTML = html;
  }

  /**
   * Render calendar header with day names
   */
  renderCalendarHeader() {
    const dayNames = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
    
    let html = '<div class="grid grid-cols-7 gap-1">';
    
    dayNames.forEach(day => {
      html += `<div class="p-2 text-center text-sm font-medium text-gray-500 dark:text-gray-400">${day}</div>`;
    });
    
    return html;
  }

  /**
   * Render calendar days
   */
  renderCalendarDays(year, month, daysInMonth, startingDayOfWeek) {
    let html = '';
    
    // Empty cells for days before month starts
    for (let i = 0; i < startingDayOfWeek; i++) {
      html += '<div class="p-2 h-24"></div>';
    }
    
    // Days of the month
    for (let day = 1; day <= daysInMonth; day++) {
      html += this.renderCalendarDay(year, month, day);
    }
    
    html += '</div>';
    return html;
  }

  /**
   * Render a single calendar day
   */
  renderCalendarDay(year, month, day) {
    const dateStr = `${year}-${String(month + 1).padStart(2, '0')}-${String(day).padStart(2, '0')}`;
    const isToday = new Date().toDateString() === new Date(year, month, day).toDateString();
    const hasAttendance = this.attendanceData[dateStr];
    const isHoliday = this.holidayData[dateStr];
    
    const dayClass = this.getDayClasses(isToday, isHoliday);
    const indicators = this.getDayIndicators(hasAttendance, isHoliday);
    const tooltip = this.getDayTooltip(dateStr, hasAttendance, isHoliday);
    const content = this.getDayContent(day, hasAttendance, isHoliday, isToday);
    
    const clickHandler = this.options.enableDateSelection ? `onclick="window.selectDate('${dateStr}')"` : '';
    
    return `
      <div class="${dayClass}" ${clickHandler} title="${tooltip}">
        ${content}
        ${indicators}
      </div>
    `;
  }

  /**
   * Get CSS classes for a day
   */
  getDayClasses(isToday, isHoliday) {
    let classes = 'p-2 h-24 border border-gray-200 dark:border-gray-600 relative transition-colors';
    
    if (this.options.enableDateSelection) {
      classes += ' cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700';
    }
    
    if (isToday) {
      classes += ' bg-blue-50 dark:bg-blue-900/20 border-blue-300 dark:border-blue-600';
    }
    
    if (isHoliday) {
      classes += ' bg-blue-50 dark:bg-blue-900/10 border-blue-200 dark:border-blue-800';
    }
    
    return classes;
  }

  /**
   * Get indicators for a day
   */
  getDayIndicators(hasAttendance, isHoliday) {
    let indicators = '';
    
    if (isHoliday) {
      indicators += '<div class="absolute top-1 right-1 w-2 h-2 bg-blue-500 rounded-full" title="Holiday"></div>';
    }
    
    if (hasAttendance) {
      const statusColor = this.getStatusColor(hasAttendance.status);
      indicators += `<div class="absolute bottom-1 left-1 w-2 h-2 ${statusColor} rounded-full" title="Attendance: ${hasAttendance.status_display}"></div>`;
    }
    
    return indicators;
  }

  /**
   * Get status color for attendance
   */
  getStatusColor(status) {
    const colors = {
      'pending': 'bg-yellow-500',
      'approved': 'bg-green-500',
      'rejected': 'bg-red-500',
      'auto_approved': 'bg-green-500',
      'cancelled': 'bg-gray-500'
    };
    return colors[status] || 'bg-gray-500';
  }

  /**
   * Get tooltip text for a day
   */
  getDayTooltip(dateStr, hasAttendance, isHoliday) {
    let tooltip = dateStr;
    
    if (isHoliday) {
      tooltip += `\nHoliday: ${isHoliday.name}`;
      if (isHoliday.description) {
        tooltip += `\n${isHoliday.description}`;
      }
    }
    
    if (hasAttendance) {
      tooltip += `\nAttendance: ${hasAttendance.type} (${hasAttendance.status_display})`;
      if (hasAttendance.start_time && hasAttendance.end_time) {
        tooltip += `\nTime: ${hasAttendance.start_time} - ${hasAttendance.end_time}`;
      }
    }
    
    return tooltip;
  }

  /**
   * Get content for a day
   */
  getDayContent(day, hasAttendance, isHoliday, isToday) {
    const dayNumberClass = isToday ? 'font-bold text-blue-600 dark:text-blue-400' : 'text-gray-900 dark:text-white';
    
    let content = `<div class="text-sm ${dayNumberClass}">${day}</div>`;
    
    if (isHoliday) {
      content += `<div class="text-xs text-blue-600 dark:text-blue-400 mt-1 truncate font-medium">${isHoliday.name}</div>`;
    }
    
    if (hasAttendance) {
      content += `<div class="text-xs text-gray-600 dark:text-gray-400 mt-1 truncate">${hasAttendance.type}</div>`;
    }
    
    return content;
  }

  /**
   * Update calendar title
   */
  updateTitle() {
    const titleElement = document.getElementById('calendarTitle');
    if (titleElement) {
      const monthNames = [
        'January', 'February', 'March', 'April', 'May', 'June',
        'July', 'August', 'September', 'October', 'November', 'December'
      ];
      titleElement.textContent = `${monthNames[this.currentDate.getMonth()]} ${this.currentDate.getFullYear()}`;
    }
  }

  /**
   * Update month and year selectors
   */
  updateSelectors() {
    const monthSelect = document.getElementById('monthSelect');
    const yearSelect = document.getElementById('yearSelect');
    
    if (monthSelect) {
      monthSelect.value = this.currentDate.getMonth();
    }
    
    if (yearSelect) {
      yearSelect.value = this.currentDate.getFullYear();
    }
  }
}

// Export for use in other modules
window.AttendanceCalendar = AttendanceCalendar;

# Database Schema Documentation

**Last Updated:** January 2025  
**Version:** 1.0.0  
**Database:** SQLite (development), PostgreSQL (production)

## Overview

The Matrix App database schema is designed to support a comprehensive employee management system with organizational structure, team management, attendance tracking, and comprehensive audit logging. The schema follows relational database best practices with proper normalization and referential integrity.

## Entity Relationship Diagram

```mermaid
erDiagram
    User ||--o| EmployeeDetail : has
    User ||--o{ Activity : creates
    User ||--o{ BusinessUnit : manages
    User ||--o{ BusinessSegment : manages
    User ||--o{ PasswordReset : requests
    
    EmployeeDetail }o--|| BusinessUnit : belongs_to
    EmployeeDetail }o--|| BusinessSegment : belongs_to
    EmployeeDetail }o--o| User : managed_by
    EmployeeDetail ||--o{ team_members : participates
    
    Team ||--o{ TeamGroup : contains
    Team ||--o{ team_members : has
    
    BusinessUnit ||--o{ EmployeeDetail : contains
    BusinessSegment ||--o{ EmployeeDetail : contains
    
    Holiday ||--o{ AttendanceRecord : affects
    WorkScheduleDefinition ||--o{ EmployeeSchedule : defines
    EmployeeDetail ||--o{ EmployeeSchedule : has
    EmployeeDetail ||--o{ AttendanceRecord : records
    AttendanceType ||--o{ AttendanceRecord : categorizes
```

## Core Tables

### Users Table

**Purpose:** Central authentication and user management

```sql
CREATE TABLE users (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name VARCHAR(100) NOT NULL,
    email VARCHAR(120) UNIQUE NOT NULL,
    password_hash VARCHAR(255),
    role VARCHAR(20) DEFAULT 'User',
    is_active BOOLEAN DEFAULT TRUE,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    last_login DATETIME,
    last_password_date DATETIME,
    bio TEXT,
    deleted_at DATETIME
);

CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_role ON users(role);
CREATE INDEX idx_users_active ON users(is_active);
```

**Fields:**
- `id`: Primary key, auto-increment
- `name`: Full name of the user
- `email`: Unique email address for authentication
- `password_hash`: Hashed password using Werkzeug
- `role`: User role (Admin, Manager, User)
- `is_active`: Account status flag
- `created_at`: Account creation timestamp
- `updated_at`: Last modification timestamp
- `last_login`: Last successful login
- `last_password_date`: Password last changed date
- `bio`: Optional user biography
- `deleted_at`: Soft deletion timestamp

### Employee Details Table

**Purpose:** Extended employee information and organizational relationships

```sql
CREATE TABLE employee_details (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER UNIQUE NOT NULL,
    employee_id VARCHAR(50) UNIQUE,
    position VARCHAR(100),
    department VARCHAR(100),
    hire_date DATE,
    business_unit_id INTEGER,
    business_segment_id INTEGER,
    direct_manager_user_id INTEGER,
    phone VARCHAR(20),
    address TEXT,
    emergency_contact_name VARCHAR(100),
    emergency_contact_phone VARCHAR(20),
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (business_unit_id) REFERENCES business_units(id),
    FOREIGN KEY (business_segment_id) REFERENCES business_segments(id),
    FOREIGN KEY (direct_manager_user_id) REFERENCES users(id)
);

CREATE INDEX idx_employee_details_user_id ON employee_details(user_id);
CREATE INDEX idx_employee_details_business_unit ON employee_details(business_unit_id);
CREATE INDEX idx_employee_details_manager ON employee_details(direct_manager_user_id);
```

### Business Units Table

**Purpose:** Organizational structure - departments and divisions

```sql
CREATE TABLE business_units (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name VARCHAR(100) NOT NULL,
    code VARCHAR(20) UNIQUE NOT NULL,
    description TEXT,
    manager_id INTEGER,
    parent_unit_id INTEGER,
    is_active BOOLEAN DEFAULT TRUE,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (manager_id) REFERENCES users(id),
    FOREIGN KEY (parent_unit_id) REFERENCES business_units(id)
);

CREATE UNIQUE INDEX idx_business_units_code ON business_units(code);
CREATE INDEX idx_business_units_manager ON business_units(manager_id);
```

### Business Segments Table

**Purpose:** Cross-functional business segments

```sql
CREATE TABLE business_segments (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name VARCHAR(100) NOT NULL,
    code VARCHAR(20) UNIQUE NOT NULL,
    description TEXT,
    manager_id INTEGER,
    business_unit_id INTEGER,
    is_active BOOLEAN DEFAULT TRUE,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (manager_id) REFERENCES users(id),
    FOREIGN KEY (business_unit_id) REFERENCES business_units(id)
);

CREATE UNIQUE INDEX idx_business_segments_code ON business_segments(code);
CREATE INDEX idx_business_segments_manager ON business_segments(manager_id);
```

## Team Management Tables

### Teams Table

**Purpose:** Cross-functional team organization

```sql
CREATE TABLE teams (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name VARCHAR(100) NOT NULL,
    slug VARCHAR(100) UNIQUE NOT NULL,
    description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

CREATE UNIQUE INDEX idx_teams_slug ON teams(slug);
CREATE INDEX idx_teams_active ON teams(is_active);
```

### Team Groups Table

**Purpose:** Subgroups within teams

```sql
CREATE TABLE team_groups (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    team_id INTEGER NOT NULL,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (team_id) REFERENCES teams(id) ON DELETE CASCADE
);

CREATE INDEX idx_team_groups_team_id ON team_groups(team_id);
```

### Team Members Association Table

**Purpose:** Many-to-many relationship between employees and teams

```sql
CREATE TABLE team_members (
    team_id INTEGER NOT NULL,
    employee_detail_id INTEGER NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    
    PRIMARY KEY (team_id, employee_detail_id),
    FOREIGN KEY (team_id) REFERENCES teams(id) ON DELETE CASCADE,
    FOREIGN KEY (employee_detail_id) REFERENCES employee_details(id) ON DELETE CASCADE
);
```

## Attendance Management Tables

### Holidays Table

**Purpose:** Company holiday calendar

```sql
CREATE TABLE holidays (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name VARCHAR(100) NOT NULL,
    date DATE NOT NULL,
    is_recurring BOOLEAN DEFAULT FALSE,
    description TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_holidays_date ON holidays(date);
```

### Work Schedule Definitions Table

**Purpose:** Define work schedule templates

```sql
CREATE TABLE work_schedule_definitions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    monday_start TIME,
    monday_end TIME,
    tuesday_start TIME,
    tuesday_end TIME,
    wednesday_start TIME,
    wednesday_end TIME,
    thursday_start TIME,
    thursday_end TIME,
    friday_start TIME,
    friday_end TIME,
    saturday_start TIME,
    saturday_end TIME,
    sunday_start TIME,
    sunday_end TIME,
    is_active BOOLEAN DEFAULT TRUE,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

### Employee Schedules Table

**Purpose:** Assign schedules to employees

```sql
CREATE TABLE employee_schedules (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    employee_detail_id INTEGER NOT NULL,
    work_schedule_definition_id INTEGER NOT NULL,
    effective_date DATE NOT NULL,
    end_date DATE,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (employee_detail_id) REFERENCES employee_details(id) ON DELETE CASCADE,
    FOREIGN KEY (work_schedule_definition_id) REFERENCES work_schedule_definitions(id)
);

CREATE INDEX idx_employee_schedules_employee ON employee_schedules(employee_detail_id);
CREATE INDEX idx_employee_schedules_effective_date ON employee_schedules(effective_date);
```

### Attendance Types Table

**Purpose:** Categorize different types of attendance records

```sql
CREATE TABLE attendance_types (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name VARCHAR(50) NOT NULL,
    code VARCHAR(20) UNIQUE NOT NULL,
    description TEXT,
    is_paid BOOLEAN DEFAULT TRUE,
    requires_approval BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

CREATE UNIQUE INDEX idx_attendance_types_code ON attendance_types(code);
```

### Attendance Records Table

**Purpose:** Track employee attendance

```sql
CREATE TABLE attendance_records (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    employee_detail_id INTEGER NOT NULL,
    attendance_type_id INTEGER NOT NULL,
    date DATE NOT NULL,
    time_in TIME,
    time_out TIME,
    break_duration INTEGER DEFAULT 0,
    notes TEXT,
    approved_by INTEGER,
    approved_at DATETIME,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (employee_detail_id) REFERENCES employee_details(id) ON DELETE CASCADE,
    FOREIGN KEY (attendance_type_id) REFERENCES attendance_types(id),
    FOREIGN KEY (approved_by) REFERENCES users(id)
);

CREATE INDEX idx_attendance_records_employee_date ON attendance_records(employee_detail_id, date);
CREATE INDEX idx_attendance_records_date ON attendance_records(date);
```

## System Tables

### Activity Logs Table

**Purpose:** Comprehensive audit trail

```sql
CREATE TABLE activities (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER,
    action VARCHAR(255) NOT NULL,
    entity_type VARCHAR(50),
    entity_id INTEGER,
    details TEXT,
    ip_address VARCHAR(45),
    user_agent TEXT,
    url VARCHAR(500),
    method VARCHAR(10),
    severity VARCHAR(20) DEFAULT 'info',
    category VARCHAR(50),
    old_values TEXT,
    new_values TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

CREATE INDEX idx_activities_user_created ON activities(user_id, created_at);
CREATE INDEX idx_activities_entity ON activities(entity_type, entity_id);
CREATE INDEX idx_activities_category ON activities(category);
CREATE INDEX idx_activities_severity ON activities(severity);
CREATE INDEX idx_activities_created_at ON activities(created_at);
```

### Settings Table

**Purpose:** System configuration

```sql
CREATE TABLE settings (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    key VARCHAR(100) UNIQUE NOT NULL,
    value TEXT,
    type VARCHAR(20) DEFAULT 'string',
    description TEXT,
    is_public BOOLEAN DEFAULT FALSE,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

CREATE UNIQUE INDEX idx_settings_key ON settings(key);
CREATE INDEX idx_settings_public ON settings(is_public);
```

### Password Reset Table

**Purpose:** Secure password reset tokens

```sql
CREATE TABLE password_resets (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL,
    token VARCHAR(255) UNIQUE NOT NULL,
    expires_at DATETIME NOT NULL,
    used_at DATETIME,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

CREATE INDEX idx_password_resets_token ON password_resets(token);
CREATE INDEX idx_password_resets_user_id ON password_resets(user_id);
CREATE INDEX idx_password_resets_expires_at ON password_resets(expires_at);
```

## Data Relationships

### Key Relationships

1. **User ↔ Employee Detail**: One-to-one relationship
2. **Employee ↔ Business Unit**: Many-to-one relationship
3. **Employee ↔ Business Segment**: Many-to-one relationship
4. **Employee ↔ Teams**: Many-to-many through team_members
5. **User ↔ Activities**: One-to-many relationship
6. **Employee ↔ Attendance**: One-to-many relationship

### Referential Integrity

- **Cascading Deletes**: User deletion cascades to employee details and activities
- **Soft Deletes**: Users use `deleted_at` field for soft deletion
- **Foreign Key Constraints**: Maintain data consistency across relationships

## Performance Optimizations

### Indexing Strategy

1. **Primary Keys**: Automatic indexes on all primary keys
2. **Foreign Keys**: Indexes on all foreign key columns
3. **Unique Constraints**: Indexes on email, codes, slugs
4. **Composite Indexes**: Multi-column indexes for common query patterns
5. **Date Indexes**: Optimized for date-range queries

### Query Optimization

- **Eager Loading**: Prevent N+1 queries with SQLAlchemy relationships
- **Pagination**: Limit result sets for large tables
- **Selective Queries**: Only fetch required columns
- **Connection Pooling**: Efficient database connection management

---

This database schema provides a robust foundation for the Matrix App employee management system with proper normalization, referential integrity, and performance optimizations.

{% extends 'base.html' %}

{% block title %}Test Error Logging{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="max-w-md mx-auto bg-card rounded-lg shadow-md p-6">
        <h1 class="text-2xl font-bold mb-6">Test Error Logging</h1>

        <p class="mb-4 text-muted-foreground">This form will update a user and then intentionally trigger an error to test error logging.</p>

        <form method="POST" action="{{ url_for('admin.test_update_error', user_id=user.id) }}">
            <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
            <div class="space-y-4">
                <div>
                    <label for="name" class="block text-sm font-medium mb-1">Name</label>
                    <input type="text" id="name" name="name" value="{{ user.name }}"
                           class="w-full px-3 py-2 border border-border rounded-md">
                </div>

                <div>
                    <label for="email" class="block text-sm font-medium mb-1">Email</label>
                    <input type="email" id="email" name="email" value="{{ user.email }}"
                           class="w-full px-3 py-2 border border-border rounded-md">
                </div>

                <div class="pt-4">
                    <button type="submit" class="btn btn-primary w-full">
                        Update and Trigger Error
                    </button>
                </div>
            </div>
        </form>

        <div class="mt-6 text-sm text-muted-foreground">
            <p class="font-medium">What will happen:</p>
            <ol class="list-decimal pl-5 space-y-1 mt-2">
                <li>The user will be updated in the database</li>
                <li>The changes will be committed</li>
                <li>An error will be triggered intentionally</li>
                <li>The error should be logged by the log_activity decorator</li>
                <li>You'll see an error page</li>
                <li>Check the activity logs to see the error entry</li>
            </ol>
        </div>
    </div>
</div>
{% endblock %}

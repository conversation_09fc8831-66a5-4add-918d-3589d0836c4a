/**
 * Drawer Component Module
 *
 * This module provides the DrawerComponent class for creating and managing drawer UI elements.
 */

import { initFormValidation } from './form-handler.js';
import { trapFocus, applyThemeStyles } from './utils.js';

/**
 * DrawerComponent for creating and managing drawer UI elements
 */
class DrawerComponent {
  /**
   * Create a new drawer component
   * @param {Object} options - Options for the drawer
   * @param {string} options.position - Position of the drawer ('right', 'left', 'top', 'bottom')
   * @param {string} options.size - Size of the drawer ('sm', 'md', 'lg', 'xl', 'full')
   * @param {string} options.width - Custom width for the drawer (e.g., '30rem', '500px')
   * @param {boolean} options.closeOnEscape - Whether to close the drawer when the escape key is pressed
   * @param {boolean} options.closeOnOverlayClick - Whether to close the drawer when the overlay is clicked
   * @param {Function} options.onClose - Callback function to call when the drawer is closed
   */
  constructor(options = {}) {
    this.position = options.position || 'right';
    this.size = options.size || 'md';
    this.width = options.width || null; // Custom width takes precedence over size
    this.closeOnEscape = options.closeOnEscape !== false;
    this.closeOnOverlayClick = options.closeOnOverlayClick !== false;
    this.onClose = options.onClose;

    this.container = document.getElementById('drawer-container');
    if (!this.container) {
      this.container = document.createElement('div');
      this.container.id = 'drawer-container';
      document.body.appendChild(this.container);
    }

    this.drawer = null;
    this.overlay = null;
    this.removeFocusTrap = null;
  }

  /**
   * Open the drawer with the specified content
   * @param {string} content - HTML content to display in the drawer
   */
  open(content) {
    // Create overlay
    this.overlay = document.createElement('div');
    this.overlay.className = 'fixed inset-0 bg-black/50 backdrop-blur-sm z-50 transition-opacity duration-300';
    this.overlay.style.opacity = '0';
    this.container.appendChild(this.overlay);

    // Create drawer
    this.drawer = document.createElement('div');
    this.drawer.className = `fixed z-[51] bg-background shadow-lg transition-transform duration-300 overflow-auto p-6`;

    // Apply theme-based styles
    applyThemeStyles(this.drawer);

    // Apply position-specific styles
    if (this.position === 'right') {
      this.drawer.classList.add('top-0', 'right-0', 'h-full');
      this.drawer.style.transform = 'translateX(100%)';
      this.drawer.style.borderLeft = `1px solid ${this.drawer.style.borderColor}`;

      // Apply width - custom width takes precedence over size
      if (this.width) {
        this.drawer.style.width = this.width;
      } else if (this.size === 'sm') {
        this.drawer.style.width = '20rem';
      } else if (this.size === 'md') {
        this.drawer.style.width = '30rem';
      } else if (this.size === 'lg') {
        this.drawer.style.width = '40rem';
      } else if (this.size === 'xl') {
        this.drawer.style.width = '50rem';
      } else if (this.size === 'full') {
        this.drawer.style.width = '100%';
      } else {
        this.drawer.style.width = '30rem';
      }
    } else if (this.position === 'left') {
      this.drawer.classList.add('top-0', 'left-0', 'h-full');
      this.drawer.style.transform = 'translateX(-100%)';
      this.drawer.style.borderRight = `1px solid ${this.drawer.style.borderColor}`;

      // Apply width - custom width takes precedence over size
      if (this.width) {
        this.drawer.style.width = this.width;
      } else if (this.size === 'sm') {
        this.drawer.style.width = '20rem';
      } else if (this.size === 'md') {
        this.drawer.style.width = '30rem';
      } else if (this.size === 'lg') {
        this.drawer.style.width = '40rem';
      } else if (this.size === 'xl') {
        this.drawer.style.width = '50rem';
      } else if (this.size === 'full') {
        this.drawer.style.width = '100%';
      } else {
        this.drawer.style.width = '30rem';
      }
    } else if (this.position === 'top') {
      this.drawer.classList.add('top-0', 'left-0', 'w-full');
      this.drawer.style.transform = 'translateY(-100%)';
      this.drawer.style.borderBottom = `1px solid ${this.drawer.style.borderColor}`;

      // Apply height
      if (this.size === 'sm') this.drawer.style.height = '20vh';
      else if (this.size === 'md') this.drawer.style.height = '40vh';
      else if (this.size === 'lg') this.drawer.style.height = '60vh';
      else if (this.size === 'xl') this.drawer.style.height = '80vh';
      else if (this.size === 'full') this.drawer.style.height = '100vh';
      else this.drawer.style.height = '40vh';
    } else if (this.position === 'bottom') {
      this.drawer.classList.add('bottom-0', 'left-0', 'w-full');
      this.drawer.style.transform = 'translateY(100%)';
      this.drawer.style.borderTop = `1px solid ${this.drawer.style.borderColor}`;

      // Apply height
      if (this.size === 'sm') this.drawer.style.height = '20vh';
      else if (this.size === 'md') this.drawer.style.height = '40vh';
      else if (this.size === 'lg') this.drawer.style.height = '60vh';
      else if (this.size === 'xl') this.drawer.style.height = '80vh';
      else if (this.size === 'full') this.drawer.style.height = '100vh';
      else this.drawer.style.height = '40vh';
    }

    // Set content
    this.drawer.innerHTML = content;

    // Add close button if not already present
    if (!this.drawer.querySelector('[data-drawer-close]')) {
      const closeButton = document.createElement('button');
      closeButton.setAttribute('data-drawer-close', '');
      closeButton.className = 'absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none';
      closeButton.innerHTML = '<i data-lucide="x" class="h-4 w-4"></i><span class="sr-only">Close</span>';
      this.drawer.prepend(closeButton);
    }

    this.container.appendChild(this.drawer);

    // Prevent body scrolling
    document.body.style.overflow = 'hidden';

    // Add event listeners
    if (this.closeOnOverlayClick) {
      this.overlay.addEventListener('click', () => this.close());
    }

    if (this.closeOnEscape) {
      this.removeFocusTrap = trapFocus(this.drawer, () => this.close());
    }

    // Add close button event listener
    const closeButton = this.drawer.querySelector('[data-drawer-close]');
    if (closeButton) {
      closeButton.addEventListener('click', () => this.close());
    }

    // Add event listeners to any elements with drawer-close class
    const closeElements = this.drawer.querySelectorAll('.drawer-close');
    closeElements.forEach(element => {
      element.addEventListener('click', () => this.close());
    });

    // Initialize form validation
    initFormValidation(this.drawer);

    // Initialize Lucide icons if available
    if (typeof lucide !== 'undefined' && lucide.createIcons) {
      lucide.createIcons({
        root: this.drawer
      });
    }

    // Animate in
    requestAnimationFrame(() => {
      this.overlay.style.opacity = '1';
      this.drawer.style.transform = 'translate(0)';
    });
  }

  /**
   * Close the drawer
   */
  close() {
    if (!this.drawer || !this.overlay) return;

    // Animation
    if (this.position === 'right') {
      this.drawer.style.transform = 'translateX(100%)';
    } else if (this.position === 'left') {
      this.drawer.style.transform = 'translateX(-100%)';
    } else if (this.position === 'top') {
      this.drawer.style.transform = 'translateY(-100%)';
    } else if (this.position === 'bottom') {
      this.drawer.style.transform = 'translateY(100%)';
    }

    this.overlay.style.opacity = '0';

    // Remove after animation
    setTimeout(() => {
      if (this.container.contains(this.overlay)) {
        this.container.removeChild(this.overlay);
      }
      if (this.container.contains(this.drawer)) {
        this.container.removeChild(this.drawer);
      }

      // Restore scrolling
      document.body.style.overflow = '';

      // Call onClose callback
      if (typeof this.onClose === 'function') {
        this.onClose();
      }

      this.drawer = null;
      this.overlay = null;
    }, 300);
  }
}

// Export the DrawerComponent class
export { DrawerComponent };

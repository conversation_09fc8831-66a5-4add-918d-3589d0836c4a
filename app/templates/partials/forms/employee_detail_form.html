{% from "partials/forms/base_form.html" import form_group, form_actions, form_header %}

<form action="{{ action_url }}" method="POST" class="space-y-4">
  <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">

  {{ form_header(
    title = "Edit Employee Details" if entity else "Add Employee Details",
    description = "Update employee information" if entity else "Create new employee details"
  ) }}

  <div class="grid grid-cols-1 gap-4">
    {% if not entity %}
      {% if pre_selected_user %}
        {{ form_group(
          label = "User",
          name = "employee_display",
          value = pre_selected_user.name + " (" + pre_selected_user.email + ")",
          type = "text",
          required = false,
          disabled = true
        ) }}
        <input type="hidden" name="user_id" value="{{ pre_selected_user.id }}">
      {% else %}
        {{ form_group(
          label = "User",
          name = "user_id",
          type = "select",
          required = true,
          options = user_options
        ) }}
      {% endif %}
    {% else %}
      {{ form_group(
        label = "Employee",
        name = "employee_display",
        value = entity.user.name + " (" + entity.user.email + ")",
        type = "text",
        required = false,
        disabled = true
      ) }}
      <input type="hidden" name="user_id" value="{{ entity.user_id }}">
    {% endif %}

    {{ form_group(
      label = "Employee Number",
      name = "employee_number",
      value = entity.employee_number if entity else "",
      required = true,
      placeholder = "EMP-001"
    ) }}

    {{ form_group(
      label = "First Name",
      name = "first_name",
      value = entity.first_name if entity else "",
      required = true,
      placeholder = "John"
    ) }}

    {{ form_group(
      label = "Middle Name",
      name = "middle_name",
      value = entity.middle_name if entity else "",
      required = false,
      placeholder = "Robert"
    ) }}

    {{ form_group(
      label = "Last Name",
      name = "last_name",
      value = entity.last_name if entity else "",
      required = true,
      placeholder = "Doe"
    ) }}

    {{ form_group(
      label = "Legal Name",
      name = "legal_name",
      value = entity.legal_name if entity else "",
      required = true,
      placeholder = "Doe, John Robert"
    ) }}

    {{ form_group(
      label = "Phone",
      name = "phone",
      value = entity.phone if entity else "",
      required = true,
      placeholder = "+63 9XX XXX XXXX"
    ) }}

    {{ form_group(
      label = "Job Title",
      name = "job_title",
      value = entity.job_title if entity else "",
      required = true,
      placeholder = "Software Engineer"
    ) }}

    {{ form_group(
      label = "Employment Type",
      name = "emp_type",
      value = entity.emp_type if entity else "",
      required = true,
      placeholder = "Full-time"
    ) }}

    {{ form_group(
      label = "Enterprise ID",
      name = "enterprise_id",
      value = entity.enterprise_id if entity else "",
      required = true,
      placeholder = "EID12345"
    ) }}

    {{ form_group(
      label = "Manager Name",
      name = "manager_name",
      value = entity.manager_name if entity else "",
      required = true,
      placeholder = "Jane Smith"
    ) }}

    {{ form_group(
      label = "Job Code",
      name = "job_code",
      value = entity.job_code if entity else "",
      required = true,
      placeholder = "P00001"
    ) }}

    {{ form_group(
      label = "Manager Level",
      name = "manager_level",
      value = entity.manager_level if entity else "",
      required = true,
      placeholder = "Senior Manager"
    ) }}

    {{ form_group(
      label = "Job Code Track Level",
      name = "job_code_track_level",
      value = entity.job_code_track_level if entity else "",
      required = true,
      placeholder = "P3"
    ) }}

    {{ form_group(
      label = "Business Unit",
      name = "business_unit_id",
      type = "select",
      value = entity.business_unit_id|string if entity else "",
      required = true,
      options = [("", "-- Select Business Unit --")] + business_unit_options
    ) }}

    {{ form_group(
      label = "Business Segment",
      name = "business_segment_id",
      type = "select",
      value = entity.business_segment_id|string if entity else "",
      options = [("", "-- Select Business Segment --")] + business_segment_options
    ) }}

    {{ form_group(
      label = "Hire Date",
      name = "hire_date",
      type = "date",
      required = true,
      value = entity.hire_date.strftime('%Y-%m-%d') if entity and entity.hire_date else ""
    ) }}

    {{ form_group(
      label = "Employment Status",
      name = "emp_status",
      type = "select",
      value = entity.emp_status if entity else "active",
      required = true,
      options = [
        ("active", "Active"),
        ("terminated", "Terminated"),
        ("leave_of_absence", "Leave of Absence")
      ]
    ) }}

    {{ form_group(
      label = "Notes",
      name = "notes",
      type = "textarea",
      value = entity.notes if entity else "",
      required = false,
      placeholder = "Additional notes about the employee"
    ) }}
  </div>

  {{ form_actions(
    submit_label = "Update Employee Details" if entity else "Add Employee Details"
  ) }}
</form>

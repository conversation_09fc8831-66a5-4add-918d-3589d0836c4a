/**
 * Enhanced Date Input Component
 * Provides better styling and consistent appearance
 */
class DateInput {
  constructor() {
    this.init();
  }

  /**
   * Initialize date inputs
   */
  init() {
    // Find all date inputs
    const dateInputs = document.querySelectorAll('input[type="date"]');

    // Add event listeners to each date input
    dateInputs.forEach(input => {
      // Add the date-input class for styling
      input.classList.add('date-input');

      // Create calendar icon
      const iconContainer = document.createElement('div');
      iconContainer.className = 'absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none';
      iconContainer.innerHTML = '<i data-lucide="calendar" class="h-4 w-4 text-muted-foreground"></i>';

      // Create a wrapper div
      const wrapper = document.createElement('div');
      wrapper.className = 'relative';

      // Insert the wrapper and icon
      input.parentNode.insertBefore(wrapper, input);
      wrapper.appendChild(input);
      wrapper.appendChild(iconContainer);

      // Format the date when it changes
      input.addEventListener('change', () => {
        if (input.value) {
          // Store the formatted date as a data attribute
          const date = new Date(input.value);
          const formattedDate = this.formatDate(date);
          input.setAttribute('data-formatted-date', formattedDate);
        } else {
          input.removeAttribute('data-formatted-date');
        }
      });

      // Initialize with current value if present
      if (input.value) {
        const event = new Event('change');
        input.dispatchEvent(event);
      }
    });

    // Initialize Lucide icons if available
    if (typeof lucide !== 'undefined' && lucide.createIcons) {
      lucide.createIcons();
    }

    // Add a style element to handle the calendar appearance
    this.addCalendarStyles();
  }

  /**
   * Format a date as DD/MM/YYYY
   * @param {Date} date - The date to format
   * @returns {string} - The formatted date
   */
  formatDate(date) {
    const day = String(date.getDate()).padStart(2, '0');
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const year = date.getFullYear();

    return `${day}/${month}/${year}`;
  }

  /**
   * Add styles to handle calendar appearance in both light and dark modes
   */
  addCalendarStyles() {
    // Check if styles are already added
    if (document.getElementById('date-input-styles')) {
      return;
    }

    // Create style element
    const style = document.createElement('style');
    style.id = 'date-input-styles';

    // Add CSS rules
    style.textContent = `
      /* Base styles for date inputs */
      input[type="date"].date-input {
        position: relative;
        appearance: none;
        -webkit-appearance: none;
        -moz-appearance: none;
      }

      /* Hide the default calendar icon */
      input[type="date"].date-input::-webkit-calendar-picker-indicator {
        opacity: 0;
        position: absolute;
        top: 0;
        right: 0;
        width: 100%;
        height: 100%;
        cursor: pointer;
      }

      /* Style the calendar popup */
      :root {
        color-scheme: light;
      }

      .dark {
        color-scheme: dark;
      }
    `;

    // Add to document head
    document.head.appendChild(style);
  }
}

// Initialize the date input when the DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  window.dateInput = new DateInput();

  // Also initialize when drawers are opened
  if (window.drawerManager) {
    const originalOpenForm = window.drawerManager.openForm;
    window.drawerManager.openForm = function() {
      const result = originalOpenForm.apply(this, arguments);

      // Initialize date input after drawer is opened
      setTimeout(() => {
        new DateInput();
      }, 300);

      return result;
    };
  }
});

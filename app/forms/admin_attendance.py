from flask_wtf import <PERSON>laskForm
from wtforms import <PERSON><PERSON>ield, TextAreaField, BooleanField, SubmitField
from wtforms.validators import DataRequired, Length, Optional, Regexp, ValidationError
from app.models.attendance import AttendanceType

class AttendanceTypeForm(FlaskForm):
    """Form for creating and editing Attendance Types."""
    code = StringField(
        'Code',
        validators=[
            DataRequired(message="Code is required."),
            Length(min=2, max=10, message="Code must be between 2 and 10 characters."),
            Regexp('^[A-Z0-9_]+$', message="Code must be uppercase letters, numbers, or underscores only.")
        ],
        render_kw={"placeholder": "e.g., PVL, WFH_REMOTE"}
    )
    name = StringField(
        'Name',
        validators=[
            DataRequired(message="Name is required."),
            Length(min=3, max=100, message="Name must be between 3 and 100 characters.")
        ],
        render_kw={"placeholder": "e.g., Planned Vacation Leave"}
    )
    description = TextAreaField(
        'Description',
        validators=[Optional(), Length(max=255)],
        render_kw={"rows": 3, "placeholder": "Optional description of the attendance type."}
    )
    requires_approval = BooleanField('Requires Approval?', default=False)
    is_full_day = BooleanField('Is Full Day Event?', default=True)
    color_code = StringField(
        'Color Code (Hex)',
        validators=[
            Optional(),
            Regexp(r'^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$', message="Invalid hex color code. Use format like #RRGGBB or #RGB.")
        ],
        render_kw={"placeholder": "e.g., #FF5733"}
    )
    submit = SubmitField('Save Attendance Type')

    # Store the original code when editing to check for changes
    original_code = None

    def __init__(self, original_code=None, *args, **kwargs):
        super(AttendanceTypeForm, self).__init__(*args, **kwargs)
        if original_code:
            self.original_code = original_code

    def validate_code(self, code_field):
        """Validate that the code is unique."""
        # If the code hasn't changed from the original, it's valid (for edits)
        if self.original_code and self.original_code == code_field.data:
            return

        # Check if the new code already exists in the database
        existing_type = AttendanceType.query.filter_by(code=code_field.data).first()
        if existing_type:
            raise ValidationError('This code already exists. Please choose a different one.')

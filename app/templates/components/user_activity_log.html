{% macro user_activity_log(entity_type=None, entity_id=None, title="Activity Log", max_height="400px", limit=5) %}
<div class="user-activity-log bg-card rounded-lg border border-border mt-4 p-6"
     data-entity-type="{{ entity_type }}"
     data-entity-id="{{ entity_id }}"
     data-limit="{{ limit }}">
    <div class="flex items-center justify-between mb-6">
        <h3 class="text-lg font-semibold">{{ title }}</h3>
        <button type="button" class="flex items-center text-sm text-primary hover:text-primary/80 transition-colors view-all-activities">
            <span>View All</span>
            <i data-lucide="arrow-right" class="h-3.5 w-3.5 ml-1.5"></i>
        </button>
    </div>

    <div class="activities-container overflow-auto" style="max-height: {{ max_height }}">
        <div class="activities-loading flex items-center justify-center py-10">
            <div class="animate-spin rounded-full h-6 w-6 border-2 border-t-transparent border-primary"></div>
            <span class="ml-3 text-sm">Loading activities...</span>
        </div>
        <div class="activities-list hidden space-y-5"></div>
        <div class="activities-empty hidden text-center py-12">
            <div class="flex flex-col items-center justify-center">
                <div class="p-4 rounded-full bg-muted/50 mb-4">
                    <i data-lucide="activity" class="h-6 w-6 text-muted-foreground"></i>
                </div>
                <h4 class="text-base font-medium">No activities found</h4>
                <p class="text-sm text-muted-foreground mt-2">Any actions you perform will be recorded here</p>
            </div>
        </div>
        <div class="activities-error hidden text-center py-12">
            <div class="flex flex-col items-center justify-center">
                <div class="p-4 rounded-full bg-destructive/10 mb-4">
                    <i data-lucide="alert-circle" class="h-6 w-6 text-destructive"></i>
                </div>
                <h4 class="text-base font-medium text-destructive">Error loading activities</h4>
                <p class="text-sm text-muted-foreground mt-2">Please try refreshing the page</p>
                <button type="button" class="btn btn-sm btn-outline mt-5 retry-loading">
                    <i data-lucide="refresh-cw" class="h-3.5 w-3.5 mr-1.5"></i>
                    Try Again
                </button>
            </div>
        </div>
    </div>

    <!-- Activity item template -->
    <template id="activity-item-template">
        <div class="activity-item bg-card border border-border rounded-lg overflow-hidden hover:shadow-sm transition-shadow">
            <div class="p-5">
                <div class="flex items-start justify-between">
                    <div class="flex-1">
                        <div class="flex items-center">
                            <span class="activity-severity-badge mr-3 text-xs px-2.5 py-1 rounded-full"></span>
                            <div class="font-medium text-base activity-action"></div>
                        </div>
                        <div class="flex items-center mt-1">
                            <span class="activity-method-badge text-xs px-2 py-0.5 rounded-sm mr-2"></span>
                        </div>
                        <div class="text-xs text-muted-foreground mt-2 flex items-center">
                            <i data-lucide="clock" class="h-3 w-3 mr-1.5"></i>
                            <span class="activity-time"></span>
                        </div>
                    </div>
                    <div class="activity-icon-wrapper flex-shrink-0 h-10 w-10 rounded-full flex items-center justify-center text-primary bg-primary/10">
                        <i data-lucide="activity" class="h-5 w-5"></i>
                    </div>
                </div>
                <div class="activity-details mt-4 text-sm text-muted-foreground leading-relaxed"></div>

                <!-- Changes section (only shown if has_changes is true) -->
                <div class="activity-changes mt-5 pt-4 border-t border-border/60 hidden">
                    <div class="text-xs font-medium mb-3 flex items-center">
                        <i data-lucide="git-compare" class="h-3.5 w-3.5 mr-1.5 text-primary"></i>
                        Changes
                    </div>
                    <div class="changes-table-container overflow-x-auto rounded-md bg-muted/30">
                        <table class="w-full text-xs">
                            <thead>
                                <tr class="border-b border-border/60">
                                    <th class="text-left py-2 px-4 font-medium">Field</th>
                                    <th class="text-left py-2 px-4 font-medium">Old Value</th>
                                    <th class="text-left py-2 px-4 font-medium">New Value</th>
                                </tr>
                            </thead>
                            <tbody class="changes-table-body"></tbody>
                        </table>
                    </div>
                </div>

                <div class="flex items-center justify-end mt-4 pt-4 border-t border-border/60">
                    <button type="button" class="text-xs flex items-center text-primary hover:text-primary/80 transition-colors view-details-btn">
                        <i data-lucide="eye" class="h-3.5 w-3.5 mr-1.5"></i>
                        View Details
                    </button>
                </div>
            </div>
        </div>
    </template>

    <!-- Row template for changes table -->
    <template id="changes-row-template">
        <tr class="border-b border-border/40 last:border-0">
            <td class="py-2.5 px-4 font-medium align-top field-name"></td>
            <td class="py-2.5 px-4 align-top whitespace-pre-wrap break-words max-w-[120px] old-value"></td>
            <td class="py-2.5 px-4 align-top whitespace-pre-wrap break-words max-w-[120px] new-value"></td>
        </tr>
    </template>
</div>

<!-- Activity details modal -->
<div id="user-activity-details-modal" class="modal" aria-hidden="true">
    <div class="modal-overlay bg-black/50 backdrop-blur-sm"></div>
    <div class="modal-container">
        <div class="modal-content max-w-2xl bg-card rounded-lg shadow-lg border border-border overflow-hidden">
            <div class="modal-header flex items-center justify-between p-5 border-b border-border bg-card">
                <h3 class="text-lg font-semibold">Activity Details</h3>
                <button type="button" class="modal-close flex items-center justify-center h-8 w-8 rounded-full hover:bg-muted/80 transition-colors focus:outline-none focus:ring-2 focus:ring-primary/30" aria-label="Close">
                    <i data-lucide="x" class="h-4 w-4"></i>
                </button>
            </div>
            <div id="user-activity-details-content" class="p-6 max-h-[70vh] overflow-y-auto">
                <!-- Content will be loaded dynamically -->
            </div>
            <div class="modal-footer bg-muted/30 px-6 py-4 border-t border-border flex justify-end">
                <button type="button" class="btn btn-sm btn-outline modal-close-btn">
                    <i data-lucide="x" class="h-3.5 w-3.5 mr-1.5"></i>
                    Close
                </button>
            </div>
        </div>
    </div>
</div>
{% endmacro %}

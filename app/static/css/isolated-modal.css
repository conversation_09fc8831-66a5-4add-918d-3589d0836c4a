/**
 * Modal component styles
 * Uses Tailwind-compatible classes with CSS variables for theming
 */

/* Overlay styles */
.modal-overlay {
  position: fixed;
  inset: 0;
  z-index: 50;
  background-color: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(3px);
  transition: opacity 0.3s ease;
}

/* Modal dialog styles */
.modal-dialog {
  position: fixed;
  left: 50%;
  top: 50%;
  z-index: 50;
  display: grid;
  width: calc(100% - 2rem);
  max-width: 32rem;
  transform: translate(-50%, -50%);
  gap: 1rem;
  border: 1px solid var(--border-color, #e5e7eb);
  background-color: var(--background-color, white);
  padding: 1.5rem;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  transition: opacity 0.3s ease, transform 0.3s ease;
  border-radius: 0.5rem;
}

/* Dark mode support */
.dark .modal-dialog {
  --border-color: #374151;
  --background-color: #1f2937;
  --text-color: #f9fafb;
  color: var(--text-color);
}

/* Modal sizes */
.modal-dialog.sm { max-width: 24rem; }
.modal-dialog.md { max-width: 32rem; }
.modal-dialog.lg { max-width: 48rem; }
.modal-dialog.xl { max-width: 64rem; }
.modal-dialog.full { max-width: 90vw; }

/* Modal header */
.modal-header {
  display: flex;
  flex-direction: column;
  gap: 0.375rem;
  text-align: center;
}

@media (min-width: 640px) {
  .modal-header {
    text-align: left;
  }
}

.modal-title {
  font-size: 1.125rem;
  font-weight: 600;
  line-height: 1;
  letter-spacing: -0.025em;
  margin: 0;
}

.modal-description {
  font-size: 0.875rem;
  color: var(--muted-color, #6b7280);
  margin: 0;
}

.dark .modal-description {
  --muted-color: #9ca3af;
}

/* Modal content */
.modal-content {
  /* Content styles can be added here if needed */
}

/* Modal footer */
.modal-footer {
  display: flex;
  flex-direction: column-reverse;
  justify-content: flex-end;
  gap: 0.5rem;
}

@media (min-width: 640px) {
  .modal-footer {
    flex-direction: row;
  }
}

/* Buttons */
.modal-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  font-weight: 500;
  transition-property: color, background-color, border-color;
  transition-duration: 0.15s;
  transition-timing-function: ease;
}

.modal-btn:focus {
  outline: none;
  box-shadow: 0 0 0 2px var(--ring-color, rgba(59, 130, 246, 0.5));
}

.modal-btn-cancel {
  padding: 0.375rem 0.75rem;
  background-color: var(--background-color, white);
  border: 1px solid var(--border-color, #e5e7eb);
  color: var(--text-color, #374151);
}

.modal-btn-cancel:hover {
  background-color: var(--hover-bg, #f9fafb);
}

.dark .modal-btn-cancel {
  --border-color: #374151;
  --text-color: #e5e7eb;
  --hover-bg: #374151;
}

.modal-btn-primary {
  padding: 0.5rem 1rem;
  background-color: var(--primary-color, #2563eb);
  color: white;
  border: 1px solid transparent;
}

.modal-btn-primary:hover {
  background-color: var(--primary-hover, #1d4ed8);
}

.dark .modal-btn-primary {
  --primary-color: #3b82f6;
  --primary-hover: #2563eb;
}

.modal-btn-destructive {
  padding: 0.5rem 1rem;
  background-color: var(--destructive-color, #dc2626);
  color: white;
  border: 1px solid transparent;
}

.modal-btn-destructive:hover {
  background-color: var(--destructive-hover, #b91c1c);
}

.dark .modal-btn-destructive {
  --destructive-color: #ef4444;
  --destructive-hover: #dc2626;
}

/* Animation states */
.modal-overlay[data-state="open"],
.modal-dialog[data-state="open"] {
  opacity: 1;
}

.modal-overlay[data-state="closed"],
.modal-dialog[data-state="closed"] {
  opacity: 0;
}

.modal-dialog[data-state="closed"] {
  transform: translate(-50%, -45%) scale(0.95);
}

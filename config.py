import os
import secrets
from datetime import timedelta

class Config:
    """Base configuration class."""
    # Generate a random secret key if not provided
    SECRET_KEY = os.environ.get('SECRET_KEY') or secrets.token_hex(32)
    DEBUG = False
    TESTING = False
    STATIC_FOLDER = 'static'
    TEMPLATES_FOLDER = 'templates'

    # SQLAlchemy configuration
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL', 'sqlite:///dev.db')
    SQLALCHEMY_TRACK_MODIFICATIONS = False

    # Connection pooling settings
    SQLALCHEMY_ENGINE_OPTIONS = {
        'pool_size': 10,  # Maximum number of connections to keep in the pool
        'pool_timeout': 30,  # Seconds to wait before giving up on getting a connection
        'pool_recycle': 1800,  # Recycle connections after 30 minutes
        'max_overflow': 20,  # Maximum number of connections to create beyond pool_size
        'echo': False,  # Set to True to log all SQL statements (development only)
        'echo_pool': False  # Set to True to log connection pool activity (development only)
    }

    # Session configuration
    PERMANENT_SESSION_LIFETIME = timedelta(days=7)

    # Mail settings
    MAIL_SERVER = os.environ.get('MAIL_SERVER', 'smtp.example.com')
    MAIL_PORT = int(os.environ.get('MAIL_PORT', 587))
    MAIL_USE_TLS = os.environ.get('MAIL_USE_TLS', 'true').lower() in ['true', 'on', '1']
    MAIL_USERNAME = os.environ.get('MAIL_USERNAME')
    MAIL_PASSWORD = os.environ.get('MAIL_PASSWORD')
    MAIL_DEFAULT_SENDER = os.environ.get('MAIL_DEFAULT_SENDER', '<EMAIL>')

    # Security settings
    WTF_CSRF_ENABLED = True
    JWT_SECRET_KEY = os.environ.get('JWT_SECRET_KEY') or secrets.token_hex(32)
    JWT_ACCESS_TOKEN_EXPIRES = timedelta(hours=1)
    JWT_REFRESH_TOKEN_EXPIRES = timedelta(days=30)

    # Rate limiting configuration
    RATELIMIT_STORAGE_URL = "memory://"  # Use "redis://localhost:6379/0" for production
    RATELIMIT_STRATEGY = "fixed-window-elastic-expiry"  # Better for production
    RATELIMIT_HEADERS_ENABLED = True
    RATELIMIT_DEFAULT = "200 per day, 50 per hour"
    RATELIMIT_SWALLOW_ERRORS = True
    RATELIMIT_IN_MEMORY_FALLBACK = True
    RATELIMIT_KEY_PREFIX = "matrix_app_"

    # Pagination configuration
    PAGINATION_PER_PAGE = int(os.environ.get('PAGINATION_PER_PAGE', 10))
    PAGINATION_PER_PAGE_ADMIN = int(os.environ.get('PAGINATION_PER_PAGE_ADMIN', 20))
    PAGINATION_MAX_PER_PAGE = int(os.environ.get('PAGINATION_MAX_PER_PAGE', 100))

class DevelopmentConfig(Config):
    """Development configuration."""
    DEBUG = True
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL', 'sqlite:///dev.db')

    # Development-specific engine options
    SQLALCHEMY_ENGINE_OPTIONS = {
        'pool_size': 5,  # Smaller pool for development
        'pool_timeout': 30,
        'pool_recycle': 1800,
        'max_overflow': 10,
        'echo': False,  # Log SQL statements in development
        'echo_pool': False  # Log connection pool activity in development
    }

class ProductionConfig(Config):
    """Production configuration."""
    DEBUG = False
    # Use a more robust database in production
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL', 'sqlite:///prod.db')

    # Production-specific engine options
    SQLALCHEMY_ENGINE_OPTIONS = {
        'pool_size': 20,  # Larger pool for production
        'pool_timeout': 30,
        'pool_recycle': 1800,  # Recycle connections after 30 minutes
        'max_overflow': 40,
        'echo': False,
        'echo_pool': False,
        'pool_pre_ping': True,  # Check connection validity before using it
        'connect_args': {
            'check_same_thread': False  # For SQLite only, allows multithreading
        }
    }

    # Rate limiting for production
    RATELIMIT_STORAGE_URL = os.environ.get('REDIS_URL', 'memory://')
    RATELIMIT_STRATEGY = "moving-window"  # Most accurate for production
    RATELIMIT_DEFAULT = "300 per day, 60 per hour, 10 per minute"

    # SSL/HTTPS
    SESSION_COOKIE_SECURE = True
    REMEMBER_COOKIE_SECURE = True

    # Set this to True in production
    PREFERRED_URL_SCHEME = 'https'

class TestingConfig(Config):
    """Testing configuration."""
    TESTING = True
    DEBUG = True
    SQLALCHEMY_DATABASE_URI = 'sqlite:///:memory:'
    WTF_CSRF_ENABLED = False

# Configuration dictionary
config = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'testing': TestingConfig,
    'default': DevelopmentConfig
}

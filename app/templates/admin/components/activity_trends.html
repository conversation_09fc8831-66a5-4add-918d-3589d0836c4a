{% macro activity_trends() %}
<div class="analytics-card bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 shadow-lg overflow-hidden transition-all duration-200 h-full flex flex-col">
    <div class="flex flex-col sm:flex-row sm:items-center justify-between p-5 md:p-6 border-b border-gray-200 dark:border-gray-700 bg-gray-50/80 dark:bg-gray-800/80 analytics-card-header">
        <div>
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white flex items-center analytics-text">
                <i data-lucide="bar-chart-2" class="w-5 h-5 text-blue-600 dark:text-blue-400 mr-2"></i>
                <span>User Activity Trends</span>
            </h3>
            <p class="text-sm text-gray-600 dark:text-gray-400 mt-1 analytics-text">System usage patterns over time</p>
        </div>
        <div class="flex mt-4 sm:mt-0 space-x-2">
            <div class="inline-flex rounded-md shadow-sm" role="group">
                <button id="chartDailyBtn" type="button" class="bg-blue-50 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 shadow-sm px-3 py-1.5 text-sm font-medium rounded-l-md hover:bg-blue-100 dark:hover:bg-blue-900/40 transition-colors">
                    Daily
                </button>
                <button id="chartWeeklyBtn" type="button" class="text-gray-600 dark:text-gray-400 px-3 py-1.5 text-sm font-medium hover:bg-gray-100 dark:hover:bg-gray-700/50 transition-colors">
                    Weekly
                </button>
                <button id="chartMonthlyBtn" type="button" class="text-gray-600 dark:text-gray-400 px-3 py-1.5 text-sm font-medium rounded-r-md hover:bg-gray-100 dark:hover:bg-gray-700/50 transition-colors">
                    Monthly
                </button>
            </div>
        </div>
    </div>
    <div class="p-5 md:p-6 flex-1 flex flex-col relative analytics-card-content">

        <!-- Loading overlay -->
        <div class="chart-loading-overlay hidden absolute inset-0 bg-white/80 dark:bg-gray-800/80 z-10 items-center justify-center">
            <div class="flex flex-col items-center">
                <div class="w-10 h-10 border-4 border-blue-200 border-t-blue-600 rounded-full animate-spin mb-3"></div>
                <p class="text-sm text-gray-600 dark:text-gray-400 analytics-text">Loading chart data...</p>
            </div>
        </div>

        <!-- Chart canvas -->
        <div class="flex-1 min-h-[300px] flex items-center justify-center">
            <canvas id="trafficOverviewChart" class="max-h-full w-full"></canvas>
        </div>
    </div>
</div>
{% endmacro %}

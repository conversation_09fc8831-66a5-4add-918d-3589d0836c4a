"""
Authentication password management routes.
"""

from flask import render_template, redirect, url_for, flash, request, current_app
from flask_login import login_required, current_user

from app import db, limiter
from app.models import User, Activity, PasswordReset, WeakPasswordError
from app.utils.decorators import log_activity
from app.utils.request_info import get_client_info
from app.utils.email import send_password_reset_email
from app.routes.auth import auth_bp, redirect_authenticated_user


@auth_bp.route('/forgot-password', methods=['GET', 'POST'])
@limiter.limit("20 per minute", error_message="Too many password reset attempts. Please try again later.")
def forgot_password():
    """Handle password reset request."""
    # Redirect if already logged in
    redirect_result = redirect_authenticated_user()
    if redirect_result:
        return redirect_result

    if request.method == 'POST':
        email = request.form.get('email')

        if not email:
            flash('Email is required', 'error')
            return render_template('auth/forgot_password.html')

        user = User.get_by_email(email)

        if user:
            # Use the model method for creating tokens
            reset = PasswordReset.create_token(email, expiration=3600*24)  # 24 hours

            # Generate password reset link
            reset_url = url_for('auth.reset_password', token=reset.token, _external=True)

            # Send reset email
            send_password_reset_email(user, reset_url)

            # Log activity
            client_info = get_client_info()
            Activity.log(
                user_id=user.id,
                action='Password reset requested',
                entity_type='User',
                entity_id=user.id,
                ip_address=client_info['ip_address'],
                user_agent=client_info['user_agent'],
                details=f"Hostname: {client_info['hostname'] or 'Unknown'}",
                category=Activity.CATEGORY_AUTH,
                method=Activity.METHOD_CREATE,
                severity=Activity.SEVERITY_INFO
            )

        # Cleanup expired tokens
        PasswordReset.cleanup_expired_tokens()

        # Don't reveal if the user exists for security
        flash('If an account with that email exists, we have sent a password reset link', 'success')
        return redirect(url_for('auth.forgot_password'))

    return render_template('auth/forgot_password.html')


@auth_bp.route('/reset-password/<token>', methods=['GET', 'POST'])
@limiter.limit("10 per hour", error_message="Too many password reset attempts. Please try again later.")
def reset_password(token):
    """Handle password reset with token."""
    # Redirect if already logged in
    redirect_result = redirect_authenticated_user()
    if redirect_result:
        return redirect_result

    # Find and validate the token
    reset = PasswordReset.validate_token(token)
    if not reset:
        flash('The password reset link is invalid or has expired', 'error')
        return redirect(url_for('auth.forgot_password'))

    if request.method == 'POST':
        password = request.form.get('password')
        confirm_password = request.form.get('confirm_password')

        if not password:
            flash('Password is required', 'error')
            return render_template('auth/reset_password.html', token=token)

        if password != confirm_password:
            flash('Passwords do not match', 'error')
            return render_template('auth/reset_password.html', token=token)

        try:
            # Update the user's password
            user = User.get_by_email(reset.email)
            if not user:
                flash('User not found', 'error')
                return redirect(url_for('auth.forgot_password'))

            user.set_password(password)

            # Mark the token as used
            reset.is_used = True
            db.session.commit()

            # Log activity
            client_info = get_client_info()
            Activity.log(
                user_id=user.id,
                action='Password reset completed',
                entity_type='User',
                entity_id=user.id,
                ip_address=client_info['ip_address'],
                user_agent=client_info['user_agent'],
                details=f"Hostname: {client_info['hostname'] or 'Unknown'}",
                category=Activity.CATEGORY_AUTH,
                method=Activity.METHOD_UPDATE,
                severity=Activity.SEVERITY_INFO
            )

            flash('Your password has been reset successfully. Please log in.', 'success')
            return redirect(url_for('auth.login'))

        except WeakPasswordError as e:
            flash(str(e), 'error')
            return render_template('auth/reset_password.html', token=token)
        except Exception:
            db.session.rollback()
            flash('An error occurred. Please try again.', 'error')
            return render_template('auth/reset_password.html', token=token)

    return render_template('auth/reset_password.html', token=token)


@auth_bp.route('/change-password', methods=['POST'])
@login_required
@log_activity('Changed password', entity_type='User', category=Activity.CATEGORY_AUTH, method=Activity.METHOD_UPDATE)
def change_password():
    """Handle password change for logged-in users."""
    current_password = request.form.get('current_password')
    new_password = request.form.get('new_password')
    confirm_password = request.form.get('confirm_password')

    if not current_password or not new_password or not confirm_password:
        flash('All password fields are required', 'error')
        return redirect(url_for('auth.profile', _anchor='security'))

    if new_password != confirm_password:
        flash('New passwords do not match', 'error')
        return redirect(url_for('auth.profile', _anchor='security'))

    if not current_user.check_password(current_password):
        flash('Current password is incorrect', 'error')
        return redirect(url_for('auth.profile', _anchor='security'))

    try:
        current_user.set_password(new_password)
        db.session.commit()

        # Log activity
        client_info = get_client_info()
        Activity.log(
            user_id=current_user.id,
            action='Password changed',
            entity_type='User',
            entity_id=current_user.id,
            ip_address=client_info['ip_address'],
            user_agent=client_info['user_agent'],
            details=f"Hostname: {client_info['hostname'] or 'Unknown'}",
            category=Activity.CATEGORY_AUTH,
            method=Activity.METHOD_UPDATE,
            severity=Activity.SEVERITY_INFO
        )

        flash('Your password has been changed successfully', 'success')
        return redirect(url_for('auth.profile', _anchor='security', status='success', action='password_changed'))

    except WeakPasswordError as e:
        flash(str(e), 'error')
        return redirect(url_for('auth.profile', _anchor='security'))
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Password change error: {str(e)}")
        flash('An error occurred. Please try again.', 'error')
        return redirect(url_for('auth.profile', _anchor='security'))

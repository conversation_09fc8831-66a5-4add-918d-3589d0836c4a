"""
Scheduled tasks for the application.

This module contains functions that should be run on a schedule to maintain
the application's performance and data integrity.
"""

from datetime import datetime, timedelta
from flask import current_app
import logging

from app import db
from app.models import Activity, PasswordReset, PH_TZ


def cleanup_old_activities(days=30):
    """
    Delete activity logs older than the specified number of days.
    
    Args:
        days: Number of days to keep logs for (default: 30)
        
    Returns:
        int: Number of deleted records
    """
    try:
        # Calculate the cutoff date
        cutoff_date = datetime.now(PH_TZ) - timedelta(days=days)
        
        # Delete logs older than the cutoff date
        result = Activity.query.filter(Activity.created_at < cutoff_date).delete()
        db.session.commit()
        
        current_app.logger.info(f"Scheduled task: Deleted {result} activity logs older than {days} days")
        return result
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Error cleaning up old activities: {str(e)}")
        return 0


def cleanup_expired_password_tokens():
    """
    Delete expired and used password reset tokens.
    
    Returns:
        int: Number of deleted tokens
    """
    try:
        return PasswordReset.cleanup_expired_tokens()
    except Exception as e:
        current_app.logger.error(f"Error cleaning up expired password tokens: {str(e)}")
        return 0


def run_maintenance_tasks():
    """
    Run all maintenance tasks.
    This function should be called periodically to keep the application running smoothly.
    """
    # Get retention period from settings if available
    from app.utils.settings import get_setting
    
    try:
        # Get activity log retention period from settings (default: 30 days)
        retention_days = get_setting('activity_log_retention_days', 30)
        
        # Clean up old activities
        deleted_activities = cleanup_old_activities(days=retention_days)
        current_app.logger.info(f"Maintenance task: Deleted {deleted_activities} old activity logs")
        
        # Clean up expired password tokens
        deleted_tokens = cleanup_expired_password_tokens()
        current_app.logger.info(f"Maintenance task: Deleted {deleted_tokens} expired password tokens")
        
        return {
            'deleted_activities': deleted_activities,
            'deleted_tokens': deleted_tokens
        }
    except Exception as e:
        current_app.logger.error(f"Error running maintenance tasks: {str(e)}")
        return {
            'deleted_activities': 0,
            'deleted_tokens': 0,
            'error': str(e)
        }

"""
Utility functions for API routes.
"""

from flask import jsonify, current_app
from functools import wraps
from sqlalchemy.exc import SQLAlchemyError
import logging


def handle_api_error(f):
    """Decorator to handle API errors and return appropriate JSON responses."""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        try:
            return f(*args, **kwargs)
        except SQLAlchemyError as e:
            current_app.logger.error(f"Database error in API: {str(e)}")
            return jsonify({
                'success': False,
                'message': 'A database error occurred',
                'error': str(e)
            }), 500
        except Exception as e:
            current_app.logger.error(f"Unexpected error in API: {str(e)}")
            return jsonify({
                'success': False,
                'message': 'An unexpected error occurred',
                'error': str(e)
            }), 500
    return decorated_function


def create_pagination_dict(page_obj):
    """Create a standardized pagination dictionary from a SQLAlchemy pagination object."""
    return {
        'page': page_obj.page,
        'per_page': page_obj.per_page,
        'total': page_obj.total,
        'pages': page_obj.pages,
        'has_next': page_obj.has_next,
        'has_prev': page_obj.has_prev,
        'next_page': page_obj.next_num if page_obj.has_next else None,
        'prev_page': page_obj.prev_num if page_obj.has_prev else None
    }


def api_response(data=None, message=None, success=True, status_code=200, **kwargs):
    """Create a standardized API response."""
    response = {
        'success': success
    }
    
    if data is not None:
        response['data'] = data
        
    if message is not None:
        response['message'] = message
        
    # Add any additional kwargs to the response
    response.update(kwargs)
    
    return jsonify(response), status_code

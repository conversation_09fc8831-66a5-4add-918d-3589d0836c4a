"""
Team models for grouping employees.
"""

from datetime import datetime
from typing import Dict, Any, List

from app import db
from app.models import PH_TZ
from app.models.employee import team_members


class Team(db.Model):
    """Team model for grouping employees."""
    __tablename__ = 'teams'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    short_name = db.Column(db.String(20), nullable=True)
    slug = db.Column(db.String(100), unique=True, nullable=False, index=True)
    description = db.Column(db.Text, nullable=True)
    is_active = db.Column(db.<PERSON><PERSON>an, default=True)
    created_at = db.Column(db.DateTime, default=lambda: datetime.now(PH_TZ))
    updated_at = db.Column(db.DateTime, default=lambda: datetime.now(PH_TZ), onupdate=lambda: datetime.now(PH_TZ))

    # Relationships
    members = db.relationship('EmployeeDetail', secondary=team_members, back_populates='teams')
    groups = db.relationship('TeamGroup', backref='team', lazy='dynamic', cascade='all, delete-orphan')

    def __repr__(self) -> str:
        return f'<Team {self.name}>'

    def to_dict(self) -> Dict[str, Any]:
        """Serialize team to dictionary."""
        return {
            'id': self.id,
            'name': self.name,
            'short_name': self.short_name,
            'slug': self.slug,
            'description': self.description,
            'is_active': self.is_active,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'member_count': len(self.members) if self.members else 0,
            'group_count': self.groups.count() if self.groups else 0
        }


class TeamGroup(db.Model):
    """Team group model for organizing team members."""
    __tablename__ = 'team_groups'

    id = db.Column(db.Integer, primary_key=True)
    team_id = db.Column(db.Integer, db.ForeignKey('teams.id', name='fk_team_group_team_id', ondelete='CASCADE'), nullable=False)
    name = db.Column(db.String(100), nullable=False)
    description = db.Column(db.Text, nullable=True)
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=lambda: datetime.now(PH_TZ))
    updated_at = db.Column(db.DateTime, default=lambda: datetime.now(PH_TZ), onupdate=lambda: datetime.now(PH_TZ))

    def __repr__(self) -> str:
        return f'<TeamGroup {self.name} (Team: {self.team.name if self.team else "Unknown"})>'

    def to_dict(self) -> Dict[str, Any]:
        """Serialize team group to dictionary."""
        return {
            'id': self.id,
            'team_id': self.team_id,
            'name': self.name,
            'description': self.description,
            'is_active': self.is_active,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'team_name': self.team.name if self.team else None
        }

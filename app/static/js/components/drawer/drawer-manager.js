/**
 * Drawer Manager Module
 *
 * This module provides the DrawerManager class for managing multiple drawer instances.
 */

import { DrawerComponent } from './drawer-component.js';
import { loadForm } from './form-handler.js';

/**
 * DrawerManager for managing multiple drawer instances
 */
class DrawerManager {
  /**
   * Create a new drawer manager
   */
  constructor() {
    this.activeDrawers = [];
    this.drawerStack = [];
    this.formTypes = {};
  }

  /**
   * Open a drawer with the specified content
   * @param {string} content - HTML content to display in the drawer
   * @param {Object} options - Options for the drawer
   * @returns {DrawerComponent} - The created drawer component
   */
  open(content, options = {}) {
    const drawer = new DrawerComponent(options);
    drawer.open(content);

    this.activeDrawers.push(drawer);
    this.drawerStack.push(drawer);

    return drawer;
  }

  /**
   * Open a form in a drawer
   * @param {string} formType - Type of form to open
   * @param {number|string} entityId - ID of the entity to edit
   * @param {string|Object} urlOrOptions - Direct form URL or options object
   * @param {string} urlOrOptions.position - Position of the drawer ('right', 'left', 'top', 'bottom')
   * @param {string} urlOrOptions.size - Size of the drawer ('sm', 'md', 'lg', 'xl', 'full')
   * @param {string} urlOrOptions.width - Custom width for the drawer (e.g., '30rem', '500px')
   * @param {Object} urlOrOptions.queryParams - Additional query parameters for the form request
   * @returns {Promise<DrawerComponent>} - Promise resolving to the created drawer component
   */
  openForm(formType, entityId = null, urlOrOptions = {}) {
    console.log(`Opening form: ${formType}${entityId ? ` with ID: ${entityId}` : ''}`);

    // Check if third parameter is a string URL or options object
    if (typeof urlOrOptions === 'string') {
      // Direct URL provided, fetch from this URL
      const url = urlOrOptions;
      console.log(`Using direct URL: ${url}`);

      return fetch(url)
        .then(response => {
          if (!response.ok) {
            throw new Error(`HTTP error! Status: ${response.status}`);
          }
          return response.json();
        })
        .then(data => {
          if (!data.success) {
            throw new Error(data.error || 'Failed to load form');
          }

          const drawer = new DrawerComponent({});
          drawer.open(data.html);

          this.activeDrawers.push(drawer);
          this.drawerStack.push(drawer);

          return drawer;
        });
    }

    // Check if we have a registered form type
    if (this.formTypes[formType]) {
      const config = this.formTypes[formType];
      let url;

      if (entityId) {
        // Use edit URL template
        url = config.editUrl.replace('{id}', entityId);
      } else {
        // Use create URL
        url = config.createUrl;
      }

      console.log(`Using registered form type URL: ${url}`);

      // Merge options with config defaults
      const drawerOptions = {
        size: config.size || 'md',
        position: config.position || 'right',
        ...urlOrOptions
      };

      return fetch(url)
        .then(response => {
          if (!response.ok) {
            throw new Error(`HTTP error! Status: ${response.status}`);
          }
          return response.json();
        })
        .then(data => {
          if (!data.success) {
            throw new Error(data.error || 'Failed to load form');
          }

          const drawer = new DrawerComponent(drawerOptions);
          drawer.open(data.html);

          this.activeDrawers.push(drawer);
          this.drawerStack.push(drawer);

          return drawer;
        })
        .catch(error => {
          console.error('Error opening form:', error);
          if (typeof showToast === 'function') {
            showToast(error.message || 'Failed to load form', { type: 'error' });
          }
          return null;
        });
    }

    // Using default pattern with form type and entity ID
    const options = urlOrOptions;
    return loadForm(formType, entityId, options)
      .then(data => {
        if (!data.success) {
          throw new Error(data.error || 'Failed to load form');
        }

        const drawer = new DrawerComponent(options);
        drawer.open(data.html);

        this.activeDrawers.push(drawer);
        this.drawerStack.push(drawer);

        return drawer;
      })
      .catch(error => {
        console.error('Error opening form:', error);
        if (typeof showToast === 'function') {
          showToast(error.message || 'Failed to load form', { type: 'error' });
        }
        return null;
      });
  }

  /**
   * Close all open drawers
   */
  closeAll() {
    this.activeDrawers.forEach(drawer => drawer.close());
    this.activeDrawers = [];
    this.drawerStack = [];
  }

  /**
   * Close the most recently opened drawer
   */
  closeLast() {
    if (this.drawerStack.length > 0) {
      const drawer = this.drawerStack.pop();
      drawer.close();

      // Remove from active drawers
      const index = this.activeDrawers.indexOf(drawer);
      if (index > -1) {
        this.activeDrawers.splice(index, 1);
      }
    }
  }

  /**
   * Open a drawer directly with HTML content (convenience method)
   * @param {string} html - HTML content to display in the drawer
   * @param {Object} options - Options for the drawer
   * @returns {DrawerComponent} - The created drawer component
   */
  openHtml(html, options = {}) {
    const drawer = new DrawerComponent(options);
    drawer.open(html);

    this.activeDrawers.push(drawer);
    this.drawerStack.push(drawer);

    return drawer;
  }

  /**
   * Register a form type with custom URLs
   * @param {string} formType - Type of form to register
   * @param {Object} config - Configuration for the form type
   * @param {string} config.createUrl - URL for creating new entities
   * @param {string} config.editUrl - URL template for editing entities (use {id} placeholder)
   * @param {string} config.size - Default size for the drawer
   * @param {string} config.position - Default position for the drawer
   */
  registerFormType(formType, config) {
    this.formTypes[formType] = config;
    console.log(`Registered form type: ${formType}`, config);
  }

  /**
   * Test the forms API connection
   */
  testConnection() {
    // Try to determine the correct URL based on the current page
    let url;

    // If we're on an admin page, try the admin URL first
    if (window.location.pathname.startsWith('/admin')) {
      url = '/admin/forms/test';
    } else {
      // Otherwise use the regular forms URL
      url = '/forms/test';
    }

    console.log('Testing forms API connection:', url);

    fetch(url)
      .then(response => {
        if (!response.ok) {
          throw new Error(`HTTP error! Status: ${response.status}`);
        }
        return response.text().then(text => {
          try {
            return JSON.parse(text);
          } catch (e) {
            console.error('Failed to parse JSON:', text.substring(0, 100) + '...');
            throw new Error('Invalid JSON response from server');
          }
        });
      })
      .then(data => {
        console.log('Forms API test successful:', data);
        if (typeof showToast === 'function') {
          showToast('Forms API connection successful', { type: 'success' });
        }
      })
      .catch(error => {
        console.error('Forms API test failed:', error);
        if (typeof showToast === 'function') {
          showToast(`Forms API connection failed: ${error.message}`, { type: 'error' });
        }
      });
  }
}

// Export the DrawerManager class
export { DrawerManager };

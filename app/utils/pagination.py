from flask import request, current_app


class PaginationHelper:
    """Helper class for creating consistent pagination across the application."""

    def __init__(self, query, page=None, per_page=None, error_out=True):
        """
        Initialize pagination helper.

        Args:
            query: SQLAlchemy query object
            page: Current page number (defaults to request arg 'page')
            per_page: Items per page (defaults to config PAGINATION_PER_PAGE)
            error_out: Whether to raise 404 on invalid page
        """
        self.page = page or request.args.get('page', 1, type=int)
        self.per_page = per_page or current_app.config.get('PAGINATION_PER_PAGE', 10)

        # Ensure per_page doesn't exceed the maximum allowed
        max_per_page = current_app.config.get('PAGINATION_MAX_PER_PAGE', 100)
        if self.per_page > max_per_page:
            self.per_page = max_per_page

        self.pagination = query.paginate(
            page=self.page,
            per_page=self.per_page,
            error_out=error_out
        )

    @property
    def items(self):
        """Get the items for the current page."""
        return self.pagination.items

    @property
    def info(self):
        """Get pagination information dictionary."""
        total = self.pagination.total or 0
        return {
            'page': self.page,
            'per_page': self.per_page,
            'total_items': total,
            'total_pages': self.pagination.pages,
            'has_next': self.pagination.has_next,
            'has_prev': self.pagination.has_prev,
            'next_page': self.page + 1 if self.pagination.has_next else self.page,
            'prev_page': self.page - 1 if self.pagination.has_prev else self.page,
            'start_index': (self.page - 1) * self.per_page + 1 if total > 0 else 0,
            'end_index': min(self.page * self.per_page, total)
        }

    def to_dict(self):
        """Get both items and pagination info as a dictionary."""
        return {
            'items': self.items,
            'pagination': self.info
        }


def paginate_query(query, page=None, per_page=None, error_out=True):
    """
    Convenience function to paginate a query and return items and pagination info.

    Args:
        query: SQLAlchemy query object
        page: Current page number (defaults to request arg 'page')
        per_page: Items per page (defaults to config PAGINATION_PER_PAGE)
        error_out: Whether to raise 404 on invalid page

    Returns:
        tuple: (items, pagination_info)
    """
    helper = PaginationHelper(query, page, per_page, error_out)
    return helper.items, helper.info

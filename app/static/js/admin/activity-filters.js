/**
 * Activity Filters
 * Handles the activity filters functionality
 */



document.addEventListener('DOMContentLoaded', function() {
  // Initialize filter toggle
  initFilterToggle();

  // Initialize auto-refresh
  initAutoRefresh();

  // Initialize severity radio buttons
  initSeverityRadios();
});

/**
 * Initialize the filter toggle functionality
 */
function initFilterToggle() {
  const filterToggle = document.getElementById('filter-toggle');
  const filterContent = document.getElementById('filter-content');

  // Exit early if essential elements are missing
  if (!filterToggle || !filterContent) {
    console.warn('Filter toggle or content elements not found');
    return;
  }

  // Check if filters are active and show the panel if they are
  const hasActiveFilters = document.querySelector('.filter-summary') !== null;
  if (hasActiveFilters) {
    filterContent.classList.remove('hidden');

    // Find the chevron wrapper and add the expanded class
    const chevronWrapper = filterToggle.querySelector('.chevron-wrapper');
    if (chevronWrapper) {
      chevronWrapper.classList.add('expanded');
    }
  }

  // Function to update the expanded state of the chevron wrapper
  function updateExpandedState() {
    // Check if content is visible to determine state
    const isContentVisible = !filterContent.classList.contains('hidden');

    // Find the chevron wrapper
    const chevronWrapper = filterToggle.querySelector('.chevron-wrapper');

    if (chevronWrapper) {
      // Apply or remove the expanded class based on visibility
      if (isContentVisible) {
        chevronWrapper.classList.add('expanded');
      } else {
        chevronWrapper.classList.remove('expanded');
      }

      // Log for debugging
      console.log('Chevron wrapper expanded state updated:', isContentVisible ? 'expanded' : 'collapsed');
    } else {
      console.warn('Chevron wrapper not found');
    }
  }

  // Toggle content visibility and update expanded state when clicked
  filterToggle.addEventListener('click', function() {
    // Toggle the content visibility
    filterContent.classList.toggle('hidden');

    // Update the expanded state immediately
    updateExpandedState();

    // Log for debugging
    console.log('Filter toggled, content visible:', !filterContent.classList.contains('hidden'));
  });

  // Initialize expanded state on page load with a slight delay to ensure icons are loaded
  setTimeout(() => {
    updateExpandedState();
    console.log('Initial expanded state set');
  }, 100);
}

/**
 * Initialize the severity radio buttons
 */
function initSeverityRadios() {
  const severityLabels = document.querySelectorAll('label:has(input[name="severity"])');

  severityLabels.forEach(label => {
    label.addEventListener('click', function() {
      // Remove active classes from all labels
      severityLabels.forEach(l => {
        l.classList.remove('bg-primary', 'text-primary-foreground', 'bg-orange-500', 'text-white', 'bg-destructive', 'text-destructive-foreground');
      });

      // Add appropriate classes based on the severity value
      const severityValue = this.querySelector('input[name="severity"]').value;

      if (severityValue === 'warning') {
        this.classList.add('bg-orange-500', 'text-white');
      } else if (severityValue === 'error') {
        this.classList.add('bg-destructive', 'text-destructive-foreground');
      } else if (severityValue === 'info') {
        this.classList.add('bg-primary', 'text-primary-foreground');
      }
    });
  });
}

/**
 * Initialize the auto-refresh functionality
 */
function initAutoRefresh() {
  let autoRefreshEnabled = localStorage.getItem('activitiesAutoRefresh') === 'true';
  let refreshInterval;
  let statusUpdateInterval;
  let lastRefreshTime = Date.now();
  let refreshCount = 0;

  // Create the auto-refresh button with a simple approach
  const refreshButton = document.createElement('button');
  refreshButton.id = 'auto-refresh-button';
  refreshButton.className = `btn btn-sm ml-2 ${autoRefreshEnabled ? 'btn-primary' : 'btn-outline'}`;

  // Use a simple approach with text only
  if (autoRefreshEnabled) {
    const isDarkMode = document.documentElement.classList.contains('dark');
    const spinnerSrc = isDarkMode ? '/static/images/spinner-dark.svg' : '/static/images/spinner-light.svg';
    refreshButton.innerHTML = `<img src="${spinnerSrc}" class="h-4 w-4 mr-2" alt="Refreshing"> Auto-refresh: On`;
  } else {
    refreshButton.innerHTML = '<i data-lucide="refresh-cw" class="h-4 w-4 mr-2"></i> Auto-refresh: Off';
  }
  refreshButton.title = 'Toggle auto-refresh every 30 seconds';

  // Create a status indicator
  const statusIndicator = document.createElement('span');
  statusIndicator.className = 'text-xs text-muted-foreground ml-2 hidden';
  statusIndicator.id = 'refresh-status';

  // Create a container for the button and status
  const refreshContainer = document.createElement('div');
  refreshContainer.className = 'flex items-center';
  refreshContainer.appendChild(refreshButton);
  refreshContainer.appendChild(statusIndicator);

  // Add the container to the table actions div
  const tableActions = document.getElementById('table-actions');
  if (tableActions) {
    tableActions.prepend(refreshContainer);
    // Initialize Lucide icons
    if (typeof lucide !== 'undefined' && lucide.createIcons) {
      lucide.createIcons({
        root: refreshContainer
      });

      // Add animation to the icon after it's created
      setTimeout(() => {
        if (autoRefreshEnabled) {
          const icon = refreshButton.querySelector('svg');
          if (icon) {
            icon.style.animation = 'spin 1.5s linear infinite';
            icon.style.transformOrigin = 'center';
          }
        }
      }, 100);
    }
  }

  // Function to update the status indicator
  function updateStatusIndicator() {
    if (autoRefreshEnabled) {
      const timeSinceRefresh = Math.floor((Date.now() - lastRefreshTime) / 1000);
      statusIndicator.textContent = `Last refresh: ${timeSinceRefresh}s ago • Total: ${refreshCount}`;
      statusIndicator.classList.remove('hidden');
    } else {
      statusIndicator.classList.add('hidden');
    }
  }

  // Custom fetch function that updates the refresh count and time
  function refreshActivities() {
    lastRefreshTime = Date.now();
    refreshCount++;
    updateStatusIndicator();
    fetchLatestActivities();
  }

  // If auto-refresh was enabled, start it and the status updater
  if (autoRefreshEnabled) {
    refreshInterval = setInterval(refreshActivities, 30000); // 30 seconds
    // Set up interval to update the status indicator every second
    statusUpdateInterval = setInterval(updateStatusIndicator, 1000);
    // Also immediately fetch activities
    refreshActivities();
    // Update status indicator immediately
    updateStatusIndicator();
  }

  refreshButton.addEventListener('click', function() {
    autoRefreshEnabled = !autoRefreshEnabled;

    // Save state to localStorage
    localStorage.setItem('activitiesAutoRefresh', autoRefreshEnabled);

    if (autoRefreshEnabled) {
      // Enable auto-refresh
      // Update button content
      const isDarkMode = document.documentElement.classList.contains('dark');
      const spinnerSrc = isDarkMode ? '/static/images/spinner-dark.svg' : '/static/images/spinner-light.svg';
      refreshButton.innerHTML = `<img src="${spinnerSrc}" class="h-4 w-4 mr-2" alt="Refreshing"> Auto-refresh: On`;

      // Update button styling
      refreshButton.classList.remove('btn-outline');
      refreshButton.classList.add('btn-primary');

      // Reset counters
      lastRefreshTime = Date.now();
      refreshCount = 0;

      // Show status indicator
      updateStatusIndicator();

      // Set up interval to refresh activities
      refreshInterval = setInterval(refreshActivities, 30000); // 30 seconds

      // Set up interval to update the status indicator every second
      statusUpdateInterval = setInterval(updateStatusIndicator, 1000);

      // Immediately fetch latest activities
      refreshActivities();
    } else {
      // Disable auto-refresh
      // Update button content
      refreshButton.innerHTML = '<i data-lucide="refresh-cw" class="h-4 w-4 mr-2"></i> Auto-refresh: Off';

      // Update button styling
      refreshButton.classList.remove('btn-primary');
      refreshButton.classList.add('btn-outline');

      // Hide status indicator
      statusIndicator.classList.add('hidden');

      // Clear the intervals
      clearInterval(refreshInterval);
      clearInterval(statusUpdateInterval);
    }

    // Initialize Lucide icons
    if (typeof lucide !== 'undefined' && lucide.createIcons) {
      lucide.createIcons({
        root: refreshButton.parentNode
      });

      // Add animation to the icon after it's created
      setTimeout(() => {
        if (autoRefreshEnabled) {
          const icon = refreshButton.querySelector('svg');
          if (icon) {
            icon.style.animation = 'spin 1.5s linear infinite';
            icon.style.transformOrigin = 'center';
          }
        } else {
          const icon = refreshButton.querySelector('svg');
          if (icon) {
            icon.style.animation = '';
          }
        }
      }, 100);
    }
  });

  // Clear auto-refresh when navigating away
  window.addEventListener('beforeunload', function() {
    // Only clear the intervals, but keep the preference in localStorage
    clearInterval(refreshInterval);
    clearInterval(statusUpdateInterval);
  });

  // Update spinner when theme changes
  const observer = new MutationObserver(function(mutations) {
    mutations.forEach(function(mutation) {
      if (mutation.attributeName === 'class' && autoRefreshEnabled) {
        const isDarkMode = document.documentElement.classList.contains('dark');
        const spinnerSrc = isDarkMode ? '/static/images/spinner-dark.svg' : '/static/images/spinner-light.svg';
        const imgElement = refreshButton.querySelector('img');
        if (imgElement) {
          imgElement.src = spinnerSrc;
        }
      }
    });
  });

  observer.observe(document.documentElement, { attributes: true });
}

/**
 * Fetch latest activities
 */
function fetchLatestActivities() {
  const currentTableBody = document.getElementById('activity-table-body');
  if (!currentTableBody) return;

  // Show loading state
  currentTableBody.innerHTML = '<tr><td colspan="8" class="px-4 py-6 text-center"><div class="flex justify-center"><div class="animate-spin rounded-full h-5 w-5 border-b-2 border-primary"></div></div></td></tr>';

  // Get current URL parameters
  const urlParams = new URLSearchParams(window.location.search);

  // Add a cache-busting parameter
  urlParams.set('_', Date.now());

  // Add the ajax parameter to get only the table content
  urlParams.set('ajax', 'true');

  // Construct the correct URL for the table data
  const fetchUrl = `/admin/activities?${urlParams.toString()}`;

  // Use AbortController to allow canceling the fetch if needed
  const controller = new AbortController();
  const signal = controller.signal;

  // Set a timeout to abort the fetch if it takes too long
  const timeoutId = setTimeout(() => controller.abort(), 15000); // 15 second timeout

  // Fetch the updated table content
  fetch(fetchUrl, {
    headers: {
      'X-Requested-With': 'XMLHttpRequest'
    },
    signal: signal
  })
  .then(response => {
    if (!response.ok) throw new Error(`Network response was not ok: ${response.status}`);
    return response.text();
  })
  .then(html => {
    clearTimeout(timeoutId);

    // Use requestAnimationFrame to schedule DOM updates during an idle frame
    requestAnimationFrame(() => {
      // Create a document fragment to work with the HTML off the main DOM
      const tempDiv = document.createElement('div');
      tempDiv.innerHTML = html;

      // Update the table body with the new content
      currentTableBody.innerHTML = html;

      // Schedule non-critical updates for the next idle period
      if (window.requestIdleCallback) {
        requestIdleCallback(() => {
          // Initialize Lucide icons in smaller batches
          initializeIconsProgressively(currentTableBody);

          // Update warning badges
          updateWarningBadges();
        }, { timeout: 500 });
      } else {
        // Fallback for browsers without requestIdleCallback
        setTimeout(() => {
          if (typeof lucide !== 'undefined' && lucide.createIcons) {
            lucide.createIcons({
              root: currentTableBody
            });
          }
          updateWarningBadges();
        }, 50);
      }
    });
  })
  .catch(error => {
    clearTimeout(timeoutId);

    if (error.name === 'AbortError') {
      console.warn('Fetch operation was aborted due to timeout');
      currentTableBody.innerHTML = '<tr><td colspan="8" class="px-4 py-6 text-center text-muted-foreground">Request timed out. Auto-refresh will try again.</td></tr>';
    } else {
      console.error('Fetch error:', error);
      currentTableBody.innerHTML = '<tr><td colspan="8" class="px-4 py-6 text-center text-muted-foreground">Error loading activities</td></tr>';
      showNotification('Update Failed', 'Could not refresh activity logs. Please try again later.', 'error');
    }
  });
}

/**
 * Show notification using the application's toast system
 * @param {string} title - The notification title
 * @param {string} message - The notification message
 * @param {string} type - The notification type (success, error, warning, info)
 */
function showNotification(title, message, type = 'success') {
  // Create a custom message that includes the title
  const formattedMessage = `${title}: ${message}`;

  // Use the application's toast system with the specified type
  if (typeof showToast === 'function') {
    showToast(formattedMessage, { type: type });
  } else {
    // Fallback if toast system is not available
    // No action needed
  }
}

/**
 * Update warning badges in the activity table
 */
function updateWarningBadges() {
  // Find all warning badges in the table
  const warningRows = document.querySelectorAll('tr:has(.badge-warning)');

  // Add a subtle background color to rows with warning badges
  warningRows.forEach(row => {
    row.classList.add('bg-warning/5');
  });
}

/**
 * Initialize Lucide icons progressively to avoid blocking the main thread
 * @param {HTMLElement} container - The container element with icons to initialize
 */
function initializeIconsProgressively(container) {
  if (typeof lucide === 'undefined' || !lucide.createIcons) return;

  // Get all icon elements
  const iconElements = container.querySelectorAll('[data-lucide]');
  const batchSize = 10; // Process 10 icons at a time

  // Process icons in batches
  for (let i = 0; i < iconElements.length; i += batchSize) {
    const batch = Array.from(iconElements).slice(i, i + batchSize);

    // Process each batch in a separate animation frame
    if (i > 0) {
      setTimeout(() => {
        batch.forEach(el => {
          lucide.createIcons({
            icons: {
              [el.getAttribute('data-lucide')]: el
            }
          });
        });
      }, Math.floor(i / batchSize) * 20); // Stagger batches by 20ms
    } else {
      // Process first batch immediately for perceived performance
      batch.forEach(el => {
        lucide.createIcons({
          icons: {
            [el.getAttribute('data-lucide')]: el
          }
        });
      });
    }
  }
}

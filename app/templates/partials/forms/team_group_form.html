{% from "partials/forms/base_form.html" import form_group, form_actions, form_header %}

<form action="{{ action_url }}" method="POST" class="space-y-4">
  <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">

  {{ form_header(
    title = "Edit Group" if entity else "Add Group",
    description = "Update group information" if entity else "Create a new group"
  ) }}

  <div class="grid grid-cols-1 gap-4">
    {% if not entity %}
      {% if pre_selected_team %}
        {{ form_group(
          label = "Team",
          name = "team_display",
          value = pre_selected_team.name,
          type = "text",
          required = false,
          disabled = true
        ) }}
        <input type="hidden" name="team_id" value="{{ pre_selected_team.id }}">
      {% else %}
        {{ form_group(
          label = "Team",
          name = "team_id",
          type = "select",
          required = true,
          options = team_options
        ) }}
      {% endif %}
    {% else %}
      {{ form_group(
        label = "Team",
        name = "team_display",
        value = entity.team.name,
        type = "text",
        required = false,
        disabled = true
      ) }}
      <input type="hidden" name="team_id" value="{{ entity.team_id }}">
    {% endif %}

    {{ form_group(
      label = "Group Name",
      name = "name",
      type = "text",
      value = entity.name if entity else "",
      required = true,
      placeholder = "Enter group name"
    ) }}

    {{ form_group(
      label = "Description",
      name = "description",
      type = "textarea",
      value = entity.description if entity else "",
      placeholder = "Enter group description"
    ) }}
  </div>

  {{ form_actions(
    submit_label = "Update Group" if entity else "Create Group"
  ) }}
</form>

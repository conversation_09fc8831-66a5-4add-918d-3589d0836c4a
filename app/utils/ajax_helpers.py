from functools import wraps
from flask import request, jsonify, redirect, url_for, flash


def ajax_response(success_redirect=None, error_redirect=None):
    """
    Decorator to handle AJAX vs regular form submissions.

    The decorated function should return:
    - For success: {'success': True, 'message': str, 'redirect_url': str (optional)}
    - For error: {'success': False, 'message': str, 'errors': dict (optional)}

    Args:
        success_redirect: URL endpoint name for successful non-AJAX redirects
        error_redirect: URL endpoint name for failed non-AJAX redirects
    """
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            result = f(*args, **kwargs)

            # If function returns a Flask response directly, return it
            if hasattr(result, 'status_code'):
                return result

            is_ajax = request.headers.get('X-Requested-With') == 'XMLHttpRequest'

            if is_ajax:
                # Return JSON response for AJAX requests
                status_code = 200 if result.get('success') else 400
                return jsonify(result), status_code
            else:
                # Handle regular form submission
                if result.get('success'):
                    flash(result['message'], 'success')
                    redirect_url = result.get('redirect_url')
                    if redirect_url:
                        return redirect(redirect_url)
                    elif success_redirect:
                        return redirect(url_for(success_redirect))
                    else:
                        return redirect(request.url)
                else:
                    flash(result['message'], 'danger')
                    redirect_url = result.get('redirect_url')
                    if redirect_url:
                        return redirect(redirect_url)
                    elif error_redirect:
                        return redirect(url_for(error_redirect))
                    else:
                        return redirect(request.url)

        return decorated_function
    return decorator

/**
 * Critical initialization for sidebar submenus
 * This script runs before the DOM is fully loaded to prevent submenu blinking
 * Ultra-simple implementation
 */

(function() {
  // Apply submenu states immediately to prevent blinking
  try {
    const openSubmenus = JSON.parse(localStorage.getItem('openSubmenus') || '[]');
    const isSidebarCollapsed = localStorage.getItem('sidebarCollapsed') === 'true';

    // Add a style tag to immediately apply submenu states
    if (!isSidebarCollapsed && openSubmenus.length > 0) {
      const styleTag = document.createElement('style');

      // Create simple CSS rules for each open submenu
      let cssRules = '';
      openSubmenus.forEach(submenuId => {
        cssRules += `
          .submenu-toggle[data-submenu-id="${submenuId}"] + .submenu-content {
            max-height: 500px !important;
            opacity: 1 !important;
            visibility: visible !important;
            display: block !important;
          }

          .submenu-toggle[data-submenu-id="${submenuId}"] [data-lucide="chevron-down"] {
            transform: rotate(180deg) !important;
          }
        `;
      });

      styleTag.textContent = cssRules;
      document.head.appendChild(styleTag);
    }
  } catch (e) {
    console.error('Error in sidebar-critical-init.js:', e);
  }
})();

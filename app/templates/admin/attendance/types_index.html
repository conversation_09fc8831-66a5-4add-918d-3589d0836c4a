{% extends "base.html" %}

{% from "components/page_header.html" import page_header %}
{% from "components/table_header.html" import table_header %}
{% from "components/simple_table.html" import simple_table %}
{% from "components/action_buttons.html" import action_buttons %}

{% block title %}Attendance Types{% endblock %}

{% block header %}Attendance Types{% endblock %}

{% block content %}
<!-- Hidden fields to store URLs for JavaScript use -->
<input type="hidden" id="add-attendance-type-url" data-url="{{ url_for('admin_attendance.get_attendance_type_form_create') }}">
<input type="hidden" id="edit-attendance-type-base-url" data-url="{{ url_for('admin_attendance.get_attendance_type_form_edit', type_id=0) }}">
<input type="hidden" id="delete-attendance-type-base-url" data-url="{{ url_for('admin_attendance.delete_attendance_type', type_id=0) }}">
<input type="hidden" id="csrf-token" data-token="{{ csrf_token() }}">

<div class="space-y-6">
  {{ page_header(
    title="Attendance Types",
    button_text="Add Attendance Type",
    button_icon="plus",
    button_action="openAddAttendanceTypeForm()",
    description="Manage the types of attendance that can be logged (e.g., WFH, Sick Leave)."
  ) }}

  <div class="card">
    {{ table_header(
      title="Attendance Type List"
    ) }}

    {% call simple_table(
      headers=[
        {'label': 'Code'},
        {'label': 'Name'},
        {'label': 'Description'},
        {'label': 'Requires Approval?'},
        {'label': 'Full Day?'},
        {'label': 'Color'},
        {'label': 'Actions', 'align': 'right'}
      ],
      items=attendance_types,
      empty_icon="tag",
      empty_title="No attendance types found",
      empty_description="Create your first attendance type to get started.",
     empty_button_text="Add Attendance Type",
     empty_button_icon="plus",
     empty_button_action="openAddAttendanceTypeForm()"
   ) %}
     {% for type_item in attendance_types %} {# Iterate through items of pagination object #}
      <tr class="border-b border-border hover:bg-muted/30">
        <td class="px-4 py-3 text-sm font-mono">{{ type_item.code }}</td>
        <td class="px-4 py-3 text-sm">{{ type_item.name }}</td>
        <td class="px-4 py-3 text-sm text-muted-foreground">{{ type_item.description | truncate(60) if type_item.description else '-' }}</td>
        <td class="px-4 py-3 text-sm">
          {% if type_item.requires_approval %}
            <span class="badge badge-warning">Yes</span>
          {% else %}
            <span class="badge badge-secondary">No</span>
          {% endif %}
        </td>
        <td class="px-4 py-3 text-sm">
          {% if type_item.is_full_day %}
            <span class="badge badge-info">Yes</span>
          {% else %}
            <span class="badge badge-primary">No</span>
          {% endif %}
        </td>
        <td class="px-4 py-3 text-sm">
            {% if type_item.color_code %}
            <span class="inline-block w-4 h-4 rounded-full border border-border" style="background-color: {{ type_item.color_code }}; vertical-align: middle;"></span>
            <span class="ml-1 font-mono" style="vertical-align: middle;">{{ type_item.color_code }}</span>
            {% else %}
            -
            {% endif %}
        </td>
        <td class="px-4 py-3 text-sm text-right">
          {% set escaped_name = type_item.name | replace("'", "\\'") | replace('"', '\\"') %}
          {{ action_buttons([
            {'type': 'button', 'action': "openEditAttendanceTypeForm(" ~ type_item.id ~ ")", 'icon': 'edit', 'title': 'Edit'},
            {'type': 'button', 'action': "confirmDeleteAttendanceType(" ~ type_item.id ~ ", '" ~ escaped_name ~ "')", 'icon': 'trash-2', 'variant': 'text-destructive', 'title': 'Delete'}
          ]) }}
        </td>
      </tr>
      {% endfor %}
    {% endcall %}

    {# Use the pagination macro if attendance_types is a pagination object #}
    {% include "partials/pagination.html" %}
  </div>
</div>
{% endblock %}

{% block scripts %}
{{ super() }} {# Include scripts from base template if any #}
<script src="{{ url_for('static', filename='js/pages/attendance-types.js') }}"></script>
<script src="{{ url_for('static', filename='js/pages/attendance-form-setup.js') }}"></script>
{% endblock %}

"""
API routes for activity logs.
"""

from flask import jsonify, request, current_app
from flask_login import login_required, current_user
from datetime import datetime, timedelta

from app import db
from app.models import Activity
from app.utils.decorators import admin_required, log_activity
from app.utils.pagination import paginate_query
from app.routes.api import api_bp
from app.routes.api.utils import handle_api_error, create_pagination_dict, api_response


class APIError(Exception):
    """Base exception for API errors"""
    def __init__(self, message, status_code=400, payload=None):
        super().__init__()
        self.message = message
        self.status_code = status_code
        self.payload = payload

    def to_dict(self):
        rv = dict(self.payload or {})
        rv['message'] = self.message
        rv['success'] = False
        return rv


@api_bp.errorhandler(APIError)
def handle_api_exception(error):
    """Return JSON instead of HTML for API errors."""
    response = jsonify(error.to_dict())
    response.status_code = error.status_code
    return response


@api_bp.errorhandler(404)
def handle_not_found(error):
    return jsonify({
        'success': False,
        'message': 'Resource not found',
        'error': str(error)
    }), 404


@api_bp.errorhandler(500)
def handle_server_error(error):
    current_app.logger.error(f'Server error: {str(error)}')
    return jsonify({
        'success': False,
        'message': 'An unexpected error occurred'
    }), 500


@api_bp.route('/activities/user', methods=['GET'])
@login_required
@handle_api_error
def get_user_activities():
    """Get activities for the current user with filtering options."""
    # Get query parameters
    page = request.args.get('page', 1, type=int)
    per_page = min(request.args.get('per_page', 10, type=int), 50)  # Limit to 50 items max
    entity_type = request.args.get('entity_type')
    entity_id = request.args.get('entity_id', type=int)
    category = request.args.get('category')
    action_contains = request.args.get('action_contains')
    date_from = request.args.get('date_from')
    date_to = request.args.get('date_to')

    # Base query - only show activities for the current user
    query = Activity.query.filter(Activity.user_id == current_user.id)

    # Apply filters with input validation
    if entity_type:
        # Validate entity_type against allowed values
        allowed_entity_types = ['User', 'BusinessUnit', 'BusinessSegment', 'EmployeeDetail', 'Team', 'Setting']
        if entity_type in allowed_entity_types:
            query = query.filter(Activity.entity_type == entity_type)

    if entity_id:
        # Ensure entity_id is a positive integer
        if isinstance(entity_id, int) and entity_id > 0:
            query = query.filter(Activity.entity_id == entity_id)

    if category:
        # Validate category against known categories
        allowed_categories = [Activity.CATEGORY_AUTH, Activity.CATEGORY_USER, Activity.CATEGORY_ADMIN,
                            Activity.CATEGORY_DATA, Activity.CATEGORY_SYSTEM]
        if category in allowed_categories:
            query = query.filter(Activity.category == category)

    if action_contains:
        # Sanitize action_contains input and limit length
        action_contains = str(action_contains).strip()[:100]  # Limit to 100 chars
        if action_contains:
            query = query.filter(Activity.action.ilike(f'%{action_contains}%'))

    # Date filtering
    if date_from:
        try:
            from_date = datetime.fromisoformat(date_from)
            query = query.filter(Activity.created_at >= from_date)
        except ValueError:
            pass

    if date_to:
        try:
            to_date = datetime.fromisoformat(date_to)
            query = query.filter(Activity.created_at <= to_date)
        except ValueError:
            pass

    # Order by most recent first
    query = query.order_by(Activity.created_at.desc())

    # Paginate results using centralized pagination
    activities, pagination_info = paginate_query(query, page=page, per_page=per_page)

    # Format response
    activities_data = [activity.to_dict() for activity in activities]

    return jsonify({
        'success': True,
        'activities': activities_data,
        'pagination': pagination_info
    })


@api_bp.route('/activities', methods=['GET'])
@login_required
@handle_api_error
def get_entity_activities():
    """Get activities for a specific entity."""
    # Get query parameters
    entity_type = request.args.get('entity_type')
    entity_id = request.args.get('entity_id', type=int)
    limit = request.args.get('limit', 10, type=int)
    if limit == 0:  # If limit is 0, return all activities
        limit = None
    elif limit > 0:  # Otherwise enforce a reasonable limit
        limit = min(limit, 100)  # Increased max limit to 100

    method = request.args.get('method')
    exclude_method = request.args.get('exclude_method')
    show_all = request.args.get('show_all', type=bool, default=False)

    # Validate required parameters
    if not entity_type or not entity_id:
        return jsonify({
            'success': False,
            'message': 'Entity type and ID are required'
        }), 400

    # Base query - filter by entity type and ID
    query = Activity.query.filter(
        Activity.entity_type == entity_type,
        Activity.entity_id == entity_id
    )

    # Security check - regular users can only see:
    # 1. Their own activities
    # 2. Activities on entities they own (like their own profile)
    # 3. Activities with method=update (changes to entities)
    if not current_user.is_admin and not show_all:
        # For User entities, only show activities if it's the current user's profile
        if entity_type == 'User' and entity_id != current_user.id:
            return jsonify({
                'success': False,
                'message': 'You do not have permission to view these activities'
            }), 403

        # For other entities, only show update activities (changes)
        # This allows users to see changes to entities they care about
        # but not all activities related to those entities
        query = query.filter(Activity.method == Activity.METHOD_UPDATE)

    # Apply method filter if provided
    if method:
        # Handle special case for 'read' method - also check action text
        if method == 'read':
            query = query.filter(
                (Activity.method == 'read') |
                (Activity.action.ilike('%viewed%')) |
                (Activity.action.ilike('%view%'))
            )
        else:
            query = query.filter(Activity.method == method)

    # Apply exclude_method filter if provided
    if exclude_method:
        # Handle special case for 'read' method - also exclude based on action text
        if exclude_method == 'read':
            query = query.filter(
                (Activity.method != 'read') &
                ~(Activity.action.ilike('%viewed%')) &
                ~(Activity.action.ilike('%view%'))
            )
        else:
            query = query.filter(Activity.method != exclude_method)

    # Order by most recent first
    query = query.order_by(Activity.created_at.desc())

    # Limit results if a limit is specified
    if limit is not None:
        activities = query.limit(limit).all()
    else:
        activities = query.all()

    # Format response
    activities_data = [activity.to_dict() for activity in activities]

    return jsonify({
        'success': True,
        'activities': activities_data,
        'entity_type': entity_type,
        'entity_id': entity_id,
        'count': len(activities_data)
    })


@api_bp.route('/activities/delete-older-than/<int:days>', methods=['POST'])
@login_required
@admin_required
@handle_api_error
@log_activity('Deleted old activity logs')
def delete_logs_older_than(days):
    """Delete activity logs older than the specified number of days."""
    if days <= 0:
        raise APIError("Days must be a positive number", status_code=400)

    # Calculate the cutoff date
    cutoff_date = datetime.now() - timedelta(days=days)

    # Delete logs older than the cutoff date
    result = Activity.query.filter(Activity.created_at < cutoff_date).delete()
    db.session.commit()

    message = f"Deleted {result} logs older than {days} days"
    current_app.logger.info(f"Admin {current_user.name} (ID: {current_user.id}) {message}")

    return jsonify({
        'success': True,
        'message': message,
        'affected_count': result
    })


@api_bp.route('/activities/delete-by-category/<category>', methods=['POST'])
@login_required
@admin_required
@handle_api_error
@log_activity('Deleted activity logs by category')
def delete_logs_by_category(category):
    """Delete activity logs by category."""
    if not category:
        raise APIError("Category is required", status_code=400)

    # Delete logs by category
    result = Activity.query.filter(Activity.category == category).delete()
    db.session.commit()

    message = f"Deleted {result} logs with category '{category}'"
    current_app.logger.info(f"Admin {current_user.name} (ID: {current_user.id}) {message}")

    return jsonify({
        'success': True,
        'message': message,
        'affected_count': result
    })


@api_bp.route('/activities/delete-by-severity/<severity>', methods=['POST'])
@login_required
@admin_required
@handle_api_error
@log_activity('Deleted activity logs by severity')
def delete_logs_by_severity(severity):
    """Delete activity logs by severity."""
    if not severity:
        raise APIError("Severity is required", status_code=400)

    # Delete logs by severity
    result = Activity.query.filter(Activity.severity == severity).delete()
    db.session.commit()

    message = f"Deleted {result} logs with severity '{severity}'"
    current_app.logger.info(f"Admin {current_user.name} (ID: {current_user.id}) {message}")

    return jsonify({
        'success': True,
        'message': message,
        'affected_count': result
    })


@api_bp.route('/activities/delete-all', methods=['POST'])
@login_required
@admin_required
@handle_api_error
@log_activity('Deleted all activity logs')
def delete_all_logs():
    """Delete all activity logs."""
    # Get confirmation from request data
    data = request.get_json(force=True)
    confirmation = data.get('confirmation') if data else None

    if not confirmation or confirmation != 'DELETE_ALL_LOGS':
        raise APIError("Confirmation is required to delete all logs", status_code=400)

    # Delete all logs
    result = Activity.query.delete()
    db.session.commit()

    message = f"Deleted all {result} activity logs"
    current_app.logger.info(f"Admin {current_user.name} (ID: {current_user.id}) {message}")

    return jsonify({
        'success': True,
        'message': message,
        'affected_count': result
    })

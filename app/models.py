"""
Models module - imports all models from the models package.
This file is kept for backward compatibility.
"""

# Import common utilities and constants
from app import db
import pytz
from datetime import datetime

# Set Philippine Timezone
PH_TZ = pytz.timezone('Asia/Manila')

# Import all models from the models package
from app.models.user import User, WeakPasswordError, EmployeeDetailsProxy
from app.models.activity import Activity
from app.models.business import BusinessUnit, BusinessSegment
from app.models.employee import EmployeeDetail, team_members
from app.models.messaging import PasswordReset
from app.models.settings import Setting
from app.models.team import Team, TeamGroup

# Define __all__ to explicitly specify what is exported
__all__ = [
    'db', 'PH_TZ',
    'User', 'WeakPasswordError', 'EmployeeDetailsProxy',
    'Activity',
    'BusinessUnit', 'BusinessSegment',
    'EmployeeDetail', 'team_members',
    'PasswordReset',
    'Setting',
    'Team', 'TeamGroup'
]

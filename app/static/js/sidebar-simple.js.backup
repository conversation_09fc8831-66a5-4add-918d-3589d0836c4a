/**
 * Simple sidebar functionality for the Admin Panel
 */

// Prevent FOUC by applying sidebar state immediately
(function() {
  var isCollapsed = localStorage.getItem('sidebarCollapsed') === 'true';
  if (isCollapsed) document.documentElement.classList.add('sidebar-pre-collapsed');
})();

// Wait for DOM to be ready
document.addEventListener('DOMContentLoaded', function() {
  // Get DOM elements
  const sidebar = document.getElementById('sidebar');
  const mainContent = document.getElementById('main-content');
  const sidebarOverlay = document.getElementById('sidebar-overlay');
  const sidebarToggleBtn = document.getElementById('sidebar-toggle');
  const collapseSidebar = document.getElementById('collapse-sidebar');
  const userMenuButton = document.getElementById('user-menu-button');
  const userDropdown = document.getElementById('user-dropdown');
  const userMenuChevron = document.getElementById('user-menu-chevron');
  
  // Initialize sidebar state
  initSidebar();
  
  // Initialize submenus
  initSubmenus();
  
  // Setup sidebar toggle button
  if (collapseSidebar) {
    collapseSidebar.addEventListener('click', function() {
      // For mobile views, toggle mobile sidebar
      if (window.innerWidth < 1024) {
        toggleMobileSidebar();
        return;
      }
      
      // Toggle sidebar collapsed state for desktop
      const isCollapsed = sidebar.classList.toggle('sidebar-collapsed');
      
      // Save preference
      localStorage.setItem('sidebarCollapsed', isCollapsed);
      document.cookie = `sidebarCollapsed=${isCollapsed}; path=/; max-age=31536000`;
      
      // Adjust main content margin
      if (isCollapsed) {
        mainContent.classList.remove('lg:ml-64');
        mainContent.classList.add('lg:ml-0');
        
        // Update icon
        const collapseIcon = this.querySelector('[data-lucide]');
        if (collapseIcon) {
          collapseIcon.setAttribute('data-lucide', 'panel-right');
          if (window.lucide) window.lucide.createIcons();
        }
      } else {
        mainContent.classList.remove('lg:ml-0');
        mainContent.classList.add('lg:ml-64');
        
        // Update icon
        const collapseIcon = this.querySelector('[data-lucide]');
        if (collapseIcon) {
          collapseIcon.setAttribute('data-lucide', 'panel-left');
          if (window.lucide) window.lucide.createIcons();
        }
      }
    });
  }
  
  // Setup mobile sidebar toggle
  if (sidebarToggleBtn) {
    sidebarToggleBtn.addEventListener('click', toggleMobileSidebar);
  }
  
  // Close sidebar when overlay is clicked
  if (sidebarOverlay) {
    sidebarOverlay.addEventListener('click', toggleMobileSidebar);
  }
  
  // Setup user dropdown
  if (userMenuButton && userDropdown) {
    userMenuButton.addEventListener('click', function(e) {
      e.stopPropagation();
      
      // Toggle dropdown visibility
      userDropdown.classList.toggle('hidden');
      
      // Update chevron rotation
      if (userMenuChevron) {
        userMenuChevron.classList.toggle('rotate-180');
      }
      
      // Position dropdown
      if (!userDropdown.classList.contains('hidden')) {
        const buttonRect = userMenuButton.getBoundingClientRect();
        const sidebarRect = sidebar.getBoundingClientRect();
        
        userDropdown.style.left = `${sidebarRect.right + 8}px`;
        userDropdown.style.top = `${buttonRect.top}px`;
        
        // Initialize icons
        if (window.lucide) {
          window.lucide.createIcons({
            selector: '#user-dropdown [data-lucide]'
          });
        }
      }
    });
    
    // Close dropdown when clicking outside
    document.addEventListener('click', function(e) {
      if (!userDropdown.classList.contains('hidden') && 
          !userMenuButton.contains(e.target) && 
          !userDropdown.contains(e.target)) {
        userDropdown.classList.add('hidden');
        
        if (userMenuChevron) {
          userMenuChevron.classList.remove('rotate-180');
        }
      }
    });
  }
  
  // Handle window resize
  window.addEventListener('resize', function() {
    if (window.innerWidth < 1024) {
      mainContent.classList.remove('lg:ml-64', 'lg:ml-16');
      
      if (!sidebar.classList.contains('mobile-open')) {
        sidebar.style.transform = 'translateX(-100%)';
      }
    } else {
      sidebar.style.transform = '';
      initSidebar();
    }
  });
  
  // Toggle mobile sidebar function
  function toggleMobileSidebar() {
    const isOpen = sidebar.classList.toggle('mobile-open');
    sidebarOverlay.classList.toggle('hidden');
    
    if (isOpen) {
      sidebar.style.transform = 'translateX(0)';
      sidebar.style.zIndex = '50';
      setTimeout(() => { sidebarOverlay.style.opacity = '1'; }, 10);
    } else {
      sidebar.style.transform = 'translateX(-100%)';
      sidebarOverlay.style.opacity = '0';
      
      setTimeout(() => {
        if (!sidebar.classList.contains('mobile-open')) {
          sidebarOverlay.classList.add('hidden');
        }
      }, 300);
    }
    
    // Toggle icon
    const toggleIcon = sidebarToggleBtn.querySelector('[data-lucide]');
    if (toggleIcon) {
      toggleIcon.setAttribute('data-lucide', isOpen ? 'x' : 'menu');
      if (window.lucide) window.lucide.createIcons();
    }
  }
  
  // Initialize sidebar state
  function initSidebar() {
    if (!sidebar || !mainContent) return;
    
    const isCollapsed = localStorage.getItem('sidebarCollapsed') === 'true';
    
    if (window.innerWidth >= 1024) {
      sidebar.style.transform = '';
      sidebar.classList.remove('mobile-open');
      
      if (isCollapsed) {
        sidebar.classList.add('sidebar-collapsed');
        mainContent.classList.remove('lg:ml-64');
        mainContent.classList.add('lg:ml-0');
        
        // Update collapse button icon
        if (collapseSidebar) {
          const collapseIcon = collapseSidebar.querySelector('[data-lucide]');
          if (collapseIcon) {
            collapseIcon.setAttribute('data-lucide', 'panel-right');
            if (window.lucide) window.lucide.createIcons();
          }
        }
      } else {
        sidebar.classList.remove('sidebar-collapsed');
        mainContent.classList.remove('lg:ml-0');
        mainContent.classList.add('lg:ml-64');
        
        // Update collapse button icon
        if (collapseSidebar) {
          const collapseIcon = collapseSidebar.querySelector('[data-lucide]');
          if (collapseIcon) {
            collapseIcon.setAttribute('data-lucide', 'panel-left');
            if (window.lucide) window.lucide.createIcons();
          }
        }
      }
    } else {
      if (!sidebar.classList.contains('mobile-open')) {
        sidebar.style.transform = 'translateX(-100%)';
      }
    }
    
    document.documentElement.classList.remove('sidebar-pre-collapsed');
  }
  
  // Initialize submenus
  function initSubmenus() {
    // Get all submenu toggles
    const submenuToggles = document.querySelectorAll('.submenu-toggle');
    
    // Add click event to each toggle
    submenuToggles.forEach(toggle => {
      toggle.addEventListener('click', function(e) {
        e.preventDefault();
        
        // Get the submenu content
        const submenu = this.nextElementSibling;
        if (!submenu || !submenu.classList.contains('submenu-content')) return;
        
        // Toggle expanded state
        const isExpanded = submenu.classList.contains('submenu-expanded');
        
        // Get the chevron icon
        const chevron = this.querySelector('[data-lucide="chevron-down"]');
        
        if (isExpanded) {
          // Collapse the submenu
          submenu.classList.remove('submenu-expanded');
          submenu.classList.remove('submenu-open');
          
          // Rotate chevron back
          if (chevron) chevron.classList.remove('rotate-180');
        } else {
          // Expand the submenu
          submenu.classList.add('submenu-expanded');
          submenu.classList.add('submenu-open');
          
          // Rotate chevron
          if (chevron) chevron.classList.add('rotate-180');
        }
        
        // Save state to localStorage
        const submenuId = this.getAttribute('data-submenu-id');
        if (submenuId) {
          const openSubmenus = JSON.parse(localStorage.getItem('openSubmenus') || '[]');
          
          if (!isExpanded) {
            // Add to open submenus
            if (!openSubmenus.includes(submenuId)) {
              openSubmenus.push(submenuId);
            }
          } else {
            // Remove from open submenus
            const index = openSubmenus.indexOf(submenuId);
            if (index > -1) {
              openSubmenus.splice(index, 1);
            }
          }
          
          localStorage.setItem('openSubmenus', JSON.stringify(openSubmenus));
        }
      });
    });
    
    // Apply saved submenu state
    const openSubmenus = JSON.parse(localStorage.getItem('openSubmenus') || '[]');
    const isSidebarCollapsed = localStorage.getItem('sidebarCollapsed') === 'true';
    
    if (!isSidebarCollapsed && openSubmenus.length > 0) {
      openSubmenus.forEach(submenuId => {
        const toggle = document.querySelector(`.submenu-toggle[data-submenu-id="${submenuId}"]`);
        if (toggle) {
          const submenu = toggle.nextElementSibling;
          const chevron = toggle.querySelector('[data-lucide="chevron-down"]');
          
          if (submenu) {
            submenu.classList.add('submenu-expanded');
            submenu.classList.add('submenu-open');
          }
          
          if (chevron) {
            chevron.classList.add('rotate-180');
          }
        }
      });
    }
    
    // Expand submenus for active items
    const activeItems = document.querySelectorAll('.sidebar-item.active, .submenu-item.active');
    activeItems.forEach(item => {
      const parentSubmenu = item.closest('.submenu-content');
      if (parentSubmenu) {
        const parentToggle = parentSubmenu.previousElementSibling;
        if (parentToggle && parentToggle.classList.contains('submenu-toggle')) {
          parentSubmenu.classList.add('submenu-expanded');
          parentSubmenu.classList.add('submenu-open');
          
          const chevron = parentToggle.querySelector('[data-lucide="chevron-down"]');
          if (chevron) {
            chevron.classList.add('rotate-180');
          }
        }
      }
    });
  }
});

"""
Admin employee details management routes.
"""

from flask import render_template, redirect, url_for, flash, request, jsonify, current_app
from flask_login import login_required
from datetime import datetime

from app import db
from app.models import User, BusinessUnit, BusinessSegment, Activity, EmployeeDetail
from app.utils.decorators import admin_required, log_activity
from app.utils.cache_helpers import invalidate_employee_cache, invalidate_dashboard_cache
from app.utils.pagination import paginate_query
from app.utils.ajax_helpers import ajax_response
from app.routes.admin import admin_bp


@admin_bp.route('/employee-details')
@login_required
@admin_required
@log_activity('Viewed employee details list', entity_type='EmployeeDetail', category=Activity.CATEGORY_ADMIN, method=Activity.METHOD_READ)
def employee_details():
    """Display paginated list of employee details."""
    # Build the query for employee details
    query = EmployeeDetail.query

    # Use admin per_page from config
    per_page = current_app.config.get('PAGINATION_PER_PAGE_ADMIN', 20)

    # Use centralized pagination
    employee_details, pagination = paginate_query(query, per_page=per_page)

    business_units = BusinessUnit.query.all()
    business_segments = BusinessSegment.query.all()

    # Get users without employee details
    users_without_details = User.query.outerjoin(EmployeeDetail, User.id == EmployeeDetail.user_id).filter(EmployeeDetail.id == None).all()

    return render_template('admin/employee_details.html',
                          employee_details=employee_details,
                          business_units=business_units,
                          business_segments=business_segments,
                          users_without_details=users_without_details,
                          pagination=pagination,
                          active_page='employee-details')


@admin_bp.route('/employee-details/<int:detail_id>')
@login_required
@admin_required
def get_employee_detail(detail_id):
    """Get employee detail in JSON format."""
    detail = EmployeeDetail.query.get_or_404(detail_id)
    return jsonify({
        'id': detail.id,
        'user_id': detail.user_id,
        'user_name': detail.user.name,
        'first_name': detail.first_name,
        'middle_name': detail.middle_name,
        'last_name': detail.last_name,
        'legal_name': detail.legal_name,
        'job_title': detail.job_title,
        'employee_number': detail.employee_number,
        'phone': detail.phone,
        'emp_type': detail.emp_type,
        'enterprise_id': detail.enterprise_id,
        'manager_name': detail.manager_name,
        'job_code': detail.job_code,
        'manager_level': detail.manager_level,
        'job_code_track_level': detail.job_code_track_level,
        'business_unit_id': detail.business_unit_id,
        'business_segment_id': detail.business_segment_id,
        'hire_date': detail.hire_date.isoformat() if detail.hire_date else None,
        'emp_status': detail.emp_status
    })


@admin_bp.route('/employee-details/create', methods=['POST'])
@login_required
@admin_required
@log_activity('Created employee details', entity_type='EmployeeDetail', category=Activity.CATEGORY_DATA, method=Activity.METHOD_CREATE)
def create_employee_detail():
    """Create new employee details."""
    user_id = request.form.get('user_id')
    employee_number = request.form.get('employee_number')
    # Get form data
    first_name = request.form.get('first_name')
    middle_name = request.form.get('middle_name')
    last_name = request.form.get('last_name')
    legal_name = request.form.get('legal_name')
    job_title = request.form.get('job_title')  # Renamed from position
    phone = request.form.get('phone')
    emp_type = request.form.get('emp_type')
    enterprise_id = request.form.get('enterprise_id')
    manager_name = request.form.get('manager_name')
    job_code = request.form.get('job_code')
    manager_level = request.form.get('manager_level')
    job_code_track_level = request.form.get('job_code_track_level')
    business_unit_id = request.form.get('business_unit_id')
    business_segment_id = request.form.get('business_segment_id')
    hire_date_str = request.form.get('hire_date')
    emp_status = request.form.get('emp_status', EmployeeDetail.STATUS_ACTIVE)

    # Validate input
    if not user_id:
        flash('User is required', 'error')
        return redirect(url_for('admin.employee_details'))

    # Check if employee detail already exists for this user
    existing_detail = EmployeeDetail.query.filter_by(user_id=user_id).first()
    if existing_detail:
        flash('Employee details already exist for this user', 'error')
        return redirect(url_for('admin.employee_details'))

    # Convert hire_date string to Python date object
    hire_date = None
    if hire_date_str:
        try:
            hire_date = datetime.strptime(hire_date_str, '%Y-%m-%d').date()
        except ValueError:
            flash('Invalid hire date format. Please use YYYY-MM-DD format.', 'error')
            return redirect(url_for('admin.employee_details'))

    # Create new employee detail
    new_detail = EmployeeDetail(
        user_id=user_id,  # type: ignore
        employee_number=employee_number,  # type: ignore
        first_name=first_name,  # type: ignore
        middle_name=middle_name,  # type: ignore
        last_name=last_name,  # type: ignore
        legal_name=legal_name,  # type: ignore
        job_title=job_title,  # type: ignore
        phone=phone,  # type: ignore
        emp_type=emp_type,  # type: ignore
        enterprise_id=enterprise_id,  # type: ignore
        manager_name=manager_name,  # type: ignore
        job_code=job_code,  # type: ignore
        manager_level=manager_level,  # type: ignore
        job_code_track_level=job_code_track_level,  # type: ignore
        hire_date=hire_date,  # type: ignore
        emp_status=emp_status,  # type: ignore
        business_unit_id=business_unit_id if business_unit_id else None,  # type: ignore
        business_segment_id=business_segment_id if business_segment_id else None,  # type: ignore
    )

    db.session.add(new_detail)
    db.session.commit()

    # Invalidate cache
    current_app.logger.info("Invalidating employee cache after creating employee details")
    invalidate_employee_cache()
    invalidate_dashboard_cache()

    flash('Employee details created successfully', 'success')
    return redirect(url_for('admin.employee_details', status='success', action='created'))


@admin_bp.route('/employee-details/<int:detail_id>/update', methods=['POST'])
@login_required
@admin_required
@log_activity('Updated employee details', entity_type='EmployeeDetail', category=Activity.CATEGORY_DATA, method=Activity.METHOD_UPDATE, track_changes=True)
def update_employee_detail(detail_id):
    """Update existing employee details."""
    detail = EmployeeDetail.query.get_or_404(detail_id)

    # Get form data
    employee_number = request.form.get('employee_number')
    first_name = request.form.get('first_name')
    middle_name = request.form.get('middle_name')
    last_name = request.form.get('last_name')
    legal_name = request.form.get('legal_name')
    job_title = request.form.get('job_title')  # Renamed from position
    phone = request.form.get('phone')
    emp_type = request.form.get('emp_type')
    enterprise_id = request.form.get('enterprise_id')
    manager_name = request.form.get('manager_name')
    job_code = request.form.get('job_code')
    manager_level = request.form.get('manager_level')
    job_code_track_level = request.form.get('job_code_track_level')
    business_unit_id = request.form.get('business_unit_id')
    business_segment_id = request.form.get('business_segment_id')
    hire_date_str = request.form.get('hire_date')
    emp_status = request.form.get('emp_status', detail.emp_status)

    # Convert hire_date string to Python date object
    hire_date = None
    if hire_date_str:
        try:
            hire_date = datetime.strptime(hire_date_str, '%Y-%m-%d').date()
        except ValueError:
            flash('Invalid hire date format. Please use YYYY-MM-DD format.', 'error')
            return redirect(url_for('admin.employee_details'))

    # Update employee detail
    detail.employee_number = employee_number
    detail.first_name = first_name
    detail.middle_name = middle_name
    detail.last_name = last_name
    detail.legal_name = legal_name
    detail.job_title = job_title
    detail.phone = phone
    detail.emp_type = emp_type
    detail.enterprise_id = enterprise_id
    detail.manager_name = manager_name
    detail.job_code = job_code
    detail.manager_level = manager_level
    detail.job_code_track_level = job_code_track_level
    detail.hire_date = hire_date
    detail.emp_status = emp_status
    detail.business_unit_id = business_unit_id if business_unit_id else None
    detail.business_segment_id = business_segment_id if business_segment_id else None

    db.session.commit()

    # Invalidate cache
    current_app.logger.info("Invalidating employee cache after updating employee details")
    invalidate_employee_cache()
    invalidate_dashboard_cache()

    flash('Employee details updated successfully', 'success')
    return redirect(url_for('admin.employee_details', status='success', action='updated'))


@admin_bp.route('/employee-details/<int:detail_id>/delete', methods=['POST'])
@login_required
@admin_required
@log_activity('Deleted employee details', entity_type='EmployeeDetail', category=Activity.CATEGORY_DATA, severity=Activity.SEVERITY_WARNING, method=Activity.METHOD_DELETE)
def delete_employee_detail(detail_id):
    """Delete employee details."""
    detail = EmployeeDetail.query.get_or_404(detail_id)

    # Delete employee detail
    db.session.delete(detail)
    db.session.commit()

    # Invalidate cache
    current_app.logger.info("Invalidating employee cache after deleting employee details")
    invalidate_employee_cache()
    invalidate_dashboard_cache()

    flash('Employee details deleted successfully', 'success')
    return redirect(url_for('admin.employee_details', status='success', action='deleted'))


@admin_bp.route('/employee-details/<int:user_id>/view')
@login_required
@admin_required
@log_activity('Viewed specific employee details', entity_type='EmployeeDetail', category=Activity.CATEGORY_ADMIN)
def view_employee_details(user_id):
    """View specific employee details."""
    # Get employee details if they exist
    employee_detail = EmployeeDetail.query.filter_by(user_id=user_id).first()
    user = User.query.get_or_404(user_id)

    if not employee_detail:
        flash('Employee details not found for this user', 'error')
        return redirect(url_for('admin.employee_details'))

    return render_template('admin/view_employee_details.html',
                          active_page='employee_details',
                          employee_detail=employee_detail,
                          user=user)

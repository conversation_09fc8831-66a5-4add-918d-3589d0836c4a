"""
API routes package initialization.
This file creates the API Blueprint and imports all API routes.
"""

from flask import Blueprint

# Create the API Blueprint
api_bp = Blueprint('api', __name__, url_prefix='/api')

# Import all API routes
from app.routes.api.activities import *
from app.routes.api.users import *
from app.routes.api.business import *
from app.routes.api.dashboard import *
from app.routes.api.employees import *
from app.routes.api.entity import *
from app.routes.api.system import *
from app.routes.api.settings import *
from app.routes.api.holidays import *

# Define __all__ to explicitly specify what is exported
__all__ = ['api_bp']
